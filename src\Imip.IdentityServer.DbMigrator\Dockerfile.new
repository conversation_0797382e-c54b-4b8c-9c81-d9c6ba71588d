FROM mcr.microsoft.com/dotnet/aspnet:9.0

ARG PUBLISH_DIR=db-migrator
COPY ${PUBLISH_DIR}/ app/
WORKDIR /app
ENV ASPNETCORE_ENVIRONMENT=Production

# Install necessary locale packages and LDAP libraries (optional)
# Make installation optional to handle network issues gracefully
RUN set +e && \
    echo "Attempting to install packages (optional)..." && \
    apt-get update -o Acquire::Retries=3 -o Acquire::http::Timeout=30 && \
    apt-get install -y --no-install-recommends \
        locales \
        libldap-2.5-0 \
        libldap-common \
        libsasl2-2 \
        libsasl2-modules \
        libgssapi-krb5-2 && \
    echo "Packages installed successfully!" && \
    sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen && \
    locale-gen || \
    echo "WARNING: Package installation failed - some functionality may be limited" && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/* && \
    set -e

# Set locale environment variables
ENV LANG=en_US.UTF-8 \
    LANGUAGE=en_US:en \
    LC_ALL=en_US.UTF-8

# Create directory for certificates
RUN mkdir -p /app/certs

ENTRYPOINT ["dotnet", "Imip.IdentityServer.DbMigrator.dll"]
