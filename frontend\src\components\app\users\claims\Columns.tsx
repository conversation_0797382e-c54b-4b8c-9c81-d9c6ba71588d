'use client'

import { type IdentityUserDto, type IdentityUserUpdateDto } from '@/client'
import { type ColumnDef } from '@tanstack/react-table'
import { Actions } from './Actions'
import { customFilterFunction } from '@/components/data-table/filterFunctions'
import { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'

// Type for the callback function to handle user actions
type UserActionCallback = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void

// Function to create user columns with the action callback
export const getColumns = (
  handleUserAction: UserActionCallback
): ColumnDef<IdentityUserDto>[] => [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      filterFn: customFilterFunction,
      meta: {
        className: "text-left",
        displayName: "Name",
      },
    },
    {
      accessorKey: 'displayName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Display Name" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      filterFn: customFilterFunction,
      meta: {
        className: "text-left",
        displayName: "Display Name",
      },
    },
    {
      accessorKey: 'description',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      filterFn: customFilterFunction,
      meta: {
        className: "text-left",
        displayName: "Description",
      },
    },
    {
      id: 'actions',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Actions" />
      ),
      enableHiding: true,
      cell: (info) => (
        <Actions
          userId={info.row.original.id!}
          userDto={info.row.original as IdentityUserUpdateDto}
          onAction={handleUserAction}
          variant="dropdown" // Use "dropdown" for the first image style or "buttons" for the second image style
        />
      ),
      meta: {
        className: "text-right",
        displayName: "Actions",
      },
    },
  ]
