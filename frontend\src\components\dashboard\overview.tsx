import {
  getApiDashboardAnalyticsHourlyActivity,
  getApiDashboardClientsCount,
  getApiDashboardClientsTopLogins,
  getApiDashboardLoginsFailedCount,
  getApiDashboardRolesDistribution,
  getApiDashboardSecurityOverview,
  getApiDashboardSecurityThreats,
  getApiDashboardSystemHealth,
  getApiDashboardUsersActivity,
  getApiDashboardUsersCount,
  postApiSecurityLogsMyLogs
} from '@/client/sdk.gen';
import type {
  ClientLoginCountDto,
  HourlyActivityDto,
  RoleDistributionDto,
  SecurityLogDto,
  SecurityOverviewDto,
  SecurityThreatsDto,
  SystemHealthDto,
  UserActivityDto
} from '@/client/types.gen';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useCurrentUser } from '@/lib/hooks/useCurrentUser';
import {
  IconActivity,
  IconAlertTriangle,
  IconCircleCheck,
  IconServer,
  IconShield,
  IconTrendingDown,
  IconTrendingUp,
  IconUsers
} from '@tabler/icons-react';
import { useEffect, useState } from 'react';
import { UserRoleApplicationsGrid } from './UserRoleApplicationsGrid';

interface DashboardData {
  usersCount: number;
  clientsCount: number;
  failedLoginsCount: number;
  securityOverview: SecurityOverviewDto | null;
  userActivity: UserActivityDto | null;
  systemHealth: SystemHealthDto | null;
  securityThreats: SecurityThreatsDto | null;
  hourlyActivity: HourlyActivityDto[];
  rolesDistribution: RoleDistributionDto[];
  topClients: ClientLoginCountDto[];
}

export default function OverViewPage() {
  const [data, setData] = useState<DashboardData>({
    usersCount: 0,
    clientsCount: 0,
    failedLoginsCount: 0,
    securityOverview: null,
    userActivity: null,
    systemHealth: null,
    securityThreats: null,
    hourlyActivity: [],
    rolesDistribution: [],
    topClients: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const currentUser = useCurrentUser();
  const isAdmin = currentUser?.roles?.includes('admin');

  // --- New: Non-admin user activity widget state ---
  const [userLogs, setUserLogs] = useState<SecurityLogDto[]>([]);
  const [userLogsLoading, setUserLogsLoading] = useState(false);
  const [userLogsError, setUserLogsError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [
        usersCount,
        clientsCount,
        failedLoginsCount,
        securityOverview,
        userActivity,
        systemHealth,
        securityThreats,
        hourlyActivity,
        rolesDistribution,
        topClients
      ] = await Promise.all([
        getApiDashboardUsersCount(),
        getApiDashboardClientsCount(),
        getApiDashboardLoginsFailedCount(),
        getApiDashboardSecurityOverview(),
        getApiDashboardUsersActivity(),
        getApiDashboardSystemHealth(),
        getApiDashboardSecurityThreats(),
        getApiDashboardAnalyticsHourlyActivity(),
        getApiDashboardRolesDistribution(),
        getApiDashboardClientsTopLogins()
      ]);

      setData({
        usersCount: usersCount.data ?? 0,
        clientsCount: clientsCount.data ?? 0,
        failedLoginsCount: failedLoginsCount.data ?? 0,
        securityOverview: securityOverview.data ?? null,
        userActivity: userActivity.data ?? null,
        systemHealth: systemHealth.data ?? null,
        securityThreats: securityThreats.data ?? null,
        hourlyActivity: hourlyActivity.data ?? [],
        rolesDistribution: rolesDistribution.data ?? [],
        topClients: topClients.data ?? []
      });
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  useEffect(() => {
    if (!isAdmin && currentUser?.id) {
      setUserLogsLoading(true);
      postApiSecurityLogsMyLogs({
        body: {
          action: 'Login',
          startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          endDate: new Date().toISOString(),
          maxResultCount: 20,
          sort: [{ field: 'CreationTime', desc: true }],
        },
      })
        .then((res) => {
          setUserLogs(res.data?.data?.items ?? []);
        })
        .catch(() => setUserLogsError('Failed to load your activity'))
        .finally(() => setUserLogsLoading(false));
    }
  }, [isAdmin, currentUser?.id]);

  const getThreatLevelColor = (level: string) => {
    switch (level?.toLowerCase()) {
      case 'low': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'high': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  // --- New: Non-admin widget rendering ---
  if (!isAdmin) {
    // Calculate stats
    const lastLogin = userLogs.find((log) => !log.identity);
    const successfulLogins = userLogs.filter((log) => !log.identity).length;
    const failedLogins = userLogs.filter((log) => !!log.identity).length;
    const lastLoginTime = lastLogin?.creationTime ? new Date(lastLogin.creationTime).toLocaleString() : 'N/A';
    const lastLoginDevice = lastLogin?.browserInfo || 'N/A';

    return (
      <div className="flex flex-1 flex-col space-y-4">
        <h2 className="text-2xl font-bold tracking-tight">Your Recent Activity</h2>
        {userLogsError && (
          <Alert variant="destructive">
            <IconAlertTriangle className="h-4 w-4" />
            <AlertDescription>{userLogsError}</AlertDescription>
          </Alert>
        )}
        <Card>
          <CardHeader>
            <CardTitle>Last Login</CardTitle>
            <CardDescription>Most recent successful login</CardDescription>
          </CardHeader>
          <div className="p-6 space-y-2">
            <div className="flex items-center gap-2">
              <span className="font-medium">Time:</span>
              <span>{userLogsLoading ? <Skeleton className="h-4 w-24" /> : lastLoginTime}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Device/Browser:</span>
              <span>{userLogsLoading ? <Skeleton className="h-4 w-24" /> : lastLoginDevice}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Successful logins (7d):</span>
              <span>{userLogsLoading ? <Skeleton className="h-4 w-12" /> : successfulLogins}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Failed logins (7d):</span>
              <span>{userLogsLoading ? <Skeleton className="h-4 w-12" /> : failedLogins}</span>
            </div>
          </div>
        </Card>
        <UserRoleApplicationsGrid />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-1 flex-col space-y-4">
        <Alert variant="destructive">
          <IconAlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col space-y-4'>
      <div className='flex items-center justify-between space-y-2'>
        <h2 className='text-2xl font-bold tracking-tight'>
          Identity Server Dashboard 👋
        </h2>
        <Badge variant="outline" className="text-sm">
          {loading ? 'Loading...' : 'Live Data'}
        </Badge>
      </div>

      {/* Main Metrics Cards */}
      <div className='*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4'>
        <Card className='@container/card'>
          <CardHeader>
            <CardDescription className="flex items-center gap-2">
              <IconUsers className="h-4 w-4" />
              Total Users
            </CardDescription>
            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
              {loading ? <Skeleton className="h-8 w-16" /> : data.usersCount.toLocaleString()}
            </CardTitle>
            <CardAction>
              {!loading && data.userActivity && (
                <Badge variant='outline'>
                  {(data.userActivity.userRetentionRate ?? 0) > 0 ? (
                    <>
                      <IconTrendingUp className="h-3 w-3" />
                      +{data.userActivity.userRetentionRate ?? 0}%
                    </>
                  ) : (
                    <>
                      <IconTrendingDown className="h-3 w-3" />
                      {data.userActivity.userRetentionRate ?? 0}%
                    </>
                  )}
                </Badge>
              )}
            </CardAction>
          </CardHeader>
          <CardFooter className='flex-col items-start gap-1.5 text-sm'>
            <div className='line-clamp-1 flex gap-2 font-medium'>
              {loading ? (
                <Skeleton className="h-4 w-32" />
              ) : (
                <>
                  {data.userActivity?.newUsersToday || 0} new today
                  <IconUsers className='size-4' />
                </>
              )}
            </div>
            <div className='text-muted-foreground'>
              {loading ? (
                <Skeleton className="h-4 w-24" />
              ) : (
                `${data.userActivity?.activeUsersToday || 0} active today`
              )}
            </div>
          </CardFooter>
        </Card>

        <Card className='@container/card'>
          <CardHeader>
            <CardDescription className="flex items-center gap-2">
              <IconShield className="h-4 w-4" />
              Total Clients
            </CardDescription>
            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
              {loading ? <Skeleton className="h-8 w-16" /> : data.clientsCount.toLocaleString()}
            </CardTitle>
            <CardAction>
              {!loading && data.securityOverview && (
                <Badge variant='outline'>
                  {(data.securityOverview.loginGrowthPercentage ?? 0) > 0 ? (
                    <>
                      <IconTrendingUp className="h-3 w-3" />
                      +{data.securityOverview.loginGrowthPercentage ?? 0}%
                    </>
                  ) : (
                    <>
                      <IconTrendingDown className="h-3 w-3" />
                      {data.securityOverview.loginGrowthPercentage ?? 0}%
                    </>
                  )}
                </Badge>
              )}
            </CardAction>
          </CardHeader>
          <CardFooter className='flex-col items-start gap-1.5 text-sm'>
            <div className='line-clamp-1 flex gap-2 font-medium'>
              {loading ? (
                <Skeleton className="h-4 w-32" />
              ) : (
                <>
                  {data.securityOverview?.totalLoginsToday || 0} logins today
                  <IconActivity className='size-4' />
                </>
              )}
            </div>
            <div className='text-muted-foreground'>
              {loading ? (
                <Skeleton className="h-4 w-24" />
              ) : (
                `${data.securityOverview?.successRate || 0}% success rate`
              )}
            </div>
          </CardFooter>
        </Card>

        <Card className='@container/card'>
          <CardHeader>
            <CardDescription className="flex items-center gap-2">
              <IconAlertTriangle className="h-4 w-4" />
              Failed Logins
            </CardDescription>
            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
              {loading ? <Skeleton className="h-8 w-16" /> : data.failedLoginsCount.toLocaleString()}
            </CardTitle>
            <CardAction>
              {!loading && data.securityThreats && (
                <Badge
                  variant='outline'
                  className={`${getThreatLevelColor(data.securityThreats.threatLevel || '')} text-white`}
                >
                  {data.securityThreats.threatLevel || 'Unknown'}
                </Badge>
              )}
            </CardAction>
          </CardHeader>
          <CardFooter className='flex-col items-start gap-1.5 text-sm'>
            <div className='line-clamp-1 flex gap-2 font-medium'>
              {loading ? (
                <Skeleton className="h-4 w-32" />
              ) : (
                <>
                  {data.securityThreats?.suspiciousActivities || 0} suspicious activities
                  <IconAlertTriangle className='size-4' />
                </>
              )}
            </div>
            <div className='text-muted-foreground'>
              {loading ? (
                <Skeleton className="h-4 w-24" />
              ) : (
                `${data.securityThreats?.accountLockouts || 0} account lockouts`
              )}
            </div>
          </CardFooter>
        </Card>

        <Card className='@container/card'>
          <CardHeader>
            <CardDescription className="flex items-center gap-2">
              <IconServer className="h-4 w-4" />
              System Health
            </CardDescription>
            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
              {loading ? <Skeleton className="h-8 w-16" /> : data.systemHealth?.systemStatus || 'Unknown'}
            </CardTitle>
            <CardAction>
              {!loading && data.systemHealth && (
                <Badge variant='outline'>
                  <IconCircleCheck className="h-3 w-3" />
                  {data.systemHealth.uptime || 'N/A'}
                </Badge>
              )}
            </CardAction>
          </CardHeader>
          <CardFooter className='flex-col items-start gap-1.5 text-sm'>
            <div className='line-clamp-1 flex gap-2 font-medium'>
              {loading ? (
                <Skeleton className="h-4 w-32" />
              ) : (
                <>
                  Load: {data.systemHealth?.systemLoad || 'Unknown'}
                  <IconActivity className='size-4' />
                </>
              )}
            </div>
            <div className='text-muted-foreground'>
              {loading ? (
                <Skeleton className="h-4 w-24" />
              ) : (
                `Last backup: ${data.systemHealth?.lastBackup ? new Date(data.systemHealth.lastBackup).toLocaleDateString() : 'N/A'}`
              )}
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* Security Overview Card */}
      {!loading && data.securityOverview && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconShield className="h-5 w-5" />
              Security Overview
            </CardTitle>
            <CardDescription>
              Today's security metrics and trends
            </CardDescription>
          </CardHeader>
          <div className="p-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data.securityOverview.totalLoginsToday}
                </div>
                <div className="text-sm text-muted-foreground">Total Logins</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {data.securityOverview.failedLoginsToday}
                </div>
                <div className="text-sm text-muted-foreground">Failed Logins</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data.securityOverview.successRate}%
                </div>
                <div className="text-sm text-muted-foreground">Success Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {data.securityOverview.loginGrowthPercentage}%
                </div>
                <div className="text-sm text-muted-foreground">Growth</div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Charts Grid */}
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7'>
        <div className='col-span-4'>
          <Card>
            <CardHeader>
              <CardTitle>Hourly Activity</CardTitle>
              <CardDescription>Login activity over the last 24 hours</CardDescription>
            </CardHeader>
            <div className="p-6">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 24 }).map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {data.hourlyActivity.map((hour) => (
                    <div key={hour.hour} className="flex items-center justify-between">
                      <span className="text-sm">{hour.hour}:00</span>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-green-600">{hour.loginCount} logins</span>
                        <span className="text-sm text-red-600">{hour.failedLoginCount} failed</span>
                        <Progress value={hour.successRate} className="w-20" />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </div>

        <div className='col-span-4 md:col-span-3'>
          <Card>
            <CardHeader>
              <CardTitle>Top Clients</CardTitle>
              <CardDescription>Most active applications</CardDescription>
            </CardHeader>
            <div className="p-6">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-8 w-full" />
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {data.topClients.map((client, index) => (
                    <div key={client.clientId} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">#{index + 1}</span>
                        <span className="text-sm truncate">{client.clientId}</span>
                      </div>
                      <Badge variant="secondary">{client.count} logins</Badge>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </div>

        <div className='col-span-4'>
          <Card>
            <CardHeader>
              <CardTitle>Role Distribution</CardTitle>
              <CardDescription>Users per role</CardDescription>
            </CardHeader>
            <div className="p-6">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-8 w-full" />
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {data.rolesDistribution.map((role) => (
                    <div key={role.roleName} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{role.roleName}</span>
                      <div className="flex items-center gap-2">
                        <Progress value={((role.userCount ?? 0) / data.usersCount) * 100} className="w-20" />
                        <Badge variant="outline">{role.userCount ?? 0} users</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </div>

        <div className='col-span-4 md:col-span-3'>
          <Card>
            <CardHeader>
              <CardTitle>Security Recommendations</CardTitle>
              <CardDescription>Based on current threats</CardDescription>
            </CardHeader>
            <div className="p-6">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {data.securityThreats?.recommendations?.map((rec, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <IconAlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                      <span className="text-sm">{rec}</span>
                    </div>
                  )) || (
                      <div className="flex items-center gap-2 text-green-600">
                        <IconCircleCheck className="h-4 w-4" />
                        <span className="text-sm">Security posture is good. Continue monitoring.</span>
                      </div>
                    )}
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
