"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ViewOptions } from "@/components/data-table/DataTableViewOptions"
import { Search } from "@/components/ui/search"
import { type FilterOperator } from "@/client"
import { type Table } from "@tanstack/react-table"
import { RiAddLine, RiCloseLine, RiFilterLine } from "@remixicon/react"
import { Loader2 } from "lucide-react"
import { useEffect, useRef, useState } from "react"

interface NotionFilterProps<TData> {
  table: Table<TData>
  onSearch?: (value: string) => void
  searchValue?: string
  onServerFilter?: (filterConditions: Array<{ fieldName: string, operator: FilterOperator, value: string }>) => void
  activeFilters?: Array<{ fieldName: string, operator: FilterOperator, value: string }>
}

type FilterCondition = {
  id: string
  columnId: string
  operator: FilterOperator
  value: string
}

// Map of user-friendly operator names to actual FilterOperator values
const operatorOptions: { label: string; value: FilterOperator }[] = [
  { label: "Equals", value: "Equals" },
  { label: "Not Equals", value: "NotEquals" },
  { label: "Contains", value: "Contains" },
  { label: "Starts With", value: "StartsWith" },
  { label: "Ends With", value: "EndsWith" },
  { label: "Greater Than", value: "GreaterThan" },
  { label: "Greater Than or Equal", value: "GreaterThanOrEqual" },
  { label: "Less Than", value: "LessThan" },
  { label: "Less Than or Equal", value: "LessThanOrEqual" },
  { label: "Is Empty", value: "IsEmpty" },
  { label: "Is Not Empty", value: "IsNotEmpty" },
  { label: "Is Null", value: "IsNull" },
  { label: "Is Not Null", value: "IsNotNull" },
]

export function NotionFilter<TData>({
  table,
  onSearch,
  searchValue = "",
  onServerFilter,
  activeFilters = [],
}: NotionFilterProps<TData>) {
  const [isOpen, setIsOpen] = useState(false)

  // Initialize with empty array, we'll set it in useEffect
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])

  // Generate a unique ID for new filter conditions
  function generateId() {
    return `filter-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
  }

  // Keep filter conditions in sync with activeFilters prop - only on initial mount
  // We use a ref to track if this is the first render
  const initialRenderRef = useRef(true)

  useEffect(() => {
    // Only update on the initial render or if activeFilters changes significantly
    if (initialRenderRef.current) {
      initialRenderRef.current = false

      if (activeFilters && activeFilters.length > 0) {
        // Convert activeFilters to internal FilterCondition format
        const updatedFilterConditions = activeFilters.map(filter => ({
          id: generateId(),
          columnId: filter.fieldName,
          operator: filter.operator,
          value: filter.value
        }))

        setFilterConditions(updatedFilterConditions)
      }
    }
  }, [activeFilters])

  // Get all visible columns that can be filtered
  const filterableColumns = table
    .getAllColumns()
    .filter(
      (column) =>
        // Exclude special columns
        column.id !== "select" &&
        column.id !== "actions" &&
        column.id !== "drag" &&
        // Only include columns that have a filter function
        column.getCanFilter()
    )

  // Count active filters - both from table state and our filter conditions
  const activeFilterCount = filterConditions.filter(f => f.value).length
  const isFiltered = activeFilterCount > 0 || table.getState().columnFilters.length > 0

  // Track loading state for filter operations
  const [isFilterLoading, setIsFilterLoading] = useState(false)

  // Add a new empty filter condition
  const addFilterCondition = () => {
    if (filterableColumns.length === 0) return

    const newCondition: FilterCondition = {
      id: generateId(),
      columnId: filterableColumns[0]?.id ?? "",
      operator: "Contains" as FilterOperator,
      value: "",
    }

    setFilterConditions([...filterConditions, newCondition])
  }

  // Remove a filter condition by ID
  const removeFilterCondition = (id: string) => {
    setFilterConditions(filterConditions.filter(condition => condition.id !== id))
  }

  // Update a specific filter condition
  const updateFilterCondition = (id: string, updates: Partial<FilterCondition>) => {
    setFilterConditions(filterConditions.map(condition => {
      if (condition.id === id) {
        return { ...condition, ...updates }
      }
      return condition
    }))
  }

  // Apply all filter conditions to the table
  const applyFilters = () => {
    // Show loading state
    setIsFilterLoading(true)

    // First, clear any existing filters
    table.resetColumnFilters()

    // If we have a server filter callback, use it
    if (onServerFilter) {
      // Convert our filter conditions to the format expected by the API
      const apiFilterConditions = filterConditions
        .filter(condition => condition.value) // Only include conditions with values
        .map(condition => ({
          fieldName: condition.columnId,
          operator: condition.operator,
          value: condition.value
        }))

      // Call the server filter callback
      if (apiFilterConditions.length > 0) {
        onServerFilter(apiFilterConditions)
      }
    } else {
      // Otherwise, apply filters client-side
      filterConditions.forEach(condition => {
        const column = table.getColumn(condition.columnId)
        if (column && condition.value) {
          column.setFilterValue({
            operator: condition.operator,
            value: condition.value
          })
        }
      })
    }

    // Hide the filter dropdown
    setIsOpen(false)

    // Reset loading state after a short delay
    setTimeout(() => {
      setIsFilterLoading(false)
    }, 500)
  }

  // Clear all filters
  const clearFilters = () => {
    setFilterConditions([])
    table.resetColumnFilters()

    // If we have a server filter callback, call it with empty conditions
    if (onServerFilter) {
      onServerFilter([])
    }
  }

  // Get a user-friendly column name
  const getColumnDisplayName = (columnId: string) => {
    const column = table.getColumn(columnId)

    // Try to get the display name from the meta property first
    if (column?.columnDef?.meta?.displayName) {
      return column.columnDef.meta.displayName
    }

    // If no meta.displayName, try to get the header
    if (column?.columnDef?.header) {
      // If header is a function, it might be a React component
      // In this case, we'll use the column ID with first letter capitalized
      if (typeof column.columnDef.header === 'function') {
        return columnId.charAt(0).toUpperCase() + columnId.slice(1)
      }

      // If header is a string, use it directly
      return column.columnDef.header.toString()
    }

    // Fallback to the column ID with first letter capitalized
    return columnId.charAt(0).toUpperCase() + columnId.slice(1)
  }

  return (
    <div className="flex flex-wrap items-center justify-between gap-2">
      {onSearch && (
        <div className="w-full sm:w-auto sm:max-w-[250px]">
          <Search
            onUpdate={(value) => {
              if (onSearch) onSearch(value);
            }}
            value={searchValue || ''}
          />
        </div>
      )}

      <div className="flex items-center gap-2 ml-auto">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant={isFiltered ? "default" : "outline"}
              size="sm"
              className="h-8 gap-1"
            >
              <RiFilterLine className="h-3.5 w-3.5" />
              <span>Filter</span>
              {isFiltered && (
                <span className="ml-1 rounded-full bg-primary/20 px-1 text-xs font-medium">
                  {activeFilterCount}
                </span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[600px] p-3" align="end">
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium leading-none">Filter</h4>
                <p className="text-sm text-muted-foreground">
                  Filter records by specific conditions
                </p>
              </div>

              {filterConditions.length > 0 ? (
                <div className="space-y-2">
                  {filterConditions.map((condition) => (
                    <div key={condition.id} className="rounded-md border p-3">
                      <div className="grid grid-cols-3 gap-3">
                        <div className="col-span-1">
                          <div className="flex items-center justify-between mb-1">
                            <Label className="text-xs font-medium">Column</Label>
                          </div>
                          <Select
                            value={condition.columnId}
                            onValueChange={(value) => updateFilterCondition(condition.id, { columnId: value })}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select column" />
                            </SelectTrigger>
                            <SelectContent>
                              {filterableColumns.map((column) => (
                                <SelectItem key={column.id} value={column.id}>
                                  {getColumnDisplayName(column.id)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="col-span-1">
                          <div className="flex items-center justify-between mb-1">
                            <Label className="text-xs font-medium">Operator</Label>
                          </div>
                          <Select
                            value={condition.operator}
                            onValueChange={(value) => updateFilterCondition(condition.id, { operator: value as FilterOperator })}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select operator" />
                            </SelectTrigger>
                            <SelectContent>
                              {operatorOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="col-span-1">
                          <div className="flex">
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <Label className="text-xs font-medium">Value</Label>
                              </div>
                              <Input
                                placeholder="Enter value"
                                className="w-full"
                                value={condition.value}
                                onChange={(e) => updateFilterCondition(condition.id, { value: e.target.value })}
                              />
                            </div>
                            <div className="col-span-1 flex items-end justify-end">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => removeFilterCondition(condition.id)}
                              >
                                <RiCloseLine className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex h-20 items-center justify-center rounded-md border border-dashed">
                  <p className="text-sm text-muted-foreground">No filters added</p>
                </div>
              )}

              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 gap-1"
                  onClick={addFilterCondition}
                >
                  <RiAddLine className="h-3.5 w-3.5" />
                  <span>Add filter</span>
                </Button>
                {filterConditions.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8"
                    onClick={clearFilters}
                  >
                    Clear all
                  </Button>
                )}
              </div>

              <Button
                className="w-full"
                onClick={applyFilters}
                disabled={filterConditions.length === 0 || isFilterLoading}
              >
                {isFilterLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Applying...
                  </>
                ) : (
                  "Apply filters"
                )}
              </Button>
            </div>
          </PopoverContent>
        </Popover>

        {isFiltered && (
          <Button
            variant="ghost"
            onClick={clearFilters}
            className="border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500"
          >
            Clear filters
          </Button>
        )}
        <ViewOptions table={table} />
      </div>
    </div>
  )
}

