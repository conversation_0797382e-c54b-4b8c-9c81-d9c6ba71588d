import { type CreateUpdateClaimTypeDto, type IdentityClaimTypeDto, putApiIdentityClaimTypesById, type RemoteServiceErrorResponse } from '@/client'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { FormField, FormSection } from '@/components/ui/FormField'
import { Input } from '@/components/ui/input'
import { MultiSelect } from '@/components/ui/multi-select'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useToast } from '@/lib/useToast'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { itemValueType } from './variable'

type UserEditProps = {
  dataEdit: IdentityClaimTypeDto
  dataId: string
  onDismiss: () => void
}
export const Edit = ({ dataEdit, dataId, onDismiss }: UserEditProps) => {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, setValue } = useForm<CreateUpdateClaimTypeDto>()
  const [isRequired, setIsRequired] = useState(dataEdit.required ?? false)
  const [isStatic, setIsStatic] = useState(dataEdit.isStatic ?? false)
  const [selectedValueType, setSelectedValueType] = useState<string[]>([])

  const updateDataMutation = useMutation({
    mutationFn: async (formData: CreateUpdateClaimTypeDto) =>
      putApiIdentityClaimTypesById({
        path: { id: dataId },
        body: formData,
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Claim Type Updated Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })
      onCloseEvent()
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err?.error?.message as string,
        description: err?.error?.details,
        variant: 'destructive',
      })
    }
  })

  const onSubmit = (formData: CreateUpdateClaimTypeDto) => {
    // Merge form data with consent type and permissions
    const userData: CreateUpdateClaimTypeDto = {
      ...formData,
    }

    // Explicitly mark the promise as handled
    void updateDataMutation.mutate(userData)
  }

  const onCloseEvent = () => {
    setOpen(false)
    onDismiss()
  }

  // Initialize form when opened
  useEffect(() => {
    if (open) {
      // Initialize form values from dataEdit
      setValue('name', dataEdit.name ?? '')
      setValue('required', dataEdit.required ?? false)
      setValue('isStatic', dataEdit.isStatic ?? false)
      setValue('regex', dataEdit.regex ?? '')
      setValue('regexDescription', dataEdit.regexDescription ?? '')
      setValue('description', dataEdit.description ?? '')
      setValue('valueType', dataEdit.valueType ?? 0)

      // Initialize MultiSelect value
      const valueTypeStr = (dataEdit.valueType !== undefined && dataEdit.valueType !== null)
        ? dataEdit.valueType.toString()
        : '0'
      setSelectedValueType([valueTypeStr])
    }
  }, [open, dataEdit, setValue])

  useEffect(() => {
    setOpen(true)
  }, [])

  return (
    <Dialog open={open} onOpenChange={onCloseEvent}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update a Claim Type: {dataEdit.name}</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={handleSubmit(onSubmit)}
          className='mt-2'
          onKeyDown={(e) => {
            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {
              e.preventDefault();
              void handleSubmit(onSubmit)();
            }
          }}
        >
          <section className="flex w-full flex-col space-y-2">
            <FormSection>
              <FormField
                label="Name"
                description="The name of the claim"
              >
                <Input required {...register('name')} defaultValue={dataEdit.name ?? ''} placeholder="Claim Name" />
              </FormField>

              <FormField
                label="Required"
                description="Whether the claim is required"
              >
                <Checkbox
                  checked={isRequired}
                  onCheckedChange={(checked) => {
                    setIsRequired(!!checked)
                    setValue('required', !!checked)
                  }}
                />
              </FormField>

              <FormField
                label="Is Static"
                description="Whether the claim is static"
              >
                <Checkbox
                  checked={isStatic}
                  onCheckedChange={(checked) => {
                    setIsStatic(!!checked)
                    setValue('isStatic', !!checked)
                  }}
                />
              </FormField>

              <FormField
                label="Regex"
                description="The regex for the claim"
              >
                <Input {...register('regex')} defaultValue={dataEdit.regex ?? ''} placeholder="Regex" />
              </FormField>

              <FormField
                label="Regex Description"
                description="The description for the regex"
              >
                <Input {...register('regexDescription')} defaultValue={dataEdit.regexDescription ?? ''} placeholder="Regex Description" />
              </FormField>

              <FormField
                label="Description"
                description="The description for the claim"
              >
                <Input required {...register('description')} defaultValue={dataEdit.description ?? ''} placeholder="Description" />
              </FormField>

              <FormField
                label="Value Type"
                description="The value type for the claim"
              >
                <MultiSelect
                  mode='single'
                  options={itemValueType}
                  value={selectedValueType}
                  onChange={setSelectedValueType}
                  placeholder="Select value type"
                  maxHeight={300}
                  name="valueType"
                  register={register}
                  valueAsNumber={true}
                />
              </FormField>
            </FormSection>
          </section>
          <DialogFooter className="mt-5">
            <Button
              variant="ghost"
              onClick={(e) => {
                e.preventDefault()
                setOpen(false)
              }}
              disabled={updateDataMutation.isPending}
              type="button"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateDataMutation.isPending}
            >
              {updateDataMutation.isPending ? 'Saving...' : 'Save'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
