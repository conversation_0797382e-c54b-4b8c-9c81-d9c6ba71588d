import{u as A,r as o,f as D,j as e}from"./vendor-B0b15ZrB.js";import{h as v,Q as b,Y as L,u as E,B as y,Z as I,_ as k,$ as _,a0 as G,G as S,a1 as O,a2 as z,a as $,a3 as H,a4 as Q,a5 as B,a6 as K,a7 as V,a8 as Y,a9 as R,aa as W,ab as Z,A as J}from"./app-layout-D_A4XD_6.js";import{u as w,D as F,b as q,c as X,d as T,I as c,e as M,C as g}from"./index.esm-DqIqfoOW.js";import{T as ee}from"./textarea-DPuaXqY_.js";import{a as x}from"./FormField-POW7SsfI.js";import{L as te}from"./lock-CJDOiBy7.js";import{$ as se}from"./App-De6zOdMU.js";import"./radix-BQPyiA8r.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ae=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],ne=v("eye-off",ae);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ie=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],re=v("eye",ie);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],ce=v("mail",le);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]],de=v("server",oe);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ue=[["circle",{cx:"15",cy:"12",r:"3",key:"1afu0r"}],["rect",{width:"20",height:"14",x:"2",y:"5",rx:"7",key:"g7kal2"}]],me=v("toggle-right",ue),he=()=>A({queryKey:[b.GetEmailing],queryFn:async()=>{const{data:n}=await L();return n}}),xe=({onDismiss:n})=>{const[s,h]=o.useState(!1),{toast:p}=E(),{handleSubmit:a,register:t}=w(),d=D();o.useEffect(()=>{h(!0)},[]);const i=async m=>{try{await I({body:m}),p({title:"Success",description:"Test email has been sent Successfully",variant:"default"}),d.invalidateQueries({queryKey:[b.GetRoles]}),u()}catch(f){f instanceof Error&&p({title:"Failed",description:"Test email wasn't successful.",variant:"destructive"})}},u=()=>{h(!1),n()};return e.jsx(F,{open:s,onOpenChange:u,children:e.jsxs(q,{children:[e.jsx(X,{children:e.jsx(T,{children:"Send Test Email"})}),e.jsxs("form",{onSubmit:a(i),children:[e.jsxs("section",{className:"flex w-full flex-col space-y-5",children:[e.jsx(c,{required:!0,...t("senderEmailAddress"),placeholder:"Sender Email Address"}),e.jsx(c,{required:!0,...t("targetEmailAddress"),placeholder:"Target Email Address"}),e.jsx(c,{required:!0,...t("subject"),placeholder:"Subject"}),e.jsx(ee,{placeholder:"Body",...t("body")})]}),e.jsxs(M,{className:"mt-5",children:[e.jsx(y,{type:"submit",children:"Send"}),e.jsx(y,{onClick:m=>{m.preventDefault(),u()},children:"Close"})]})]})]})})},pe=()=>{const{toast:n}=E(),{data:s}=he(),h=D(),[p,a]=o.useState(!1),{handleSubmit:t,register:d}=w(),[i,u]=o.useState(s);o.useEffect(()=>{s&&u({...s})},[s]);const m=o.useCallback(l=>{const{value:j,name:C}=l.target;u({...i,[C]:j})},[i]),f=async()=>{try{await _({body:i}),n({title:"Success",description:"Email settings updated successfully",variant:"default"}),h.invalidateQueries({queryKey:[b.GetEmailing]})}catch(l){l instanceof Error&&n({title:"Failed",description:"Email settings wasn't successfull.",variant:"destructive"})}};return e.jsxs("section",{className:"emailing p-5 xl:p-10",children:[p&&e.jsx(xe,{onDismiss:()=>a(!1)}),e.jsx("h3",{className:"text-xl font-medium",children:"Emailing"}),e.jsx("hr",{className:"mt-2 border"}),e.jsx("div",{className:"pt-5",children:e.jsxs("form",{onSubmit:t(f),children:[e.jsxs("div",{className:"mb-5 space-y-5",children:[e.jsx(c,{type:"text",...d("defaultFromDisplayName"),required:!0,placeholder:"Default from display name",value:i?.defaultFromDisplayName??"",onChange:m}),e.jsx(c,{type:"email",...d("defaultFromAddress"),required:!0,placeholder:"Default from address",value:i?.defaultFromAddress??"",onChange:m}),e.jsx(c,{type:"text",...d("smtpHost"),placeholder:"Host",value:i?.smtpHost??"",onChange:m}),e.jsx(c,{type:"number",...d("smtpPort"),required:!0,placeholder:"Port",value:i?.smtpPort??0,onChange:m}),e.jsxs("div",{className:k("flex items-center space-x-2"),children:[e.jsx(g,{id:"ssl",...d("smtpEnableSsl"),checked:i?.smtpEnableSsl,onCheckedChange:l=>{u({...i,smtpEnableSsl:!!l.valueOf()})}}),e.jsx("label",{htmlFor:"ssl",className:"text-sm font-medium leading-none",children:"Enable ssl"})]}),e.jsxs("div",{className:k("flex items-center space-x-2"),children:[e.jsx(g,{id:"credentials",name:"smtpUseDefaultCredentials",checked:i?.smtpUseDefaultCredentials,onCheckedChange:l=>u({...i,smtpUseDefaultCredentials:!!l.valueOf()})}),e.jsx("label",{htmlFor:"credentials",className:"text-sm font-medium leading-none",children:"Use default credentials"})]}),!i?.smtpUseDefaultCredentials&&e.jsxs(e.Fragment,{children:[e.jsx(c,{type:"text",...d("smtpDomain"),placeholder:"Domain",value:i?.smtpDomain??"",onChange:m}),e.jsx(c,{type:"text",...d("smtpUserName"),placeholder:"User name",value:i?.smtpUserName??"",onChange:m}),e.jsx(c,{type:"password",...d("smtpPassword"),placeholder:"Password",value:i?.smtpPassword??"",onChange:m})]})]}),e.jsxs("div",{className:"w-full space-x-5 space-y-5",children:[e.jsx(y,{type:"submit",children:"Save"}),e.jsx(y,{variant:"default",onClick:l=>{l.preventDefault(),a(!0)},children:"Send Test Email"})]})]})})]})},ye=({onDismiss:n})=>{const[s,h]=o.useState(!1),p=()=>{h(!1),n()};return o.useEffect(()=>{h(!0)},[]),e.jsx(F,{open:s,onOpenChange:p,children:e.jsxs(q,{children:[e.jsx(T,{children:"Features"}),e.jsx("article",{children:e.jsx("p",{children:"There isn't any available feature."})}),e.jsxs(M,{className:"mt-5",children:[e.jsx(y,{onClick:a=>{a.preventDefault(),p()},children:"Cancel"}),e.jsx(y,{type:"submit",children:"Save"})]})]})})},fe=()=>{const[n,s]=o.useState(!1);return e.jsxs("section",{className:"feature-management p-5 xl:p-10",children:[n&&e.jsx(ye,{onDismiss:()=>s(!1)}),e.jsx("h1",{className:"text-xl font-medium",children:"Feature Management"}),e.jsx("hr",{className:"mt-2 border"}),e.jsxs("div",{className:"pt-5",children:[e.jsx("article",{className:"text-base-content mb-5",children:e.jsx("p",{children:"You can manage the host side features by clicking the following button."})}),e.jsx(y,{onClick:()=>s(!0),children:"Manage Host Features"})]})]})},je=()=>A({queryKey:[b.GetActiveDirectory],queryFn:async()=>{const{data:n}=await G();return n}}),ge=()=>{const{toast:n}=E(),{data:s}=je(),h=D(),{handleSubmit:p,register:a}=w(),[t,d]=o.useState(s),i=o.useId(),[u,m]=o.useState(!1),f=()=>m(r=>!r);o.useEffect(()=>{s&&d({...s})},[s]);const l=o.useCallback(r=>{const{value:N,name:U}=r.target;t&&d({...t,[U]:N})},[t]),j=o.useCallback((r,N)=>{t&&d({...t,[r]:N})},[t]),C=async()=>{try{await z({body:t}),n({title:"Success",description:"Active Directory settings updated successfully",variant:"default"}),h.invalidateQueries({queryKey:[b.GetActiveDirectory]})}catch(r){r instanceof Error&&n({title:"Failed",description:"Active Directory settings update wasn't successful.",variant:"destructive"})}},P=async()=>{try{await O({body:t}),n({title:"Success",description:"Connection to Active Directory successful",variant:"default"})}catch(r){r instanceof Error&&n({title:"Failed",description:"Connection to Active Directory failed",variant:"destructive"})}};return e.jsxs("section",{className:"p-5 xl:p-10",children:[e.jsx("h3",{className:"font-medium text-xl",children:"Active Directory"}),e.jsx("hr",{className:"mt-2 border"}),e.jsx("div",{className:"pt-5",children:e.jsxs("form",{onSubmit:p(C),children:[e.jsxs("div",{className:"mb-5 space-y-5",children:[e.jsx(x,{label:"Enable Active Directory",description:"",children:e.jsxs("div",{className:S("flex items-center space-x-2"),children:[e.jsx(g,{id:"enabled",checked:t?.enabled,onCheckedChange:r=>j("enabled",!!r)}),e.jsx("label",{htmlFor:"enabled",className:"font-medium leading-none text-sm",children:"Enable Active Directory"})]})}),e.jsx(x,{label:"Domain",description:"",children:e.jsx(c,{type:"text",...a("domain"),required:!0,placeholder:"Domain",value:t?.domain??"",onChange:l})}),e.jsx(x,{label:"LDAP Server",description:"",children:e.jsx(c,{type:"text",...a("ldapServer"),required:!0,placeholder:"LDAP Server",value:t?.ldapServer??"",onChange:l})}),e.jsx(x,{label:"Base DN",description:"",children:e.jsx(c,{type:"text",...a("baseDn"),placeholder:"Base DN",value:t?.baseDn??"",onChange:l})}),e.jsx(x,{label:"Username",description:"",children:e.jsx(c,{type:"text",...a("username"),required:!0,placeholder:"Username",value:t?.username??"",onChange:l})}),e.jsx(x,{label:"Password",description:"",children:e.jsxs("div",{className:"relative",children:[e.jsx(c,{id:i,type:u?"text":"password",...a("password"),required:!0,placeholder:"Password",className:"pe-9",value:t?.password??"",onChange:l}),e.jsx("button",{className:"text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",type:"button",onClick:f,"aria-label":u?"Hide password":"Show password","aria-pressed":u,"aria-controls":"password",children:u?e.jsx(ne,{size:16,"aria-hidden":"true"}):e.jsx(re,{size:16,"aria-hidden":"true"})})]})}),e.jsx(x,{label:"port",description:"",children:e.jsx(c,{type:"text",...a("port"),required:!0,placeholder:"Port",value:t?.port??"",onChange:l})}),e.jsx(x,{label:"useSsl",description:"",children:e.jsxs("div",{className:S("flex items-center space-x-2"),children:[e.jsx(g,{id:"useSsl",checked:t?.useSsl,onCheckedChange:r=>j("useSsl",!!r)}),e.jsx("label",{htmlFor:"useSsl",className:"font-medium leading-none text-sm",children:"Use SSL"})]})}),e.jsx(x,{label:"autoLogin",description:"",children:e.jsxs("div",{className:S("flex items-center space-x-2"),children:[e.jsx(g,{id:"autoLogin",checked:t?.autoLogin,onCheckedChange:r=>j("autoLogin",!!r)}),e.jsx("label",{htmlFor:"autoLogin",className:"font-medium leading-none text-sm",children:"Auto Login"})]})}),e.jsx(x,{label:"Token Secret",description:"",children:e.jsx(c,{type:"text",...a("tokenSecret"),placeholder:"Token Secret",value:t?.tokenSecret??"",onChange:l})}),e.jsx(x,{label:"Default Username",description:"",children:e.jsx(c,{type:"text",...a("defaultUsername"),placeholder:"Default Username",value:t?.defaultUsername??"",onChange:l})}),e.jsx(x,{label:" Enable Windows Authentication",description:"",children:e.jsxs("div",{className:S("flex items-center space-x-2"),children:[e.jsx(g,{id:"windowsAuthEnabled",checked:t?.windowsAuthEnabled,onCheckedChange:r=>j("windowsAuthEnabled",!!r)}),e.jsx("label",{htmlFor:"windowsAuthEnabled",className:"text-sm font-medium leading-none",children:"Enable Windows Authentication"})]})})]}),e.jsxs("div",{className:"w-full space-x-5",children:[e.jsx(y,{type:"submit",children:"Save"}),e.jsx(y,{type:"button",variant:"outline",onClick:r=>{r.preventDefault(),P()},children:"Test Connection"})]})]})})]})},ve={nav:[{name:"Emailing",icon:ce,content:pe,policy:"SettingManagement.Emailing"},{name:"Feature management",icon:me,content:fe,policy:"FeatureManagement.ManageHostFeatures"},{name:"Active Directory",icon:de,content:ge,policy:"IdentityServer.ActiveDirectorySettings"},{name:"Privacy & visibility",icon:te,policy:"AbpIdentity.Users",content:()=>e.jsxs("section",{className:"privacy p-5 xl:p-10",children:[e.jsx("h1",{className:"font-medium text-xl",children:"Privacy & Visibility"}),e.jsx("hr",{className:"mt-2 border"}),e.jsx("div",{className:"pt-5",children:e.jsx("article",{className:"mb-5 text-base-content",children:e.jsx("p",{children:"Configure privacy and visibility settings here."})})})]})}]};function be(){const{can:n}=$(),s=ve.nav.filter(t=>n(t.policy)),[h,p]=o.useState(s.length>0&&s[0]?.name?s[0].name:""),a=s.length>0?s.find(t=>t.name===h)||s[0]:void 0;return s.length===0?e.jsx("div",{className:"flex items-center justify-center",children:e.jsx("p",{className:"text-muted-foreground",children:"You don't have permission to access any settings."})}):e.jsxs(H,{className:"items-start",children:[e.jsx(Q,{collapsible:"none",className:"hidden md:flex",children:e.jsx(B,{children:e.jsx(K,{children:e.jsx(V,{children:e.jsx(Y,{children:s.map(t=>e.jsx(R,{children:e.jsx(W,{asChild:!0,isActive:t.name===h,onClick:()=>p(t.name),children:e.jsxs("button",{type:"button",children:[e.jsx(t.icon,{}),e.jsx("span",{children:t.name})]})})},t.name))})})})})}),e.jsx("main",{className:"flex flex-1 flex-col overflow-hidden",children:e.jsx("div",{className:"flex flex-1 flex-col overflow-y-auto",children:a&&e.jsx(Z,{policy:a.policy,message:`You don't have permission to access ${a.name} settings.`,children:typeof a.content=="function"?e.jsx(a.content,{}):o.createElement(a.content)})})})]})}function Fe(){return e.jsxs(J,{children:[e.jsx(se,{title:"Settings"}),e.jsx(be,{})]})}export{Fe as default};
//# sourceMappingURL=setting-4wr_CdUz.js.map
