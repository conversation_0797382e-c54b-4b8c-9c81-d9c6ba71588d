# Certificate Setup Guide for Identity Server

This guide explains how to prepare your .pfx certificate for use with the Identity Server in GitLab CI/CD and Kubernetes.

## SSL Configuration

The current setup uses your .pfx certificate for SSL within the Identity Server application itself, rather than terminating SSL at the ingress level. This means:

1. Your application handles the SSL/TLS encryption
2. The ingress controller passes traffic directly to your application without SSL termination
3. Your .pfx certificate is used for all secure communications

## Prerequisites

- Your .pfx certificate file
- The certificate passphrase
- Access to GitLab CI/CD variables

## Steps to Prepare Your Certificate

### 1. Base64 Encode Your Certificate

You need to encode your .pfx file as a base64 string to store it in GitLab CI/CD variables.

#### On Windows:

```powershell
$certPath = "path\to\your\certificate.pfx"
$base64Cert = [Convert]::ToBase64String([IO.File]::ReadAllBytes($certPath))
$base64Cert | Out-File -FilePath "certificate-base64.txt"
```

#### On Linux/macOS:

```bash
base64 -i path/to/your/certificate.pfx > certificate-base64.txt
```

### 2. Add the Certificate to GitLab CI/CD Variables

1. Go to your GitLab project
2. Navigate to Settings > CI/CD
3. Expand the "Variables" section
4. Click "Add Variable"
5. Set the following:
   - Key: `IDENTITY_SERVER_CERT`
   - Value: Paste the base64-encoded certificate string from the file you created
   - Type: Variable
   - Environment scope: All (or specific environments if needed)
   - Protect variable: Yes
   - Mask variable: No (it's already encoded)

### 3. Add the Certificate Passphrase to GitLab CI/CD Variables

1. In the same Variables section, click "Add Variable" again
2. Set the following:
   - Key: `CERT_PASSPHRASE`
   - Value: Your certificate passphrase
   - Type: Variable
   - Environment scope: All (or specific environments if needed)
   - Protect variable: Yes
   - Mask variable: Yes

## How It Works

The GitLab CI pipeline will:

1. Create a Kubernetes Secret containing your certificate
2. Mount this Secret as a volume in your application pods
3. Configure the application to use the certificate with the provided passphrase

The certificate will be available at `/app/certs/identity-server.pfx` inside your containers, and the application is configured to use this path.

## Troubleshooting

If you encounter issues with the certificate:

1. Verify the base64 encoding is correct
2. Check that the passphrase is correct
3. Ensure the certificate is valid and not expired
4. Check the Kubernetes pod logs for any certificate-related errors

### SSL Connection Issues

If clients have trouble connecting to your service:

1. Ensure your .pfx certificate is trusted by clients
2. Verify that your application is correctly configured to use the certificate
3. Check that the certificate contains the correct domain names in its Subject Alternative Name (SAN) field
4. Confirm that your application is listening on the correct port (usually 443 for HTTPS)

For security reasons, never commit your certificate files or passphrase to the repository.
