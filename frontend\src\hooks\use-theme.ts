import * as React from "react"

const { useContext } = React

type Theme = "dark" | "light" | "system"

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => void,
  resolvedTheme?: "dark" | "light" | "system" // Add this property
}

const initialState: ThemeProviderState = {
  theme: "system",
  setTheme: () => null,
  resolvedTheme: undefined, // Add this to initial state
}

// This context will be imported from the theme provider
export const ThemeProviderContext = React.createContext<ThemeProviderState>(initialState)

export function useTheme() {
  const context = useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider")

  return context
}

// Re-export the types for convenience
export type { Theme, ThemeProviderState }
