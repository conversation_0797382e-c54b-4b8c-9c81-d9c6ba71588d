{"version": 3, "file": "user-6kqXUNQx.js", "sources": ["../../../../../frontend/src/lib/hooks/useUsers.ts", "../../../../../frontend/src/components/app/users/AddUser.tsx", "../../../../../frontend/src/components/app/users/UploadUsersCsv.tsx", "../../../../../frontend/src/components/app/users/DeleteUser.tsx", "../../../../../frontend/src/lib/hooks/useAssignableRoles.ts", "../../../../../frontend/src/lib/hooks/useUserRoles.ts", "../../../../../frontend/src/components/app/users/UserEdit.tsx", "../../../../../frontend/src/components/app/users/UserPermission.tsx", "../../../../../frontend/src/components/app/users/StatusBadge.tsx", "../../../../../frontend/src/components/app/users/UserActions.tsx", "../../../../../frontend/src/components/app/users/columns.tsx", "../../../../../frontend/src/components/app/users/UserFilterbar.tsx", "../../../../../frontend/src/components/app/users/UserList.tsx", "../../../../../frontend/src/pages/user.tsx"], "sourcesContent": ["import { getApiIdentityUsers } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch a list of users.\r\n *\r\n * This hook uses the `useQuery` hook from `react-query` to fetch the user data\r\n * asynchronously. The query key used is `QueryNames.GetUsers` along with pagination,\r\n * filter, and sorting parameters.\r\n *\r\n * @param {number} pageIndex - The current page index.\r\n * @param {number} pageSize - The number of items per page.\r\n * @param {string} [filter] - Optional filter string.\r\n * @param {string} [sorting] - Optional sorting string.\r\n * @returns {UseQueryResult} The result of the query, which includes the user data and query status.\r\n */\r\nexport const useUsers = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filter?: string,\r\n  sorting?: string\r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetUsers, pageIndex, pageSize, filter, sorting],\r\n    queryFn: async () => {\r\n      let skip = 0\r\n      if (pageIndex > 0) {\r\n        skip = pageIndex * pageSize\r\n      }\r\n      const response = await getApiIdentityUsers({\r\n        query: {\r\n          MaxResultCount: pageSize,\r\n          SkipCount: skip,\r\n          Filter: filter,\r\n          Sorting: sorting,\r\n        }\r\n      })\r\n      return response.data\r\n    },\r\n  })\r\n}\r\n", "'use client'\r\nimport { type IdentityUserCreateDto, postApiIdentityUsers, type RemoteServiceErrorResponse, type ConcurrentLoginPreventionMode } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Select, SelectContent, SelectItemExtended, SelectTrigger, SelectValue } from '@/components/ui/select'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { RiAddLine } from '@remixicon/react'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\n\r\n\r\nexport type AddUserProps = {\r\n  children?: React.ReactNode\r\n}\r\n\r\nexport const AddUser = ({ children }: AddUserProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n  const [isActive, setIsActive] = useState(true)\r\n  const [lockoutEnabled, setLockoutEnabled] = useState(true)\r\n  const [needLoginHris, setNeedLoginHris] = useState(false)\r\n  const [concurrentLoginPreventionMode, setConcurrentLoginPreventionMode] = useState<ConcurrentLoginPreventionMode>(0)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register } = useForm<IdentityUserCreateDto>()\r\n\r\n  const createUserMutation = useMutation({\r\n    mutationFn: async (userData: IdentityUserCreateDto) =>\r\n      postApiIdentityUsers({ body: userData }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'User Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n      setOpen(false)\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: IdentityUserCreateDto) => {\r\n    // Merge form data with checkbox states\r\n    const userData: IdentityUserCreateDto = {\r\n      ...formData,\r\n      isActive,\r\n      lockoutEnabled,\r\n      // ensure extension properties are sent to satisfy backend validation\r\n      extraProperties: {\r\n        ...(formData.extraProperties ?? {}),\r\n        ConcurrentLoginPreventionMode: concurrentLoginPreventionMode,\r\n        NeedLoginHris: needLoginHris,\r\n      }\r\n    }\r\n\r\n    createUserMutation.mutate(userData)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={setOpen}>\r\n        <DialogTrigger asChild>{children}</DialogTrigger>\r\n        <section className=\"flex items-center justify-between pb-5\">\r\n          {can('AbpIdentity.Users.Create') && (\r\n            <Button size='sm' className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\" onClick={() => setOpen(true)}>\r\n              <RiAddLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"hidden truncate sm:inline\">Create New User</span>\r\n            </Button>\r\n          )}\r\n        </section>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Create a New User</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Username\"\r\n                  description=\"The username of the user\"\r\n                >\r\n                  <Input required {...register('userName')} placeholder=\"Username\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Password\"\r\n                  description=\"The password of the user\"\r\n                >\r\n                  <Input required type=\"password\" {...register('password')} placeholder=\"Password\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The name of the user\"\r\n                >\r\n                  <Input required {...register('name')} placeholder=\"Name\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Surname\"\r\n                  description=\"The surname of the user\"\r\n                >\r\n                  <Input {...register('surname')} placeholder=\"Surname\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Email\"\r\n                  description=\"The email of the user\"\r\n                >\r\n                  <Input required {...register('email')} placeholder=\"Email\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Phone Number\"\r\n                  description=\"The phone number of the user\"\r\n                >\r\n                  <Input {...register('phoneNumber')} placeholder=\"Phone Number\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Concurrent Login Prevention\"\r\n                  description=\"How to handle concurrent logins for this user\"\r\n                >\r\n                  <Select onValueChange={(val) => setConcurrentLoginPreventionMode(Number(val) as ConcurrentLoginPreventionMode)}>\r\n                    <SelectTrigger className=\"w-full\">\r\n                      <SelectValue placeholder=\"Select mode\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItemExtended value={String(0)} option={'Disabled'} description=\"No restriction on concurrent logins\" />\r\n                      <SelectItemExtended value={String(1)} option={'Logout From Same Type Devices'} description=\"Only one session per device type\" />\r\n                      <SelectItemExtended value={String(2)} option={'Logout From All Devices'} description=\"Log out other sessions on new login\" />\r\n                    </SelectContent>\r\n                  </Select>\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Need Login HRIS\"\r\n                  description=\"Require HRIS authentication for this user\"\r\n                >\r\n                  <Checkbox\r\n                    id=\"needLoginHris\"\r\n                    name=\"needLoginHris\"\r\n                    checked={needLoginHris}\r\n                    onCheckedChange={(checked) => setNeedLoginHris(!!checked.valueOf())}\r\n                  />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Is Active\"\r\n                  description=\"The active status of the user\"\r\n                >\r\n                  <Checkbox\r\n                    id=\"isActive\"\r\n                    name=\"isActive\"\r\n                    defaultChecked\r\n                    checked={isActive}\r\n                    onCheckedChange={(checked) => setIsActive(!!checked.valueOf())}\r\n                  />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Lockout Enabled\"\r\n                  description=\"The lockout status of the user\"\r\n                >\r\n                  <Checkbox\r\n                    id=\"lockoutEnabled\"\r\n                    name=\"lockoutEnabled\"\r\n                    defaultChecked\r\n                    checked={lockoutEnabled}\r\n                    onCheckedChange={(checked) => setLockoutEnabled(!!checked.valueOf())}\r\n                  />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createUserMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createUserMutation.isPending}>\r\n                {createUserMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "'use client'\r\nimport { getApiImportTemplates, postApiImportCsv, type RemoteServiceErrorResponse } from '@/client'\r\n\r\n// Define interfaces for API error handling\r\n\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { RiDownloadLine, RiUploadLine } from '@remixicon/react'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\n\r\nexport const UploadUsersCsv = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const [open, setOpen] = useState(false)\r\n  const [file, setFile] = useState<File | null>(null)\r\n  const [isUploading, setIsUploading] = useState(false)\r\n  const [isDownloading, setIsDownloading] = useState(false)\r\n  const [uploadType, setUploadType] = useState<'users' | 'roles' | 'userroles'>('users')\r\n  // Handle file selection\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files.length > 0) {\r\n      const selectedFile = e.target.files[0]\r\n\r\n      // Validate file type\r\n      if (!selectedFile?.name.toLowerCase().endsWith('.csv')) {\r\n        toast({\r\n          title: 'Invalid File Type',\r\n          description: 'Please select a CSV file',\r\n          variant: 'destructive',\r\n        })\r\n        // Reset the input\r\n        e.target.value = ''\r\n        return\r\n      }\r\n\r\n      setFile(selectedFile)\r\n    }\r\n  }\r\n\r\n  // Download template mutation\r\n  const downloadTemplateMutation = useMutation({\r\n    mutationFn: async () => {\r\n      setIsDownloading(true)\r\n      try {\r\n        const response = await getApiImportTemplates({\r\n          query: {\r\n            type: uploadType\r\n          }\r\n        })\r\n\r\n        // Create a blob from the response data\r\n        console.log('response.data', response.data)\r\n        const blob = new Blob([response.data as unknown as BlobPart], {\r\n          type: 'text/csv'\r\n        })\r\n\r\n        // Create a download link and trigger the download\r\n        const url = window.URL.createObjectURL(blob)\r\n        const a = document.createElement('a')\r\n        a.href = url\r\n        a.download = 'users_import_template.csv'\r\n        document.body.appendChild(a)\r\n        a.click()\r\n        window.URL.revokeObjectURL(url)\r\n        document.body.removeChild(a)\r\n\r\n        return response.data\r\n      } finally {\r\n        setIsDownloading(false)\r\n      }\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  // Upload CSV mutation\r\n  const uploadCsvMutation = useMutation({\r\n    mutationFn: async () => {\r\n      if (!file) {\r\n        throw new Error('No file selected')\r\n      }\r\n\r\n      setIsUploading(true)\r\n\r\n      try {\r\n        // Use the SDK function with formDataBodySerializer\r\n        const response = await postApiImportCsv({\r\n          body: {\r\n            File: file,  // Use uppercase to match backend DTO FromForm attributes\r\n            Type: uploadType,  // Use uppercase to match backend DTO FromForm attributes\r\n            Delimiter: ';'  // Use uppercase to match backend DTO FromForm attributes\r\n          },\r\n        })\r\n\r\n        return response.data\r\n      } finally {\r\n        setIsUploading(false)\r\n      }\r\n    },\r\n    onSuccess: () => {\r\n      // Check if there were any errors during import\r\n      toast({\r\n        title: 'Import Successful',\r\n        description: `Imported successfully`,\r\n        variant: 'success',\r\n      })\r\n\r\n      // Refresh the users list\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n\r\n      // Close the dialog\r\n      setOpen(false)\r\n\r\n      // Reset the file input\r\n      setFile(null)\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  // Handle download template button click\r\n  const handleDownloadTemplate = () => {\r\n    downloadTemplateMutation.mutate()\r\n  }\r\n\r\n  // Handle upload button click\r\n  const handleUpload = () => {\r\n    if (!file) {\r\n      toast({\r\n        title: 'No File Selected',\r\n        description: 'Please select a CSV file to upload',\r\n        variant: 'warning',\r\n      })\r\n      return\r\n    }\r\n\r\n    uploadCsvMutation.mutate()\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={setOpen}>\r\n      <DialogTrigger asChild>\r\n        <Button size=\"sm\" className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\">\r\n          <RiUploadLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n          <span className=\"hidden truncate sm:inline\">Upload CSV</span>\r\n        </Button>\r\n      </DialogTrigger>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader>\r\n          <DialogTitle>Upload Users CSV</DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"flex flex-col gap-4 py-4\">\r\n          <div className=\"flex flex-col gap-2\">\r\n            <Label htmlFor=\"csvFile\" className=\"text-left\">Upload Type</Label>\r\n            <Select value={uploadType} onValueChange={(value) => setUploadType(value as \"users\" | \"roles\" | \"userroles\")}>\r\n              <SelectTrigger className='w-full'>\r\n                <SelectValue placeholder=\"Select upload type\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"users\">Users</SelectItem>\r\n                <SelectItem value=\"roles\">Roles</SelectItem>\r\n                <SelectItem value=\"userroles\">User Roles</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          <div className=\"flex flex-col gap-2\">\r\n            <Label htmlFor=\"template\" className=\"text-left\">Download Template</Label>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleDownloadTemplate}\r\n                disabled={isDownloading}\r\n                className=\"w-full\"\r\n              >\r\n                <RiDownloadLine className=\"mr-2 h-4 w-4\" />\r\n                {isDownloading ? 'Downloading...' : 'Download Template'}\r\n              </Button>\r\n            </div>\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Download the template first, fill it with your data, and then upload it.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col gap-2\">\r\n            <Label htmlFor=\"csvFile\" className=\"text-left\">Upload CSV File</Label>\r\n            <Input\r\n              id=\"csvFile\"\r\n              type=\"file\"\r\n              accept=\".csv\"\r\n              onChange={handleFileChange}\r\n              disabled={isUploading}\r\n            />\r\n            {file && (\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Selected file: {file.name}\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter>\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => {\r\n              setOpen(false)\r\n              setFile(null)\r\n            }}\r\n            disabled={isUploading}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleUpload}\r\n            disabled={!file || isUploading}\r\n          >\r\n            {isUploading ? 'Uploading...' : 'Upload'}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\n", "import { deleteApiIdentityUsersById } from '@/client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { useToast } from \"@/lib/useToast\"\r\nimport { useEffect, useState } from 'react'\r\n\r\ntype DeleteUserProps = {\r\n  user: { userId: string; username: string }\r\n  onDismiss: () => void\r\n}\r\nexport const DeleteUser = ({ user: { userId, username }, onDismiss }: DeleteUserProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiIdentityUsersById({\r\n        path: { id: userId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `User \"${username}\" has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: `There was a problem when deleting the user \"${username}\". Kindly try again.`,\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your this user &quot;\r\n            {username}&quot;\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "import { getApiIdentityUsersAssignableRoles } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch assignable roles using a query.\r\n *\r\n * This hook uses the `useQuery` hook from the `react-query` library to fetch\r\n * the assignable roles. The query key used is `QueryNames.GetAssignableRoles`.\r\n *\r\n * @returns {object} The result of the `useQuery` hook, which includes the data,\r\n * status, and other properties related to the query.\r\n */\r\nexport const useAssignableRoles = () => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetAssignableRoles],\r\n    queryFn: async () => {\r\n      const { data } = await getApiIdentityUsersAssignableRoles()\r\n      return data\r\n    },\r\n  })\r\n}\r\n", "import { getApiIdentityUsersByIdRoles } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\ntype UseUserRolesProps = {\r\n  userId: string\r\n}\r\n\r\n/**\r\n * Custom hook to fetch the roles of a specific user.\r\n *\r\n * This hook uses the `useQuery` hook from `react-query` to fetch the user roles\r\n * asynchronously. The query key used is `QueryNames.GetUserRoles` along with the user ID.\r\n *\r\n * @param {UseUserRolesProps} props - The properties object containing the user ID.\r\n * @returns {UseQueryResult} The result of the query, which includes the user roles data and query status.\r\n */\r\nexport const useUserRoles = ({ userId }: UseUserRolesProps) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetUserRoles, userId],\r\n    queryFn: async () => {\r\n      const { data } = await getApiIdentityUsersByIdRoles({\r\n        path: {\r\n          id: userId\r\n        }\r\n      })\r\n      return data\r\n    },\r\n  })\r\n}\r\n", "import { type IdentityRoleDto, type IdentityUserUpdateDto, putApiIdentityUsersById, type RemoteServiceErrorResponse } from '@/client'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { type MouseEvent, useCallback, useEffect, useState } from 'react'\r\nimport { Controller, useForm } from 'react-hook-form'\r\nimport { v4 } from 'uuid'\r\n\r\nimport { getApiIdentityUserDetailsById } from '@/client/sdk.gen'\r\nimport { client as httpClient } from '@/client/client.gen'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport Loader from '@/components/ui/Loader'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useAssignableRoles } from '@/lib/hooks/useAssignableRoles'\r\nimport { useUserRoles } from '@/lib/hooks/useUserRoles'\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'\r\nimport classNames from 'clsx'\r\n\r\nconst TABS_NAME = {\r\n  USERS_EDIT: 'user_edit',\r\n  USERS_ROLE_ASSIGN: 'user_role_assign',\r\n  USERS_DETAILS: 'user_details',\r\n  USERS_RAW: 'user_raw',\r\n}\r\n\r\ntype RoleType = {\r\n  name: string\r\n  id: string\r\n}\r\n\r\ntype UserEditProps = {\r\n  userDto: IdentityUserUpdateDto\r\n  userId: string\r\n  onDismiss: () => void\r\n}\r\nexport const UserEdit = ({ userDto, userId, onDismiss }: UserEditProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, control } = useForm<IdentityUserUpdateDto>({\r\n    defaultValues: {\r\n      ...userDto\r\n    }\r\n  })\r\n  const [roles, setRoles] = useState<RoleType[]>([])\r\n  // const [isActive, setIsActive] = useState(true)\r\n  // const [lockoutEnabled, setLockoutEnabled] = useState(true)\r\n  const userRole = useUserRoles({ userId })\r\n  const assignableRoles = useAssignableRoles()\r\n\r\n  // Fetch user details for Details & Raw tabs\r\n  const { data: userDetails, isLoading: detailsLoading } = useQuery({\r\n    queryKey: ['user-details', userId],\r\n    queryFn: () => getApiIdentityUserDetailsById({\r\n      path: { id: userId }\r\n    })\r\n  })\r\n\r\n  const createDataMutation = useMutation({\r\n    mutationFn: async (dataMutation: IdentityUserUpdateDto) =>\r\n      putApiIdentityUsersById({\r\n        path: { id: userId },\r\n        body: { ...userDto, ...dataMutation },\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Claim Type Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n      setOpen(false)\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: IdentityUserUpdateDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: IdentityUserUpdateDto = {\r\n      ...formData,\r\n    }\r\n\r\n    // Explicitly mark the promise as handled\r\n    void createDataMutation.mutate(userData)\r\n  }\r\n\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  useEffect(() => {\r\n    if (userRole.data?.items) {\r\n      const temp: RoleType[] = []\r\n      userRole.data.items.forEach((r) => {\r\n        temp.push({ name: r.name!, id: r.id! })\r\n      })\r\n      setRoles(temp)\r\n    }\r\n  }, [userRole.data?.items])\r\n\r\n  const onRoleAssignEvent = useCallback(\r\n    (role: IdentityRoleDto) => {\r\n      const hasAssignedRoleExistAlready = roles.findIndex((r) => role.id === r.id)\r\n\r\n      console.log(hasAssignedRoleExistAlready, 'hasAssignedRoleExistAlready')\r\n      if (hasAssignedRoleExistAlready !== -1) {\r\n        roles.splice(hasAssignedRoleExistAlready, 1)\r\n        setRoles([...roles])\r\n      } else {\r\n        roles.push({ name: role.name!, id: role.id! })\r\n        setRoles([...roles])\r\n      }\r\n    },\r\n    [roles]\r\n  )\r\n\r\n  const onRoleAssignedSaveEvent = (e: MouseEvent) => {\r\n    e.preventDefault()\r\n    const updateUserDto: IdentityUserUpdateDto = {\r\n      ...userDto,\r\n      roleNames: roles?.map((r) => r.name) ?? [],\r\n    }\r\n    onSubmit(updateUserDto)\r\n  }\r\n\r\n  const handleUnlockUser = async (e: MouseEvent) => {\r\n    e.preventDefault()\r\n    try {\r\n      await httpClient.post({ url: `/api/identity/users/${userId}/unlock` })\r\n      toast({ title: 'User unlocked', description: `${userDto.userName} has been unlocked.`, variant: 'success' })\r\n      // Refresh details tab\r\n      void queryClient.invalidateQueries({ queryKey: ['user-details', userId] })\r\n    } catch (err: unknown) {\r\n      const message = err && typeof err === 'object' && 'message' in err ? String((err as { message?: string }).message) : 'An error occurred'\r\n      toast({ title: 'Failed to unlock', description: message, variant: 'destructive' })\r\n    }\r\n  }\r\n  const rawData = userDetails?.data as unknown as Record<string, unknown> | undefined\r\n  const extraProps = (rawData?.['extraProperties'] as Record<string, unknown> | undefined)\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent size='2xl' className=\"\">\r\n        <DialogHeader>\r\n          <DialogTitle>Update a User: {userDto.userName}</DialogTitle>\r\n        </DialogHeader>\r\n        <Tabs defaultValue={TABS_NAME.USERS_EDIT}>\r\n          <TabsList className=\"w-full\">\r\n            <TabsTrigger value={TABS_NAME.USERS_EDIT}>\r\n              User Information\r\n            </TabsTrigger>\r\n            <TabsTrigger value={TABS_NAME.USERS_ROLE_ASSIGN}>\r\n              Roles\r\n            </TabsTrigger>\r\n            <TabsTrigger value={TABS_NAME.USERS_DETAILS}>\r\n              Details\r\n            </TabsTrigger>\r\n            <TabsTrigger value={TABS_NAME.USERS_RAW}>\r\n              Raw\r\n            </TabsTrigger>\r\n          </TabsList>\r\n          <TabsContent value={TABS_NAME.USERS_EDIT}>\r\n            <form onSubmit={handleSubmit(onSubmit)}>\r\n              <section className=\"flex flex-col space-y-2 mt-4\">\r\n                <FormSection>\r\n                  <FormField\r\n                    label=\"Name\"\r\n                    description=\"The name of the user\"\r\n                  >\r\n                    <Input required {...register('name')} defaultValue={userDto.name ?? ''} placeholder=\"Name\" />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    label=\"Surname\"\r\n                    description=\"The surname of the user\"\r\n                  >\r\n                    <Input {...register('surname')} defaultValue={userDto.surname ?? ''} placeholder=\"Surname\" />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    label=\"Email\"\r\n                    description=\"The email of the user\"\r\n                  >\r\n                    <Input required {...register('email')} defaultValue={userDto.email ?? ''} placeholder=\"Email\" />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    label=\"Phone Number\"\r\n                    description=\"The phone number of the user\"\r\n                  >\r\n                    <Input {...register('phoneNumber')} defaultValue={userDto.phoneNumber ?? ''} placeholder=\"Phone Number\" />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    label=\"Is Active\"\r\n                    description=\"The active status of the user\"\r\n                  >\r\n                    <Controller\r\n                      name=\"isActive\"\r\n                      control={control}\r\n                      render={({ field }) => (\r\n                        <Checkbox\r\n                          id=\"isActive\"\r\n                          checked={!!field.value}\r\n                          onCheckedChange={(checked) => field.onChange(!!checked)}\r\n                        />\r\n                      )}\r\n                    />\r\n                  </FormField>\r\n                  <FormField\r\n                    label=\"Lockout Enabled\"\r\n                    description=\"The lockout status of the user\"\r\n                  >\r\n                    <Controller\r\n                      name=\"lockoutEnabled\"\r\n                      control={control}\r\n                      render={({ field }) => (\r\n                        <Checkbox\r\n                          id=\"lockoutEnabled\"\r\n                          checked={!!field.value}\r\n                          onCheckedChange={(checked) => field.onChange(!!checked)}\r\n                        />\r\n                      )}\r\n                    />\r\n                  </FormField>\r\n                </FormSection>\r\n              </section>\r\n\r\n              <DialogFooter className=\"mt-5 flex items-center gap-2\">\r\n                <Button\r\n                  variant='outline'\r\n                  onClick={(e) => {\r\n                    e.preventDefault()\r\n                    onCloseEvent()\r\n                  }}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n                {userDetails?.data?.lockoutEnd && (\r\n                  <Button type=\"button\" variant=\"secondary\" onClick={handleUnlockUser}>\r\n                    Unlock User\r\n                  </Button>\r\n                )}\r\n                <Button type=\"submit\">Save</Button>\r\n              </DialogFooter>\r\n            </form>\r\n          </TabsContent>\r\n          <TabsContent className='max-h-[70vh] overflow-hidden flex flex-col' value={TABS_NAME.USERS_ROLE_ASSIGN}>\r\n            {assignableRoles?.isLoading && <Loader />}\r\n            {assignableRoles?.isError && (\r\n              <div className=\"bg-error p-10 text-3xl\">\r\n                There was an error while fetching roles information for the {userDto.userName}\r\n              </div>\r\n            )}\r\n            {!assignableRoles.isLoading && !assignableRoles.isError && (\r\n              <div className='flex-1 overflow-y-auto'>\r\n                {assignableRoles?.data?.items?.map((r) => (\r\n                  <div key={v4()} className={classNames('flex items-center space-x-2 pb-5 mt-3')}>\r\n                    <Checkbox\r\n                      id={r.id}\r\n                      name={r.name!}\r\n                      checked={!!roles?.find((l) => l.id === r.id)}\r\n                      onCheckedChange={() => {\r\n                        onRoleAssignEvent(r)\r\n                      }}\r\n                    />\r\n                    <label htmlFor={r.id} className=\"text-sm font-medium leading-none\">\r\n                      {r.name}\r\n                    </label>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant='outline'\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  onCloseEvent()\r\n                }}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button onClick={onRoleAssignedSaveEvent}>Save</Button>\r\n            </DialogFooter>\r\n          </TabsContent>\r\n          <TabsContent value={TABS_NAME.USERS_DETAILS}>\r\n            {detailsLoading ? (\r\n              <Loader />\r\n            ) : userDetails ? (\r\n              <div className=\"space-y-3 p-4\">\r\n                <div>\r\n                  <span className=\"font-semibold\">Created by :</span> {userDetails.data?.creatorUserName ?? '-'}\r\n                </div>\r\n                <div>\r\n                  <span className=\"font-semibold\">Creation time :</span> {userDetails.data?.creationTime ? new Date(userDetails.data?.creationTime).toLocaleString() : '-'}\r\n                </div>\r\n                <div>\r\n                  <span className=\"font-semibold\">Modified by :</span> {userDetails.data?.lastModifierUserName ?? '-'}\r\n                </div>\r\n                <div>\r\n                  <span className=\"font-semibold\">Modification time :</span> {userDetails.data?.lastModificationTime ? new Date(userDetails.data?.lastModificationTime).toLocaleString() : '-'}\r\n                </div>\r\n                <div>\r\n                  <span className=\"font-semibold\">Password update time :</span> {userDetails.data?.passwordChangeTime ? new Date(userDetails.data?.passwordChangeTime).toLocaleString() : '-'}\r\n                </div>\r\n                <div>\r\n                  <span className=\"font-semibold\">Lockout end time :</span> {userDetails.data?.lockoutEnd ? new Date(userDetails.data?.lockoutEnd).toLocaleString() : '-'}\r\n                </div>\r\n                <div>\r\n                  <span className=\"font-semibold\">Failed access count :</span> {userDetails.data?.accessFailedCount ?? 0}\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"p-4 text-gray-500\">No details found.</div>\r\n            )}\r\n          </TabsContent>\r\n          <TabsContent value={TABS_NAME.USERS_RAW}>\r\n            {detailsLoading ? (\r\n              <Loader />\r\n            ) : userDetails ? (\r\n              <div className=\"space-y-3 p-4\">\r\n                <div>\r\n                  <span className=\"font-semibold\">Extra Properties :</span>\r\n                  <div className=\"mt-2 rounded border p-3 bg-muted text-sm\">\r\n                    {!extraProps || Object.keys(extraProps).length === 0 ? (\r\n                      <div className=\"text-gray-500\">No extra properties.</div>\r\n                    ) : (\r\n                      <div className=\"grid grid-cols-1 gap-2\">\r\n                        {Object.entries(extraProps).map(([key, value]) => (\r\n                          <div key={key} className=\"flex items-start gap-2\">\r\n                            <div className=\"w-64 font-medium truncate\">{key}</div>\r\n                            <div className=\"flex-1 break-all\">{String(value)}</div>\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"p-4 text-gray-500\">No details found.</div>\r\n            )}\r\n          </TabsContent>\r\n        </Tabs>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "import {\r\n  type IdentityUserUpdateDto,\r\n  type PermissionGrantInfoDto,\r\n  type PermissionGroupDto,\r\n  type UpdatePermissionsDto,\r\n  putApiPermissionManagementPermissions,\r\n} from '@/client'\r\nimport { Permission } from '@/components/app/permission/PermissionToggle'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  Di<PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\r\nimport { usePermissions } from '@/lib/hooks/usePermissions'\r\nimport { useUserRoles } from '@/lib/hooks/useUserRoles'\r\nimport { useToast } from \"@/lib/useToast\"\r\nimport { PermissionProvider, USER_ROLE } from '@/lib/utils'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { type FormEvent, useCallback, useEffect, useMemo, useState } from 'react'\r\n\r\ntype UserPermissionProps = {\r\n  userDto: IdentityUserUpdateDto\r\n  userId: string\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const UserPermission = ({ userDto, userId, onDismiss }: UserPermissionProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const userRoles = useUserRoles({ userId })\r\n\r\n  // flag determine to enable/disable all the permissions to a user.\r\n  const [hasAllGranted, setHasAllGranted] = useState(false)\r\n  const { data } = usePermissions(PermissionProvider.U, userId)\r\n  const queryClient = useQueryClient()\r\n\r\n  const [permissionGroups, setPermissionGroups] = useState<PermissionGroupDto[]>([])\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n    return () => {\r\n      void queryClient.invalidateQueries({ queryKey: [PermissionProvider.U] })\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  // Update the local state with the remote data\r\n  useEffect(() => {\r\n    if (data?.groups && Array.isArray(data.groups)) {\r\n      setPermissionGroups([...data.groups])\r\n    }\r\n  }, [data])\r\n\r\n  // check if user have all the permissions are granted already.\r\n  // Only run this when data changes, not when permissionGroups changes internally\r\n  useEffect(() => {\r\n    if (data?.groups && data.groups.length > 0) {\r\n      const hasAllPermissionGranted = data.groups\r\n        .map((g) => g.permissions?.every((p) => p.isGranted))\r\n        .every((e) => e)\r\n      setHasAllGranted(hasAllPermissionGranted)\r\n    }\r\n  }, [data])\r\n\r\n  // Apply hasAllGranted to all permissions - only when hasAllGranted changes\r\n  useEffect(() => {\r\n    if (permissionGroups.length > 0) {\r\n      const updatedGroups = permissionGroups.map(group => {\r\n        // Create a new group object to avoid reference issues\r\n        return {\r\n          ...group,\r\n          permissions: group.permissions?.map(permission => ({\r\n            ...permission,\r\n            isGranted: hasAllGranted\r\n          }))\r\n        };\r\n      });\r\n\r\n      // Break the circular dependency by using a ref to track updates\r\n      setPermissionGroups(updatedGroups);\r\n    }\r\n  }, [hasAllGranted]); // Remove permissionGroups from dependency array\r\n\r\n  const onSubmit = useCallback(\r\n    async (e: FormEvent) => {\r\n      e.preventDefault()\r\n      const payload = permissionGroups\r\n        ?.map((p) =>\r\n          p.permissions!.map((grant) => ({\r\n            name: grant.name,\r\n            isGranted: grant.isGranted,\r\n          }))\r\n        )\r\n        .flat()\r\n      const requestPayload: UpdatePermissionsDto = {\r\n        permissions: payload,\r\n      }\r\n      try {\r\n        await putApiPermissionManagementPermissions({\r\n          query: {\r\n            providerName: PermissionProvider.U,\r\n            providerKey: userId\r\n          },\r\n          body: requestPayload\r\n        })\r\n        toast({\r\n          title: 'Success',\r\n          description: 'Permission Updated Successfully',\r\n          variant: 'default',\r\n        })\r\n        void queryClient.invalidateQueries({\r\n          queryKey: [PermissionProvider.U],\r\n        })\r\n        onCloseEvent()\r\n      } catch (err: unknown) {\r\n        if (err instanceof Error) {\r\n          toast({\r\n            title: 'Failed',\r\n            description: \"Permission update wasn't successful.\",\r\n            variant: 'destructive',\r\n          })\r\n        }\r\n      }\r\n    },\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    [permissionGroups]\r\n  )\r\n\r\n  const onCloseEvent = useCallback(() => {\r\n    setOpen(false)\r\n    onDismiss()\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  const hasAdmin = useMemo(() => {\r\n    if (userRoles?.data?.items) {\r\n      return userRoles.data.items.filter((role) => role.name?.includes(USER_ROLE.ADMIN)).length > 0\r\n    }\r\n    return false\r\n  }, [userRoles])\r\n\r\n  const [selectedTab, setSelectedTab] = useState(0)\r\n\r\n  // Helper: Group permissions by parent and children (Create, View, Edit, Delete)\r\n  const groupPermissionsByParent = (permissions: PermissionGrantInfoDto[] | null | undefined) => {\r\n    if (!Array.isArray(permissions) || permissions.length === 0) return { parentRows: [], otherPermissions: [] }\r\n    const parentMap: Record<string, { parent: PermissionGrantInfoDto, children: Record<string, PermissionGrantInfoDto> }> = {}\r\n    const otherPermissions: PermissionGrantInfoDto[] = []\r\n    const childActions = [\"Create\", \"View\", \"Edit\", \"Delete\"]\r\n    permissions.forEach((perm) => {\r\n      // Parent: no dot in name, or ends with parent entity\r\n      const dotCount = (perm.name?.match(/\\./g) || []).length\r\n      if (dotCount === 1 && !childActions.some(a => perm.name?.endsWith('.' + a))) {\r\n        // e.g., EkbApp.MasterAgent\r\n        parentMap[perm.name!] = { parent: perm, children: {} }\r\n      }\r\n    })\r\n    // Now, assign children\r\n    permissions.forEach((perm) => {\r\n      const match = perm.name?.match(/^(.*)\\.(Create|View|Edit|Delete)$/)\r\n      if (match) {\r\n        const parentKey = match[1]\r\n        const action = match[2]\r\n        if (parentMap[parentKey]) {\r\n          parentMap[parentKey].children[action] = perm\r\n        } else {\r\n          otherPermissions.push(perm)\r\n        }\r\n      } else {\r\n        // If not a parent or child, and not already in parentMap, treat as other\r\n        if (!parentMap[perm.name!]) {\r\n          otherPermissions.push(perm)\r\n        }\r\n      }\r\n    })\r\n    return { parentRows: Object.values(parentMap), otherPermissions }\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent size={'4xl'} className=\"max-w-4xl max-h-[90vh] overflow-hidden flex flex-col\">\r\n        <DialogHeader>\r\n          <DialogTitle>Permissions - {userDto.userName}</DialogTitle>\r\n        </DialogHeader>\r\n        <form onSubmit={onSubmit} className=\"flex-1 overflow-y-auto\">\r\n          <div className=\"p-1\">\r\n            <Permission\r\n              name=\"Grant All Permissions\"\r\n              isGranted={hasAllGranted}\r\n              id=\"all_granted\"\r\n              disabled={!hasAdmin}\r\n              onUpdate={() => {\r\n                setHasAllGranted((prev) => !prev)\r\n              }}\r\n              className=\"ml-2 mb-4\"\r\n            />\r\n            {/* Tabs for permission groups */}\r\n            <Tabs\r\n              value={selectedTab.toString()}\r\n              onValueChange={v => setSelectedTab(Number(v))}\r\n              orientation='vertical'\r\n              className=\"flex flex-col justify-stretch lg:flex-row gap-4 text-sm text-muted-foreground w-full p-4 border border-border rounded-lg\"\r\n            >\r\n              <div className=\"lg:w-[200px] lg:shrink-0\">\r\n                <TabsList variant=\"button\" className=\"flex flex-col items-stretch *:justify-start sticky top-0 bg-white z-10\">\r\n                  {permissionGroups.map((group, idx) => (\r\n                    <TabsTrigger key={group.name} value={idx.toString()}>{group.displayName}</TabsTrigger>\r\n                  ))}\r\n                </TabsList>\r\n              </div>\r\n              <div className=\"grow border-s border-border py-0 ps-4 overflow-y-auto\">\r\n                {permissionGroups.map((group, idx) => {\r\n                  const { parentRows, otherPermissions } = groupPermissionsByParent(group.permissions ?? [])\r\n                  // Gather all parent and child permissions in this group (table only)\r\n                  const allTablePerms: PermissionGrantInfoDto[] = [\r\n                    ...parentRows.map(r => r.parent),\r\n                    ...parentRows.flatMap(r => Object.values(r.children))\r\n                  ]\r\n                  const allTableChecked = allTablePerms.length > 0 && allTablePerms.every(p => p.isGranted)\r\n                  const handleSelectAllTable = (checked: boolean) => {\r\n                    setPermissionGroups(prev => prev.map((g, gIdx) => {\r\n                      if (gIdx !== idx) return g\r\n                      return {\r\n                        ...g,\r\n                        permissions: g.permissions?.map(p => {\r\n                          // Only update parent and child permissions\r\n                          const isParent = parentRows.some(r => r.parent.name === p.name)\r\n                          const isChild = parentRows.some(r => Object.values(r.children).some(c => c.name === p.name))\r\n                          if (isParent || isChild) {\r\n                            return { ...p, isGranted: checked }\r\n                          }\r\n                          return p\r\n                        })\r\n                      }\r\n                    }))\r\n                  }\r\n                  return (\r\n                    <TabsContent key={group.name} value={idx.toString()} className=\"overflow-x-auto\">\r\n                      {/* Select All for table */}\r\n                      <div className=\"mb-2 flex items-center gap-2\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={allTableChecked}\r\n                          onChange={e => handleSelectAllTable(e.target.checked)}\r\n                          aria-label=\"Select all permissions in this table\"\r\n                          className=\"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400\"\r\n                        />\r\n                        <span>Select All</span>\r\n                      </div>\r\n                      {/* Table for parent/child permissions */}\r\n                      {parentRows.length > 0 && (\r\n                        <table className=\"min-w-full border mb-6\">\r\n                          <thead>\r\n                            <tr>\r\n                              <th className=\"px-4 py-2 text-left\">Name</th>\r\n                              <th className=\"px-4 py-2 text-center\">Parent</th>\r\n                              <th className=\"px-4 py-2 text-center\">View</th>\r\n                              <th className=\"px-4 py-2 text-center\">Create</th>\r\n                              <th className=\"px-4 py-2 text-center\">Edit</th>\r\n                              <th className=\"px-4 py-2 text-center\">Delete</th>\r\n                            </tr>\r\n                          </thead>\r\n                          <tbody>\r\n                            {parentRows.map(({ parent, children }) => (\r\n                              <tr key={parent.name} className=\"border-t\">\r\n                                <td className=\"px-4 py-2 font-medium\">{parent.displayName ?? parent.name}</td>\r\n                                {/* Parent checkbox */}\r\n                                <td className=\"px-4 py-2 text-center\">\r\n                                  <input\r\n                                    type=\"checkbox\"\r\n                                    checked={parent.isGranted}\r\n                                    onChange={() => {\r\n                                      setPermissionGroups(prev => prev.map((g, gIdx) => {\r\n                                        if (gIdx !== idx) return g\r\n                                        return {\r\n                                          ...g,\r\n                                          permissions: g.permissions?.map(p =>\r\n                                            p.name === parent.name ? { ...p, isGranted: !p.isGranted } : p\r\n                                          )\r\n                                        }\r\n                                      }))\r\n                                    }}\r\n                                    aria-label={String(parent.displayName ?? parent.name)}\r\n                                    className=\"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400\"\r\n                                  />\r\n                                </td>\r\n                                {/* Child checkboxes: View, Create, Edit, Delete */}\r\n                                {['View', 'Create', 'Edit', 'Delete'].map(action => (\r\n                                  <td key={action} className=\"px-4 py-2 text-center\">\r\n                                    {children[action] ? (\r\n                                      <input\r\n                                        type=\"checkbox\"\r\n                                        checked={children[action].isGranted}\r\n                                        onChange={() => {\r\n                                          setPermissionGroups(prev => prev.map((g, gIdx) => {\r\n                                            if (gIdx !== idx) return g\r\n                                            return {\r\n                                              ...g,\r\n                                              permissions: g.permissions?.map(p =>\r\n                                                p.name === children[action].name ? { ...p, isGranted: !p.isGranted } : p\r\n                                              )\r\n                                            }\r\n                                          }))\r\n                                        }}\r\n                                        aria-label={String(children[action].displayName ?? children[action].name)}\r\n                                        className=\"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400\"\r\n                                      />\r\n                                    ) : null}\r\n                                  </td>\r\n                                ))}\r\n                              </tr>\r\n                            ))}\r\n                          </tbody>\r\n                        </table>\r\n                      )}\r\n                      {/* Other permissions */}\r\n                      {otherPermissions.length > 0 && (\r\n                        <div className=\"mb-4\">\r\n                          <div className=\"font-semibold mb-2\">Other Permissions</div>\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-1 gap-2\">\r\n                            {otherPermissions.map(perm => (\r\n                              <label key={perm.name} className=\"flex items-center gap-2\">\r\n                                <input\r\n                                  type=\"checkbox\"\r\n                                  checked={perm.isGranted}\r\n                                  onChange={() => {\r\n                                    setPermissionGroups(prev => prev.map((g, gIdx) => {\r\n                                      if (gIdx !== idx) return g\r\n                                      return {\r\n                                        ...g,\r\n                                        permissions: g.permissions?.map(p =>\r\n                                          p.name === perm.name ? { ...p, isGranted: !p.isGranted } : p\r\n                                        )\r\n                                      }\r\n                                    }))\r\n                                  }}\r\n                                  aria-label={String(perm.name ?? perm.displayName)}\r\n                                  className=\"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400\"\r\n                                />\r\n                                <span>{perm.name ?? perm.displayName}</span>\r\n                              </label>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </TabsContent>\r\n                  )\r\n                })}\r\n              </div>\r\n            </Tabs>\r\n          </div>\r\n        </form>\r\n        <DialogFooter className=\"mt-4 border-t pt-4 bg-white dark:bg-gray-950\">\r\n          <Button\r\n            onClick={(e) => {\r\n              e.preventDefault()\r\n              onCloseEvent()\r\n            }}\r\n            variant=\"ghost\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={onSubmit}>Save</Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { cn } from '@/lib/utils'\r\n\r\ntype StatusBadgeProps = {\r\n  status: boolean | string\r\n}\r\n\r\nexport const StatusBadge = ({ status }: StatusBadgeProps) => {\r\n  // Convert boolean to string status\r\n  let statusText = 'Inactive'\r\n  let statusClass = 'bg-gray-100 text-gray-700 border-gray-200'\r\n\r\n  if (typeof status === 'boolean') {\r\n    if (status) {\r\n      statusText = 'Active'\r\n      statusClass = 'bg-green-50 text-green-700 border-green-200'\r\n    }\r\n  } else if (typeof status === 'string') {\r\n    // Handle string status values\r\n    statusText = status\r\n\r\n    if (status.toLowerCase() === 'active') {\r\n      statusClass = 'bg-green-50 text-green-700 border-green-200'\r\n    } else if (status.toLowerCase() === 'archived') {\r\n      statusClass = 'bg-amber-50 text-amber-700 border-amber-200'\r\n    } else if (status.toLowerCase() === 'inactive') {\r\n      statusClass = 'bg-gray-100 text-gray-700 border-gray-200'\r\n    }\r\n  }\r\n\r\n  return (\r\n    <span className={cn(\r\n      'inline-flex items-center rounded-md border px-2 py-1 text-xs font-medium',\r\n      statusClass\r\n    )}>\r\n      {statusText}\r\n    </span>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type IdentityUserUpdateDto } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { RiMoreLine, RiPencilLine, RiShieldKeyholeLine } from '@remixicon/react'\r\n\r\ntype UserActionProps = {\r\n  userId: string\r\n  userDto: IdentityUserUpdateDto\r\n  onAction: (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n  variant?: 'dropdown' | 'buttons'\r\n}\r\n\r\nexport const UserActions = ({ userId, userDto, onAction, variant = 'dropdown' }: UserActionProps) => {\r\n  const { can } = useGrantedPolicies()\r\n\r\n  // For dropdown menu style (first image)\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <div className=\"flex justify-end\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 w-8 p-0\"\r\n            >\r\n              <RiMoreLine className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Open menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"w-[160px]\">\r\n            {can('AbpIdentity.Users.ManagePermissions') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'permission')}\r\n              >\r\n                Permission\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('AbpIdentity.Users.Update') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'edit')}\r\n              >\r\n                Edit\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('AbpIdentity.Users.Delete') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm text-red-500\"\r\n                onClick={() => onAction(userId, userDto, 'delete')}\r\n              >\r\n                Delete\r\n              </DropdownMenuItem>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // For button group style (second image)\r\n  return (\r\n    <div className=\"flex items-center justify-end gap-1\">\r\n      {can('AbpIdentity.Users.ManagePermissions') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'permission')}\r\n        >\r\n          <RiShieldKeyholeLine className=\"h-4 w-4\" />\r\n          <span>Permission</span>\r\n        </Button>\r\n      )}\r\n      {can('AbpIdentity.Users.Update') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'edit')}\r\n        >\r\n          <RiPencilLine className=\"h-4 w-4\" />\r\n          <span>Edit</span>\r\n        </Button>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type IdentityUserDto, type IdentityUserUpdateDto } from '@/client'\r\nimport { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { type ColumnDef } from '@tanstack/react-table'\r\nimport { StatusBadge } from './StatusBadge'\r\nimport { UserActions } from './UserActions'\r\n\r\n// Type for the callback function to handle user actions\r\ntype UserActionCallback = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n\r\n// Function to create user columns with the action callback\r\nexport const getUserColumns = (\r\n  handleUserAction: UserActionCallback\r\n): ColumnDef<IdentityUserDto>[] => {\r\n  return [\r\n    {\r\n      id: \"select\",\r\n      header: ({ table }) => (\r\n        <Checkbox\r\n          checked={\r\n            table.getIsAllPageRowsSelected()\r\n              ? true\r\n              : table.getIsSomeRowsSelected()\r\n                ? \"indeterminate\"\r\n                : false\r\n          }\r\n          onCheckedChange={() => table.toggleAllPageRowsSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select all\"\r\n        />\r\n      ),\r\n      cell: ({ row }) => (\r\n        <Checkbox\r\n          checked={row.getIsSelected()}\r\n          onCheckedChange={() => row.toggleSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select row\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: false,\r\n      meta: {\r\n        displayName: \"Select\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'userName',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Username\" />,\r\n      enableHiding: true,\r\n      enableSorting: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Username\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'name',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Name\" />,\r\n      enableHiding: true,\r\n      enableSorting: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Name\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'extraProperties.Company',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Company\" />,\r\n      enableHiding: true,\r\n      enableSorting: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Company\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'email',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Email\" />,\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Email\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'isActive',\r\n      header: ({ column }) => <DataTableColumnHeader column={column} title=\"Status\" />,\r\n      enableHiding: true,\r\n      enableSorting: true,\r\n      cell: (info) => <StatusBadge status={info.getValue() as boolean} />,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Status\",\r\n      },\r\n    },\r\n    {\r\n      id: 'actions',\r\n      header: \"Actions\",\r\n      enableHiding: true,\r\n      cell: (info) => (\r\n        <UserActions\r\n          userId={info.row.original.id!}\r\n          userDto={info.row.original as IdentityUserUpdateDto}\r\n          onAction={handleUserAction}\r\n          variant=\"dropdown\" // Use \"dropdown\" for the first image style or \"buttons\" for the second image style\r\n        />\r\n      ),\r\n      meta: {\r\n        className: \"text-right\",\r\n        displayName: \"Actions\",\r\n      },\r\n    },\r\n  ]\r\n}\r\n\r\n", "\"use client\"\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { type Table } from \"@tanstack/react-table\"\r\nimport { ViewOptions } from \"@/components/data-table/DataTableViewOptions\"\r\nimport { Search } from \"@/components/ui/search\"\r\n\r\ninterface UserFilterbarProps<TData> {\r\n  table: Table<TData>\r\n  onSearch?: (value: string) => void\r\n  searchValue?: string\r\n}\r\n\r\nexport function UserFilterbar<TData>({\r\n  table,\r\n  onSearch,\r\n  searchValue = \"\",\r\n}: UserFilterbarProps<TData>) {\r\n  const isFiltered = table.getState().columnFilters.length > 0\r\n\r\n  return (\r\n    <div className=\"flex flex-wrap items-center justify-between gap-2\">\r\n      {onSearch && (\r\n        <div className=\"w-full sm:w-auto sm:max-w-[250px]\">\r\n          <Search onUpdate={onSearch} value={searchValue} />\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex items-center gap-2 ml-auto\">\r\n        {isFiltered && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => table.resetColumnFilters()}\r\n            className=\"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500\"\r\n          >\r\n            Clear filters\r\n          </Button>\r\n        )}\r\n        {/* <Button\r\n          variant=\"secondary\"\r\n          className=\"gap-x-2 px-2 py-1.5 text-sm sm:text-xs\"\r\n        >\r\n          <RiDownloadLine className=\"size-4 shrink-0\" aria-hidden=\"true\" />\r\n          <span className=\"hidden sm:inline\">Export</span>\r\n        </Button> */}\r\n        <ViewOptions table={table} />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useUsers } from '@/lib/hooks/useUsers'\r\nimport { useState } from 'react'\r\n\r\nimport { type IdentityUserUpdateDto } from '@/client'\r\nimport Error from '@/components/ui/Error'\r\nimport { type PaginationState, type SortingState } from '@tanstack/react-table'\r\n\r\nimport { AddUser } from '@/components/app/users/AddUser'\r\nimport { UploadUsersCsv } from '@/components/app/users/UploadUsersCsv'\r\nimport { useToast } from \"@/lib/useToast\"\r\nimport { DeleteUser } from './DeleteUser'\r\nimport { UserEdit } from './UserEdit'\r\nimport { UserPermission } from './UserPermission'\r\n\r\nimport { DataTable } from '@/components/data-table/DataTable'\r\nimport { TableSkeleton } from '@/components/ui/TableSkeleton'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { getUserColumns } from './columns'\r\nimport { UserFilterbar } from './UserFilterbar'\r\n\r\nexport const UserList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  const [searchStr, setSearchStr] = useState<string>('')\r\n  const [userActionDialog, setUserActionDialog] = useState<{\r\n    userId: string\r\n    userDto: IdentityUserUpdateDto\r\n    dialogType?: 'edit' | 'permission' | 'delete'\r\n  } | null>()\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n\r\n  // Initialize sorting state\r\n  const [sorting, setSorting] = useState<SortingState>([\r\n    { id: 'name', desc: false }\r\n  ])\r\n\r\n  // Convert SortingState to API sorting string\r\n  const getSortingString = (sortState: SortingState): string => {\r\n    if (!sortState.length) return 'name asc';\r\n\r\n    const sort = sortState[0] ?? { id: 'name', desc: false };\r\n    return `${sort.id} ${sort.desc ? 'desc' : 'asc'}`;\r\n  }\r\n\r\n  const { isLoading, data, isError } = useUsers(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    searchStr,\r\n    getSortingString(sorting)\r\n  )\r\n\r\n  // Handler for user actions (edit, permission, delete)\r\n  const handleUserAction = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => {\r\n    setUserActionDialog(null); // Reset first to force re-mount\r\n    setTimeout(() => {\r\n      setUserActionDialog({\r\n        userId,\r\n        userDto,\r\n        dialogType,\r\n      });\r\n    }, 0);\r\n  }\r\n\r\n  // Get columns with the action handler\r\n  const columns = getUserColumns(handleUserAction)\r\n\r\n  const handleSearch = (value: string) => {\r\n    setSearchStr(value)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search\r\n  }\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination)\r\n  }\r\n\r\n  // Handler for sorting change\r\n  const handleSortingChange = (newSorting: SortingState) => {\r\n    setSorting(newSorting)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change\r\n  }\r\n\r\n  // Handler for refreshing the data\r\n  const handleRefresh = () => {\r\n    // Invalidate the query to fetch fresh data\r\n    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n\r\n    // Show toast notification after a short delay to match the animation\r\n    setTimeout(() => {\r\n      toast({\r\n        title: \"Data refreshed\",\r\n        description: \"The user list has been refreshed.\",\r\n        variant: \"default\",\r\n      })\r\n    }, 800)\r\n  }\r\n\r\n  if (isLoading) return (\r\n    <TableSkeleton\r\n      rowCount={pagination.pageSize}\r\n      columnCount={4}\r\n      hasTitle={true}\r\n      hasSearch={true}\r\n      hasFilters={true}\r\n      hasPagination={true}\r\n      hasActions={true}\r\n    />\r\n  )\r\n  if (isError) return <Error />\r\n\r\n  return (\r\n    <>\r\n      {userActionDialog && userActionDialog.dialogType === 'edit' && (\r\n        <UserEdit\r\n          userId={userActionDialog.userId}\r\n          userDto={userActionDialog.userDto}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'permission' && (\r\n        <UserPermission\r\n          userId={userActionDialog.userId}\r\n          userDto={userActionDialog.userDto}\r\n          onDismiss={() => setUserActionDialog(null)}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'delete' && (\r\n        <DeleteUser\r\n          user={{\r\n            username: userActionDialog.userDto.userName,\r\n            userId: userActionDialog.userId,\r\n          }}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n\r\n      <div className=\"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4\">\r\n        <DataTable\r\n          title=\"Users Management\"\r\n          columns={columns}\r\n          data={data?.items ?? []}\r\n          totalCount={data?.totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          manualSorting={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={handlePaginationChange}\r\n          onSortingChange={handleSortingChange}\r\n          sortingState={sorting}\r\n          onSearch={handleSearch}\r\n          searchValue={searchStr}\r\n          customFilterbar={UserFilterbar}\r\n          hideDefaultFilterbar={true}\r\n          onRefresh={handleRefresh}\r\n          enableRowSelection={false}\r\n          actionButton={{\r\n            // label: \"Create New User\",\r\n            // No action needed - this is handled by the custom cell renderer\r\n            onClick: () => { /* Required but not used */ },\r\n            content: (\r\n              <div className=\"flex gap-2\">\r\n                <AddUser />\r\n                <UploadUsersCsv />\r\n              </div>\r\n            )\r\n          }}\r\n        />\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { UserList } from '@/components/app/users/UserList';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Users\" />\r\n\r\n      <UserList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["useUsers", "pageIndex", "pageSize", "filter", "sorting", "useQuery", "QueryNames", "skip", "getApiIdentityUsers", "AddUser", "children", "can", "useGrantedPolicies", "open", "<PERSON><PERSON><PERSON>", "useState", "isActive", "setIsActive", "lockoutEnabled", "setLockoutEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "concurrentLoginPreventionMode", "setConcurrentLoginPreventionMode", "toast", "useToast", "queryClient", "useQueryClient", "handleSubmit", "register", "useForm", "createUserMutation", "useMutation", "userData", "postApiIdentityUsers", "err", "onSubmit", "formData", "jsx", "Toaster", "jsxs", "Dialog", "DialogTrigger", "<PERSON><PERSON>", "RiAddLine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "FormSection", "FormField", "Input", "Select", "val", "SelectTrigger", "SelectValue", "SelectContent", "SelectItemExtended", "Checkbox", "checked", "<PERSON><PERSON><PERSON><PERSON>er", "e", "UploadUsersCsv", "file", "setFile", "isUploading", "setIsUploading", "isDownloading", "setIsDownloading", "uploadType", "setUploadType", "handleFileChange", "selectedFile", "downloadTemplateMutation", "response", "getApiImportTemplates", "blob", "url", "a", "uploadCsvMutation", "postApiImportCsv", "handleDownloadTemplate", "handleUpload", "RiUploadLine", "Label", "value", "SelectItem", "RiDownloadLine", "DeleteUser", "userId", "username", "on<PERSON><PERSON><PERSON>", "onYesEvent", "deleteApiIdentityUsersById", "useEffect", "AlertDialog", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "useAssignableRoles", "data", "getApiIdentityUsersAssignableRoles", "useUserRoles", "getApiIdentityUsersByIdRoles", "TABS_NAME", "UserEdit", "userDto", "control", "roles", "setRoles", "userRole", "assignableRoles", "userDetails", "detailsLoading", "getApiIdentityUserDetailsById", "createDataMutation", "dataMutation", "putApiIdentityUsersById", "onCloseEvent", "temp", "r", "onRoleAssignEvent", "useCallback", "role", "hasAssignedRoleExistAlready", "onRoleAssignedSaveEvent", "updateUserDto", "handleUnlockUser", "httpClient", "message", "extraProps", "Tabs", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Controller", "field", "Loader", "classNames", "l", "v4", "key", "UserPermission", "userRoles", "hasAllGranted", "setHasAllGranted", "usePermissions", "PermissionProvider", "permissionGroups", "setPermissionGroups", "hasAllPermissionGranted", "g", "p", "updatedGroups", "group", "permission", "requestPayload", "grant", "putApiPermissionManagementPermissions", "has<PERSON>dmin", "useMemo", "USER_ROLE", "selectedTab", "setSelectedTab", "groupPermissionsByParent", "permissions", "parentMap", "otherPermissions", "childActions", "perm", "match", "parent<PERSON><PERSON>", "action", "Permission", "prev", "v", "idx", "parentRows", "allTablePerms", "allTableChecked", "handleSelectAllTable", "gIdx", "isParent", "<PERSON><PERSON><PERSON><PERSON>", "c", "parent", "StatusBadge", "status", "statusText", "statusClass", "cn", "UserActions", "onAction", "variant", "DropdownMenu", "DropdownMenuTrigger", "RiMoreLine", "DropdownMenuContent", "DropdownMenuItem", "RiShieldKeyholeLine", "RiPencilLine", "getUserColumns", "handleUserAction", "table", "row", "column", "DataTableColumnHeader", "info", "UserFilterbar", "onSearch", "searchValue", "isFiltered", "Search", "ViewOptions", "UserList", "searchStr", "setSearchStr", "userActionDialog", "setUserActionDialog", "pagination", "setPagination", "setSorting", "getSortingString", "sortState", "sort", "isLoading", "isError", "columns", "dialogType", "handleSearch", "handlePaginationChange", "newPagination", "handleSortingChange", "newSorting", "handleRefresh", "TableSkeleton", "Error", "Fragment", "DataTable", "OverViewLayout", "AppLayout", "Head"], "mappings": "m/BAiBO,MAAMA,GAAW,CACtBC,EACAC,EACAC,EACAC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,SAAUL,EAAWC,EAAUC,EAAQC,CAAO,EACpE,QAAS,SAAY,CACnB,IAAIG,EAAO,EACX,OAAIN,EAAY,IACdM,EAAON,EAAYC,IAEJ,MAAMM,GAAoB,CACzC,MAAO,CACL,eAAgBN,EAChB,UAAWK,EACX,OAAQJ,EACR,QAASC,CAAA,CACX,CACD,GACe,IAAA,CAClB,CACD,ECXUK,GAAU,CAAC,CAAE,SAAAC,KAA6B,CAC/C,KAAA,CAAE,IAAAC,CAAI,EAAIC,GAAmB,EAC7B,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAACC,EAAUC,CAAW,EAAIF,EAAAA,SAAS,EAAI,EACvC,CAACG,EAAgBC,CAAiB,EAAIJ,EAAAA,SAAS,EAAI,EACnD,CAACK,EAAeC,CAAgB,EAAIN,EAAAA,SAAS,EAAK,EAClD,CAACO,EAA+BC,CAAgC,EAAIR,EAAAA,SAAwC,CAAC,EAC7G,CAAE,MAAAS,CAAM,EAAIC,EAAS,EACrBC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,CAAS,EAAIC,GAA+B,EAE5DC,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBC,GAAqB,CAAE,KAAMD,EAAU,EACzC,UAAW,IAAM,CACTT,EAAA,CACJ,MAAO,UACP,YAAa,4BACb,QAAS,SAAA,CACV,EACIE,EAAY,kBAAkB,CAAE,SAAU,CAACpB,EAAW,QAAQ,EAAG,EACtEQ,EAAQ,EAAK,CACf,EACA,QAAUqB,GAAoC,CACtCX,EAAA,CACJ,MAAOW,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKC,EAAYC,GAAoC,CAEpD,MAAMJ,EAAkC,CACtC,GAAGI,EACH,SAAArB,EACA,eAAAE,EAEA,gBAAiB,CACf,GAAImB,EAAS,iBAAmB,CAAC,EACjC,8BAA+Bf,EAC/B,cAAeF,CAAA,CAEnB,EAEAW,EAAmB,OAAOE,CAAQ,CACpC,EAEA,cACG,UACC,CAAA,SAAA,CAAAK,EAAA,IAACC,GAAQ,EAAA,EACRC,EAAA,KAAAC,EAAA,CAAO,KAAA5B,EAAY,aAAcC,EAChC,SAAA,CAACwB,EAAAA,IAAAI,GAAA,CAAc,QAAO,GAAE,SAAAhC,CAAS,CAAA,QAChC,UAAQ,CAAA,UAAU,yCAChB,SAAAC,EAAI,0BAA0B,GAC7B6B,OAACG,EAAO,CAAA,KAAK,KAAK,UAAU,kCAAkC,QAAS,IAAM7B,EAAQ,EAAI,EACvF,SAAA,CAAAwB,EAAA,IAACM,GAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/DN,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAe,iBAAA,CAAA,CAAA,CAAA,CAC7D,CAEJ,CAAA,SACCO,EACC,CAAA,SAAA,CAAAP,MAACQ,EACC,CAAA,SAAAR,EAAA,IAACS,EAAY,CAAA,SAAA,mBAAiB,CAAA,EAChC,SACC,OAAK,CAAA,SAAUnB,EAAaQ,CAAQ,EAAG,UAAU,OAChD,SAAA,CAAAE,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAACQ,GACC,CAAA,SAAA,CAAAV,EAAA,IAACW,EAAA,CACC,MAAM,WACN,YAAY,2BAEZ,SAAAX,EAAA,IAACY,GAAM,SAAQ,GAAE,GAAGrB,EAAS,UAAU,EAAG,YAAY,UAAW,CAAA,CAAA,CACnE,EACAS,EAAA,IAACW,EAAA,CACC,MAAM,WACN,YAAY,2BAEZ,SAAAX,EAAAA,IAACY,EAAM,CAAA,SAAQ,GAAC,KAAK,WAAY,GAAGrB,EAAS,UAAU,EAAG,YAAY,UAAW,CAAA,CAAA,CACnF,EACAS,EAAA,IAACW,EAAA,CACC,MAAM,OACN,YAAY,uBAEZ,SAAAX,EAAA,IAACY,GAAM,SAAQ,GAAE,GAAGrB,EAAS,MAAM,EAAG,YAAY,MAAO,CAAA,CAAA,CAC3D,EACAS,EAAA,IAACW,EAAA,CACC,MAAM,UACN,YAAY,0BAEZ,eAACC,EAAO,CAAA,GAAGrB,EAAS,SAAS,EAAG,YAAY,SAAU,CAAA,CAAA,CACxD,EACAS,EAAA,IAACW,EAAA,CACC,MAAM,QACN,YAAY,wBAEZ,SAAAX,EAAA,IAACY,GAAM,SAAQ,GAAE,GAAGrB,EAAS,OAAO,EAAG,YAAY,OAAQ,CAAA,CAAA,CAC7D,EACAS,EAAA,IAACW,EAAA,CACC,MAAM,eACN,YAAY,+BAEZ,eAACC,EAAO,CAAA,GAAGrB,EAAS,aAAa,EAAG,YAAY,cAAe,CAAA,CAAA,CACjE,EACAS,EAAA,IAACW,EAAA,CACC,MAAM,8BACN,YAAY,gDAEZ,SAAAT,EAAA,KAACW,IAAO,cAAgBC,GAAQ7B,EAAiC,OAAO6B,CAAG,CAAkC,EAC3G,SAAA,CAAAd,EAAAA,IAACe,IAAc,UAAU,SACvB,eAACC,GAAY,CAAA,YAAY,cAAc,CACzC,CAAA,SACCC,GACC,CAAA,SAAA,CAACjB,EAAAA,IAAAkB,GAAA,CAAmB,MAAO,OAAO,CAAC,EAAG,OAAQ,WAAY,YAAY,sCAAsC,EAC5GlB,EAAAA,IAACkB,IAAmB,MAAO,OAAO,CAAC,EAAG,OAAQ,gCAAiC,YAAY,mCAAmC,EAC9HlB,MAACkB,IAAmB,MAAO,OAAO,CAAC,EAAG,OAAQ,0BAA2B,YAAY,qCAAsC,CAAA,CAAA,CAC7H,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAlB,EAAA,IAACW,EAAA,CACC,MAAM,kBACN,YAAY,4CAEZ,SAAAX,EAAA,IAACmB,EAAA,CACC,GAAG,gBACH,KAAK,gBACL,QAASrC,EACT,gBAAkBsC,GAAYrC,EAAiB,CAAC,CAACqC,EAAQ,QAAS,CAAA,CAAA,CAAA,CACpE,CACF,EACApB,EAAA,IAACW,EAAA,CACC,MAAM,YACN,YAAY,gCAEZ,SAAAX,EAAA,IAACmB,EAAA,CACC,GAAG,WACH,KAAK,WACL,eAAc,GACd,QAASzC,EACT,gBAAkB0C,GAAYzC,EAAY,CAAC,CAACyC,EAAQ,QAAS,CAAA,CAAA,CAAA,CAC/D,CACF,EACApB,EAAA,IAACW,EAAA,CACC,MAAM,kBACN,YAAY,iCAEZ,SAAAX,EAAA,IAACmB,EAAA,CACC,GAAG,iBACH,KAAK,iBACL,eAAc,GACd,QAASvC,EACT,gBAAkBwC,GAAYvC,EAAkB,CAAC,CAACuC,EAAQ,QAAS,CAAA,CAAA,CAAA,CACrE,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACAlB,EAAAA,KAACmB,EAAa,CAAA,UAAU,OACtB,SAAA,CAAArB,EAAA,IAACK,EAAA,CACC,QAAQ,QACR,QAAUiB,GAAM,CACdA,EAAE,eAAe,EACjB9C,EAAQ,EAAK,CACf,EACA,SAAUiB,EAAmB,UAC9B,SAAA,QAAA,CAED,EACAO,EAAAA,IAACK,EAAO,CAAA,KAAK,SAAS,SAAUZ,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECvLa8B,GAAiB,IAAM,CAC5B,KAAA,CAAE,MAAArC,CAAM,EAAIC,EAAS,EACrBC,EAAcC,EAAe,EAC7B,CAACd,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAC+C,EAAMC,CAAO,EAAIhD,EAAAA,SAAsB,IAAI,EAC5C,CAACiD,EAAaC,CAAc,EAAIlD,EAAAA,SAAS,EAAK,EAC9C,CAACmD,EAAeC,CAAgB,EAAIpD,EAAAA,SAAS,EAAK,EAClD,CAACqD,EAAYC,CAAa,EAAItD,EAAAA,SAA0C,OAAO,EAE/EuD,EAAoBV,GAA2C,CACnE,GAAIA,EAAE,OAAO,OAASA,EAAE,OAAO,MAAM,OAAS,EAAG,CAC/C,MAAMW,EAAeX,EAAE,OAAO,MAAM,CAAC,EAGrC,GAAI,CAACW,GAAc,KAAK,cAAc,SAAS,MAAM,EAAG,CAChD/C,EAAA,CACJ,MAAO,oBACP,YAAa,2BACb,QAAS,aAAA,CACV,EAEDoC,EAAE,OAAO,MAAQ,GACjB,MAAA,CAGFG,EAAQQ,CAAY,CAAA,CAExB,EAGMC,EAA2BxC,EAAY,CAC3C,WAAY,SAAY,CACtBmC,EAAiB,EAAI,EACjB,GAAA,CACI,MAAAM,EAAW,MAAMC,GAAsB,CAC3C,MAAO,CACL,KAAMN,CAAA,CACR,CACD,EAIKO,EAAO,IAAI,KAAK,CAACF,EAAS,IAA2B,EAAG,CAC5D,KAAM,UAAA,CACP,EAGKG,EAAM,OAAO,IAAI,gBAAgBD,CAAI,EACrCE,EAAI,SAAS,cAAc,GAAG,EACpC,OAAAA,EAAE,KAAOD,EACTC,EAAE,SAAW,4BACJ,SAAA,KAAK,YAAYA,CAAC,EAC3BA,EAAE,MAAM,EACD,OAAA,IAAI,gBAAgBD,CAAG,EACrB,SAAA,KAAK,YAAYC,CAAC,EAEpBJ,EAAS,IAAA,QAChB,CACAN,EAAiB,EAAK,CAAA,CAE1B,EACA,QAAUhC,GAAoC,CACtCX,EAAA,CACJ,MAAOW,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAGK2C,EAAoB9C,EAAY,CACpC,WAAY,SAAY,CACtB,GAAI,CAAC8B,EACG,MAAA,IAAI,MAAM,kBAAkB,EAGpCG,EAAe,EAAI,EAEf,GAAA,CAUF,OARiB,MAAMc,GAAiB,CACtC,KAAM,CACJ,KAAMjB,EACN,KAAMM,EACN,UAAW,GAAA,CACb,CACD,GAEe,IAAA,QAChB,CACAH,EAAe,EAAK,CAAA,CAExB,EACA,UAAW,IAAM,CAETzC,EAAA,CACJ,MAAO,oBACP,YAAa,wBACb,QAAS,SAAA,CACV,EAGIE,EAAY,kBAAkB,CAAE,SAAU,CAACpB,EAAW,QAAQ,EAAG,EAGtEQ,EAAQ,EAAK,EAGbiD,EAAQ,IAAI,CACd,EACA,QAAU5B,GAAoC,CACtCX,EAAA,CACJ,MAAOW,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAGK6C,EAAyB,IAAM,CACnCR,EAAyB,OAAO,CAClC,EAGMS,EAAe,IAAM,CACzB,GAAI,CAACnB,EAAM,CACHtC,EAAA,CACJ,MAAO,mBACP,YAAa,qCACb,QAAS,SAAA,CACV,EACD,MAAA,CAGFsD,EAAkB,OAAO,CAC3B,EAEA,OACGtC,EAAAA,KAAAC,EAAA,CAAO,KAAA5B,EAAY,aAAcC,EAChC,SAAA,CAACwB,EAAAA,IAAAI,GAAA,CAAc,QAAO,GACpB,SAAAF,EAAAA,KAACG,GAAO,KAAK,KAAK,UAAU,kCAC1B,SAAA,CAAAL,EAAA,IAAC4C,GAAa,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAClE5C,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAU,YAAA,CAAA,CAAA,CAAA,CACxD,CACF,CAAA,EACAE,EAAAA,KAACK,EAAc,CAAA,UAAU,cACvB,SAAA,CAAAP,MAACQ,EACC,CAAA,SAAAR,EAAA,IAACS,EAAY,CAAA,SAAA,kBAAgB,CAAA,EAC/B,EAEAP,EAAAA,KAAC,MAAI,CAAA,UAAU,2BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,sBACb,SAAA,CAAAF,MAAC6C,GAAM,CAAA,QAAQ,UAAU,UAAU,YAAY,SAAW,cAAA,EAC1D3C,EAAAA,KAACW,IAAO,MAAOiB,EAAY,cAAgBgB,GAAUf,EAAce,CAAwC,EACzG,SAAA,CAAA9C,EAAAA,IAACe,IAAc,UAAU,SACvB,eAACC,GAAY,CAAA,YAAY,qBAAqB,CAChD,CAAA,SACCC,GACC,CAAA,SAAA,CAACjB,EAAA,IAAA+C,GAAA,CAAW,MAAM,QAAQ,SAAK,QAAA,EAC9B/C,EAAA,IAAA+C,GAAA,CAAW,MAAM,QAAQ,SAAK,QAAA,EAC9B/C,EAAA,IAAA+C,GAAA,CAAW,MAAM,YAAY,SAAU,YAAA,CAAA,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EACA7C,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAF,MAAC6C,GAAM,CAAA,QAAQ,WAAW,UAAU,YAAY,SAAiB,oBAAA,EACjE7C,EAAAA,IAAC,MAAI,CAAA,UAAU,0BACb,SAAAE,EAAA,KAACG,EAAA,CACC,QAAQ,UACR,QAASqC,EACT,SAAUd,EACV,UAAU,SAEV,SAAA,CAAC5B,EAAAA,IAAAgD,GAAA,CAAe,UAAU,cAAe,CAAA,EACxCpB,EAAgB,iBAAmB,mBAAA,CAAA,CAAA,EAExC,EACC5B,EAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,SAE7C,0EAAA,CAAA,CAAA,EACF,EAEAE,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAF,MAAC6C,GAAM,CAAA,QAAQ,UAAU,UAAU,YAAY,SAAe,kBAAA,EAC9D7C,EAAA,IAACY,EAAA,CACC,GAAG,UACH,KAAK,OACL,OAAO,OACP,SAAUoB,EACV,SAAUN,CAAA,CACZ,EACCF,GACCtB,EAAA,KAAC,IAAE,CAAA,UAAU,gCAAgC,SAAA,CAAA,kBAC3BsB,EAAK,IAAA,CACvB,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,SAECH,EACC,CAAA,SAAA,CAAArB,EAAA,IAACK,EAAA,CACC,QAAQ,QACR,QAAS,IAAM,CACb7B,EAAQ,EAAK,EACbiD,EAAQ,IAAI,CACd,EACA,SAAUC,EACX,SAAA,QAAA,CAED,EACA1B,EAAA,IAACK,EAAA,CACC,QAASsC,EACT,SAAU,CAACnB,GAAQE,EAElB,WAAc,eAAiB,QAAA,CAAA,CAClC,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECnOauB,GAAa,CAAC,CAAE,KAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAA,EAAY,UAAAC,KAAiC,CAClF,KAAA,CAAE,MAAAlE,CAAM,EAAIC,EAAS,EACrB,CAACZ,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EACzC4E,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,GAA2B,CAC/B,KAAM,CAAE,GAAIJ,CAAO,CAAA,CACpB,EACKhE,EAAA,CACJ,MAAO,UACP,YAAa,SAASiE,CAAQ,kCAAA,CAC/B,EACSC,EAAA,QACHvD,EAAc,CACjBA,aAAe,OACXX,EAAA,CACJ,MAAO,SACP,YAAa,+CAA+CiE,CAAQ,uBACpE,QAAS,aAAA,CACV,CACH,CAEJ,EAEAI,OAAAA,EAAAA,UAAU,IAAM,CACd/E,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFwB,EAAA,IAAAwD,GAAA,CAAY,KAAAjF,EACX,SAAA2B,OAACuD,GACC,CAAA,SAAA,CAAAvD,OAACwD,GACC,CAAA,SAAA,CAAA1D,EAAAA,IAAC2D,IAAiB,SAAwB,0BAAA,CAAA,SACzCC,GAAuB,CAAA,SAAA,CAAA,8EAErBT,EAAS,GAAA,CACZ,CAAA,CAAA,EACF,SACCU,GACC,CAAA,SAAA,CAAC7D,EAAA,IAAA8D,GAAA,CAAkB,QAASV,EAAW,SAAM,SAAA,EAC5CpD,EAAA,IAAA+D,GAAA,CAAkB,QAASV,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EClDaW,GAAqB,IACzBjG,EAAS,CACd,SAAU,CAACC,EAAW,kBAAkB,EACxC,QAAS,SAAY,CACnB,KAAM,CAAE,KAAAiG,GAAS,MAAMC,GAAmC,EACnD,OAAAD,CAAA,CACT,CACD,ECHUE,GAAe,CAAC,CAAE,OAAAjB,KACtBnF,EAAS,CACd,SAAU,CAACC,EAAW,aAAckF,CAAM,EAC1C,QAAS,SAAY,CACnB,KAAM,CAAE,KAAAe,GAAS,MAAMG,GAA6B,CAClD,KAAM,CACJ,GAAIlB,CAAA,CACN,CACD,EACM,OAAAe,CAAA,CACT,CACD,ECDGI,EAAY,CAChB,WAAY,YACZ,kBAAmB,mBACnB,cAAe,eACf,UAAW,UACb,EAYaC,GAAW,CAAC,CAAE,QAAAC,EAAS,OAAArB,EAAQ,UAAAE,KAA+B,CACzE,KAAM,CAAC7E,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAS,CAAM,EAAIC,EAAS,EACrBC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,QAAAiF,CAAA,EAAYhF,GAA+B,CACzE,cAAe,CACb,GAAG+E,CAAA,CACL,CACD,EACK,CAACE,EAAOC,CAAQ,EAAIjG,EAAAA,SAAqB,CAAA,CAAE,EAG3CkG,EAAWR,GAAa,CAAE,OAAAjB,EAAQ,EAClC0B,EAAkBZ,GAAmB,EAGrC,CAAE,KAAMa,EAAa,UAAWC,CAAA,EAAmB/G,EAAS,CAChE,SAAU,CAAC,eAAgBmF,CAAM,EACjC,QAAS,IAAM6B,GAA8B,CAC3C,KAAM,CAAE,GAAI7B,CAAO,CACpB,CAAA,CAAA,CACF,EAEK8B,EAAqBtF,EAAY,CACrC,WAAY,MAAOuF,GACjBC,GAAwB,CACtB,KAAM,CAAE,GAAIhC,CAAO,EACnB,KAAM,CAAE,GAAGqB,EAAS,GAAGU,CAAa,CAAA,CACrC,EACH,UAAW,IAAM,CACT/F,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EACIE,EAAY,kBAAkB,CAAE,SAAU,CAACpB,EAAW,QAAQ,EAAG,EACtEQ,EAAQ,EAAK,CACf,EACA,QAAUqB,GAAoC,CACtCX,EAAA,CACJ,MAAOW,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKC,EAAYC,GAAoC,CAEpD,MAAMJ,EAAkC,CACtC,GAAGI,CACL,EAGKiF,EAAmB,OAAOrF,CAAQ,CACzC,EAEMwF,EAAe,IAAM,CACzB3G,EAAQ,EAAK,EACH4E,EAAA,CACZ,EAEAG,EAAAA,UAAU,IAAM,CACd/E,EAAQ,EAAI,CACd,EAAG,EAAE,EAEL+E,EAAAA,UAAU,IAAM,CACV,GAAAoB,EAAS,MAAM,MAAO,CACxB,MAAMS,EAAmB,CAAC,EAC1BT,EAAS,KAAK,MAAM,QAASU,GAAM,CAC5BD,EAAA,KAAK,CAAE,KAAMC,EAAE,KAAO,GAAIA,EAAE,GAAK,CAAA,CACvC,EACDX,EAASU,CAAI,CAAA,CAEd,EAAA,CAACT,EAAS,MAAM,KAAK,CAAC,EAEzB,MAAMW,EAAoBC,EAAA,YACvBC,GAA0B,CACnB,MAAAC,EAA8BhB,EAAM,UAAWY,GAAMG,EAAK,KAAOH,EAAE,EAAE,EAGvEI,IAAgC,IAC5BhB,EAAA,OAAOgB,EAA6B,CAAC,EAClCf,EAAA,CAAC,GAAGD,CAAK,CAAC,IAEbA,EAAA,KAAK,CAAE,KAAMe,EAAK,KAAO,GAAIA,EAAK,GAAK,EACpCd,EAAA,CAAC,GAAGD,CAAK,CAAC,EAEvB,EACA,CAACA,CAAK,CACR,EAEMiB,EAA2BpE,GAAkB,CACjDA,EAAE,eAAe,EACjB,MAAMqE,EAAuC,CAC3C,GAAGpB,EACH,UAAWE,GAAO,IAAKY,GAAMA,EAAE,IAAI,GAAK,CAAA,CAC1C,EACAvF,EAAS6F,CAAa,CACxB,EAEMC,EAAmB,MAAOtE,GAAkB,CAChDA,EAAE,eAAe,EACb,GAAA,CACF,MAAMuE,GAAW,KAAK,CAAE,IAAK,uBAAuB3C,CAAM,UAAW,EAC/DhE,EAAA,CAAE,MAAO,gBAAiB,YAAa,GAAGqF,EAAQ,QAAQ,sBAAuB,QAAS,SAAA,CAAW,EAEtGnF,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAgB8D,CAAM,EAAG,QAClErD,EAAc,CACf,MAAAiG,EAAUjG,GAAO,OAAOA,GAAQ,UAAY,YAAaA,EAAM,OAAQA,EAA6B,OAAO,EAAI,oBACrHX,EAAM,CAAE,MAAO,mBAAoB,YAAa4G,EAAS,QAAS,cAAe,CAAA,CAErF,EAEMC,EADUlB,GAAa,MACC,gBAG5B,OAAA7E,EAAAA,IAACG,EAAO,CAAA,KAAA5B,EAAY,aAAc4G,EAChC,gBAAC5E,EAAc,CAAA,KAAK,MAAM,UAAU,GAClC,SAAA,CAACP,EAAA,IAAAQ,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,kBAAgB8D,EAAQ,QAAA,CAAA,CAAS,CAChD,CAAA,EACCrE,EAAA,KAAA8F,GAAA,CAAK,aAAc3B,EAAU,WAC5B,SAAA,CAACnE,EAAAA,KAAA+F,GAAA,CAAS,UAAU,SAClB,SAAA,CAAAjG,EAAA,IAACkG,EAAY,CAAA,MAAO7B,EAAU,WAAY,SAE1C,mBAAA,EACCrE,EAAA,IAAAkG,EAAA,CAAY,MAAO7B,EAAU,kBAAmB,SAEjD,QAAA,EACCrE,EAAA,IAAAkG,EAAA,CAAY,MAAO7B,EAAU,cAAe,SAE7C,UAAA,EACCrE,EAAA,IAAAkG,EAAA,CAAY,MAAO7B,EAAU,UAAW,SAEzC,KAAA,CAAA,CAAA,EACF,EACArE,EAAAA,IAACmG,EAAY,CAAA,MAAO9B,EAAU,WAC5B,gBAAC,OAAK,CAAA,SAAU/E,EAAaQ,CAAQ,EACnC,SAAA,CAAAE,MAAC,UAAQ,CAAA,UAAU,+BACjB,SAAAE,EAAA,KAACQ,GACC,CAAA,SAAA,CAAAV,EAAA,IAACW,EAAA,CACC,MAAM,OACN,YAAY,uBAEZ,SAACX,EAAA,IAAAY,EAAA,CAAM,SAAQ,GAAE,GAAGrB,EAAS,MAAM,EAAG,aAAcgF,EAAQ,MAAQ,GAAI,YAAY,MAAO,CAAA,CAAA,CAC7F,EAEAvE,EAAA,IAACW,EAAA,CACC,MAAM,UACN,YAAY,0BAEZ,SAAAX,EAAAA,IAACY,EAAO,CAAA,GAAGrB,EAAS,SAAS,EAAG,aAAcgF,EAAQ,SAAW,GAAI,YAAY,SAAU,CAAA,CAAA,CAC7F,EAEAvE,EAAA,IAACW,EAAA,CACC,MAAM,QACN,YAAY,wBAEZ,SAACX,EAAA,IAAAY,EAAA,CAAM,SAAQ,GAAE,GAAGrB,EAAS,OAAO,EAAG,aAAcgF,EAAQ,OAAS,GAAI,YAAY,OAAQ,CAAA,CAAA,CAChG,EAEAvE,EAAA,IAACW,EAAA,CACC,MAAM,eACN,YAAY,+BAEZ,SAAAX,EAAAA,IAACY,EAAO,CAAA,GAAGrB,EAAS,aAAa,EAAG,aAAcgF,EAAQ,aAAe,GAAI,YAAY,cAAe,CAAA,CAAA,CAC1G,EAEAvE,EAAA,IAACW,EAAA,CACC,MAAM,YACN,YAAY,gCAEZ,SAAAX,EAAA,IAACoG,GAAA,CACC,KAAK,WACL,QAAA5B,EACA,OAAQ,CAAC,CAAE,MAAA6B,CAAA,IACTrG,EAAA,IAACmB,EAAA,CACC,GAAG,WACH,QAAS,CAAC,CAACkF,EAAM,MACjB,gBAAkBjF,GAAYiF,EAAM,SAAS,CAAC,CAACjF,CAAO,CAAA,CAAA,CACxD,CAAA,CAEJ,CACF,EACApB,EAAA,IAACW,EAAA,CACC,MAAM,kBACN,YAAY,iCAEZ,SAAAX,EAAA,IAACoG,GAAA,CACC,KAAK,iBACL,QAAA5B,EACA,OAAQ,CAAC,CAAE,MAAA6B,CAAA,IACTrG,EAAA,IAACmB,EAAA,CACC,GAAG,iBACH,QAAS,CAAC,CAACkF,EAAM,MACjB,gBAAkBjF,GAAYiF,EAAM,SAAS,CAAC,CAACjF,CAAO,CAAA,CAAA,CACxD,CAAA,CAEJ,CAAA,CACF,CAAA,CACF,CACF,CAAA,EAEAlB,EAAAA,KAACmB,EAAa,CAAA,UAAU,+BACtB,SAAA,CAAArB,EAAA,IAACK,EAAA,CACC,QAAQ,UACR,QAAUiB,GAAM,CACdA,EAAE,eAAe,EACJ6D,EAAA,CACf,EACD,SAAA,QAAA,CAED,EACCN,GAAa,MAAM,YACjB7E,EAAAA,IAAAK,EAAA,CAAO,KAAK,SAAS,QAAQ,YAAY,QAASuF,EAAkB,SAErE,aAAA,CAAA,EAED5F,EAAA,IAAAK,EAAA,CAAO,KAAK,SAAS,SAAI,MAAA,CAAA,CAAA,CAC5B,CAAA,CAAA,CAAA,CACF,CACF,CAAA,SACC8F,EAAY,CAAA,UAAU,6CAA6C,MAAO9B,EAAU,kBAClF,SAAA,CAAiBO,GAAA,iBAAc0B,GAAO,CAAA,CAAA,EACtC1B,GAAiB,SACf1E,OAAA,MAAA,CAAI,UAAU,yBAAyB,SAAA,CAAA,+DACuBqE,EAAQ,QAAA,EACvE,EAED,CAACK,EAAgB,WAAa,CAACA,EAAgB,SAC9C5E,MAAC,OAAI,UAAU,yBACZ,YAAiB,MAAM,OAAO,IAAKqF,UACjC,MAAe,CAAA,UAAWkB,GAAW,uCAAuC,EAC3E,SAAA,CAAAvG,EAAA,IAACmB,EAAA,CACC,GAAIkE,EAAE,GACN,KAAMA,EAAE,KACR,QAAS,CAAC,CAACZ,GAAO,KAAM+B,GAAMA,EAAE,KAAOnB,EAAE,EAAE,EAC3C,gBAAiB,IAAM,CACrBC,EAAkBD,CAAC,CAAA,CACrB,CACF,EACArF,EAAAA,IAAC,SAAM,QAASqF,EAAE,GAAI,UAAU,mCAC7B,WAAE,IACL,CAAA,CAAA,GAXQoB,IAYV,CACD,EACH,EAEFvG,EAAAA,KAACmB,EAAa,CAAA,UAAU,OACtB,SAAA,CAAArB,EAAA,IAACK,EAAA,CACC,QAAQ,UACR,QAAUiB,GAAM,CACdA,EAAE,eAAe,EACJ6D,EAAA,CACf,EACD,SAAA,QAAA,CAED,EACCnF,EAAA,IAAAK,EAAA,CAAO,QAASqF,EAAyB,SAAI,MAAA,CAAA,CAAA,CAChD,CAAA,CAAA,EACF,EACC1F,EAAA,IAAAmG,EAAA,CAAY,MAAO9B,EAAU,cAC3B,SAAAS,EACE9E,EAAA,IAAAsG,GAAA,CAAO,CAAA,EACNzB,EACD3E,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACF,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAY,eAAA,EAAO,IAAE6E,EAAY,MAAM,iBAAmB,GAAA,EAC5F,SACC,MACC,CAAA,SAAA,CAAC7E,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAe,kBAAA,EAAO,IAAE6E,EAAY,MAAM,aAAe,IAAI,KAAKA,EAAY,MAAM,YAAY,EAAE,iBAAmB,GAAA,EACvJ,SACC,MACC,CAAA,SAAA,CAAC7E,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAa,gBAAA,EAAO,IAAE6E,EAAY,MAAM,sBAAwB,GAAA,EAClG,SACC,MACC,CAAA,SAAA,CAAC7E,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAmB,sBAAA,EAAO,IAAE6E,EAAY,MAAM,qBAAuB,IAAI,KAAKA,EAAY,MAAM,oBAAoB,EAAE,iBAAmB,GAAA,EAC3K,SACC,MACC,CAAA,SAAA,CAAC7E,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAsB,yBAAA,EAAO,IAAE6E,EAAY,MAAM,mBAAqB,IAAI,KAAKA,EAAY,MAAM,kBAAkB,EAAE,iBAAmB,GAAA,EAC1K,SACC,MACC,CAAA,SAAA,CAAC7E,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAkB,qBAAA,EAAO,IAAE6E,EAAY,MAAM,WAAa,IAAI,KAAKA,EAAY,MAAM,UAAU,EAAE,iBAAmB,GAAA,EACtJ,SACC,MACC,CAAA,SAAA,CAAC7E,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAqB,wBAAA,EAAO,IAAE6E,EAAY,MAAM,mBAAqB,CAAA,CACvG,CAAA,CAAA,EACF,EAEC7E,MAAA,MAAA,CAAI,UAAU,oBAAoB,4BAAiB,CAAA,EAExD,EACCA,MAAAmG,EAAA,CAAY,MAAO9B,EAAU,UAC3B,SACCS,EAAA9E,EAAA,IAACsG,GAAO,CAAA,CAAA,EACNzB,EACD7E,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,gBAAC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAkB,qBAAA,EACjDA,MAAA,MAAA,CAAI,UAAU,2CACZ,UAAC+F,GAAc,OAAO,KAAKA,CAAU,EAAE,SAAW,EAChD/F,MAAA,MAAA,CAAI,UAAU,gBAAgB,SAAA,sBAAA,CAAoB,EAEnDA,MAAC,OAAI,UAAU,yBACZ,SAAO,OAAA,QAAQ+F,CAAU,EAAE,IAAI,CAAC,CAACW,EAAK5D,CAAK,IACzC5C,EAAAA,KAAA,MAAA,CAAc,UAAU,yBACvB,SAAA,CAACF,EAAA,IAAA,MAAA,CAAI,UAAU,4BAA6B,SAAI0G,EAAA,QAC/C,MAAI,CAAA,UAAU,mBAAoB,SAAA,OAAO5D,CAAK,CAAE,CAAA,CAAA,GAFzC4D,CAGV,CACD,CAAA,CACH,CAEJ,CAAA,CAAA,EACF,EACF,EAEA1G,EAAAA,IAAC,OAAI,UAAU,oBAAoB,6BAAiB,CAExD,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EClVa2G,GAAiB,CAAC,CAAE,QAAApC,EAAS,OAAArB,EAAQ,UAAAE,KAAqC,CACrF,KAAM,CAAC7E,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAS,CAAM,EAAIC,EAAS,EACrByH,EAAYzC,GAAa,CAAE,OAAAjB,EAAQ,EAGnC,CAAC2D,EAAeC,CAAgB,EAAIrI,EAAAA,SAAS,EAAK,EAClD,CAAE,KAAAwF,CAAK,EAAI8C,GAAeC,EAAmB,EAAG9D,CAAM,EACtD9D,EAAcC,EAAe,EAE7B,CAAC4H,EAAkBC,CAAmB,EAAIzI,EAAAA,SAA+B,CAAA,CAAE,EAEjF8E,EAAAA,UAAU,KACR/E,EAAQ,EAAI,EACL,IAAM,CACNY,EAAY,kBAAkB,CAAE,SAAU,CAAC4H,EAAmB,CAAC,EAAG,CACzE,GAEC,EAAE,EAGLzD,EAAAA,UAAU,IAAM,CACVU,GAAM,QAAU,MAAM,QAAQA,EAAK,MAAM,GAC3CiD,EAAoB,CAAC,GAAGjD,EAAK,MAAM,CAAC,CACtC,EACC,CAACA,CAAI,CAAC,EAITV,EAAAA,UAAU,IAAM,CACd,GAAIU,GAAM,QAAUA,EAAK,OAAO,OAAS,EAAG,CAC1C,MAAMkD,EAA0BlD,EAAK,OAClC,IAAKmD,GAAMA,EAAE,aAAa,MAAOC,GAAMA,EAAE,SAAS,CAAC,EACnD,MAAO/F,GAAMA,CAAC,EACjBwF,EAAiBK,CAAuB,CAAA,CAC1C,EACC,CAAClD,CAAI,CAAC,EAGTV,EAAAA,UAAU,IAAM,CACV,GAAA0D,EAAiB,OAAS,EAAG,CACzB,MAAAK,EAAgBL,EAAiB,IAAaM,IAE3C,CACL,GAAGA,EACH,YAAaA,EAAM,aAAa,IAAmBC,IAAA,CACjD,GAAGA,EACH,UAAWX,CAAA,EACX,CACJ,EACD,EAGDK,EAAoBI,CAAa,CAAA,CACnC,EACC,CAACT,CAAa,CAAC,EAElB,MAAM/G,EAAWyF,EAAA,YACf,MAAOjE,GAAiB,CACtBA,EAAE,eAAe,EASjB,MAAMmG,EAAuC,CAC3C,YATcR,GACZ,IAAKI,GACLA,EAAE,YAAa,IAAKK,IAAW,CAC7B,KAAMA,EAAM,KACZ,UAAWA,EAAM,SAAA,EACjB,GAEH,KAAK,CAGR,EACI,GAAA,CACF,MAAMC,GAAsC,CAC1C,MAAO,CACL,aAAcX,EAAmB,EACjC,YAAa9D,CACf,EACA,KAAMuE,CAAA,CACP,EACKvI,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EACIE,EAAY,kBAAkB,CACjC,SAAU,CAAC4H,EAAmB,CAAC,CAAA,CAChC,EACY7B,EAAA,QACNtF,EAAc,CACjBA,aAAe,OACXX,EAAA,CACJ,MAAO,SACP,YAAa,uCACb,QAAS,aAAA,CACV,CACH,CAEJ,EAEA,CAAC+H,CAAgB,CACnB,EAEM9B,EAAeI,EAAAA,YAAY,IAAM,CACrC/G,EAAQ,EAAK,EACH4E,EAAA,CAEZ,EAAG,EAAE,EAECwE,EAAWC,EAAAA,QAAQ,IACnBjB,GAAW,MAAM,MACZA,EAAU,KAAK,MAAM,OAAQpB,GAASA,EAAK,MAAM,SAASsC,GAAU,KAAK,CAAC,EAAE,OAAS,EAEvF,GACN,CAAClB,CAAS,CAAC,EAER,CAACmB,EAAaC,CAAc,EAAIvJ,EAAAA,SAAS,CAAC,EAG1CwJ,EAA4BC,GAA6D,CAC7F,GAAI,CAAC,MAAM,QAAQA,CAAW,GAAKA,EAAY,SAAW,EAAG,MAAO,CAAE,WAAY,GAAI,iBAAkB,CAAA,CAAG,EAC3G,MAAMC,EAAkH,CAAC,EACnHC,EAA6C,CAAC,EAC9CC,EAAe,CAAC,SAAU,OAAQ,OAAQ,QAAQ,EAC5C,OAAAH,EAAA,QAASI,GAAS,EAEVA,EAAK,MAAM,MAAM,KAAK,GAAK,CAAA,GAAI,SAChC,GAAK,CAACD,EAAa,KAAK9F,GAAK+F,EAAK,MAAM,SAAS,IAAM/F,CAAC,CAAC,IAE9D4F,EAAAG,EAAK,IAAK,EAAI,CAAE,OAAQA,EAAM,SAAU,EAAG,EACvD,CACD,EAEWJ,EAAA,QAASI,GAAS,CAC5B,MAAMC,EAAQD,EAAK,MAAM,MAAM,mCAAmC,EAClE,GAAIC,EAAO,CACH,MAAAC,EAAYD,EAAM,CAAC,EACnBE,EAASF,EAAM,CAAC,EAClBJ,EAAUK,CAAS,EACrBL,EAAUK,CAAS,EAAE,SAASC,CAAM,EAAIH,EAExCF,EAAiB,KAAKE,CAAI,CAC5B,MAGKH,EAAUG,EAAK,IAAK,GACvBF,EAAiB,KAAKE,CAAI,CAE9B,CACD,EACM,CAAE,WAAY,OAAO,OAAOH,CAAS,EAAG,iBAAAC,CAAiB,CAClE,EAGE,OAAApI,EAAAA,IAACG,EAAO,CAAA,KAAA5B,EAAY,aAAc4G,EAChC,gBAAC5E,EAAc,CAAA,KAAM,MAAO,UAAU,uDACpC,SAAA,CAACP,EAAA,IAAAQ,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,iBAAe8D,EAAQ,QAAA,CAAA,CAAS,CAC/C,CAAA,EACAvE,EAAAA,IAAC,QAAK,SAAAF,EAAoB,UAAU,yBAClC,SAACI,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAF,EAAA,IAAC0I,GAAA,CACC,KAAK,wBACL,UAAW7B,EACX,GAAG,cACH,SAAU,CAACe,EACX,SAAU,IAAM,CACGd,EAAC6B,GAAS,CAACA,CAAI,CAClC,EACA,UAAU,WAAA,CACZ,EAEAzI,EAAA,KAAC8F,GAAA,CACC,MAAO+B,EAAY,SAAS,EAC5B,cAAea,GAAKZ,EAAe,OAAOY,CAAC,CAAC,EAC5C,YAAY,WACZ,UAAU,2HAEV,SAAA,CAAC5I,EAAA,IAAA,MAAA,CAAI,UAAU,2BACb,SAACA,EAAAA,IAAAiG,GAAA,CAAS,QAAQ,SAAS,UAAU,yEAClC,SAAiBgB,EAAA,IAAI,CAACM,EAAOsB,IAC3B7I,EAAA,IAAAkG,EAAA,CAA6B,MAAO2C,EAAI,SAAS,EAAI,SAAMtB,EAAA,WAAA,EAA1CA,EAAM,IAAgD,CACzE,CACH,CAAA,EACF,EACAvH,MAAC,OAAI,UAAU,wDACZ,WAAiB,IAAI,CAACuH,EAAOsB,IAAQ,CAC9B,KAAA,CAAE,WAAAC,EAAY,iBAAAV,CAAiB,EAAIH,EAAyBV,EAAM,aAAe,EAAE,EAEnFwB,EAA0C,CAC9C,GAAGD,EAAW,IAAIzD,GAAKA,EAAE,MAAM,EAC/B,GAAGyD,EAAW,QAAQzD,GAAK,OAAO,OAAOA,EAAE,QAAQ,CAAC,CACtD,EACM2D,EAAkBD,EAAc,OAAS,GAAKA,EAAc,MAAM1B,GAAKA,EAAE,SAAS,EAClF4B,EAAwB7H,GAAqB,CACjD8F,EAA4ByB,GAAAA,EAAK,IAAI,CAACvB,EAAG8B,IACnCA,IAASL,EAAYzB,EAClB,CACL,GAAGA,EACH,YAAaA,EAAE,aAAa,IAASC,GAAA,CAE7B,MAAA8B,EAAWL,EAAW,KAAKzD,IAAKA,GAAE,OAAO,OAASgC,EAAE,IAAI,EACxD+B,EAAUN,EAAW,KAAKzD,IAAK,OAAO,OAAOA,GAAE,QAAQ,EAAE,KAAUgE,IAAAA,GAAE,OAAShC,EAAE,IAAI,CAAC,EAC3F,OAAI8B,GAAYC,EACP,CAAE,GAAG/B,EAAG,UAAWjG,CAAQ,EAE7BiG,CACR,CAAA,CACH,CACD,CAAC,CACJ,EACA,cACGlB,EAA6B,CAAA,MAAO0C,EAAI,WAAY,UAAU,kBAE7D,SAAA,CAAC3I,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAF,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASgJ,EACT,SAAU1H,GAAK2H,EAAqB3H,EAAE,OAAO,OAAO,EACpD,aAAW,uCACX,UAAU,kEAAA,CACZ,EACAtB,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,EAClB,EAEC8I,EAAW,OAAS,GAClB5I,EAAA,KAAA,QAAA,CAAM,UAAU,yBACf,SAAA,CAACF,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,sBAAsB,SAAI,OAAA,EACvCA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAM,SAAA,EAC3CA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAI,OAAA,EACzCA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAM,SAAA,EAC3CA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAI,OAAA,EACzCA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAM,QAAA,CAAA,CAAA,CAAA,CAC9C,CACF,CAAA,EACCA,EAAA,IAAA,QAAA,CACE,SAAW8I,EAAA,IAAI,CAAC,CAAE,OAAAQ,EAAQ,SAAAlL,CACzB,IAAA8B,EAAA,KAAC,KAAqB,CAAA,UAAU,WAC9B,SAAA,CAAAF,MAAC,MAAG,UAAU,wBAAyB,SAAOsJ,EAAA,aAAeA,EAAO,KAAK,EAEzEtJ,EAAAA,IAAC,KAAG,CAAA,UAAU,wBACZ,SAAAA,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASsJ,EAAO,UAChB,SAAU,IAAM,CACdpC,EAA4ByB,GAAAA,EAAK,IAAI,CAACvB,EAAG8B,IACnCA,IAASL,EAAYzB,EAClB,CACL,GAAGA,EACH,YAAaA,EAAE,aAAa,IAC1BC,GAAAA,EAAE,OAASiC,EAAO,KAAO,CAAE,GAAGjC,EAAG,UAAW,CAACA,EAAE,SAAA,EAAcA,CAAA,CAEjE,CACD,CAAC,CACJ,EACA,aAAY,OAAOiC,EAAO,aAAeA,EAAO,IAAI,EACpD,UAAU,kEAAA,CAAA,EAEd,EAEC,CAAC,OAAQ,SAAU,OAAQ,QAAQ,EAAE,IACpCb,GAAAzI,EAAA,IAAC,KAAgB,CAAA,UAAU,wBACxB,SAAA5B,EAASqK,CAAM,EACdzI,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS5B,EAASqK,CAAM,EAAE,UAC1B,SAAU,IAAM,CACdvB,EAA4ByB,GAAAA,EAAK,IAAI,CAACvB,EAAG8B,IACnCA,IAASL,EAAYzB,EAClB,CACL,GAAGA,EACH,YAAaA,EAAE,aAAa,IAC1BC,GAAAA,EAAE,OAASjJ,EAASqK,CAAM,EAAE,KAAO,CAAE,GAAGpB,EAAG,UAAW,CAACA,EAAE,WAAcA,CAAA,CAE3E,CACD,CAAC,CACJ,EACA,aAAY,OAAOjJ,EAASqK,CAAM,EAAE,aAAerK,EAASqK,CAAM,EAAE,IAAI,EACxE,UAAU,kEAAA,CAAA,EAEV,IAnBG,EAAAA,CAoBT,CACD,CAAA,GA7CMa,EAAO,IA8ChB,CACD,CACH,CAAA,CAAA,EACF,EAGDlB,EAAiB,OAAS,GACxBlI,EAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACF,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAAiB,oBAAA,EACrDA,EAAA,IAAC,MAAI,CAAA,UAAU,wCACZ,SAAAoI,EAAiB,IAChBE,GAAApI,EAAA,KAAC,QAAsB,CAAA,UAAU,0BAC/B,SAAA,CAAAF,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASsI,EAAK,UACd,SAAU,IAAM,CACdpB,EAA4ByB,GAAAA,EAAK,IAAI,CAACvB,EAAG8B,IACnCA,IAASL,EAAYzB,EAClB,CACL,GAAGA,EACH,YAAaA,EAAE,aAAa,IAC1BC,GAAAA,EAAE,OAASiB,EAAK,KAAO,CAAE,GAAGjB,EAAG,UAAW,CAACA,EAAE,SAAA,EAAcA,CAAA,CAE/D,CACD,CAAC,CACJ,EACA,aAAY,OAAOiB,EAAK,MAAQA,EAAK,WAAW,EAChD,UAAU,kEAAA,CACZ,EACCtI,EAAA,IAAA,OAAA,CAAM,SAAKsI,EAAA,MAAQA,EAAK,WAAY,CAAA,CAAA,GAlB3BA,EAAK,IAmBjB,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,EA1Gcf,EAAM,IA4GxB,CAAA,CAEH,CACH,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACArH,EAAAA,KAACmB,EAAa,CAAA,UAAU,+CACtB,SAAA,CAAArB,EAAA,IAACK,EAAA,CACC,QAAUiB,GAAM,CACdA,EAAE,eAAe,EACJ6D,EAAA,CACf,EACA,QAAQ,QACT,SAAA,QAAA,CAED,EACCnF,EAAA,IAAAK,EAAA,CAAO,QAASP,EAAU,SAAI,MAAA,CAAA,CAAA,CACjC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC3WayJ,GAAc,CAAC,CAAE,OAAAC,KAA+B,CAE3D,IAAIC,EAAa,WACbC,EAAc,4CAEd,OAAA,OAAOF,GAAW,UAChBA,IACWC,EAAA,SACCC,EAAA,+CAEP,OAAOF,GAAW,WAEdC,EAAAD,EAETA,EAAO,YAAY,IAAM,SACbE,EAAA,8CACLF,EAAO,YAAY,IAAM,WACpBE,EAAA,8CACLF,EAAO,YAAY,IAAM,aACpBE,EAAA,8CAKhB1J,MAAC,QAAK,UAAW2J,GACf,2EACAD,CAAA,EAEC,SACHD,EAAA,CAEJ,ECnBaG,GAAc,CAAC,CAAE,OAAA1G,EAAQ,QAAAqB,EAAS,SAAAsF,EAAU,QAAAC,EAAU,cAAkC,CAC7F,KAAA,CAAE,IAAAzL,CAAI,EAAIC,GAAmB,EAGnC,OAAIwL,IAAY,WAEX9J,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,gBAAC+J,GACC,CAAA,SAAA,CAAC/J,EAAAA,IAAAgK,GAAA,CAAoB,QAAO,GAC1B,SAAA9J,EAAA,KAACG,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cAEV,SAAA,CAACL,EAAAA,IAAAiK,GAAA,CAAW,UAAU,SAAU,CAAA,EAC/BjK,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEvC,EACCE,EAAA,KAAAgK,GAAA,CAAoB,MAAM,MAAM,UAAU,YACxC,SAAA,CAAA7L,EAAI,qCAAqC,GACxC2B,EAAA,IAACmK,GAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAAS3G,EAAQqB,EAAS,YAAY,EACtD,SAAA,YAAA,CAED,EAEDlG,EAAI,0BAA0B,GAC7B2B,EAAA,IAACmK,GAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAAS3G,EAAQqB,EAAS,MAAM,EAChD,SAAA,MAAA,CAED,EAEDlG,EAAI,0BAA0B,GAC7B2B,EAAA,IAACmK,GAAA,CACC,UAAU,sCACV,QAAS,IAAMN,EAAS3G,EAAQqB,EAAS,QAAQ,EAClD,SAAA,QAAA,CAAA,CAED,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAMFrE,EAAA,KAAC,MAAI,CAAA,UAAU,sCACZ,SAAA,CAAA7B,EAAI,qCAAqC,GACxC6B,EAAA,KAACG,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMwJ,EAAS3G,EAAQqB,EAAS,YAAY,EAErD,SAAA,CAACvE,EAAAA,IAAAoK,GAAA,CAAoB,UAAU,SAAU,CAAA,EACzCpK,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,EAED3B,EAAI,0BAA0B,GAC7B6B,EAAA,KAACG,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMwJ,EAAS3G,EAAQqB,EAAS,MAAM,EAE/C,SAAA,CAACvE,EAAAA,IAAAqK,GAAA,CAAa,UAAU,SAAU,CAAA,EAClCrK,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,CAAA,CAAA,CACZ,EAEJ,CAEJ,ECnFasK,GACXC,GAEO,CACL,CACE,GAAI,SACJ,OAAQ,CAAC,CAAE,MAAAC,CAAA,IACTxK,EAAA,IAACmB,EAAA,CACC,QACEqJ,EAAM,2BACF,GACAA,EAAM,sBAAA,EACJ,gBACA,GAER,gBAAiB,IAAMA,EAAM,0BAA0B,EACvD,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,KAAM,CAAC,CAAE,IAAAC,CAAA,IACPzK,EAAA,IAACmB,EAAA,CACC,QAASsJ,EAAI,cAAc,EAC3B,gBAAiB,IAAMA,EAAI,eAAe,EAC1C,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,YAAa,QAAA,CAEjB,EACA,CACE,YAAa,WACb,OAAQ,CAAC,CAAE,OAAAC,CAAA,IAAc1K,EAAA,IAAA2K,EAAA,CAAsB,OAAAD,EAAgB,MAAM,WAAW,EAChF,aAAc,GACd,cAAe,GACf,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,UAAA,CAEjB,EACA,CACE,YAAa,OACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IAAc1K,EAAA,IAAA2K,EAAA,CAAsB,OAAAD,EAAgB,MAAM,OAAO,EAC5E,aAAc,GACd,cAAe,GACf,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,MAAA,CAEjB,EACA,CACE,YAAa,0BACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IAAc1K,EAAA,IAAA2K,EAAA,CAAsB,OAAAD,EAAgB,MAAM,UAAU,EAC/E,aAAc,GACd,cAAe,GACf,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,SAAA,CAEjB,EACA,CACE,YAAa,QACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IAAc1K,EAAA,IAAA2K,EAAA,CAAsB,OAAAD,EAAgB,MAAM,QAAQ,EAC7E,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,OAAA,CAEjB,EACA,CACE,YAAa,WACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IAAc1K,EAAA,IAAA2K,EAAA,CAAsB,OAAAD,EAAgB,MAAM,SAAS,EAC9E,aAAc,GACd,cAAe,GACf,KAAOE,GAAS5K,MAACuJ,IAAY,OAAQqB,EAAK,WAAuB,EACjE,KAAM,CACJ,UAAW,YACX,YAAa,QAAA,CAEjB,EACA,CACE,GAAI,UACJ,OAAQ,UACR,aAAc,GACd,KAAOA,GACL5K,EAAA,IAAC4J,GAAA,CACC,OAAQgB,EAAK,IAAI,SAAS,GAC1B,QAASA,EAAK,IAAI,SAClB,SAAUL,EACV,QAAQ,UAAA,CACV,EAEF,KAAM,CACJ,UAAW,aACX,YAAa,SAAA,CACf,CAEJ,EC1GK,SAASM,GAAqB,CACnC,MAAAL,EACA,SAAAM,EACA,YAAAC,EAAc,EAChB,EAA8B,CAC5B,MAAMC,EAAaR,EAAM,SAAS,EAAE,cAAc,OAAS,EAGzD,OAAAtK,EAAA,KAAC,MAAI,CAAA,UAAU,oDACZ,SAAA,CACC4K,GAAA9K,EAAA,IAAC,MAAI,CAAA,UAAU,oCACb,SAAAA,EAAAA,IAACiL,IAAO,SAAUH,EAAU,MAAOC,CAAA,CAAa,CAClD,CAAA,EAGF7K,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACZ,SAAA,CACC8K,GAAAhL,EAAA,IAACK,EAAA,CACC,QAAQ,QACR,QAAS,IAAMmK,EAAM,mBAAmB,EACxC,UAAU,6HACX,SAAA,eAAA,CAED,EASFxK,MAACkL,IAAY,MAAAV,CAAc,CAAA,CAAA,CAC7B,CAAA,CAAA,EACF,CAEJ,CC3BO,MAAMW,GAAW,IAAM,CACtB,KAAA,CAAE,MAAAjM,CAAM,EAAIC,EAAS,EACrBC,EAAcC,EAAe,EAE7B,CAAC+L,EAAWC,CAAY,EAAI5M,EAAAA,SAAiB,EAAE,EAC/C,CAAC6M,EAAkBC,CAAmB,EAAI9M,WAItC,EAEJ,CAAC+M,EAAYC,CAAa,EAAIhN,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAGK,CAACX,EAAS4N,CAAU,EAAIjN,WAAuB,CACnD,CAAE,GAAI,OAAQ,KAAM,EAAM,CAAA,CAC3B,EAGKkN,EAAoBC,GAAoC,CACxD,GAAA,CAACA,EAAU,OAAe,MAAA,WAExB,MAAAC,EAAOD,EAAU,CAAC,GAAK,CAAE,GAAI,OAAQ,KAAM,EAAM,EACvD,MAAO,GAAGC,EAAK,EAAE,IAAIA,EAAK,KAAO,OAAS,KAAK,EACjD,EAEM,CAAE,UAAAC,EAAW,KAAA7H,EAAM,QAAA8H,CAAY,EAAArO,GACnC8N,EAAW,UACXA,EAAW,SACXJ,EACAO,EAAiB7N,CAAO,CAC1B,EAeMkO,EAAU1B,GAZS,CAACpH,EAAgBqB,EAAgC0H,IAAiD,CACzHV,EAAoB,IAAI,EACxB,WAAW,IAAM,CACKA,EAAA,CAClB,OAAArI,EACA,QAAAqB,EACA,WAAA0H,CAAA,CACD,GACA,CAAC,CACN,CAG+C,EAEzCC,EAAgBpJ,GAAkB,CACtCuI,EAAavI,CAAK,EAClB2I,MAAuB,CAAE,GAAG9C,EAAM,UAAW,GAAI,CACnD,EAEMwD,EAA0BC,GAAmC,CACjEX,EAAcW,CAAa,CAC7B,EAGMC,EAAuBC,GAA6B,CACxDZ,EAAWY,CAAU,EACrBb,MAAuB,CAAE,GAAG9C,EAAM,UAAW,GAAI,CACnD,EAGM4D,EAAgB,IAAM,CAErBnN,EAAY,kBAAkB,CAAE,SAAU,CAACpB,EAAW,QAAQ,EAAG,EAGtE,WAAW,IAAM,CACTkB,EAAA,CACJ,MAAO,iBACP,YAAa,oCACb,QAAS,SAAA,CACV,GACA,GAAG,CACR,EAEA,OAAI4M,EACF9L,EAAA,IAACwM,GAAA,CACC,SAAUhB,EAAW,SACrB,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CACd,EAEEO,EAAgB/L,MAACyM,GAAM,CAAA,CAAA,EAItBvM,EAAA,KAAAwM,WAAA,CAAA,SAAA,CAAoBpB,GAAAA,EAAiB,aAAe,QACnDtL,EAAA,IAACsE,GAAA,CACC,OAAQgH,EAAiB,OACzB,QAASA,EAAiB,QAC1B,UAAW,IAAM,CACVlM,EAAY,kBAAkB,CAAE,SAAU,CAACpB,EAAW,QAAQ,EAAG,EACtEuN,EAAoB,IAAI,CAAA,CAC1B,CACF,EAEDD,GAAoBA,EAAiB,aAAe,cACnDtL,EAAA,IAAC2G,GAAA,CACC,OAAQ2E,EAAiB,OACzB,QAASA,EAAiB,QAC1B,UAAW,IAAMC,EAAoB,IAAI,CAAA,CAC3C,EAEDD,GAAoBA,EAAiB,aAAe,UACnDtL,EAAA,IAACiD,GAAA,CACC,KAAM,CACJ,SAAUqI,EAAiB,QAAQ,SACnC,OAAQA,EAAiB,MAC3B,EACA,UAAW,IAAM,CACVlM,EAAY,kBAAkB,CAAE,SAAU,CAACpB,EAAW,QAAQ,EAAG,EACtEuN,EAAoB,IAAI,CAAA,CAC1B,CACF,EAGFvL,EAAAA,IAAC,MAAI,CAAA,UAAU,+EACb,SAAAA,EAAA,IAAC2M,GAAA,CACC,MAAM,mBACN,QAAAX,EACA,KAAM/H,GAAM,OAAS,CAAC,EACtB,WAAYA,GAAM,WAClB,UAAA6H,EACA,iBAAkB,GAClB,cAAe,GACf,SAAUN,EAAW,SACrB,mBAAoBW,EACpB,gBAAiBE,EACjB,aAAcvO,EACd,SAAUoO,EACV,YAAad,EACb,gBAAiBP,GACjB,qBAAsB,GACtB,UAAW0B,EACX,mBAAoB,GACpB,aAAc,CAGZ,QAAS,IAAM,CAA8B,EAC7C,QACErM,EAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAF,EAAA,IAAC7B,GAAQ,EAAA,QACRoD,GAAe,CAAA,CAAA,CAAA,CAClB,CAAA,CAAA,CAEJ,CAAA,CAEJ,CAAA,CAAA,EACF,CAEJ,ECjLA,SAAwBqL,IAAiB,CACvC,cACGC,GACC,CAAA,SAAA,CAAC7M,EAAAA,IAAA8M,GAAA,CAAK,MAAM,OAAQ,CAAA,QAEnB3B,GAAS,CAAA,CAAA,CAAA,EACZ,CAEJ"}