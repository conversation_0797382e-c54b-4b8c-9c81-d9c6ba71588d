import * as React from 'react'
import { usePage } from '@inertiajs/react'
import { Link } from '@inertiajs/react'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

export function DynamicBreadcrumb() {
  const { url } = usePage()

  // Skip rendering breadcrumbs on home page
  if (url === '/') {
    return null
  }

  // Create breadcrumb items from URL
  const pathSegments = url.split('/').filter(Boolean)

  // Create formatted display names for breadcrumb items
  const formatPathName = (name: string) => {
    // Handle special paths like admin, etc.
    return name
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href="/">Home</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        {pathSegments.map((segment, index) => {
          const href = `/${pathSegments.slice(0, index + 1).join('/')}`
          const isLastItem = index === pathSegments.length - 1

          return (
            <React.Fragment key={href}>
              <BreadcrumbItem>
                {isLastItem ? (
                  <BreadcrumbPage>{formatPathName(segment)}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link href={href}>{formatPathName(segment)}</Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {!isLastItem && <BreadcrumbSeparator />}
            </React.Fragment>
          )
        })}
      </BreadcrumbList>
    </Breadcrumb>
  )
}