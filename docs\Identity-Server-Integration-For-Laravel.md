# Identity Server Integration Guide for Laravel Applications

This guide provides step-by-step instructions for integrating an ABP Framework Identity Server with a Laravel application. It covers authentication, authorization, and handling of token validation.

## Overview

ABP Framework's Identity Server is built on OpenIddict and provides a robust identity management solution. This guide will help you integrate your Laravel application with an existing ABP Identity Server for:

- User authentication
- Token validation
- Permission-based authorization
- Handling token expiration and authorization errors

## Prerequisites

- An existing ABP Framework application with Identity Server configured
- A Laravel application (version 8.0 or later)
- PHP 7.4 or later
- Composer

## Implementation Steps

### 1. Install Required Packages

First, install the necessary packages for OAuth 2.0 and JWT handling:

```bash
composer require laravel/passport
composer require firebase/php-jwt
composer require guzzlehttp/guzzle
```

### 2. Create a Custom Authentication Guard

Create a new JWT guard for handling authentication with the ABP Identity Server:

```bash
php artisan make:provider JwtAuthServiceProvider
```

Edit the service provider:

```php
<?php
// app/Providers/JwtAuthServiceProvider.php

namespace App\Providers;

use App\Guards\JwtGuard;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class JwtAuthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        Auth::extend('jwt', function ($app, $name, array $config) {
            return new JwtGuard(Auth::createUserProvider($config['provider']), $app['request']);
        });
    }
}
```

Register the service provider in `config/app.php`:

```php
'providers' => [
    // Other service providers...
    App\Providers\JwtAuthServiceProvider::class,
],
```

### 3. Create the JWT Guard

Create a new directory `app/Guards` and add a new file `JwtGuard.php`:

```php
<?php
// app/Guards/JwtGuard.php

namespace App\Guards;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Auth\GuardHelpers;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class JwtGuard implements Guard
{
    use GuardHelpers;

    /**
     * The request instance.
     *
     * @var \Illuminate\Http\Request
     */
    protected $request;

    /**
     * The name of the query string item from the request containing the API token.
     *
     * @var string
     */
    protected $inputKey;

    /**
     * The name of the token "type" claim.
     *
     * @var string
     */
    protected $tokenType;

    /**
     * @var string
     */
    protected $identityServerUrl;

    /**
     * Create a new authentication guard.
     *
     * @param  \Illuminate\Contracts\Auth\UserProvider  $provider
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    public function __construct(UserProvider $provider, Request $request)
    {
        $this->request = $request;
        $this->provider = $provider;
        $this->inputKey = 'api_token';
        $this->tokenType = 'Bearer';
        $this->identityServerUrl = config('auth.identity_server.url');
    }

    /**
     * Get the currently authenticated user.
     *
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function user()
    {
        if (! is_null($this->user)) {
            return $this->user;
        }

        $token = $this->getTokenForRequest();

        if (empty($token)) {
            return null;
        }

        $payload = $this->validateToken($token);
        if (! $payload) {
            return null;
        }

        // Find or create the user based on the JWT claims
        $user = $this->provider->retrieveById($payload->sub);
        
        if (! $user) {
            // If user doesn't exist in your database, you might want to create one
            // based on the claims in the token
            // This is application-specific and depends on your user model
            
            // Example:
            // $user = new \App\Models\User();
            // $user->id = $payload->sub;
            // $user->name = $payload->name ?? 'Unknown';
            // $user->email = $payload->email ?? '';
            // $user->save();
        }

        // Store permissions from the Identity Server
        if ($user) {
            $this->loadUserPermissions($user, $token);
        }

        return $this->user = $user;
    }

    /**
     * Validate a user's credentials.
     *
     * @param  array  $credentials
     * @return bool
     */
    public function validate(array $credentials = [])
    {
        return false;
    }

    /**
     * Get the token for the current request.
     *
     * @return string
     */
    protected function getTokenForRequest()
    {
        $token = $this->request->bearerToken();

        if (empty($token)) {
            $token = $this->request->query($this->inputKey);
        }

        return $token;
    }

    /**
     * Validate the JWT token.
     *
     * @param  string  $token
     * @return object|null The JWT payload if valid, null otherwise
     */
    protected function validateToken($token)
    {
        try {
            // Get the JWKS from the Identity Server
            $jwksUri = $this->identityServerUrl . '/.well-known/openid-configuration/jwks';
            
            // Cache the JWKS to avoid frequent requests
            $jwks = Cache::remember('identity_server_jwks', 3600, function () use ($jwksUri) {
                $response = Http::get($jwksUri);
                return $response->json();
            });

            if (empty($jwks) || !isset($jwks['keys']) || empty($jwks['keys'])) {
                return null;
            }

            // Parse the token header to get the key ID (kid)
            $tokenParts = explode('.', $token);
            if (count($tokenParts) !== 3) {
                return null;
            }

            $header = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $tokenParts[0])), true);
            if (!isset($header['kid'])) {
                return null;
            }

            // Find the matching key in the JWKS
            $key = null;
            foreach ($jwks['keys'] as $jwk) {
                if ($jwk['kid'] === $header['kid']) {
                    $key = $jwk;
                    break;
                }
            }

            if (!$key) {
                return null;
            }

            // Convert the JWK to a key that can be used with the JWT library
            $pem = $this->jwkToPem($key);
            if (!$pem) {
                return null;
            }

            // Decode and validate the token
            $payload = JWT::decode($token, new Key($pem, $header['alg']));

            // Validate the audience and issuer
            if (!isset($payload->aud) || !in_array(config('auth.identity_server.audience'), (array)$payload->aud)) {
                return null;
            }

            if (!isset($payload->iss) || $payload->iss !== $this->identityServerUrl) {
                return null;
            }

            // Check if the token is expired
            if (!isset($payload->exp) || $payload->exp < time()) {
                return null;
            }

            return $payload;
        } catch (\Exception $e) {
            \Log::error('JWT validation error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Convert a JWK to a PEM format key.
     *
     * @param  array  $jwk
     * @return string|null
     */
    protected function jwkToPem($jwk)
    {
        if ($jwk['kty'] !== 'RSA' || !isset($jwk['n']) || !isset($jwk['e'])) {
            return null;
        }

        // Convert the base64url encoded values to binary
        $modulus = JWT::urlsafeB64Decode($jwk['n']);
        $exponent = JWT::urlsafeB64Decode($jwk['e']);

        // Convert the binary values to hexadecimal
        $modulusHex = bin2hex($modulus);
        $exponentHex = bin2hex($exponent);

        // Create the RSA key
        $rsa = [
            'modulus' => $modulusHex,
            'exponent' => $exponentHex
        ];

        // Convert the RSA key to PEM format
        $rsaKey = [
            'modulus' => pack('H*', $rsa['modulus']),
            'exponent' => pack('H*', $rsa['exponent'])
        ];

        $rsaKey = [
            'modulus' => $this->convertBinaryToBigInteger($rsaKey['modulus']),
            'exponent' => $this->convertBinaryToBigInteger($rsaKey['exponent'])
        ];

        $sequence = [
            'modulus' => pack('Ca*a*', 2, $this->encodeLength(strlen($rsaKey['modulus'])), $rsaKey['modulus']),
            'exponent' => pack('Ca*a*', 2, $this->encodeLength(strlen($rsaKey['exponent'])), $rsaKey['exponent'])
        ];

        $derSequence = pack(
            'Ca*a*a*',
            48,
            $this->encodeLength(strlen($sequence['modulus']) + strlen($sequence['exponent'])),
            $sequence['modulus'],
            $sequence['exponent']
        );

        $rsaPublicKey = pack(
            'Ca*Ca*',
            48,
            $this->encodeLength(strlen($derSequence) + 2 + strlen("\x06\x09\x2a\x86\x48\x86\xf7\x0d\x01\x01\x01\x05\x00")),
            48,
            $this->encodeLength(strlen("\x06\x09\x2a\x86\x48\x86\xf7\x0d\x01\x01\x01\x05\x00"))
        ) . "\x06\x09\x2a\x86\x48\x86\xf7\x0d\x01\x01\x01\x05\x00" . pack(
            'Ca*a*',
            3,
            $this->encodeLength(strlen("\x00" . $derSequence)),
            "\x00" . $derSequence
        );

        return "-----BEGIN PUBLIC KEY-----\r\n" .
            chunk_split(base64_encode($rsaPublicKey), 64) .
            "-----END PUBLIC KEY-----";
    }

    /**
     * Convert binary data to a big integer.
     *
     * @param  string  $binary
     * @return string
     */
    protected function convertBinaryToBigInteger($binary)
    {
        // Ensure the number is positive by adding a leading zero byte if the high bit is set
        if (ord($binary[0]) > 127) {
            return "\x00" . $binary;
        }

        return $binary;
    }

    /**
     * Encode the length for DER encoding.
     *
     * @param  int  $length
     * @return string
     */
    protected function encodeLength($length)
    {
        if ($length <= 127) {
            return chr($length);
        }

        $temp = ltrim(pack('N', $length), chr(0));
        return pack('Ca*', 0x80 | strlen($temp), $temp);
    }

    /**
     * Load user permissions from the Identity Server.
     *
     * @param  \Illuminate\Contracts\Auth\Authenticatable  $user
     * @param  string  $token
     * @return void
     */
    protected function loadUserPermissions($user, $token)
    {
        try {
            // Call the ABP application configuration endpoint to get granted policies
            $response = Http::withToken($token)
                ->get($this->identityServerUrl . '/api/abp/application-configuration');

            if ($response->successful()) {
                $appConfig = $response->json();
                
                if (isset($appConfig['auth']['grantedPolicies'])) {
                    // Store the granted policies in the user instance
                    $user->setPermissions($appConfig['auth']['grantedPolicies']);
                }
            }
        } catch (\Exception $e) {
            \Log::error('Error loading user permissions: ' . $e->getMessage());
        }
    }
}
```

### 4. Extend the User Model

Modify your User model to support permissions from the Identity Server:

```php
<?php
// app/Models/User.php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'email', 'password',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * The user's permissions from the Identity Server.
     *
     * @var array
     */
    protected $permissions = [];

    /**
     * Set the user's permissions.
     *
     * @param  array  $permissions
     * @return $this
     */
    public function setPermissions(array $permissions)
    {
        $this->permissions = $permissions;
        return $this;
    }

    /**
     * Get the user's permissions.
     *
     * @return array
     */
    public function getPermissions()
    {
        return $this->permissions;
    }

    /**
     * Check if the user has a specific permission.
     *
     * @param  string  $permission
     * @return bool
     */
    public function hasPermission($permission)
    {
        return isset($this->permissions[$permission]) && $this->permissions[$permission] === true;
    }
}
```

### 5. Update Auth Configuration

Update your `config/auth.php` file to use the JWT guard:

```php
<?php

return [
    'defaults' => [
        'guard' => 'web',
        'passwords' => 'users',
    ],

    'guards' => [
        'web' => [
            'driver' => 'session',
            'provider' => 'users',
        ],

        'api' => [
            'driver' => 'jwt',
            'provider' => 'users',
        ],
    ],

    'providers' => [
        'users' => [
            'driver' => 'eloquent',
            'model' => App\Models\User::class,
        ],
    ],

    'identity_server' => [
        'url' => env('IDENTITY_SERVER_URL', 'https://your-abp-identity-server.com'),
        'audience' => env('IDENTITY_SERVER_AUDIENCE', 'YourApiResourceName'),
    ],
];
```

Add the Identity Server configuration to your `.env` file:

```
IDENTITY_SERVER_URL=https://your-abp-identity-server.com
IDENTITY_SERVER_AUDIENCE=YourApiResourceName
```

### 6. Create a Middleware for Permission Checks

Create a middleware to check permissions:

```bash
php artisan make:middleware CheckPermission
```

Edit the middleware:

```php
<?php
// app/Http/Middleware/CheckPermission.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $permission
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $permission)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'error' => [
                    'code' => 'Unauthorized',
                    'message' => 'You are not authenticated',
                    'details' => 'Please provide a valid authentication token'
                ]
            ], 401);
        }

        if (!$user->hasPermission($permission)) {
            return response()->json([
                'error' => [
                    'code' => 'Forbidden',
                    'message' => 'You do not have permission to access this resource',
                    'details' => "The required permission '{$permission}' is not granted for this operation"
                ]
            ], 403);
        }

        return $next($request);
    }
}
```

Register the middleware in `app/Http/Kernel.php`:

```php
protected $routeMiddleware = [
    // Other middleware...
    'permission' => \App\Http\Middleware\CheckPermission::class,
];
```

### 7. Create a Helper Service for Permission Checks

Create a service to check permissions programmatically:

```php
<?php
// app/Services/PermissionService.php

namespace App\Services;

use Illuminate\Support\Facades\Auth;

class PermissionService
{
    /**
     * Check if the current user has a specific permission.
     *
     * @param  string  $permission
     * @return bool
     */
    public function hasPermission($permission)
    {
        $user = Auth::user();
        
        if (!$user) {
            return false;
        }
        
        return $user->hasPermission($permission);
    }
    
    /**
     * Check if the current user has any of the given permissions.
     *
     * @param  array  $permissions
     * @return bool
     */
    public function hasAnyPermission(array $permissions)
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if the current user has all of the given permissions.
     *
     * @param  array  $permissions
     * @return bool
     */
    public function hasAllPermissions(array $permissions)
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission)) {
                return false;
            }
        }
        
        return true;
    }
}
```

Register the service in `app/Providers/AppServiceProvider.php`:

```php
<?php

namespace App\Providers;

use App\Services\PermissionService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(PermissionService::class, function ($app) {
            return new PermissionService();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
```

### 8. Create a Controller for Token Authentication

Create a controller to handle token authentication:

```bash
php artisan make:controller AuthController
```

Edit the controller:

```php
<?php
// app/Http/Controllers/AuthController.php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class AuthController extends Controller
{
    /**
     * Get a token from the Identity Server.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getToken(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        try {
            $response = Http::asForm()->post(config('auth.identity_server.url') . '/connect/token', [
                'client_id' => config('auth.identity_server.client_id'),
                'client_secret' => config('auth.identity_server.client_secret'),
                'grant_type' => 'password',
                'username' => $request->username,
                'password' => $request->password,
                'scope' => config('auth.identity_server.scope'),
            ]);

            if ($response->successful()) {
                return response()->json($response->json());
            }

            return response()->json([
                'error' => [
                    'code' => 'AuthenticationFailed',
                    'message' => 'Failed to authenticate with the Identity Server',
                    'details' => $response->json()
                ]
            ], $response->status());
        } catch (\Exception $e) {
            return response()->json([
                'error' => [
                    'code' => 'AuthenticationFailed',
                    'message' => 'Failed to authenticate with the Identity Server',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }
}
```

Update your `.env` file with the client credentials:

```
IDENTITY_SERVER_CLIENT_ID=your_client_id
IDENTITY_SERVER_CLIENT_SECRET=your_client_secret
IDENTITY_SERVER_SCOPE="YourApiResourceName"
```

And update your `config/auth.php` file:

```php
'identity_server' => [
    'url' => env('IDENTITY_SERVER_URL', 'https://your-abp-identity-server.com'),
    'audience' => env('IDENTITY_SERVER_AUDIENCE', 'YourApiResourceName'),
    'client_id' => env('IDENTITY_SERVER_CLIENT_ID'),
    'client_secret' => env('IDENTITY_SERVER_CLIENT_SECRET'),
    'scope' => env('IDENTITY_SERVER_SCOPE', 'YourApiResourceName'),
],
```

### 9. Create Routes for Authentication

Add routes in `routes/api.php`:

```php
<?php

use App\Http\Controllers\AuthController;
use Illuminate\Support\Facades\Route;

Route::post('/auth/token', [AuthController::class, 'getToken']);

// Protected routes
Route::middleware('auth:api')->group(function () {
    Route::get('/user', function () {
        return auth()->user();
    });
    
    // Routes that require specific permissions
    Route::middleware('permission:YourApp.Products.Create')->post('/products', 'ProductController@store');
    Route::middleware('permission:YourApp.Products.Update')->put('/products/{id}', 'ProductController@update');
    Route::middleware('permission:YourApp.Products.Delete')->delete('/products/{id}', 'ProductController@destroy');
});
```

### 10. Using the Permission Service in Controllers

```php
<?php
// app/Http/Controllers/ProductController.php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Services\PermissionService;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Check if user has permission to view products
        if (!$this->permissionService->hasPermission('YourApp.Products.View')) {
            return response()->json([
                'error' => [
                    'code' => 'Forbidden',
                    'message' => 'You do not have permission to view products',
                    'details' => "The required permission 'YourApp.Products.View' is not granted for this operation"
                ]
            ], 403);
        }

        $products = Product::all();
        return response()->json($products);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Permission check is handled by middleware

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        $product = Product::create($validated);
        return response()->json($product, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // Check if user has permission to view products
        if (!$this->permissionService->hasPermission('YourApp.Products.View')) {
            return response()->json([
                'error' => [
                    'code' => 'Forbidden',
                    'message' => 'You do not have permission to view products',
                    'details' => "The required permission 'YourApp.Products.View' is not granted for this operation"
                ]
            ], 403);
        }

        $product = Product::findOrFail($id);
        return response()->json($product);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Permission check is handled by middleware

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'price' => 'sometimes|required|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        $product = Product::findOrFail($id);
        $product->update($validated);
        
        return response()->json($product);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Permission check is handled by middleware

        $product = Product::findOrFail($id);
        $product->delete();
        
        return response()->json(null, 204);
    }
}
```

## Testing the Integration

### 1. Obtaining a Token from the ABP Identity Server

You can use your Laravel application's `/api/auth/token` endpoint to obtain a token:

```bash
curl -X POST "http://your-laravel-app.com/api/auth/token" \
  -H "Content-Type: application/json" \
  -d '{"username":"your_username","password":"your_password"}'
```

Or you can directly call the Identity Server:

```bash
curl -X POST "https://your-abp-identity-server.com/connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=your_client_id&client_secret=your_client_secret&grant_type=password&username=your_username&password=your_password&scope=YourApiResourceName"
```

### 2. Making Authenticated Requests

To make an authenticated request to your API:

```bash
curl -X GET "http://your-laravel-app.com/api/user" \
  -H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IkYyNjZCQzA5RkE5..."
```

## Troubleshooting

### Common Issues and Solutions

1. **Token validation fails**
   - Ensure the Identity Server URL is correct
   - Check that the audience matches the API resource name in the ABP Identity Server
   - Verify that the token is not expired

2. **Permission checks fail**
   - Ensure the user has the required permissions in the ABP Identity Server
   - Check that the permission names match exactly between your API and the ABP Identity Server
   - Verify that the permissions are being loaded correctly from the application configuration endpoint

3. **Cannot connect to the ABP Identity Server**
   - Check network connectivity
   - Ensure the ABP Identity Server is running
   - Verify that CORS is configured correctly on the ABP Identity Server

## Conclusion

By following this guide, you've integrated your Laravel application with an ABP Framework Identity Server. Your application now supports:

- User authentication using JWT tokens
- Permission-based authorization using the ABP permission system
- Proper handling of authentication and authorization errors

This integration allows you to leverage the robust identity management features of ABP Framework's Identity Server while maintaining the flexibility of a Laravel application.

## Additional Resources

- [ABP Framework Documentation](https://docs.abp.io/)
- [Laravel Documentation](https://laravel.com/docs)
- [JWT Authentication in Laravel](https://jwt-auth.readthedocs.io/en/develop/)
- [OAuth 2.0 and OpenID Connect](https://oauth.net/2/)
