import { useToast } from "@/lib/useToast"
import { But<PERSON> } from "@/components/ui/button"
import { ToastAction } from "@/components/ui/toast"

export default function Component() {
  const { toast } = useToast()

  return (
    <Button
      variant="outline"
      onClick={() => {
        toast({
          title: "We couldn't complete your request!",
          description: "There was a problem with your request.",
          action: <ToastAction altText="Try again">Try again</ToastAction>,
        })
      }}
    >
      Show toast
    </Button>
  )
}
