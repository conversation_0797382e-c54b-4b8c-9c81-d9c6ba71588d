import {
  type PagedResultDtoOfOpenIddictApplicationDto,
  postApiOpeniddictScopesList,
} from '@/client'
import { extractApiError, generateQueryParameters } from '@/lib/query-utils'
import { toast } from '@/lib/useToast'
import { useQuery } from '@tanstack/react-query'
import { QueryNames } from './QueryConstants'

export const useOpeniddictScopes = (
  pageIndex: number,
  pageSize: number,
  filter?: string  ,
  sorting?: string  
) => {
  return useQuery({
    queryKey: [QueryNames.GetOpeniddictScopes, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      try {
        // Generate query parameters using the utility function
        const body = generateQueryParameters({
          pageIndex,
          pageSize,
          filter,
          sorting,
          filterField: 'clientId', // You can customize the field to filter on
        })

        const response = await postApiOpeniddictScopesList({
          body,
        })

        // Ensure we return a valid data structure even if the API response is unexpected
        return response.data?.data as PagedResultDtoOfOpenIddictApplicationDto
      } catch (error) {
        // Use the error extraction utility
        const { title, description } = extractApiError(error, 'Error loading clients')

        // Show toast notification
        toast({
          title,
          description,
          variant: 'destructive',
        })

        // Return empty data to prevent UI crashes
        return { items: [], totalCount: 0 }
      }
    },
    retry: false, // Don't retry on error
  })
}
