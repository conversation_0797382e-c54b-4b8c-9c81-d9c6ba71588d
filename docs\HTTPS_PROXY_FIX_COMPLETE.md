﻿# HTTPS Proxy Configuration Fix for Development and Production

This document explains how to fix the "This server only accepts HTTPS requests" error when accessing the OpenID Connect endpoints through a proxy in both development and production environments.

## Problem

The application is running behind an Nginx proxy that terminates SSL, but the application isn't properly configured to recognize requests coming through the proxy as HTTPS requests. This causes OpenIddict to reject the requests with the error:

```json
{
  "error": "invalid_request",
  "error_description": "This server only accepts HTTPS requests.",
  "error_uri": "https://documentation.openiddict.com/errors/ID2083"
}
```

## Solution

### 1. Add a Custom ForwardedHeaders Middleware

Create a new file `src/Imip.IdentityServer.Web/ForwardedHeadersMiddleware.cs` with the following content:

```csharp
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace Imip.IdentityServer.Web
{
    /// <summary>
    /// Custom middleware to ensure HTTPS scheme is properly set when behind a proxy
    /// </summary>
    public class ForwardedHeadersMiddleware
    {
        private readonly RequestDelegate _next;

        public ForwardedHeadersMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Log original values for debugging
            System.Console.WriteLine($"Original Request Scheme: {context.Request.Scheme}");
            System.Console.WriteLine($"X-Forwarded-Proto: {context.Request.Headers["X-Forwarded-Proto"]}");
            System.Console.WriteLine($"X-Forwarded-Host: {context.Request.Headers["X-Forwarded-Host"]}");
            
            // Force HTTPS scheme if X-Forwarded-Proto is https
            var forwardedProto = context.Request.Headers["X-Forwarded-Proto"].ToString();
            if (!string.IsNullOrEmpty(forwardedProto) && forwardedProto.Equals("https", System.StringComparison.OrdinalIgnoreCase))
            {
                context.Request.Scheme = "https";
                System.Console.WriteLine("Scheme set to HTTPS based on X-Forwarded-Proto header");
            }
            
            // Log final values
            System.Console.WriteLine($"Final Request Scheme: {context.Request.Scheme}");
            System.Console.WriteLine($"Request Host: {context.Request.Host}");
            
            await _next(context);
        }
    }

    // Extension method used to add the middleware to the HTTP request pipeline
    public static class ForwardedHeadersMiddlewareExtensions
    {
        public static IApplicationBuilder UseCustomForwardedHeaders(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ForwardedHeadersMiddleware>();
        }
    }
}
```

### 2. Update the IdentityServerWebModule.cs File

In the `ConfigureServices` method, add the following code to configure ForwardedHeaders:

```csharp
// Configure forwarded headers to handle proxy scenarios
Configure<ForwardedHeadersOptions>(options =>
{
    options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedHost;
    // Only loopback proxies are allowed by default
    // Clear that restriction because forwarders are enabled by explicit configuration
    options.KnownNetworks.Clear();
    options.KnownProxies.Clear();
});

// Configure OpenIddict to handle proxy scenarios
Configure<OpenIddictServerAspNetCoreOptions>(options =>
{
    // Disable HTTPS requirement when explicitly configured
    if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
    {
        options.DisableTransportSecurityRequirement = true;
        Console.WriteLine("Transport security requirement disabled");
    }
});
```

In the `OnApplicationInitialization` method, add the following code after `app.UseForwardedHeaders()`:

```csharp
// Apply our custom forwarded headers middleware
app.UseCustomForwardedHeaders();
```

### 3. Update the Kubernetes ConfigMap Templates

#### Development Environment

Create or update `k8s/dev/configmap-template.yaml`:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-identity-config
  namespace: imip-identity-dev-new
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  App__SelfUrl: "https://api-identity-dev.imip.co.id"
  App__ClientUrl: "https://identity-dev.imip.co.id"
  App__CorsOrigins: "https://identity-dev.imip.co.id,https://api-identity-dev.imip.co.id"
  AuthServer__Authority: "https://api-identity-dev.imip.co.id"
  AuthServer__RequireHttpsMetadata: "false"
  Seq__ServerUrl: "http://**********:5341"
  ExternalAuth__ApiUrl: "http://***************/api/common/RequestAuthenticationToken"
  ExternalAuth__Enabled: "true"
```

#### Production Environment

Create or update `k8s/prod/configmap-template.yaml`:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-identity-config
  namespace: imip-identity-prod
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  App__SelfUrl: "https://api-identity.imip.co.id"
  App__ClientUrl: "https://identity.imip.co.id"
  App__CorsOrigins: "https://identity.imip.co.id,https://api-identity.imip.co.id"
  AuthServer__Authority: "https://api-identity.imip.co.id"
  AuthServer__RequireHttpsMetadata: "false"
  Seq__ServerUrl: "http://**********:5341"
  ExternalAuth__ApiUrl: "http://***************/api/common/RequestAuthenticationToken"
  ExternalAuth__Enabled: "true"
```

### 4. Update the GitLab CI/CD Pipeline

Update the `prepare_dev_config` and `prepare_prod_config` jobs in your `.gitlab-ci.yml` file to use the ConfigMap templates:

#### Development Environment

```yaml
prepare_dev_config:
  stage: migrate
  image: bitnami/kubectl:latest
  tags:
    - identityserverbackend
  environment:
    name: development
  script:
    # Create namespace if it doesn't exist
    - kubectl create namespace imip-identity-dev-new --dry-run=client -o yaml | kubectl apply -f -

    # Apply the ConfigMap from the template file
    - cat k8s/dev/configmap-template.yaml | envsubst | kubectl apply -f -

    # Create or update Secrets
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: v1
      kind: Secret
      metadata:
        name: imip-identity-secrets
        namespace: imip-identity-dev-new
      type: Opaque
      stringData:
        ConnectionStrings__Default: "${DEV_DB_CONNECTION}"
        Seq__ApiKey: "${SEQ_API_KEY}"
        AuthServer__CertificatePassPhrase: "${CERT_PASSPHRASE}"
        StringEncryption__DefaultPassPhrase: "${ENCRYPTION_PASSPHRASE}"
      EOF

    # Create certificate secret
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: v1
      kind: Secret
      metadata:
        name: imip-identity-certificate
        namespace: imip-identity-dev-new
      type: Opaque
      data:
        identity-server.pfx: "${IDENTITY_SERVER_CERT}"
      EOF

    # Create GitLab registry credentials secret
    - |
      echo "Creating GitLab registry credentials secret..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-dev-new \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -
  only:
    - dev
```

#### Production Environment

```yaml
prepare_prod_config:
  stage: migrate
  image: bitnami/kubectl:latest
  tags:
    - identityserverbackend
  environment:
    name: production
  script:
    # Create namespace if it doesn't exist
    - kubectl create namespace imip-identity-prod --dry-run=client -o yaml | kubectl apply -f -

    # Apply the ConfigMap from the template file
    - cat k8s/prod/configmap-template.yaml | envsubst | kubectl apply -f -

    # Create or update Secrets
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: v1
      kind: Secret
      metadata:
        name: imip-identity-secrets
        namespace: imip-identity-prod
      type: Opaque
      stringData:
        ConnectionStrings__Default: "${PROD_DB_CONNECTION}"
        Seq__ApiKey: "${SEQ_API_KEY}"
        AuthServer__CertificatePassPhrase: "${CERT_PASSPHRASE}"
        StringEncryption__DefaultPassPhrase: "${ENCRYPTION_PASSPHRASE}"
      EOF

    # Create certificate secret
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: v1
      kind: Secret
      metadata:
        name: imip-identity-certificate
        namespace: imip-identity-prod
      type: Opaque
      data:
        identity-server.pfx: "${IDENTITY_SERVER_CERT}"
      EOF

    # Create GitLab registry credentials secret
    - |
      echo "Creating GitLab registry credentials secret..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-prod \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -
  only:
    - main
  when: manual
```

### 5. Apply the Changes

1. Add the ForwardedHeadersMiddleware.cs file to your project
2. Update your IdentityServerWebModule.cs file as described
3. Update your ConfigMap templates in the k8s directory
4. Update your GitLab CI/CD pipeline
5. Commit and push your changes to trigger the pipeline

## Verification

After applying these changes and running the GitLab CI/CD pipeline, you should be able to access both:
- `https://api-identity-dev.imip.co.id/.well-known/openid-configuration` (development)
- `https://api-identity.imip.co.id/.well-known/openid-configuration` (production)

without the HTTPS error.

## Explanation

The issue was that your application was running behind a reverse proxy (Nginx) that terminates SSL, but your application wasn't properly configured to recognize requests coming through the proxy as HTTPS requests. The changes we've made ensure that:

1. Your application properly processes the X-Forwarded-Proto header
2. OpenIddict is configured to not require HTTPS (since it's handled by the proxy)
3. Both development and production environments use the same ASPNETCORE_ENVIRONMENT="Production" setting
4. The ConfigMaps are dynamically generated through GitLab CI/CD

These changes should resolve the "This server only accepts HTTPS requests" error when accessing your OpenID Connect endpoints through the proxy in both environments.
