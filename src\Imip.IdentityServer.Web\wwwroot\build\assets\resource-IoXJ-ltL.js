import{r as f,j as e,u as W,f as I,h as q}from"./vendor-B0b15ZrB.js";import{u as D,s as $,Q as j,v as Y,t as _,a as P,T as k,B as y,w as X,D as Z,c as ee,e as se,f as E,x as te,A as re}from"./app-layout-D_A4XD_6.js";import{$ as ie}from"./App-De6zOdMU.js";import{A as ae,a as ne,b as oe,c as le,d as ce,e as de,f as ue,g as pe,T as me}from"./TableSkeleton-DgDki6RL.js";import{h as he}from"./handleApiError-DnmY5fx8.js";import{p as xe,l as fe,a as ye,_ as ge,D as A,b as je}from"./DataTableColumnHeader-CSMG3Uqi.js";import{N as Ne}from"./NotionFilter-B-J2qhpm.js";import{g as be,e as Se}from"./query-utils-extended-wVOeERM5.js";import{u as z,D as L,a as Ce,b as U,c as V,d as Q,I as C,e as G}from"./index.esm-DqIqfoOW.js";import{F as K,a as N}from"./FormField-POW7SsfI.js";import{T as M}from"./textarea-DPuaXqY_.js";import"./radix-BQPyiA8r.js";import"./card-Iy60I049.js";const De=({user:{userId:n,username:t},onDismiss:i})=>{const{toast:c}=D(),[s,l]=f.useState(!1),r=async()=>{try{await $({path:{id:n}}),c({title:"Success",description:`User "${t}" has been deleted successfully.`}),i()}catch(a){const o=he(a);c({title:o.title,description:o.description,variant:"error"})}};return f.useEffect(()=>{l(!0)},[]),e.jsx(ae,{open:s,children:e.jsxs(ne,{children:[e.jsxs(oe,{children:[e.jsx(le,{children:"Are you absolutely sure?"}),e.jsxs(ce,{children:['This action cannot be undone. This will permanently delete your this user "',t,'"']})]}),e.jsxs(de,{children:[e.jsx(ue,{onClick:i,children:"Cancel"}),e.jsx(pe,{onClick:r,children:"Yes"})]})]})})},ve=(n,t,i=[],c)=>W({queryKey:[j.GetOpeniddictResources,n,t,JSON.stringify(i),c],queryFn:async()=>{try{const s=be({pageIndex:n,pageSize:t,sorting:c,filterConditions:i});return(await Y({body:s})).data?.data}catch(s){const{title:l,description:r}=Se(s,"Error loading resources");return _({title:l,description:r,variant:"destructive"}),{items:[],totalCount:0}}},retry:!1}),we=({children:n})=>{const{can:t}=P(),[i,c]=f.useState(!1),{toast:s}=D(),l=I(),{handleSubmit:r,register:a,reset:o}=z(),p=()=>{o({name:"",displayName:"",description:""})},m=q({mutationFn:async d=>X({body:d}),onSuccess:()=>{s({title:"Success",description:"Resource Created Successfully",variant:"success"}),l.invalidateQueries({queryKey:[j.GetOpeniddictResources]}),p(),c(!1)},onError:d=>{s({title:d?.error?.message,description:d?.error?.details,variant:"destructive"})}}),h=d=>{const u={...d};m.mutate(u)},b=d=>{d&&p(),c(d)};return e.jsxs("section",{children:[e.jsx(k,{}),e.jsxs(L,{open:i,onOpenChange:b,children:[e.jsx(Ce,{asChild:!0,children:n}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:t("AbpIdentity.Users.Create")&&e.jsxs(y,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>b(!0),children:[e.jsx(xe,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Create New Resources"})]})}),e.jsxs(U,{size:"xl",children:[e.jsx(V,{children:e.jsx(Q,{children:"Create a New Resource"})}),e.jsxs("form",{onSubmit:r(h),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(K,{children:[e.jsx(N,{label:"Name",description:"The unique identifier for this resource. Used in requests",children:e.jsx(C,{required:!0,...a("name"),placeholder:"Name"})}),e.jsx(N,{label:"Display Name",description:"The display name for this resource",children:e.jsx(C,{required:!0,...a("displayName"),placeholder:"Display Name"})}),e.jsx(N,{label:"Description",description:"The description for this resource",children:e.jsx(M,{required:!0,...a("description"),placeholder:"Description"})})]})}),e.jsxs(G,{className:"mt-5",children:[e.jsx(y,{variant:"ghost",onClick:d=>{d.preventDefault(),c(!1)},disabled:m.isPending,children:"Cancel"}),e.jsx(y,{type:"submit",disabled:m.isPending,children:m.isPending?"Saving...":"Save"})]})]})]})]})]})},Fe=({dataId:n,dataEdit:t,onAction:i,variant:c="dropdown"})=>{const{can:s}=P();return c==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(Z,{children:[e.jsx(ee,{asChild:!0,children:e.jsxs(y,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(fe,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(se,{align:"end",className:"w-[160px]",children:[s("IdentityServer.OpenIddictResources.Edit")&&e.jsx(E,{className:"cursor-pointer text-sm",onClick:()=>i(n,t,"edit"),children:"Edit"}),s("IdentityServer.OpenIddictResources.Delete")&&e.jsx(E,{className:"cursor-pointer text-sm text-red-500",onClick:()=>i(n,t,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[s("AbpIdentity.Users.ManagePermissions")&&e.jsxs(y,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>i(n,t,"permission"),children:[e.jsx(ye,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),s("AbpIdentity.Users.Update")&&e.jsxs(y,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>i(n,t,"edit"),children:[e.jsx(ge,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})};function O(n,t,i){if(!i||typeof i!="object"||!("operator"in i)||!(p=>typeof p=="object"&&p!==null&&"operator"in p&&"value"in p)(i))return!0;const s=n.getValue(t),{operator:l,value:r}=i;if(s==null)return l==="IsNull"?!0:l==="IsNotNull"?!1:l==="IsEmpty";let a="";if(typeof s=="object"&&s!==null)try{a=JSON.stringify(s).toLowerCase()}catch{a=`[${Object.prototype.toString.call(s)}]`}else typeof s=="number"||typeof s=="boolean"||typeof s=="string"?a=String(s).toLowerCase():a=`[${typeof s}]`;const o=r?String(r).toLowerCase():"";switch(l){case"Equals":return a===o;case"NotEquals":return a!==o;case"Contains":return a.includes(o);case"StartsWith":return a.startsWith(o);case"EndsWith":return a.endsWith(o);case"GreaterThan":return Number(s)>Number(r);case"GreaterThanOrEqual":return Number(s)>=Number(r);case"LessThan":return Number(s)<Number(r);case"LessThanOrEqual":return Number(s)<=Number(r);case"IsEmpty":return a==="";case"IsNotEmpty":return a!=="";case"IsNull":return s==null;case"IsNotNull":return s!=null;default:return!0}}const Te=n=>[{accessorKey:"name",header:({column:t})=>e.jsx(A,{column:t,title:"Resource Name"}),enableSorting:!0,enableHiding:!0,cell:t=>t.getValue(),meta:{className:"text-left",displayName:"Name"},filterFn:O},{accessorKey:"displayName",header:({column:t})=>e.jsx(A,{column:t,title:"Display Name"}),enableSorting:!0,enableHiding:!0,cell:t=>t.getValue(),meta:{className:"text-left",displayName:"Display Name"},filterFn:O},{accessorKey:"description",header:({column:t})=>e.jsx(A,{column:t,title:"Description"}),enableSorting:!0,enableHiding:!0,cell:t=>t.getValue(),meta:{className:"text-left",displayName:"Description"},filterFn:O},{id:"actions",header:"Actions",enableHiding:!0,cell:t=>e.jsx(Fe,{dataId:t.row.original.id,dataEdit:t.row.original,onAction:n,variant:"dropdown"}),meta:{className:"text-right",displayName:"Actions"}}],R=({dataEdit:n,dataId:t,onDismiss:i})=>{const[c,s]=f.useState(!0),{toast:l}=D(),r=I(),{handleSubmit:a,register:o,reset:p}=z(),m=()=>{p({name:"",displayName:"",description:""})},h=q({mutationFn:async u=>te({path:{id:t||""},body:u}),onSuccess:()=>{l({title:"Success",description:"Resource Updated Successfully",variant:"success"}),r.invalidateQueries({queryKey:[j.GetOpeniddictResources]}),m(),s(!1),i()},onError:u=>{l({title:u?.error?.message,description:u?.error?.details,variant:"destructive"})}}),b=u=>{const v={...u};h.mutate(v)},d=u=>{u||i(),s(u)};return e.jsxs("section",{children:[e.jsx(k,{}),e.jsx(L,{open:c,onOpenChange:d,children:e.jsxs(U,{size:"xl",children:[e.jsx(V,{children:e.jsx(Q,{children:"Edit Resource"})}),e.jsxs("form",{onSubmit:a(b),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(K,{children:[e.jsx(N,{label:"Name",description:"The unique identifier for this resource. Used in requests",children:e.jsx(C,{required:!0,...o("name"),defaultValue:n.name??"",placeholder:"Name"})}),e.jsx(N,{label:"Display Name",description:"The display name for this resource",children:e.jsx(C,{required:!0,...o("displayName"),defaultValue:n.displayName??"",placeholder:"Display Name"})}),e.jsx(N,{label:"Description",description:"The description for this resource",children:e.jsx(M,{required:!0,...o("description"),defaultValue:n.description??"",placeholder:"Description"})})]})}),e.jsxs(G,{className:"mt-5",children:[e.jsx(y,{variant:"ghost",onClick:u=>{u.preventDefault(),s(!1),i()},disabled:h.isPending,children:"Cancel"}),e.jsx(y,{type:"submit",disabled:h.isPending,children:h.isPending?"Saving...":"Save"})]})]})]})})]})},Ae=()=>{const{toast:n}=D(),t=I(),[i,c]=f.useState(""),[s,l]=f.useState([]),[r,a]=f.useState(null),[o,p]=f.useState({pageIndex:0,pageSize:10}),{isLoading:m,data:h}=ve(o.pageIndex,o.pageSize,s),d=Te((x,S,g)=>{a({dataId:x,dataEdit:S,dialogType:g})}),u=x=>{c(x);const g=[...s.filter(T=>T.fieldName!=="name")];x&&g.push({fieldName:"name",operator:"Contains",value:x});const w=JSON.stringify(s),F=JSON.stringify(g);w!==F&&(l(g),p(T=>({...T,pageIndex:0})))},v=x=>{p(x)},H=()=>{t.invalidateQueries({queryKey:[j.GetOpeniddictResources]}),setTimeout(()=>{n({title:"Data refreshed",description:"The client list has been refreshed.",variant:"success"})},800)};if(m)return e.jsx(me,{rowCount:o.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const J=h?.items??[],B=h?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4",children:e.jsx(je,{title:"Resources Management",columns:d,data:J,totalCount:B,isLoading:m,manualPagination:!0,pageSize:o.pageSize,onPaginationChange:v,onSearch:u,searchValue:i,customFilterbar:x=>e.jsx(Ne,{...x,activeFilters:s,onServerFilter:S=>{const g=JSON.stringify(s),w=JSON.stringify(S);g!==w&&(l(S),p(F=>({...F,pageIndex:0})))}}),hideDefaultFilterbar:!0,onRefresh:H,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(we,{})}})}),r&&r.dialogType==="edit"&&e.jsx(R,{dataId:r.dataId,dataEdit:r.dataEdit,onDismiss:()=>{t.invalidateQueries({queryKey:[j.GetOpeniddictResources]}),a(null)}}),r&&r.dialogType==="permission"&&e.jsx(R,{dataId:r.dataId,dataEdit:r.dataEdit,onDismiss:()=>a(null)}),r&&r.dialogType==="delete"&&e.jsx(De,{user:{username:r.dataEdit.name,userId:r.dataId},onDismiss:()=>{t.invalidateQueries({queryKey:[j.GetOpeniddictApplications]}),a(null)}})]})};function Ke(){return e.jsxs(re,{children:[e.jsx(ie,{title:"Client Resources"}),e.jsx(Ae,{})]})}export{Ke as default};
//# sourceMappingURL=resource-IoXJ-ltL.js.map
