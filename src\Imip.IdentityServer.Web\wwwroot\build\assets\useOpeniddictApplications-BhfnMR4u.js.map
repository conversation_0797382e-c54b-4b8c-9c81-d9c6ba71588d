{"version": 3, "file": "useOpeniddictApplications-BhfnMR4u.js", "sources": ["../../../../../frontend/src/lib/hooks/useOpeniddictApplications.ts"], "sourcesContent": ["import {\r\n  postApiOpeniddictApplicationsList,\r\n} from '@/client'\r\nimport { extractApiError } from '@/lib/query-utils'\r\nimport { generateExtendedQueryParameters } from '@/lib/query-utils-extended'\r\nimport { toast } from '@/lib/useToast'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\nimport type { FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\n\r\n\r\nexport const useOpeniddictApplications = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filterConditions: FilterCondition[] = [],\r\n  sorting?: string\r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetOpeniddictApplications, pageIndex, pageSize, JSON.stringify(filterConditions), sorting],\r\n    queryFn: async () => {\r\n      try {\r\n        // Generate query parameters using the extended utility function\r\n        const body = generateExtendedQueryParameters({\r\n          pageIndex,\r\n          pageSize,\r\n          sorting,\r\n          filterConditions,\r\n        })\r\n\r\n        const response = await postApiOpeniddictApplicationsList({\r\n          body,\r\n        })\r\n\r\n        // Ensure we return a valid data structure even if the API response is unexpected\r\n        return response.data?.data\r\n      } catch (error) {\r\n        // Use the error extraction utility\r\n        const { title, description } = extractApiError(error, 'Error loading clients')\r\n\r\n        // Show toast notification\r\n        toast({\r\n          title,\r\n          description,\r\n          variant: 'destructive',\r\n        })\r\n\r\n        // Return empty data to prevent UI crashes\r\n        return { items: [], totalCount: 0 }\r\n      }\r\n    },\r\n    retry: false, // Don't retry on error\r\n  })\r\n}\r\n"], "names": ["useOpeniddictApplications", "pageIndex", "pageSize", "filterConditions", "sorting", "useQuery", "QueryNames", "body", "generateExtendedQueryParameters", "postApiOpeniddictApplicationsList", "error", "title", "description", "extractApiError", "toast"], "mappings": "kKAWO,MAAMA,EAA4B,CACvCC,EACAC,EACAC,EAAsC,CAAA,EACtCC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,0BAA2BL,EAAWC,EAAU,KAAK,UAAUC,CAAgB,EAAGC,CAAO,EAC/G,QAAS,SAAY,CACf,GAAA,CAEF,MAAMG,EAAOC,EAAgC,CAC3C,UAAAP,EACA,SAAAC,EACA,QAAAE,EACA,iBAAAD,CAAA,CACD,EAOD,OALiB,MAAMM,EAAkC,CACvD,KAAAF,CAAA,CACD,GAGe,MAAM,WACfG,EAAO,CAEd,KAAM,CAAE,MAAAC,EAAO,YAAAC,CAAA,EAAgBC,EAAgBH,EAAO,uBAAuB,EAGvE,OAAAI,EAAA,CACJ,MAAAH,EACA,YAAAC,EACA,QAAS,aAAA,CACV,EAGM,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CAEtC,EACA,MAAO,EAAA,CACR"}