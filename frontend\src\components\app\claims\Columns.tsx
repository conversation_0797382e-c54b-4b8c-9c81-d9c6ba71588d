'use client'

import { type IdentityClaimTypeDto } from '@/client'
import { type ColumnDef } from '@tanstack/react-table'
import { Actions } from './Actions'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'
import { customFilterFunction } from '@/components/data-table/filterFunctions'

// Type for the callback function to handle claim type actions
type ClaimActionCallback = (dataId: string, dataEdit: IdentityClaimTypeDto, dialogType: 'edit' | 'permission' | 'delete') => void

// Function to create columns with the action callback
export const getColumns = (
  handleUserAction: ClaimActionCallback
): ColumnDef<IdentityClaimTypeDto>[] => {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected()
              ? true
              : table.getIsSomeRowsSelected()
                ? "indeterminate"
                : false
          }
          onCheckedChange={() => table.toggleAllPageRowsSelected()}
          className="translate-y-0.5"
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={() => row.toggleSelected()}
          className="translate-y-0.5"
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: true,
      meta: {
        displayName: "Select",
      },
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      filterFn: customFilterFunction,
      meta: {
        className: "text-left",
        displayName: "Name",
      },
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      enableSorting: true,
      enableHiding: true,
      filterFn: customFilterFunction,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Description",
      },
    },
    {
      accessorKey: "valueType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Value Type" />
      ),
      cell: ({ row }) => {
        const valueType = row.getValue("valueType");
        const valueTypeMap = {
          0: "String",
          1: "Int",
          2: "Boolean",
          3: "DateTime"
        };
        return valueTypeMap[valueType as keyof typeof valueTypeMap] || "Unknown";
      },
      enableSorting: true,
      enableHiding: true,
      meta: {
        className: "text-left",
        displayName: "Value Type",
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: (info) => (
        <Actions
          userId={info.row.original.id!}
          userDto={info.row.original}
          onAction={handleUserAction}
          variant="dropdown"
        />
      ),
      enableSorting: false,
      enableHiding: true,
      meta: {
        className: "text-right",
        displayName: "Action",
      },
    }
  ];
}
