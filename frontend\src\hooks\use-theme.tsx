import * as React from "react"

type Theme = "dark" | "light" | "system"

interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
}

interface UseThemeHook extends ThemeContextType {
  resolvedTheme: "dark" | "light"
}

const ThemeProviderContext = React.createContext<ThemeContextType | undefined>(undefined)

export { ThemeProviderContext }

export const useTheme = (): UseThemeHook => {
  const context = React.useContext(ThemeProviderContext)
  const [systemTheme, setSystemTheme] = React.useState<"dark" | "light">(() => 
    window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"
  )

  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }

  // Listen for system theme changes
  React.useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? "dark" : "light")
    }

    mediaQuery.addEventListener("change", handleChange)
    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [])

  // Calculate resolved theme
  const resolvedTheme: "dark" | "light" = React.useMemo(() => {
    if (context.theme === "system") {
      return systemTheme
    }
    return context.theme as "dark" | "light"
  }, [context.theme, systemTheme])

  return {
    theme: context.theme,
    setTheme: context.setTheme,
    resolvedTheme,
  }
}