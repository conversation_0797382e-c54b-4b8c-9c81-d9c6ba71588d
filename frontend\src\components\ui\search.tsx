import { memo, type SyntheticEvent, useCallback, useEffect, useRef, useState } from 'react'
import { Input } from '@/components/ui/input'
import { useDebounce } from './useDebounce'

type SearchProps = {
  onUpdate: (searchStr: string) => void
  value: string
}
const SearchInput = ({ onUpdate, value }: SearchProps) => {
  // Initialize with the provided value
  const [searchTerm, setSearchTerm] = useState<string>(value)
  const ref = useRef<HTMLInputElement>(null)
  const searchStr = useDebounce<string>(searchTerm)

  // Track if we're handling a user input vs. prop change
  const isUserInput = useRef(false)

  // Handle user typing in the search box
  const onSearchEvent = useCallback((e: SyntheticEvent) => {
    const target = e.target as HTMLInputElement
    const { value } = target
    isUserInput.current = true
    setSearchTerm(value)
  }, [])

  // Only update parent when debounced search term changes (from user input)
  useEffect(() => {
    if (isUserInput.current) {
      onUpdate(searchStr || '')
      isUserInput.current = false
    }
  }, [searchStr, onUpdate])

  // Update internal state when prop value changes (from parent)
  useEffect(() => {
    // Only update if the value has actually changed and is different from current searchTerm
    // And only if it's not from user input
    if (value !== searchTerm && !isUserInput.current) {
      setSearchTerm(value)
    }

    // Focus the input if there's a value
    if (value) {
      ref.current?.focus()
    }
  }, [value, searchTerm])

  return (
    <section className="search">
      <Input
        ref={ref}
        type="text"
        value={searchTerm}
        placeholder="Search..."
        onChange={onSearchEvent}
      />
    </section>
  )
}

export const Search = memo(SearchInput)
