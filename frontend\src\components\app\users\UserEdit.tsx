import { type IdentityRoleDto, type IdentityUserUpdateDto, putApiIdentityUsersById, type RemoteServiceErrorResponse } from '@/client'
import { useToast } from '@/lib/useToast'
import { type MouseEvent, useCallback, useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { v4 } from 'uuid'

import { getApiIdentityUserDetailsById } from '@/client/sdk.gen'
import { client as httpClient } from '@/client/client.gen'
import { FormField, FormSection } from '@/components/ui/FormField'
import Loader from '@/components/ui/Loader'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useAssignableRoles } from '@/lib/hooks/useAssignableRoles'
import { useUserRoles } from '@/lib/hooks/useUserRoles'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import classNames from 'clsx'

const TABS_NAME = {
  USERS_EDIT: 'user_edit',
  USERS_ROLE_ASSIGN: 'user_role_assign',
  USERS_DETAILS: 'user_details',
  USERS_RAW: 'user_raw',
}

type RoleType = {
  name: string
  id: string
}

type UserEditProps = {
  userDto: IdentityUserUpdateDto
  userId: string
  onDismiss: () => void
}
export const UserEdit = ({ userDto, userId, onDismiss }: UserEditProps) => {
  const [needLoginHris, setNeedLoginHris] = useState(false)
  const [roleSearchTerm, setRoleSearchTerm] = useState('')
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register, control } = useForm<IdentityUserUpdateDto>({
    defaultValues: {
      ...userDto
    }
  })
  const [roles, setRoles] = useState<RoleType[]>([])
  // const [isActive, setIsActive] = useState(true)
  // const [lockoutEnabled, setLockoutEnabled] = useState(true)
  const userRole = useUserRoles({ userId })
  const assignableRoles = useAssignableRoles()

  // Fetch user details for Details & Raw tabs
  const { data: userDetails, isLoading: detailsLoading } = useQuery({
    queryKey: ['user-details', userId],
    queryFn: () => getApiIdentityUserDetailsById({
      path: { id: userId }
    })
  })

  const createDataMutation = useMutation({
    mutationFn: async (dataMutation: IdentityUserUpdateDto) =>
      putApiIdentityUsersById({
        path: { id: userId },
        body: { ...userDto, ...dataMutation },
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Claim Type Created Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })
      onDismiss()
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err?.error?.message as string,
        description: err?.error?.details,
        variant: 'destructive',
      })
    }
  })

  const onSubmit = (formData: IdentityUserUpdateDto) => {
    // Merge form data with consent type and permissions
    const userData: IdentityUserUpdateDto = {
      ...formData,
      extraProperties: {
        ...(formData.extraProperties as Record<string, unknown> | undefined),
        NeedLoginHris: needLoginHris,
      }
    }

    // Explicitly mark the promise as handled
    void createDataMutation.mutate(userData)
  }

  const onCloseEvent = () => {
    onDismiss()
  }

  // Filter roles based on search term
  const filteredRoles = assignableRoles?.data?.items?.filter((role) =>
    role.name?.toLowerCase().includes(roleSearchTerm.toLowerCase())
  ) || []



  // Initialize NeedLoginHris from incoming dto extra properties
  useEffect(() => {
    const needLogin = Boolean((userDto.extraProperties as Record<string, unknown> | undefined)?.['NeedLoginHris'])
    setNeedLoginHris(needLogin)
  }, [userDto.extraProperties])

  // Refresh NeedLoginHris from fetched user details when available
  useEffect(() => {
    if (!userDetails?.data) return
    const detailsExtra = (userDetails.data as unknown as Record<string, unknown>)?.['extraProperties'] as Record<string, unknown> | undefined
    if (detailsExtra && 'NeedLoginHris' in detailsExtra) {
      setNeedLoginHris(Boolean(detailsExtra['NeedLoginHris']))
    }
  }, [userDetails?.data])

  useEffect(() => {
    if (userRole.data?.items) {
      const temp: RoleType[] = []
      userRole.data.items.forEach((r) => {
        temp.push({ name: r.name!, id: r.id! })
      })
      setRoles(temp)
    }
  }, [userRole.data?.items])

  const onRoleAssignEvent = useCallback(
    (role: IdentityRoleDto) => {
      const hasAssignedRoleExistAlready = roles.findIndex((r) => role.id === r.id)

      console.log(hasAssignedRoleExistAlready, 'hasAssignedRoleExistAlready')
      if (hasAssignedRoleExistAlready !== -1) {
        roles.splice(hasAssignedRoleExistAlready, 1)
        setRoles([...roles])
      } else {
        roles.push({ name: role.name!, id: role.id! })
        setRoles([...roles])
      }
    },
    [roles]
  )

  const onRoleAssignedSaveEvent = (e: MouseEvent) => {
    e.preventDefault()
    const updateUserDto: IdentityUserUpdateDto = {
      ...userDto,
      roleNames: roles?.map((r) => r.name) ?? [],
      extraProperties: {
        ...(userDto.extraProperties as Record<string, unknown> | undefined),
        NeedLoginHris: needLoginHris,
      }
    }
    onSubmit(updateUserDto)
  }

  const handleUnlockUser = async (e: MouseEvent) => {
    e.preventDefault()
    try {
      await httpClient.post({ url: `/api/identity/users/${userId}/unlock` })
      toast({ title: 'User unlocked', description: `${userDto.userName} has been unlocked.`, variant: 'success' })
      // Refresh details tab
      void queryClient.invalidateQueries({ queryKey: ['user-details', userId] })
    } catch (err: unknown) {
      const message = err && typeof err === 'object' && 'message' in err ? String((err as { message?: string }).message) : 'An error occurred'
      toast({ title: 'Failed to unlock', description: message, variant: 'destructive' })
    }
  }
  const rawData = userDetails?.data as unknown as Record<string, unknown> | undefined
  const extraProps = (rawData?.['extraProperties'] as Record<string, unknown> | undefined)

  return (
    <Dialog open={true} onOpenChange={onCloseEvent}>
      <DialogContent size='2xl' className="">
        <DialogHeader>
          <DialogTitle>Update a User: {userDto.userName}</DialogTitle>
        </DialogHeader>
        <Tabs defaultValue={TABS_NAME.USERS_EDIT}>
          <TabsList className="w-full">
            <TabsTrigger value={TABS_NAME.USERS_EDIT}>
              User Information
            </TabsTrigger>
            <TabsTrigger value={TABS_NAME.USERS_ROLE_ASSIGN}>
              Roles
            </TabsTrigger>
            <TabsTrigger value={TABS_NAME.USERS_DETAILS}>
              Details
            </TabsTrigger>
            <TabsTrigger value={TABS_NAME.USERS_RAW}>
              Raw
            </TabsTrigger>
          </TabsList>
          <TabsContent value={TABS_NAME.USERS_EDIT}>
            <form onSubmit={handleSubmit(onSubmit)}>
              <section className="flex flex-col space-y-2 mt-4">
                <FormSection>
                  <FormField
                    label="Name"
                    description="The name of the user"
                  >
                    <Input required {...register('name')} defaultValue={userDto.name ?? ''} placeholder="Name" />
                  </FormField>

                  <FormField
                    label="Surname"
                    description="The surname of the user"
                  >
                    <Input {...register('surname')} defaultValue={userDto.surname ?? ''} placeholder="Surname" />
                  </FormField>

                  <FormField
                    label="Email"
                    description="The email of the user"
                  >
                    <Input required {...register('email')} defaultValue={userDto.email ?? ''} placeholder="Email" />
                  </FormField>

                  <FormField
                    label="Phone Number"
                    description="The phone number of the user"
                  >
                    <Input {...register('phoneNumber')} defaultValue={userDto.phoneNumber ?? ''} placeholder="Phone Number" />
                  </FormField>

                  <FormField
                    label="Need Login HRIS"
                    description="Require HRIS authentication for this user"
                  >
                    <Checkbox
                      id="needLoginHris"
                      name="needLoginHris"
                      checked={needLoginHris}
                      onCheckedChange={(checked) => setNeedLoginHris(!!checked.valueOf())}
                    />
                  </FormField>

                  <FormField
                    label="Is Active"
                    description="The active status of the user"
                  >
                    <Controller
                      name="isActive"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          id="isActive"
                          checked={!!field.value}
                          onCheckedChange={(checked) => field.onChange(!!checked)}
                        />
                      )}
                    />
                  </FormField>
                  <FormField
                    label="Lockout Enabled"
                    description="The lockout status of the user"
                  >
                    <Controller
                      name="lockoutEnabled"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          id="lockoutEnabled"
                          checked={!!field.value}
                          onCheckedChange={(checked) => field.onChange(!!checked)}
                        />
                      )}
                    />
                  </FormField>
                </FormSection>
              </section>

              <DialogFooter className="mt-5 flex items-center gap-2">
                <Button
                  variant='outline'
                  onClick={(e) => {
                    e.preventDefault()
                    onCloseEvent()
                  }}
                >
                  Cancel
                </Button>
                {userDetails?.data?.lockoutEnd && (
                  <Button type="button" variant="secondary" onClick={handleUnlockUser}>
                    Unlock User
                  </Button>
                )}
                <Button type="submit">Save</Button>
              </DialogFooter>
            </form>
          </TabsContent>
          <TabsContent className='max-h-[70vh] overflow-hidden flex flex-col' value={TABS_NAME.USERS_ROLE_ASSIGN}>
            {assignableRoles?.isLoading && <Loader />}
            {assignableRoles?.isError && (
              <div className="bg-error p-10 text-3xl">
                There was an error while fetching roles information for the {userDto.userName}
              </div>
            )}
            {!assignableRoles.isLoading && !assignableRoles.isError && (
              <div className="flex flex-col h-full">
                <div className="mb-4">
                  <Input
                    type="text"
                    placeholder="Search roles..."
                    value={roleSearchTerm}
                    onChange={(e) => setRoleSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className='flex-1 overflow-y-auto'>
                  {filteredRoles.length === 0 && roleSearchTerm ? (
                    <div className="text-center text-gray-500 py-4">
                      No roles found matching "{roleSearchTerm}"
                    </div>
                  ) : (
                    filteredRoles.map((r) => (
                      <div key={v4()} className={classNames('flex items-center space-x-2 pb-5 mt-3')}>
                        <Checkbox
                          id={r.id}
                          name={r.name!}
                          checked={!!roles?.find((l) => l.id === r.id)}
                          onCheckedChange={() => {
                            onRoleAssignEvent(r)
                          }}
                        />
                        <label htmlFor={r.id} className="text-sm font-medium leading-none">
                          {r.name}
                        </label>
                      </div>
                    )))}
                </div>
              </div>
            )}
            <DialogFooter className="mt-5">
              <Button
                variant='outline'
                onClick={(e) => {
                  e.preventDefault()
                  onCloseEvent()
                }}
              >
                Cancel
              </Button>
              <Button onClick={onRoleAssignedSaveEvent}>Save</Button>
            </DialogFooter>
          </TabsContent>
          <TabsContent value={TABS_NAME.USERS_DETAILS}>
            {detailsLoading ? (
              <Loader />
            ) : userDetails ? (
              <div className="space-y-3 p-4">
                <div>
                  <span className="font-semibold">Created by :</span> {userDetails.data?.creatorUserName ?? '-'}
                </div>
                <div>
                  <span className="font-semibold">Creation time :</span> {userDetails.data?.creationTime ? new Date(userDetails.data?.creationTime).toLocaleString() : '-'}
                </div>
                <div>
                  <span className="font-semibold">Modified by :</span> {userDetails.data?.lastModifierUserName ?? '-'}
                </div>
                <div>
                  <span className="font-semibold">Modification time :</span> {userDetails.data?.lastModificationTime ? new Date(userDetails.data?.lastModificationTime).toLocaleString() : '-'}
                </div>
                <div>
                  <span className="font-semibold">Password update time :</span> {userDetails.data?.passwordChangeTime ? new Date(userDetails.data?.passwordChangeTime).toLocaleString() : '-'}
                </div>
                <div>
                  <span className="font-semibold">Lockout end time :</span> {userDetails.data?.lockoutEnd ? new Date(userDetails.data?.lockoutEnd).toLocaleString() : '-'}
                </div>
                <div>
                  <span className="font-semibold">Failed access count :</span> {userDetails.data?.accessFailedCount ?? 0}
                </div>
              </div>
            ) : (
              <div className="p-4 text-gray-500">No details found.</div>
            )}
          </TabsContent>
          <TabsContent value={TABS_NAME.USERS_RAW}>
            {detailsLoading ? (
              <Loader />
            ) : userDetails ? (
              <div className="space-y-3 p-4">
                <div>
                  <span className="font-semibold">Extra Properties :</span>
                  <div className="mt-2 rounded border p-3 bg-muted text-sm">
                    {!extraProps || Object.keys(extraProps).length === 0 ? (
                      <div className="text-gray-500">No extra properties.</div>
                    ) : (
                      <div className="grid grid-cols-1 gap-2">
                        {Object.entries(extraProps).map(([key, value]) => (
                          <div key={key} className="flex items-start gap-2">
                            <div className="w-64 font-medium truncate">{key}</div>
                            <div className="flex-1 break-all">{String(value)}</div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 text-gray-500">No details found.</div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
