import{j as e,r as h}from"./vendor-B0b15ZrB.js";import{aB as v,G as k,at as te,aC as re,aD as ie,aE as le,aF as ne,aG as ce,aH as oe,aI as de,aJ as xe,aK as me,aL as ue,aM as he,aN as ge,aO as ve}from"./app-layout-D_A4XD_6.js";import{B as p}from"./badge-B7pYtUY6.js";import{C as d,a as x,b as m,c as u,S as i,d as b,e as A}from"./card-Iy60I049.js";import{x as je,P as B}from"./radix-BQPyiA8r.js";/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var R=v("outline","activity","IconActivity",[["path",{d:"M3 12h4l3 8l4 -16l3 8h4",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var N=v("outline","alert-triangle","IconAlertTriangle",[["path",{d:"M12 9v4",key:"svg-0"}],["path",{d:"M10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0z",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var U=v("outline","circle-check","IconCircleCheck",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M9 12l2 2l4 -4",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var pe=v("outline","server","IconServer",[["path",{d:"M3 4m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v2a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-0"}],["path",{d:"M3 12m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v2a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-1"}],["path",{d:"M7 8l0 .01",key:"svg-2"}],["path",{d:"M7 16l0 .01",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var O=v("outline","shield","IconShield",[["path",{d:"M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var F=v("outline","trending-down","IconTrendingDown",[["path",{d:"M3 7l6 6l4 -4l8 8",key:"svg-0"}],["path",{d:"M21 10l0 7l-7 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var E=v("outline","trending-up","IconTrendingUp",[["path",{d:"M3 17l6 -6l4 4l8 -8",key:"svg-0"}],["path",{d:"M14 7l7 0l0 7",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var $=v("outline","users","IconUsers",[["path",{d:"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"svg-2"}],["path",{d:"M21 21v-2a4 4 0 0 0 -3 -3.85",key:"svg-3"}]]);const fe=te("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function H({className:s,variant:r,...t}){return e.jsx("div",{"data-slot":"alert",role:"alert",className:k(fe({variant:r}),s),...t})}function _({className:s,...r}){return e.jsx("div",{"data-slot":"alert-description",className:k("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...r})}var I="Progress",Ne=100,[ye,Re]=je(I),[we,be]=ye(I),X=h.forwardRef((s,r)=>{const{__scopeProgress:t,value:g=null,max:n,getValueLabel:y=Ae,...f}=s;(n||n===0)&&G(n);const c=G(n)?n:Ne;g!==null&&V(g,c);const o=V(g,c)?g:null,L=C(o)?y(o,c):void 0;return e.jsx(we,{scope:t,value:o,max:c,children:e.jsx(B.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":C(o)?o:void 0,"aria-valuetext":L,role:"progressbar","data-state":K(o,c),"data-value":o??void 0,"data-max":c,...f,ref:r})})});X.displayName=I;var Y="ProgressIndicator",J=h.forwardRef((s,r)=>{const{__scopeProgress:t,...g}=s,n=be(Y,t);return e.jsx(B.div,{"data-state":K(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...g,ref:r})});J.displayName=Y;function Ae(s,r){return`${Math.round(s/r*100)}%`}function K(s,r){return s==null?"indeterminate":s===r?"complete":"loading"}function C(s){return typeof s=="number"}function G(s){return C(s)&&!isNaN(s)&&s>0}function V(s,r){return C(s)&&!isNaN(s)&&s<=r&&s>=0}var Ce=X,Le=J;function z({className:s,value:r,...t}){return e.jsx(Ce,{"data-slot":"progress",className:k("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",s),...t,children:e.jsx(Le,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})})}const De=()=>{const{data:s}=re();return s?.extraProperties?.userRoleApplications??[]},Se=()=>{const s=De();return s.length?e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Your Accessible Applications"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:s.map(r=>e.jsxs("a",{href:r.clientUrl||void 0,target:r.clientUrl?"_blank":void 0,rel:"noopener noreferrer",tabIndex:0,"aria-label":r.applicationName,className:"block p-4 rounded-lg border border-gray-200 shadow hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition",children:[e.jsx("div",{className:"font-medium truncate",children:r.applicationName}),r.clientUrl&&e.jsx("div",{className:"text-xs text-blue-600 truncate mt-1",children:r.clientUrl})]},r.applicationId))})]}):null};function Ue(){const[s,r]=h.useState({usersCount:0,clientsCount:0,failedLoginsCount:0,securityOverview:null,userActivity:null,systemHealth:null,securityThreats:null,hourlyActivity:[],rolesDistribution:[],topClients:[]}),[t,g]=h.useState(!0),[n,y]=h.useState(null),f=ie(),c=f?.roles?.includes("admin"),[o,L]=h.useState([]),[w,P]=h.useState(!1),[M,q]=h.useState(null),Q=async()=>{try{g(!0),y(null);const[a,l,D,S,T,j,Z,ee,se,ae]=await Promise.all([ne(),ce(),oe(),de(),xe(),me(),ue(),he(),ge(),ve()]);r({usersCount:a.data??0,clientsCount:l.data??0,failedLoginsCount:D.data??0,securityOverview:S.data??null,userActivity:T.data??null,systemHealth:j.data??null,securityThreats:Z.data??null,hourlyActivity:ee.data??[],rolesDistribution:se.data??[],topClients:ae.data??[]})}catch{y("Failed to load dashboard data")}finally{g(!1)}};h.useEffect(()=>{Q()},[]),h.useEffect(()=>{!c&&f?.id&&(P(!0),le({body:{action:"Login",startDate:new Date(Date.now()-7*24*60*60*1e3).toISOString(),endDate:new Date().toISOString(),maxResultCount:20,sort:[{field:"CreationTime",desc:!0}]}}).then(a=>{L(a.data?.data?.items??[])}).catch(()=>q("Failed to load your activity")).finally(()=>P(!1)))},[c,f?.id]);const W=a=>{switch(a?.toLowerCase()){case"low":return"bg-green-500";case"medium":return"bg-yellow-500";case"high":return"bg-orange-500";case"critical":return"bg-red-500";default:return"bg-gray-500"}};if(!c){const a=o.find(j=>!j.identity),l=o.filter(j=>!j.identity).length,D=o.filter(j=>!!j.identity).length,S=a?.creationTime?new Date(a.creationTime).toLocaleString():"N/A",T=a?.browserInfo||"N/A";return e.jsxs("div",{className:"flex flex-1 flex-col space-y-4",children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Your Recent Activity"}),M&&e.jsxs(H,{variant:"destructive",children:[e.jsx(N,{className:"h-4 w-4"}),e.jsx(_,{children:M})]}),e.jsxs(d,{children:[e.jsxs(x,{children:[e.jsx(m,{children:"Last Login"}),e.jsx(u,{children:"Most recent successful login"})]}),e.jsxs("div",{className:"p-6 space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:"Time:"}),e.jsx("span",{children:w?e.jsx(i,{className:"h-4 w-24"}):S})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:"Device/Browser:"}),e.jsx("span",{children:w?e.jsx(i,{className:"h-4 w-24"}):T})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:"Successful logins (7d):"}),e.jsx("span",{children:w?e.jsx(i,{className:"h-4 w-12"}):l})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:"Failed logins (7d):"}),e.jsx("span",{children:w?e.jsx(i,{className:"h-4 w-12"}):D})]})]})]}),e.jsx(Se,{})]})}return n?e.jsx("div",{className:"flex flex-1 flex-col space-y-4",children:e.jsxs(H,{variant:"destructive",children:[e.jsx(N,{className:"h-4 w-4"}),e.jsx(_,{children:n})]})}):e.jsxs("div",{className:"flex flex-1 flex-col space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between space-y-2",children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Identity Server Dashboard 👋"}),e.jsx(p,{variant:"outline",className:"text-sm",children:t?"Loading...":"Live Data"})]}),e.jsxs("div",{className:"*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(d,{className:"@container/card",children:[e.jsxs(x,{children:[e.jsxs(u,{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-4 w-4"}),"Total Users"]}),e.jsx(m,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:t?e.jsx(i,{className:"h-8 w-16"}):s.usersCount.toLocaleString()}),e.jsx(b,{children:!t&&s.userActivity&&e.jsx(p,{variant:"outline",children:(s.userActivity.userRetentionRate??0)>0?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-3 w-3"}),"+",s.userActivity.userRetentionRate??0,"%"]}):e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"h-3 w-3"}),s.userActivity.userRetentionRate??0,"%"]})})})]}),e.jsxs(A,{className:"flex-col items-start gap-1.5 text-sm",children:[e.jsx("div",{className:"line-clamp-1 flex gap-2 font-medium",children:t?e.jsx(i,{className:"h-4 w-32"}):e.jsxs(e.Fragment,{children:[s.userActivity?.newUsersToday||0," new today",e.jsx($,{className:"size-4"})]})}),e.jsx("div",{className:"text-muted-foreground",children:t?e.jsx(i,{className:"h-4 w-24"}):`${s.userActivity?.activeUsersToday||0} active today`})]})]}),e.jsxs(d,{className:"@container/card",children:[e.jsxs(x,{children:[e.jsxs(u,{className:"flex items-center gap-2",children:[e.jsx(O,{className:"h-4 w-4"}),"Total Clients"]}),e.jsx(m,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:t?e.jsx(i,{className:"h-8 w-16"}):s.clientsCount.toLocaleString()}),e.jsx(b,{children:!t&&s.securityOverview&&e.jsx(p,{variant:"outline",children:(s.securityOverview.loginGrowthPercentage??0)>0?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-3 w-3"}),"+",s.securityOverview.loginGrowthPercentage??0,"%"]}):e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"h-3 w-3"}),s.securityOverview.loginGrowthPercentage??0,"%"]})})})]}),e.jsxs(A,{className:"flex-col items-start gap-1.5 text-sm",children:[e.jsx("div",{className:"line-clamp-1 flex gap-2 font-medium",children:t?e.jsx(i,{className:"h-4 w-32"}):e.jsxs(e.Fragment,{children:[s.securityOverview?.totalLoginsToday||0," logins today",e.jsx(R,{className:"size-4"})]})}),e.jsx("div",{className:"text-muted-foreground",children:t?e.jsx(i,{className:"h-4 w-24"}):`${s.securityOverview?.successRate||0}% success rate`})]})]}),e.jsxs(d,{className:"@container/card",children:[e.jsxs(x,{children:[e.jsxs(u,{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-4 w-4"}),"Failed Logins"]}),e.jsx(m,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:t?e.jsx(i,{className:"h-8 w-16"}):s.failedLoginsCount.toLocaleString()}),e.jsx(b,{children:!t&&s.securityThreats&&e.jsx(p,{variant:"outline",className:`${W(s.securityThreats.threatLevel||"")} text-white`,children:s.securityThreats.threatLevel||"Unknown"})})]}),e.jsxs(A,{className:"flex-col items-start gap-1.5 text-sm",children:[e.jsx("div",{className:"line-clamp-1 flex gap-2 font-medium",children:t?e.jsx(i,{className:"h-4 w-32"}):e.jsxs(e.Fragment,{children:[s.securityThreats?.suspiciousActivities||0," suspicious activities",e.jsx(N,{className:"size-4"})]})}),e.jsx("div",{className:"text-muted-foreground",children:t?e.jsx(i,{className:"h-4 w-24"}):`${s.securityThreats?.accountLockouts||0} account lockouts`})]})]}),e.jsxs(d,{className:"@container/card",children:[e.jsxs(x,{children:[e.jsxs(u,{className:"flex items-center gap-2",children:[e.jsx(pe,{className:"h-4 w-4"}),"System Health"]}),e.jsx(m,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:t?e.jsx(i,{className:"h-8 w-16"}):s.systemHealth?.systemStatus||"Unknown"}),e.jsx(b,{children:!t&&s.systemHealth&&e.jsxs(p,{variant:"outline",children:[e.jsx(U,{className:"h-3 w-3"}),s.systemHealth.uptime||"N/A"]})})]}),e.jsxs(A,{className:"flex-col items-start gap-1.5 text-sm",children:[e.jsx("div",{className:"line-clamp-1 flex gap-2 font-medium",children:t?e.jsx(i,{className:"h-4 w-32"}):e.jsxs(e.Fragment,{children:["Load: ",s.systemHealth?.systemLoad||"Unknown",e.jsx(R,{className:"size-4"})]})}),e.jsx("div",{className:"text-muted-foreground",children:t?e.jsx(i,{className:"h-4 w-24"}):`Last backup: ${s.systemHealth?.lastBackup?new Date(s.systemHealth.lastBackup).toLocaleDateString():"N/A"}`})]})]})]}),!t&&s.securityOverview&&e.jsxs(d,{children:[e.jsxs(x,{children:[e.jsxs(m,{className:"flex items-center gap-2",children:[e.jsx(O,{className:"h-5 w-5"}),"Security Overview"]}),e.jsx(u,{children:"Today's security metrics and trends"})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:s.securityOverview.totalLoginsToday}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Logins"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:s.securityOverview.failedLoginsToday}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Failed Logins"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-600",children:[s.securityOverview.successRate,"%"]}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Success Rate"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-orange-600",children:[s.securityOverview.loginGrowthPercentage,"%"]}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Growth"})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7",children:[e.jsx("div",{className:"col-span-4",children:e.jsxs(d,{children:[e.jsxs(x,{children:[e.jsx(m,{children:"Hourly Activity"}),e.jsx(u,{children:"Login activity over the last 24 hours"})]}),e.jsx("div",{className:"p-6",children:t?e.jsx("div",{className:"space-y-2",children:Array.from({length:24}).map((a,l)=>e.jsx(i,{className:"h-4 w-full"},l))}):e.jsx("div",{className:"space-y-2",children:s.hourlyActivity.map(a=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm",children:[a.hour,":00"]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("span",{className:"text-sm text-green-600",children:[a.loginCount," logins"]}),e.jsxs("span",{className:"text-sm text-red-600",children:[a.failedLoginCount," failed"]}),e.jsx(z,{value:a.successRate,className:"w-20"})]})]},a.hour))})})]})}),e.jsx("div",{className:"col-span-4 md:col-span-3",children:e.jsxs(d,{children:[e.jsxs(x,{children:[e.jsx(m,{children:"Top Clients"}),e.jsx(u,{children:"Most active applications"})]}),e.jsx("div",{className:"p-6",children:t?e.jsx("div",{className:"space-y-2",children:Array.from({length:5}).map((a,l)=>e.jsx(i,{className:"h-8 w-full"},l))}):e.jsx("div",{className:"space-y-2",children:s.topClients.map((a,l)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm font-medium",children:["#",l+1]}),e.jsx("span",{className:"text-sm truncate",children:a.clientId})]}),e.jsxs(p,{variant:"secondary",children:[a.count," logins"]})]},a.clientId))})})]})}),e.jsx("div",{className:"col-span-4",children:e.jsxs(d,{children:[e.jsxs(x,{children:[e.jsx(m,{children:"Role Distribution"}),e.jsx(u,{children:"Users per role"})]}),e.jsx("div",{className:"p-6",children:t?e.jsx("div",{className:"space-y-2",children:Array.from({length:5}).map((a,l)=>e.jsx(i,{className:"h-8 w-full"},l))}):e.jsx("div",{className:"space-y-2",children:s.rolesDistribution.map(a=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:a.roleName}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(z,{value:(a.userCount??0)/s.usersCount*100,className:"w-20"}),e.jsxs(p,{variant:"outline",children:[a.userCount??0," users"]})]})]},a.roleName))})})]})}),e.jsx("div",{className:"col-span-4 md:col-span-3",children:e.jsxs(d,{children:[e.jsxs(x,{children:[e.jsx(m,{children:"Security Recommendations"}),e.jsx(u,{children:"Based on current threats"})]}),e.jsx("div",{className:"p-6",children:t?e.jsx("div",{className:"space-y-2",children:Array.from({length:3}).map((a,l)=>e.jsx(i,{className:"h-4 w-full"},l))}):e.jsx("div",{className:"space-y-2",children:s.securityThreats?.recommendations?.map((a,l)=>e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(N,{className:"h-4 w-4 text-orange-500 mt-0.5"}),e.jsx("span",{className:"text-sm",children:a})]},l))||e.jsxs("div",{className:"flex items-center gap-2 text-green-600",children:[e.jsx(U,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:"Security posture is good. Continue monitoring."})]})})})]})})]})]})}export{Ue as O};
//# sourceMappingURL=overview-SzT15zKv.js.map
