"use client"

import { But<PERSON> } from "@/components/ui/button"
import { type Table } from "@tanstack/react-table"
import { ViewOptions } from "@/components/data-table/DataTableViewOptions"
import { Search } from "@/components/ui/search"

interface ClientFilterbarProps<TData> {
  table: Table<TData>
  onSearch?: (value: string) => void
  searchValue?: string
}

export function ClientFilterbar<TData>({
  table,
  onSearch,
  searchValue = "",
}: ClientFilterbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className="flex flex-wrap items-center justify-between gap-2">
      {onSearch && (
        <div className="w-full sm:w-auto sm:max-w-[250px]">
          <Search onUpdate={onSearch} value={searchValue} />
        </div>
      )}

      <div className="flex items-center gap-2 ml-auto">
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500"
          >
            Clear filters
          </Button>
        )}
        {/* <Button
          variant="secondary"
          className="gap-x-2 px-2 py-1.5 text-sm sm:text-xs"
        >
          <RiDownloadLine className="size-4 shrink-0" aria-hidden="true" />
          <span className="hidden sm:inline">Export</span>
        </Button> */}
        <ViewOptions table={table} />
      </div>
    </div>
  )
}
