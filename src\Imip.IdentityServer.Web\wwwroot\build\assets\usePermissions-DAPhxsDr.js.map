{"version": 3, "file": "usePermissions-DAPhxsDr.js", "sources": ["../../../../../frontend/src/components/app/permission/PermissionToggle.tsx", "../../../../../frontend/src/components/ui/tabs.tsx", "../../../../../frontend/src/lib/hooks/usePermissions.ts"], "sourcesContent": ["import clsx from 'clsx'\r\nimport { memo, useCallback } from 'react'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\n\r\nexport type Management = 'identity' | 'tenant' | 'setting' | 'feature'\r\nexport type PermissionTracker = {\r\n  name: string\r\n  isGranted: boolean\r\n}\r\n\r\ntype PermissionProps = {\r\n  name: string\r\n  id: string\r\n  isGranted: boolean\r\n  onUpdate?: () => void\r\n  className?: string\r\n  disabled: boolean\r\n}\r\n\r\nfunction PermissionToggle({ name, id, onUpdate, className, isGranted, disabled = false }: PermissionProps) {\r\n  const onChangeEvent = useCallback(() => {\r\n    onUpdate?.()\r\n  }, [onUpdate])\r\n\r\n  return (\r\n    <div className={clsx('flex items-center space-x-2 pb-2', className)}>\r\n      <Checkbox id={id} onCheckedChange={onChangeEvent} checked={isGranted} disabled={disabled} />\r\n      <label htmlFor={id} className=\"text-sm font-medium leading-none\">\r\n        {name}\r\n      </label>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport const Permission = memo(PermissionToggle)\r\n", "'use client';\n\nimport * as React from 'react';\nimport { cn } from '@/lib/utils';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { Tabs as TabsPrimitive } from 'radix-ui';\n\n// Variants for TabsList\nconst tabsListVariants = cva('flex items-center shrink-0', {\n  variants: {\n    variant: {\n      default: 'bg-accent p-1',\n      button: '',\n      line: 'border-b border-border',\n    },\n    shape: {\n      default: '',\n      pill: '',\n    },\n    size: {\n      lg: 'gap-2.5',\n      md: 'gap-2',\n      sm: 'gap-1.5',\n      xs: 'gap-1',\n    },\n  },\n  compoundVariants: [\n    { variant: 'default', size: 'lg', className: 'p-1.5 gap-2.5' },\n    { variant: 'default', size: 'md', className: 'p-1 gap-2' },\n    { variant: 'default', size: 'sm', className: 'p-1 gap-1.5' },\n    { variant: 'default', size: 'xs', className: 'p-1 gap-1' },\n\n    {\n      variant: 'default',\n      shape: 'default',\n      size: 'lg',\n      className: 'rounded-lg',\n    },\n    {\n      variant: 'default',\n      shape: 'default',\n      size: 'md',\n      className: 'rounded-lg',\n    },\n    {\n      variant: 'default',\n      shape: 'default',\n      size: 'sm',\n      className: 'rounded-md',\n    },\n    {\n      variant: 'default',\n      shape: 'default',\n      size: 'xs',\n      className: 'rounded-md',\n    },\n\n    { variant: 'line', size: 'lg', className: 'gap-9' },\n    { variant: 'line', size: 'md', className: 'gap-8' },\n    { variant: 'line', size: 'sm', className: 'gap-4' },\n    { variant: 'line', size: 'xs', className: 'gap-4' },\n\n    {\n      variant: 'default',\n      shape: 'pill',\n      className: 'rounded-full [&_[role=tab]]:rounded-full',\n    },\n    {\n      variant: 'button',\n      shape: 'pill',\n      className: 'rounded-full [&_[role=tab]]:rounded-full',\n    },\n  ],\n  defaultVariants: {\n    variant: 'default',\n    size: 'md',\n  },\n});\n\n// Variants for TabsTrigger\nconst tabsTriggerVariants = cva(\n  'shrink-0 cursor-pointer whitespace-nowrap inline-flex justify-center items-center font-medium ring-offset-background transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-disabled:pointer-events-none data-disabled:opacity-50 [&_svg]:shrink-0 [&_svg]:text-muted-foreground [&:hover_svg]:text-primary [&[data-state=active]_svg]:text-primary',\n  {\n    variants: {\n      variant: {\n        default:\n          'text-muted-foreground data-[state=active]:bg-background hover:text-foreground data-[state=active]:text-foreground data-[state=active]:shadow-xs data-[state=active]:shadow-black/5',\n        button:\n          'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-lg text-accent-foreground hover:text-foreground data-[state=active]:bg-accent data-[state=active]:text-foreground',\n        line: 'border-b-2 text-muted-foreground border-transparent data-[state=active]:border-primary hover:text-primary data-[state=active]:text-primary data-[state=active]:border-primary data-[state=active]:text-primary',\n      },\n      size: {\n        lg: 'gap-2.5 [&_svg]:size-5 text-sm',\n        md: 'gap-2 [&_svg]:size-4 text-sm',\n        sm: 'gap-1.5 [&_svg]:size-3.5 text-xs',\n        xs: 'gap-1 [&_svg]:size-3.5 text-xs',\n      },\n    },\n    compoundVariants: [\n      { variant: 'default', size: 'lg', className: 'py-2.5 px-4 rounded-md' },\n      { variant: 'default', size: 'md', className: 'py-1.5 px-3 rounded-md' },\n      { variant: 'default', size: 'sm', className: 'py-1.5 px-2.5 rounded-sm' },\n      { variant: 'default', size: 'xs', className: 'py-1 px-2 rounded-sm' },\n\n      { variant: 'button', size: 'lg', className: 'py-3 px-4 rounded-lg' },\n      { variant: 'button', size: 'md', className: 'py-2.5 px-3 rounded-lg' },\n      { variant: 'button', size: 'sm', className: 'py-2 px-2.5 rounded-md' },\n      { variant: 'button', size: 'xs', className: 'py-1.5 px-2 rounded-md' },\n\n      { variant: 'line', size: 'lg', className: 'py-3' },\n      { variant: 'line', size: 'md', className: 'py-2.5' },\n      { variant: 'line', size: 'sm', className: 'py-2' },\n      { variant: 'line', size: 'xs', className: 'py-1.5' },\n    ],\n    defaultVariants: {\n      variant: 'default',\n      size: 'md',\n    },\n  },\n);\n\n// Variants for TabsContent\nconst tabsContentVariants = cva(\n  'mt-2.5 focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default: '',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  },\n);\n\n// Context\ntype TabsContextType = {\n  variant?: 'default' | 'button' | 'line';\n  size?: 'lg' | 'sm' | 'xs' | 'md';\n};\nconst TabsContext = React.createContext<TabsContextType>({\n  variant: 'default',\n  size: 'md',\n});\n\n// Components\nfunction Tabs({ className, ...props }: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return <TabsPrimitive.Root data-slot=\"tabs\" className={cn('', className)} {...props} />;\n}\n\nfunction TabsList({\n  className,\n  variant = 'default',\n  shape = 'default',\n  size = 'md',\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List> & VariantProps<typeof tabsListVariants>) {\n  return (\n    <TabsContext.Provider value={{ variant: variant || 'default', size: size || 'md' }}>\n      <TabsPrimitive.List\n        data-slot=\"tabs-list\"\n        className={cn(tabsListVariants({ variant, shape, size }), className)}\n        {...props}\n      />\n    </TabsContext.Provider>\n  );\n}\n\nfunction TabsTrigger({ className, ...props }: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  const { variant, size } = React.useContext(TabsContext);\n\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(tabsTriggerVariants({ variant, size }), className)}\n      {...props}\n    />\n  );\n}\n\nfunction TabsContent({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content> & VariantProps<typeof tabsContentVariants>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(tabsContentVariants({ variant }), className)}\n      {...props}\n    />\n  );\n}\n\nexport { Tabs, TabsContent, TabsList, TabsTrigger };\n", "import { type GetPermissionListResultDto, getApiPermissionManagementPermissions } from '@/client'\r\nimport { type UseQueryResult, useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch permissions based on provider name and provider key.\r\n *\r\n * @param providerName - The name of the provider.\r\n * @param providerKey - The key of the provider.\r\n * @returns A `UseQueryResult` containing the permission list result.\r\n */\r\nexport const usePermissions = (\r\n  providerName: string | undefined,\r\n  providerKey: string | undefined\r\n): UseQueryResult<GetPermissionListResultDto, unknown> => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetPermissions, providerName, providerKey],\r\n    queryFn: async () => {\r\n      const { data } = await getApiPermissionManagementPermissions({\r\n        query: { providerName, providerKey },\r\n      })\r\n      return data!\r\n    },\r\n  })\r\n}\r\n"], "names": ["PermissionToggle", "name", "id", "onUpdate", "className", "isGranted", "disabled", "onChangeEvent", "useCallback", "clsx", "jsx", "Checkbox", "Permission", "memo", "tabsListVariants", "cva", "tabsTriggerVariants", "tabsContentVariants", "TabsContext", "React.createContext", "Tabs", "props", "TabsPrimitive.Root", "cn", "TabsList", "variant", "shape", "size", "TabsPrimitive.List", "TabsTrigger", "React.useContext", "TabsPrimitive.Trigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabsPrimitive.Content", "usePermissions", "providerName", "providerKey", "useQuery", "QueryNames", "data", "getApiPermissionManagementPermissions"], "mappings": "2OAmBA,SAASA,EAAiB,CAAE,KAAAC,EAAM,GAAAC,EAAI,SAAAC,EAAU,UAAAC,EAAW,UAAAC,EAAW,SAAAC,EAAW,IAA0B,CACnG,MAAAC,EAAgBC,EAAAA,YAAY,IAAM,CAC3BL,IAAA,CAAA,EACV,CAACA,CAAQ,CAAC,EAEb,cACG,MAAI,CAAA,UAAWM,EAAK,mCAAoCL,CAAS,EAChE,SAAA,CAAAM,MAACC,GAAS,GAAAT,EAAQ,gBAAiBK,EAAe,QAASF,EAAW,SAAAC,EAAoB,QACzF,QAAM,CAAA,QAASJ,EAAI,UAAU,mCAC3B,SACHD,CAAA,CAAA,CAAA,EACF,CAEJ,CAEa,MAAAW,EAAaC,OAAKb,CAAgB,EC1BzCc,EAAmBC,EAAI,6BAA8B,CACzD,SAAU,CACR,QAAS,CACP,QAAS,gBACT,OAAQ,GACR,KAAM,wBACR,EACA,MAAO,CACL,QAAS,GACT,KAAM,EACR,EACA,KAAM,CACJ,GAAI,UACJ,GAAI,QACJ,GAAI,UACJ,GAAI,OAAA,CAER,EACA,iBAAkB,CAChB,CAAE,QAAS,UAAW,KAAM,KAAM,UAAW,eAAgB,EAC7D,CAAE,QAAS,UAAW,KAAM,KAAM,UAAW,WAAY,EACzD,CAAE,QAAS,UAAW,KAAM,KAAM,UAAW,aAAc,EAC3D,CAAE,QAAS,UAAW,KAAM,KAAM,UAAW,WAAY,EAEzD,CACE,QAAS,UACT,MAAO,UACP,KAAM,KACN,UAAW,YACb,EACA,CACE,QAAS,UACT,MAAO,UACP,KAAM,KACN,UAAW,YACb,EACA,CACE,QAAS,UACT,MAAO,UACP,KAAM,KACN,UAAW,YACb,EACA,CACE,QAAS,UACT,MAAO,UACP,KAAM,KACN,UAAW,YACb,EAEA,CAAE,QAAS,OAAQ,KAAM,KAAM,UAAW,OAAQ,EAClD,CAAE,QAAS,OAAQ,KAAM,KAAM,UAAW,OAAQ,EAClD,CAAE,QAAS,OAAQ,KAAM,KAAM,UAAW,OAAQ,EAClD,CAAE,QAAS,OAAQ,KAAM,KAAM,UAAW,OAAQ,EAElD,CACE,QAAS,UACT,MAAO,OACP,UAAW,0CACb,EACA,CACE,QAAS,SACT,MAAO,OACP,UAAW,0CAAA,CAEf,EACA,gBAAiB,CACf,QAAS,UACT,KAAM,IAAA,CAEV,CAAC,EAGKC,EAAsBD,EAC1B,6cACA,CACE,SAAU,CACR,QAAS,CACP,QACE,qLACF,OACE,qMACF,KAAM,gNACR,EACA,KAAM,CACJ,GAAI,iCACJ,GAAI,+BACJ,GAAI,mCACJ,GAAI,gCAAA,CAER,EACA,iBAAkB,CAChB,CAAE,QAAS,UAAW,KAAM,KAAM,UAAW,wBAAyB,EACtE,CAAE,QAAS,UAAW,KAAM,KAAM,UAAW,wBAAyB,EACtE,CAAE,QAAS,UAAW,KAAM,KAAM,UAAW,0BAA2B,EACxE,CAAE,QAAS,UAAW,KAAM,KAAM,UAAW,sBAAuB,EAEpE,CAAE,QAAS,SAAU,KAAM,KAAM,UAAW,sBAAuB,EACnE,CAAE,QAAS,SAAU,KAAM,KAAM,UAAW,wBAAyB,EACrE,CAAE,QAAS,SAAU,KAAM,KAAM,UAAW,wBAAyB,EACrE,CAAE,QAAS,SAAU,KAAM,KAAM,UAAW,wBAAyB,EAErE,CAAE,QAAS,OAAQ,KAAM,KAAM,UAAW,MAAO,EACjD,CAAE,QAAS,OAAQ,KAAM,KAAM,UAAW,QAAS,EACnD,CAAE,QAAS,OAAQ,KAAM,KAAM,UAAW,MAAO,EACjD,CAAE,QAAS,OAAQ,KAAM,KAAM,UAAW,QAAS,CACrD,EACA,gBAAiB,CACf,QAAS,UACT,KAAM,IAAA,CACR,CAEJ,EAGME,EAAsBF,EAC1B,+GACA,CACE,SAAU,CACR,QAAS,CACP,QAAS,EAAA,CAEb,EACA,gBAAiB,CACf,QAAS,SAAA,CACX,CAEJ,EAOMG,EAAcC,EAAAA,cAAqC,CACvD,QAAS,UACT,KAAM,IACR,CAAC,EAGD,SAASC,EAAK,CAAE,UAAAhB,EAAW,GAAGiB,GAA0D,CACtF,OAAQX,MAAAY,EAAA,CAAmB,YAAU,OAAO,UAAWC,EAAG,GAAInB,CAAS,EAAI,GAAGiB,CAAO,CAAA,CACvF,CAEA,SAASG,EAAS,CAChB,UAAApB,EACA,QAAAqB,EAAU,UACV,MAAAC,EAAQ,UACR,KAAAC,EAAO,KACP,GAAGN,CACL,EAA4F,CAC1F,OACGX,EAAA,IAAAQ,EAAY,SAAZ,CAAqB,MAAO,CAAE,QAASO,GAAW,UAAW,KAAME,GAAQ,IAC1E,EAAA,SAAAjB,EAAA,IAACkB,EAAA,CACC,YAAU,YACV,UAAWL,EAAGT,EAAiB,CAAE,QAAAW,EAAS,MAAAC,EAAO,KAAAC,CAAA,CAAM,EAAGvB,CAAS,EAClE,GAAGiB,CAAA,CAAA,EAER,CAEJ,CAEA,SAASQ,EAAY,CAAE,UAAAzB,EAAW,GAAGiB,GAA6D,CAChG,KAAM,CAAE,QAAAI,EAAS,KAAAE,GAASG,EAAAA,WAAiBZ,CAAW,EAGpD,OAAAR,EAAA,IAACqB,EAAA,CACC,YAAU,eACV,UAAWR,EAAGP,EAAoB,CAAE,QAAAS,EAAS,KAAAE,CAAK,CAAC,EAAGvB,CAAS,EAC9D,GAAGiB,CAAA,CACN,CAEJ,CAEA,SAASW,EAAY,CACnB,UAAA5B,EACA,QAAAqB,EACA,GAAGJ,CACL,EAAkG,CAE9F,OAAAX,EAAA,IAACuB,EAAA,CACC,YAAU,eACV,UAAWV,EAAGN,EAAoB,CAAE,QAAAQ,CAAS,CAAA,EAAGrB,CAAS,EACxD,GAAGiB,CAAA,CACN,CAEJ,CCtLa,MAAAa,EAAiB,CAC5BC,EACAC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,eAAgBH,EAAcC,CAAW,EAC/D,QAAS,SAAY,CACnB,KAAM,CAAE,KAAAG,GAAS,MAAMC,EAAsC,CAC3D,MAAO,CAAE,aAAAL,EAAc,YAAAC,CAAY,CAAA,CACpC,EACM,OAAAG,CAAA,CACT,CACD"}