# Authorization Error Handling Integration Guide

This guide provides step-by-step instructions for implementing proper JSON error responses for authorization failures and token expiration in an ABP Framework application.

## Overview

By default, ABP Framework returns empty responses with HTTP status codes for authorization failures (403 Forbidden) and token expiration (401 Unauthorized). This guide shows how to enhance these responses with meaningful JSON error messages that provide more context to API clients.

## Implementation Steps

### 1. Create Authorization Response Middleware

First, create a middleware to handle 403 Forbidden responses:

```csharp
// src/YourApp.Web/Middleware/AuthorizationResponseMiddleware.cs
using System;
using System.IO;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace YourApp.Web.Middleware
{
    public class AuthorizationResponseMiddleware : IMiddleware, ITransientDependency
    {
        private readonly ILogger<AuthorizationResponseMiddleware> _logger;

        public AuthorizationResponseMiddleware(ILogger<AuthorizationResponseMiddleware> logger)
        {
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            // Create a wrapper for the original response body stream
            var originalBodyStream = context.Response.Body;

            try
            {
                // Create a new memory stream to capture the response
                using var responseBody = new MemoryStream();
                context.Response.Body = responseBody;

                // Continue down the pipeline
                await next(context);

                // Check if we have a 403 status code
                if (context.Response.StatusCode == (int)HttpStatusCode.Forbidden)
                {
                    _logger.LogInformation("Handling 403 Forbidden response with JSON error message");

                    // Reset the stream position to the beginning
                    responseBody.Seek(0, SeekOrigin.Begin);

                    // Read the response body to check if it's empty
                    string responseText = await new StreamReader(responseBody).ReadToEndAsync();

                    // If the response is empty or not a valid JSON, replace it with our custom JSON
                    if (string.IsNullOrWhiteSpace(responseText) || !IsValidJson(responseText))
                    {
                        // Reset the response to add our custom JSON
                        responseBody.SetLength(0);

                        // Check if we have information about the denied permission
                        string deniedPermission = "unknown";
                        if (context.Items.ContainsKey("DeniedPermission"))
                        {
                            deniedPermission = context.Items["DeniedPermission"]?.ToString() ?? "unknown";
                        }

                        var errorResponse = new
                        {
                            error = new
                            {
                                code = "Forbidden",
                                message = "You do not have permission to access this resource",
                                details = $"The required permission '{deniedPermission}' is not granted for this operation"
                            }
                        };

                        var jsonResponse = JsonSerializer.Serialize(errorResponse);

                        // Write the JSON response to the memory stream
                        var bytes = Encoding.UTF8.GetBytes(jsonResponse);
                        await responseBody.WriteAsync(bytes, 0, bytes.Length);

                        // Set the content type and length
                        context.Response.ContentType = "application/json";
                        context.Response.ContentLength = bytes.Length;
                    }
                }

                // Copy the contents of the new memory stream to the original stream
                responseBody.Seek(0, SeekOrigin.Begin);
                await responseBody.CopyToAsync(originalBodyStream);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in AuthorizationResponseMiddleware");
                throw;
            }
            finally
            {
                // Restore the original response body stream
                context.Response.Body = originalBodyStream;
            }
        }

        private bool IsValidJson(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return false;

            try
            {
                using (JsonDocument.Parse(text))
                {
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }
    }
}
```

### 2. Create Token Expiration Middleware

Next, create a middleware to handle 401 Unauthorized responses:

```csharp
// src/YourApp.Web/Middleware/TokenExpirationMiddleware.cs
using System;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace YourApp.Web.Middleware
{
    public class TokenExpirationMiddleware : IMiddleware, ITransientDependency
    {
        private readonly ILogger<TokenExpirationMiddleware> _logger;

        public TokenExpirationMiddleware(ILogger<TokenExpirationMiddleware> logger)
        {
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            // Subscribe to the JWT Bearer authentication events
            context.Response.OnStarting(() =>
            {
                // Check if we have a 401 Unauthorized status code
                if (context.Response.StatusCode == (int)HttpStatusCode.Unauthorized)
                {
                    _logger.LogInformation("Handling 401 Unauthorized response with JSON error message");

                    // Check if the response has already been started
                    if (!context.Response.HasStarted)
                    {
                        // Set the content type to JSON
                        context.Response.ContentType = "application/json";

                        // Create a JSON error response
                        var errorResponse = new
                        {
                            error = new
                            {
                                code = "Unauthorized",
                                message = "You are not authenticated or your token has expired",
                                details = "Please provide a valid authentication token"
                            }
                        };

                        // Serialize the error response to JSON
                        var jsonResponse = JsonSerializer.Serialize(errorResponse);

                        // Write the JSON response to the response body
                        return context.Response.WriteAsync(jsonResponse);
                    }
                }

                return Task.CompletedTask;
            });

            try
            {
                await next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in TokenExpirationMiddleware");
                throw;
            }
        }
    }
}
```

### 3. Create Middleware Extension Methods

Create extension methods to make it easier to use the middleware:

```csharp
// src/YourApp.Web/Middleware/AuthorizationResponseMiddlewareExtensions.cs
using Microsoft.AspNetCore.Builder;

namespace YourApp.Web.Middleware
{
    public static class AuthorizationResponseMiddlewareExtensions
    {
        public static IApplicationBuilder UseAuthorizationResponseMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<AuthorizationResponseMiddleware>();
        }

        public static IApplicationBuilder UseTokenExpirationMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<TokenExpirationMiddleware>();
        }
    }
}
```

### 4. Update Permission Authorization Handler

Modify the `PermissionAuthorizationHandler.cs` to store the denied permission in the HttpContext:

```csharp
// src/YourApp.Web/Authorization/PermissionAuthorizationHandler.cs
// Find the section where permission is denied and add:

// If we couldn't check with the application configuration, the permission is denied
_logger.LogWarning("Permission {PermissionName} is denied", requirement.PermissionName);

// Store the denied permission in the HttpContext for our middleware to use
if (context.Resource is HttpContext currentContext)
{
    currentContext.Items["DeniedPermission"] = requirement.PermissionName;
}
```

### 5. Update Dynamic Policy Authorization Handler

If you're using dynamic policies, modify the `DynamicPolicyAuthorizationHandler.cs` to store the denied policy in the HttpContext:

```csharp
// src/YourApp.Application/Permissions/DynamicPolicyAuthorizationHandler.cs
// Find the section where policy is denied and add:

// If we couldn't check with the application configuration, the permission is denied
_logger.LogWarning("Policy {PolicyName} is denied", requirement.PolicyName);

// Store the denied policy in the HttpContext for our middleware to use
if (_httpContextAccessor.HttpContext != null)
{
    _httpContextAccessor.HttpContext.Items["DeniedPermission"] = requirement.PolicyName;
}
```

### 6. Update JWT Bearer Authentication Configuration

Update the JWT Bearer authentication configuration to always validate token lifetime and handle token expiration:

```csharp
// src/YourApp.Web/YourAppWebModule.cs
// In the ConfigureAuthentication method:

// Configure JWT Bearer authentication
context.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.Authority = configuration["AuthServer:Authority"];
        options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata");
        options.Audience = "YourApp";

        // Always validate lifetime regardless of environment
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateLifetime = true,
            RequireExpirationTime = true,

            // Other validation parameters...
            ClockSkew = TimeSpan.FromMinutes(5),
        };

        // Configure JWT Bearer authentication events
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                _logger.LogInformation("Authentication failed: {ExceptionType} - {ExceptionMessage}",
                    context.Exception.GetType().Name, context.Exception.Message);

                // In production, return a proper JSON error response
                if (!hostingEnvironment.IsDevelopment())
                {
                    // Set NoResult to suppress the 401 challenge response
                    context.NoResult();

                    // Set the response status code to 401 Unauthorized
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    context.Response.ContentType = "application/json";

                    // Create a JSON error response
                    var errorResponse = new
                    {
                        error = new
                        {
                            code = "Unauthorized",
                            message = context.Exception is SecurityTokenExpiredException
                                ? "Your authentication token has expired"
                                : "Invalid authentication token",
                            details = context.Exception.Message
                        }
                    };

                    // Serialize the error response to JSON
                    var jsonResponse = JsonSerializer.Serialize(errorResponse);

                    // Write the JSON response to the response body
                    return context.Response.WriteAsync(jsonResponse);
                }

                return Task.CompletedTask;
            }
        };
    });
```

### 7. Register Middleware in the Application Module

Register the middleware in the `ConfigureServices` method of your application module:

```csharp
// src/YourApp.Web/YourAppWebModule.cs
// In the ConfigureServices method:

// Register our custom middlewares
context.Services.AddTransient<AuthorizationResponseMiddleware>();
context.Services.AddTransient<TokenExpirationMiddleware>();
```

### 8. Use Middleware in the Application Pipeline

Use the middleware in the `OnApplicationInitialization` method of your application module:

```csharp
// src/YourApp.Web/YourAppWebModule.cs
// In the OnApplicationInitialization method:

// Add our token expiration middleware before authentication
app.UseTokenExpirationMiddleware();

// Configure authentication middleware
app.UseAuthentication();

// Use JWT token middleware for JWT authentication
app.UseJwtTokenMiddleware();

// Add our authorization response middleware before authorization
app.UseAuthorizationResponseMiddleware();

// Configure authorization middleware
app.UseAuthorization();
```

## Testing

You can test this implementation by:

1. Building and running the application.
2. Accessing protected endpoints with:
   - A valid token with the required permissions (should succeed)
   - A valid token without the required permissions (should return a 403 Forbidden with a JSON error message)
   - An expired token (should return a 401 Unauthorized with a JSON error message)

## Example Test Controller

You can create a test controller to verify your implementation:

```csharp
// src/YourApp.Web/Controllers/TestAuthorizationController.cs
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace YourApp.Web.Controllers
{
    [Route("api/test-authorization")]
    [ApiController]
    public class TestAuthorizationController : ControllerBase
    {
        private readonly ILogger<TestAuthorizationController> _logger;

        public TestAuthorizationController(ILogger<TestAuthorizationController> logger)
        {
            _logger = logger;
        }

        [HttpGet("public")]
        public IActionResult GetPublic()
        {
            return Ok(new { message = "This is a public endpoint that anyone can access" });
        }

        [HttpGet("authenticated")]
        [Authorize]
        public IActionResult GetAuthenticated()
        {
            return Ok(new { message = "This is an authenticated endpoint that requires a valid token" });
        }

        [HttpGet("admin-only")]
        [Authorize("YourApp.Admin")]
        public IActionResult GetAdminOnly()
        {
            return Ok(new { message = "This is an admin-only endpoint that requires the YourApp.Admin permission" });
        }
    }
}
```

## Implementing Authorization in Application Services

ABP Framework provides several ways to implement authorization in your application services. Here are examples of different approaches:

### 1. Using the `[Authorize]` Attribute

The simplest way to implement authorization is to use the `[Authorize]` attribute on your application service methods:

```csharp
// src/YourApp.Application/Products/ProductAppService.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Services;
using YourApp.Products.Dtos;
using YourApp.Permissions;

namespace YourApp.Products
{
    [Authorize]
    public class ProductAppService : ApplicationService, IProductAppService
    {
        private readonly IProductRepository _productRepository;

        public ProductAppService(IProductRepository productRepository)
        {
            _productRepository = productRepository;
        }

        public async Task<List<ProductDto>> GetListAsync()
        {
            var products = await _productRepository.GetListAsync();
            return ObjectMapper.Map<List<Product>, List<ProductDto>>(products);
        }

        [Authorize(YourAppPermissions.Products.Create)]
        public async Task<ProductDto> CreateAsync(CreateProductDto input)
        {
            var product = new Product
            {
                Name = input.Name,
                Price = input.Price,
                Description = input.Description
            };

            await _productRepository.InsertAsync(product);
            await CurrentUnitOfWork.SaveChangesAsync();

            return ObjectMapper.Map<Product, ProductDto>(product);
        }

        [Authorize(YourAppPermissions.Products.Update)]
        public async Task<ProductDto> UpdateAsync(Guid id, UpdateProductDto input)
        {
            var product = await _productRepository.GetAsync(id);

            product.Name = input.Name;
            product.Price = input.Price;
            product.Description = input.Description;

            await _productRepository.UpdateAsync(product);
            await CurrentUnitOfWork.SaveChangesAsync();

            return ObjectMapper.Map<Product, ProductDto>(product);
        }

        [Authorize(YourAppPermissions.Products.Delete)]
        public async Task DeleteAsync(Guid id)
        {
            await _productRepository.DeleteAsync(id);
            await CurrentUnitOfWork.SaveChangesAsync();
        }
    }
}
```

### 2. Using the Permission Checker Service

For more complex authorization logic, you can use the `IPermissionChecker` service:

```csharp
// src/YourApp.Application/Orders/OrderAppService.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization;
using YourApp.Orders.Dtos;
using YourApp.Permissions;

namespace YourApp.Orders
{
    [Authorize]
    public class OrderAppService : ApplicationService, IOrderAppService
    {
        private readonly IOrderRepository _orderRepository;
        private readonly IPermissionChecker _permissionChecker;

        public OrderAppService(
            IOrderRepository orderRepository,
            IPermissionChecker permissionChecker)
        {
            _orderRepository = orderRepository;
            _permissionChecker = permissionChecker;
        }

        public async Task<List<OrderDto>> GetListAsync()
        {
            var orders = await _orderRepository.GetListAsync();
            return ObjectMapper.Map<List<Order>, List<OrderDto>>(orders);
        }

        public async Task<OrderDto> GetAsync(Guid id)
        {
            var order = await _orderRepository.GetAsync(id);

            // Check if the user has permission to view this specific order
            if (order.UserId != CurrentUser.Id &&
                !await _permissionChecker.IsGrantedAsync(YourAppPermissions.Orders.ManageAll))
            {
                throw new AbpAuthorizationException("You don't have permission to view this order!");
            }

            return ObjectMapper.Map<Order, OrderDto>(order);
        }

        public async Task<OrderDto> CreateAsync(CreateOrderDto input)
        {
            // Check if the user has permission to create orders
            await CheckCreateOrderPermissionAsync();

            var order = new Order
            {
                UserId = CurrentUser.Id,
                ProductId = input.ProductId,
                Quantity = input.Quantity,
                Status = OrderStatus.Pending
            };

            await _orderRepository.InsertAsync(order);
            await CurrentUnitOfWork.SaveChangesAsync();

            return ObjectMapper.Map<Order, OrderDto>(order);
        }

        public async Task<OrderDto> UpdateAsync(Guid id, UpdateOrderDto input)
        {
            var order = await _orderRepository.GetAsync(id);

            // Check if the user has permission to update this specific order
            if (order.UserId != CurrentUser.Id &&
                !await _permissionChecker.IsGrantedAsync(YourAppPermissions.Orders.ManageAll))
            {
                throw new AbpAuthorizationException("You don't have permission to update this order!");
            }

            order.Quantity = input.Quantity;

            await _orderRepository.UpdateAsync(order);
            await CurrentUnitOfWork.SaveChangesAsync();

            return ObjectMapper.Map<Order, OrderDto>(order);
        }

        public async Task CancelAsync(Guid id)
        {
            var order = await _orderRepository.GetAsync(id);

            // Check if the user has permission to cancel this specific order
            if (order.UserId != CurrentUser.Id &&
                !await _permissionChecker.IsGrantedAsync(YourAppPermissions.Orders.ManageAll))
            {
                throw new AbpAuthorizationException("You don't have permission to cancel this order!");
            }

            order.Status = OrderStatus.Cancelled;

            await _orderRepository.UpdateAsync(order);
            await CurrentUnitOfWork.SaveChangesAsync();
        }

        private async Task CheckCreateOrderPermissionAsync()
        {
            await AuthorizationService.CheckAsync(YourAppPermissions.Orders.Create);
        }
    }
}
```

### 3. Using the Authorization Service

ABP Framework also provides an `IAuthorizationService` that you can use for more complex authorization scenarios:

```csharp
// src/YourApp.Application/Documents/DocumentAppService.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Services;
using YourApp.Documents.Dtos;
using YourApp.Permissions;

namespace YourApp.Documents
{
    public class DocumentAppService : ApplicationService, IDocumentAppService
    {
        private readonly IDocumentRepository _documentRepository;

        public DocumentAppService(IDocumentRepository documentRepository)
        {
            _documentRepository = documentRepository;
        }

        public async Task<List<DocumentDto>> GetListAsync()
        {
            // Check if the user has permission to view documents
            await AuthorizationService.CheckAsync(YourAppPermissions.Documents.Default);

            var documents = await _documentRepository.GetListAsync();
            return ObjectMapper.Map<List<Document>, List<DocumentDto>>(documents);
        }

        public async Task<DocumentDto> GetAsync(Guid id)
        {
            var document = await _documentRepository.GetAsync(id);

            // Check if the user has permission to view this specific document
            await AuthorizationService.CheckAsync(
                YourAppPermissions.Documents.Default,
                new DocumentOperationAuthorizationRequirement(DocumentOperation.Read, document)
            );

            return ObjectMapper.Map<Document, DocumentDto>(document);
        }

        public async Task<DocumentDto> CreateAsync(CreateDocumentDto input)
        {
            // Check if the user has permission to create documents
            await AuthorizationService.CheckAsync(YourAppPermissions.Documents.Create);

            var document = new Document
            {
                Title = input.Title,
                Content = input.Content,
                OwnerId = CurrentUser.Id,
                IsPublic = input.IsPublic
            };

            await _documentRepository.InsertAsync(document);
            await CurrentUnitOfWork.SaveChangesAsync();

            return ObjectMapper.Map<Document, DocumentDto>(document);
        }

        public async Task<DocumentDto> UpdateAsync(Guid id, UpdateDocumentDto input)
        {
            var document = await _documentRepository.GetAsync(id);

            // Check if the user has permission to update this specific document
            await AuthorizationService.CheckAsync(
                YourAppPermissions.Documents.Edit,
                new DocumentOperationAuthorizationRequirement(DocumentOperation.Update, document)
            );

            document.Title = input.Title;
            document.Content = input.Content;
            document.IsPublic = input.IsPublic;

            await _documentRepository.UpdateAsync(document);
            await CurrentUnitOfWork.SaveChangesAsync();

            return ObjectMapper.Map<Document, DocumentDto>(document);
        }

        public async Task DeleteAsync(Guid id)
        {
            var document = await _documentRepository.GetAsync(id);

            // Check if the user has permission to delete this specific document
            await AuthorizationService.CheckAsync(
                YourAppPermissions.Documents.Delete,
                new DocumentOperationAuthorizationRequirement(DocumentOperation.Delete, document)
            );

            await _documentRepository.DeleteAsync(id);
            await CurrentUnitOfWork.SaveChangesAsync();
        }
    }
}
```

### 4. Custom Authorization Requirements and Handlers

For complex authorization logic, you can create custom authorization requirements and handlers:

```csharp
// src/YourApp.Application/Documents/DocumentOperationAuthorizationRequirement.cs
using Microsoft.AspNetCore.Authorization;

namespace YourApp.Documents
{
    public enum DocumentOperation
    {
        Read,
        Update,
        Delete
    }

    public class DocumentOperationAuthorizationRequirement : IAuthorizationRequirement
    {
        public DocumentOperation Operation { get; }
        public Document Document { get; }

        public DocumentOperationAuthorizationRequirement(DocumentOperation operation, Document document)
        {
            Operation = operation;
            Document = document;
        }
    }
}
```

```csharp
// src/YourApp.Application/Documents/DocumentOperationAuthorizationHandler.cs
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Authorization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Security.Claims;
using YourApp.Permissions;

namespace YourApp.Documents
{
    public class DocumentOperationAuthorizationHandler
        : AuthorizationHandler<DocumentOperationAuthorizationRequirement>, ITransientDependency
    {
        private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;
        private readonly IPermissionChecker _permissionChecker;

        public DocumentOperationAuthorizationHandler(
            ICurrentPrincipalAccessor currentPrincipalAccessor,
            IPermissionChecker permissionChecker)
        {
            _currentPrincipalAccessor = currentPrincipalAccessor;
            _permissionChecker = permissionChecker;
        }

        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            DocumentOperationAuthorizationRequirement requirement)
        {
            var currentUserId = _currentPrincipalAccessor.Principal?.FindUserId();
            if (currentUserId == null)
            {
                context.Fail();
                return;
            }

            // If the user is the owner of the document, they can do anything with it
            if (requirement.Document.OwnerId == currentUserId)
            {
                context.Succeed(requirement);
                return;
            }

            // If the document is public, anyone can read it
            if (requirement.Operation == DocumentOperation.Read && requirement.Document.IsPublic)
            {
                context.Succeed(requirement);
                return;
            }

            // Check if the user has admin permissions
            if (await _permissionChecker.IsGrantedAsync(YourAppPermissions.Documents.Admin))
            {
                context.Succeed(requirement);
                return;
            }

            // Otherwise, the user doesn't have permission
            context.Fail();
        }
    }
}
```

### 5. Defining Permissions

Define your application's permissions in a static class:

```csharp
// src/YourApp.Application.Contracts/Permissions/YourAppPermissions.cs
namespace YourApp.Permissions
{
    public static class YourAppPermissions
    {
        public const string GroupName = "YourApp";

        public static class Products
        {
            public const string Default = GroupName + ".Products";
            public const string Create = Default + ".Create";
            public const string Update = Default + ".Update";
            public const string Delete = Default + ".Delete";
        }

        public static class Orders
        {
            public const string Default = GroupName + ".Orders";
            public const string Create = Default + ".Create";
            public const string Update = Default + ".Update";
            public const string Cancel = Default + ".Cancel";
            public const string ManageAll = Default + ".ManageAll";
        }

        public static class Documents
        {
            public const string Default = GroupName + ".Documents";
            public const string Create = Default + ".Create";
            public const string Edit = Default + ".Edit";
            public const string Delete = Default + ".Delete";
            public const string Admin = Default + ".Admin";
        }
    }
}
```

### 6. Defining Permission Definitions

Define your permission definitions in a permission definition provider:

```csharp
// src/YourApp.Application.Contracts/Permissions/YourAppPermissionDefinitionProvider.cs
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using YourApp.Localization;

namespace YourApp.Permissions
{
    public class YourAppPermissionDefinitionProvider : PermissionDefinitionProvider
    {
        public override void Define(IPermissionDefinitionContext context)
        {
            var yourAppGroup = context.AddGroup(YourAppPermissions.GroupName, L("YourApp"));

            var productsPermission = yourAppGroup.AddPermission(YourAppPermissions.Products.Default, L("Products"));
            productsPermission.AddChild(YourAppPermissions.Products.Create, L("CreateProduct"));
            productsPermission.AddChild(YourAppPermissions.Products.Update, L("UpdateProduct"));
            productsPermission.AddChild(YourAppPermissions.Products.Delete, L("DeleteProduct"));

            var ordersPermission = yourAppGroup.AddPermission(YourAppPermissions.Orders.Default, L("Orders"));
            ordersPermission.AddChild(YourAppPermissions.Orders.Create, L("CreateOrder"));
            ordersPermission.AddChild(YourAppPermissions.Orders.Update, L("UpdateOrder"));
            ordersPermission.AddChild(YourAppPermissions.Orders.Cancel, L("CancelOrder"));
            ordersPermission.AddChild(YourAppPermissions.Orders.ManageAll, L("ManageAllOrders"));

            var documentsPermission = yourAppGroup.AddPermission(YourAppPermissions.Documents.Default, L("Documents"));
            documentsPermission.AddChild(YourAppPermissions.Documents.Create, L("CreateDocument"));
            documentsPermission.AddChild(YourAppPermissions.Documents.Edit, L("EditDocument"));
            documentsPermission.AddChild(YourAppPermissions.Documents.Delete, L("DeleteDocument"));
            documentsPermission.AddChild(YourAppPermissions.Documents.Admin, L("AdminDocuments"));
        }

        private static LocalizableString L(string name)
        {
            return LocalizableString.Create<YourAppResource>(name);
        }
    }
}
```

## Handling Authorization Errors in Client Applications

### 1. Angular Client

For Angular clients, you can create an HTTP interceptor to handle authorization errors:

```typescript
// src/app/core/interceptors/auth-error.interceptor.ts
import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { OAuthService } from 'angular-oauth2-oidc';

@Injectable()
export class AuthErrorInterceptor implements HttpInterceptor {
  constructor(
    private router: Router,
    private toastr: ToastrService,
    private oauthService: OAuthService
  ) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          // Handle unauthorized error (expired token)
          this.toastr.error(
            'Your session has expired. Please log in again.',
            'Session Expired'
          );

          // Log the user out
          this.oauthService.logOut();

          // Redirect to login page
          this.router.navigate(['/account/login']);
        } else if (error.status === 403) {
          // Handle forbidden error (insufficient permissions)
          const errorMessage = error.error?.error?.message || 'You do not have permission to access this resource';
          const errorDetails = error.error?.error?.details || '';

          this.toastr.error(
            `${errorMessage}${errorDetails ? ': ' + errorDetails : ''}`,
            'Access Denied'
          );

          // Redirect to home page or access denied page
          this.router.navigate(['/access-denied']);
        }

        return throwError(error);
      })
    );
  }
}
```

Register the interceptor in your app module:

```typescript
// src/app/app.module.ts
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { AuthErrorInterceptor } from './core/interceptors/auth-error.interceptor';

@NgModule({
  // ...
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthErrorInterceptor,
      multi: true
    }
  ],
  // ...
})
export class AppModule { }
```

### 2. React Client

For React clients, you can create an axios interceptor to handle authorization errors:

```javascript
// src/api/axiosInstance.js
import axios from 'axios';
import { toast } from 'react-toastify';
import { AuthService } from '../auth/authService';

const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add a response interceptor
axiosInstance.interceptors.response.use(
  response => response,
  error => {
    if (error.response) {
      const { status, data } = error.response;

      if (status === 401) {
        // Handle unauthorized error (expired token)
        toast.error('Your session has expired. Please log in again.');

        // Log the user out
        AuthService.logout();

        // Redirect to login page
        window.location.href = '/login';
      } else if (status === 403) {
        // Handle forbidden error (insufficient permissions)
        const errorMessage = data?.error?.message || 'You do not have permission to access this resource';
        const errorDetails = data?.error?.details || '';

        toast.error(`${errorMessage}${errorDetails ? ': ' + errorDetails : ''}`);

        // Redirect to access denied page
        window.location.href = '/access-denied';
      }
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
```

## Conclusion

This implementation provides a more user-friendly and informative response when authorization fails or tokens expire, making it easier for clients to understand why their request was denied.

By following these steps, you've enhanced your ABP Framework application with proper JSON error responses for authorization failures and token expiration, and you've learned how to implement authorization in your application services using different approaches.

The examples provided in this guide demonstrate best practices for implementing authorization in ABP Framework applications and handling authorization errors in client applications.
