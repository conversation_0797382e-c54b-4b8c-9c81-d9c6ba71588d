{"version": 3, "file": "handleApiError-DnmY5fx8.js", "sources": ["../../../../../frontend/src/lib/handleApiError.ts"], "sourcesContent": ["/**\r\n * Type definition for API errors returned from the backend\r\n */\r\nexport interface ApiError {\r\n  details: {\r\n    error: {\r\n      message: string;\r\n      details: string;\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * Type guard to check if an error matches our ApiError structure\r\n * @param error - The error to check\r\n * @returns True if the error matches the ApiError structure\r\n */\r\nexport const isApiError = (error: unknown): error is ApiError => {\r\n  if (typeof error !== 'object' || error === null) return false;\r\n\r\n  // Create a safer type assertion\r\n  const errorObj = error as Record<string, unknown>;\r\n\r\n  if (!('details' in errorObj)) return false;\r\n  if (typeof errorObj.details !== 'object' || errorObj.details === null) return false;\r\n\r\n  // Check if details has an error property\r\n  const details = errorObj.details as Record<string, unknown>;\r\n  return 'error' in details;\r\n};\r\n\r\n/**\r\n * Helper function to handle API errors and display appropriate toast messages\r\n * @param err - The error to handle\r\n * @param toast - The toast function to use for displaying messages\r\n */\r\nexport const handleApiError = (\r\n  err: unknown\r\n) => {\r\n  if (isApiError(err)) {\r\n    return {\r\n      title: err.details.error.message,\r\n      description: err.details.error.details,\r\n      variant: 'error',\r\n    };\r\n  } else {\r\n    // Fallback for unexpected error format\r\n    return {\r\n      title: 'Error',\r\n      description: 'An unexpected error occurred',\r\n      variant: 'error',\r\n    };\r\n  }\r\n};\r\n"], "names": ["isApiError", "error", "errorObj", "handleApiError", "err"], "mappings": "AAiBa,MAAAA,EAAcC,GAAsC,CAC/D,GAAI,OAAOA,GAAU,UAAYA,IAAU,KAAa,MAAA,GAGxD,MAAMC,EAAWD,EAGjB,MADI,EAAE,YAAaC,IACf,OAAOA,EAAS,SAAY,UAAYA,EAAS,UAAY,KAAa,GAIvE,UADSA,EAAS,OAE3B,EAOaC,EACXC,GAEIJ,EAAWI,CAAG,EACT,CACL,MAAOA,EAAI,QAAQ,MAAM,QACzB,YAAaA,EAAI,QAAQ,MAAM,QAC/B,QAAS,OACX,EAGO,CACL,MAAO,QACP,YAAa,+BACb,QAAS,OACX"}