import{j as r}from"./vendor-B0b15ZrB.js";import{S as t}from"./scroll-area-Dw-Gl1P6.js";import{A as i}from"./app-layout-D_A4XD_6.js";import{O as o}from"./overview-SzT15zKv.js";import{$ as a}from"./App-De6zOdMU.js";import"./radix-BQPyiA8r.js";import"./badge-B7pYtUY6.js";import"./card-Iy60I049.js";function m({children:e,scrollable:s=!0}){return r.jsx(r.Fragment,{children:s?r.jsx(t,{className:"h-[calc(100dvh-52px)]",children:r.jsx("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):r.jsx("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}function u(){return r.jsxs(i,{policy:"AbpIdentity.Users",children:[r.jsx(a,{title:"Users"}),r.jsx(m,{children:r.jsx(o,{})})]})}export{u as default};
//# sourceMappingURL=users-C4u1pkGv.js.map
