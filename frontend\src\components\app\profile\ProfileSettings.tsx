'use client'
import { type UpdateProfileDto, putApiAccountMyProfile } from '@/client'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useProfile } from '@/lib/hooks/useProfile'
import { useQueryClient } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'
import Error from '@/components/ui/Error'
import Loader from '@/components/ui/Loader'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useToast } from "@/lib/useToast"
import { FormField, FormSection } from '@/components/ui/FormField'

export const ProfileSettings = () => {
  const { data, isLoading, isError } = useProfile()
  const { handleSubmit, register } = useForm()
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const onSubmit = async (data: UpdateProfileDto) => {
    try {
      await putApiAccountMyProfile({ body: data })
      toast({
        title: 'Success',
        description: 'Profile has been updated successfully.',
        variant: 'default',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetProfile] })
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast({
          title: 'Failed',
          description: "Profile update wasn't successful.",
          variant: 'destructive',
        })
      }
    }
  }

  if (isLoading) return <Loader />
  if (isError) return <Error />

  return (
    <section className="mt-10 ">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-2">
        <section className="flex w-full flex-col space-y-2">
          <FormSection>
            <FormField
              label="User Name"
              description="The unique identifier for this resource. Used in requests"
            >
              <Input
                type="text"
                defaultValue={data?.userName ?? ''}
                required
                placeholder="User name"
                {...register('userName')}
              />
            </FormField>

            <FormField
              label="Name"
              description="Your first name or given name"
            >
              <Input
                type="text"
                defaultValue={data?.name ?? ''}
                required
                placeholder="Name"
                {...register('name')}
              />
            </FormField>

            <FormField
              label="Surname"
              description="Your last name or family name"
            >
              <Input
                type="text"
                defaultValue={data?.surname ?? ''}
                required
                placeholder="Surname"
                {...register('surname')}
              />
            </FormField>

            <FormField
              label="Email"
              description="Your email address"
            >
              <Input
                type="email"
                defaultValue={data?.email ?? ''}
                required
                placeholder="Email address"
                {...register('email')}
              />
            </FormField>
            <FormField
              label="Phone Number"
              description="Your phone number"
            >
              <Input
                type="text"
                defaultValue={data?.phoneNumber ?? ''}
                required
                placeholder="Phone number"
                {...register('phoneNumber')}
              />
            </FormField>
          </FormSection>
        </section>

        <Button type="submit">Save</Button>
      </form>
    </section>
  )
}
