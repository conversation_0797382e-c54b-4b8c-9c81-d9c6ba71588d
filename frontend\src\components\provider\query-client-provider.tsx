import * as React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Create QueryClient instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 1000, // 10s
      refetchOnWindowFocus: true,
    },
  },
})

interface ReactQueryProvidersProps {
  children: React.ReactNode
}

function ReactQueryProviders({ children }: ReactQueryProvidersProps) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

export default ReactQueryProviders
