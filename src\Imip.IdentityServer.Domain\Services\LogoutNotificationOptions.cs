namespace Imip.IdentityServer.Domain.Services;

/// <summary>
/// Configuration options for logout notification service
/// </summary>
public class LogoutNotificationOptions
{
    /// <summary>
    /// Redis channel name for logout events
    /// </summary>
    public string RedisLogoutChannel { get; set; } = "identityserver:logout";

    /// <summary>
    /// Redis channel prefix for client-specific logout notifications
    /// </summary>
    public string RedisClientLogoutChannelPrefix { get; set; } = "identityserver:logout:client";

    /// <summary>
    /// Whether to enable Redis Pub/Sub notifications
    /// </summary>
    public bool EnableRedisNotifications { get; set; } = true;

    /// <summary>
    /// Whether to enable RabbitMQ notifications
    /// </summary>
    public bool EnableRabbitMQNotifications { get; set; } = true;

    /// <summary>
    /// Timeout for HTTP requests to client applications (in seconds)
    /// </summary>
    public int HttpClientTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Maximum number of retry attempts for failed notifications
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay between retry attempts (in seconds)
    /// </summary>
    public int RetryDelaySeconds { get; set; } = 5;

    /// <summary>
    /// Whether to notify all configured clients or only those with active sessions.
    /// When true, only clients with active user sessions will be notified.
    /// When false, all clients with logout URLs configured will be notified.
    /// Default: false (notify all configured clients for backward compatibility)
    /// </summary>
    public bool NotifyOnlyActiveSessionClients { get; set; } = false;

    /// <summary>
    /// Whether to enable RabbitMQ health checks and circuit breaker functionality.
    /// When true, RabbitMQ operations will be skipped if the broker is unhealthy.
    /// Default: true (enable health checks for better resilience)
    /// </summary>
    public bool EnableRabbitMQHealthChecks { get; set; } = true;

    /// <summary>
    /// Whether to continue logout process even if RabbitMQ operations fail.
    /// When true, logout will complete successfully even if event publishing fails.
    /// When false, logout will fail if RabbitMQ operations fail.
    /// Default: true (prioritize user experience over event delivery)
    /// </summary>
    public bool ContinueOnRabbitMQFailure { get; set; } = true;
}