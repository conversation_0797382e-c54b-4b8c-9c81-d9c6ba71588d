{"version": 3, "file": "badge-B7pYtUY6.js", "sources": ["../../../../../frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\"\r\nimport type { VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        primary:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90 focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        success:\r\n          \"border-transparent bg-green-600 text-white [a&]:hover:bg-green-700 focus-visible:ring-green-600/20 dark:focus-visible:ring-green-400/40 dark:bg-green-600/80\",\r\n        warning:\r\n          \"border-transparent bg-yellow-600 text-white [a&]:hover:bg-yellow-700 focus-visible:ring-yellow-600/20 dark:focus-visible:ring-yellow-400/40 dark:bg-yellow-600/80\",\r\n        error:\r\n          \"border-transparent bg-red-600 text-white [a&]:hover:bg-red-700 focus-visible:ring-red-600/20 dark:focus-visible:ring-red-400/40 dark:bg-red-600/80\",\r\n        neutral:\r\n          \"border-transparent bg-gray-600 text-white [a&]:hover:bg-gray-700 focus-visible:ring-gray-600/20 dark:focus-visible:ring-gray-400/40 dark:bg-gray-600/80\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": ["badgeVariants", "cva", "Badge", "className", "variant", "<PERSON><PERSON><PERSON><PERSON>", "props", "Comp", "Slot", "jsx", "cn"], "mappings": "sIAOA,MAAMA,EAAgBC,EACpB,iZACA,CACE,SAAU,CACR,QAAS,CACP,QACE,iFACF,QACE,kJACF,UACE,uFACF,YACE,4KACF,QACE,+JACF,QACE,oKACF,MACE,qJACF,QACE,0JACF,QACE,wEAAA,CAEN,EACA,gBAAiB,CACf,QAAS,SAAA,CACX,CAEJ,EAEA,SAASC,EAAM,CACb,UAAAC,EACA,QAAAC,EACA,QAAAC,EAAU,GACV,GAAGC,CACL,EAC8D,CACtD,MAAAC,EAAOF,EAAUG,EAAO,OAG5B,OAAAC,EAAA,IAACF,EAAA,CACC,YAAU,QACV,UAAWG,EAAGV,EAAc,CAAE,QAAAI,CAAS,CAAA,EAAGD,CAAS,EAClD,GAAGG,CAAA,CACN,CAEJ"}