using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using RabbitMQ.Client.Exceptions;
using Volo.Abp.DependencyInjection;

namespace Imip.IdentityServer.Domain.Services;

/// <summary>
/// Service to monitor RabbitMQ connection health and provide circuit breaker functionality
/// </summary>
public class RabbitMqHealthService : ISingletonDependency
{
    private readonly ILogger<RabbitMqHealthService> _logger;
    private readonly object _lock = new object();
    private DateTime _lastHealthCheck = DateTime.MinValue;
    private bool _isHealthy = true;
    private int _consecutiveFailures = 0;
    private readonly TimeSpan _healthCheckInterval = TimeSpan.FromMinutes(1);
    private readonly int _maxConsecutiveFailures = 3;

    public RabbitMqHealthService(ILogger<RabbitMqHealthService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Checks if RabbitMQ is currently considered healthy
    /// </summary>
    public bool IsHealthy
    {
        get
        {
            lock (_lock)
            {
                // If we haven't checked recently, perform a health check
                if (DateTime.UtcNow - _lastHealthCheck > _healthCheckInterval)
                {
                    _ = Task.Run(CheckHealthAsync);
                }
                return _isHealthy;
            }
        }
    }

    /// <summary>
    /// Performs a health check on RabbitMQ connection
    /// </summary>
    public async Task<bool> CheckHealthAsync()
    {
        try
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            
            var factory = new ConnectionFactory
            {
                HostName = "**********",
                UserName = "guest",
                Password = "guest",
                Port = 5672,
                RequestedConnectionTimeout = TimeSpan.FromSeconds(10),
                SocketReadTimeout = TimeSpan.FromSeconds(10),
                SocketWriteTimeout = TimeSpan.FromSeconds(10),
                AutomaticRecoveryEnabled = true,
                NetworkRecoveryInterval = TimeSpan.FromSeconds(10)
            };

            using var connection = factory.CreateConnection();
            using var channel = connection.CreateModel();
            
            // Simple operation to test connectivity
            channel.QueueDeclarePassive("amq.direct");

            lock (_lock)
            {
                _isHealthy = true;
                _consecutiveFailures = 0;
                _lastHealthCheck = DateTime.UtcNow;
            }

            _logger.LogDebug("RabbitMQ health check passed");
            return true;
        }
        catch (Exception ex)
        {
            lock (_lock)
            {
                _consecutiveFailures++;
                _lastHealthCheck = DateTime.UtcNow;
                
                if (_consecutiveFailures >= _maxConsecutiveFailures)
                {
                    _isHealthy = false;
                    _logger.LogWarning(
                        "RabbitMQ marked as unhealthy after {ConsecutiveFailures} consecutive failures. Latest error: {Error}",
                        _consecutiveFailures, ex.Message);
                }
                else
                {
                    _logger.LogDebug(
                        "RabbitMQ health check failed ({FailureCount}/{MaxFailures}): {Error}",
                        _consecutiveFailures, _maxConsecutiveFailures, ex.Message);
                }
            }

            return false;
        }
    }

    /// <summary>
    /// Executes an action only if RabbitMQ is healthy, otherwise logs a warning
    /// </summary>
    public async Task ExecuteIfHealthyAsync(Func<Task> action, string operationName)
    {
        if (!IsHealthy)
        {
            _logger.LogWarning(
                "Skipping {OperationName} because RabbitMQ is currently unhealthy",
                operationName);
            return;
        }

        try
        {
            await action();
        }
        catch (BrokerUnreachableException ex)
        {
            _logger.LogWarning(ex,
                "RabbitMQ broker unreachable during {OperationName}. Marking as unhealthy.",
                operationName);
            
            lock (_lock)
            {
                _isHealthy = false;
                _consecutiveFailures = _maxConsecutiveFailures;
            }
        }
        catch (TimeoutException ex)
        {
            _logger.LogWarning(ex,
                "Timeout occurred during {OperationName}. This may indicate RabbitMQ connectivity issues.",
                operationName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Unexpected error during {OperationName}: {ErrorMessage}",
                operationName, ex.Message);
        }
    }

    /// <summary>
    /// Manually marks RabbitMQ as unhealthy (useful for external error handling)
    /// </summary>
    public void MarkUnhealthy(string reason)
    {
        lock (_lock)
        {
            _isHealthy = false;
            _consecutiveFailures = _maxConsecutiveFailures;
            _lastHealthCheck = DateTime.UtcNow;
        }
        
        _logger.LogWarning("RabbitMQ manually marked as unhealthy: {Reason}", reason);
    }

    /// <summary>
    /// Manually resets the health status (useful for recovery scenarios)
    /// </summary>
    public void ResetHealth()
    {
        lock (_lock)
        {
            _isHealthy = true;
            _consecutiveFailures = 0;
            _lastHealthCheck = DateTime.MinValue;
        }
        
        _logger.LogInformation("RabbitMQ health status reset");
    }
}
