{"version": 3, "file": "NotionFilter-B-J2qhpm.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js", "../../../../../frontend/src/components/data-table/NotionFilter.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [[\"path\", { d: \"M21 12a9 9 0 1 1-6.219-8.56\", key: \"13zald\" }]];\nconst LoaderCircle = createLucideIcon(\"loader-circle\", __iconNode);\n\nexport { __iconNode, LoaderCircle as default };\n//# sourceMappingURL=loader-circle.js.map\n", "\"use client\"\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\"\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\"\r\nimport { ViewOptions } from \"@/components/data-table/DataTableViewOptions\"\r\nimport { Search } from \"@/components/ui/search\"\r\nimport { type FilterOperator } from \"@/client\"\r\nimport { type Table } from \"@tanstack/react-table\"\r\nimport { RiAddLine, RiCloseLine, RiFilterLine } from \"@remixicon/react\"\r\nimport { Loader2 } from \"lucide-react\"\r\nimport { useEffect, useRef, useState } from \"react\"\r\n\r\ninterface NotionFilterProps<TData> {\r\n  table: Table<TData>\r\n  onSearch?: (value: string) => void\r\n  searchValue?: string\r\n  onServerFilter?: (filterConditions: Array<{ fieldName: string, operator: FilterOperator, value: string }>) => void\r\n  activeFilters?: Array<{ fieldName: string, operator: FilterOperator, value: string }>\r\n}\r\n\r\ntype FilterCondition = {\r\n  id: string\r\n  columnId: string\r\n  operator: FilterOperator\r\n  value: string\r\n}\r\n\r\n// Map of user-friendly operator names to actual FilterOperator values\r\nconst operatorOptions: { label: string; value: FilterOperator }[] = [\r\n  { label: \"Equals\", value: \"Equals\" },\r\n  { label: \"Not Equals\", value: \"NotEquals\" },\r\n  { label: \"Contains\", value: \"Contains\" },\r\n  { label: \"Starts With\", value: \"StartsWith\" },\r\n  { label: \"Ends With\", value: \"EndsWith\" },\r\n  { label: \"Greater Than\", value: \"GreaterThan\" },\r\n  { label: \"Greater Than or Equal\", value: \"GreaterThanOrEqual\" },\r\n  { label: \"Less Than\", value: \"LessThan\" },\r\n  { label: \"Less Than or Equal\", value: \"LessThanOrEqual\" },\r\n  { label: \"Is Empty\", value: \"IsEmpty\" },\r\n  { label: \"Is Not Empty\", value: \"IsNotEmpty\" },\r\n  { label: \"Is Null\", value: \"IsNull\" },\r\n  { label: \"Is Not Null\", value: \"IsNotNull\" },\r\n]\r\n\r\nexport function NotionFilter<TData>({\r\n  table,\r\n  onSearch,\r\n  searchValue = \"\",\r\n  onServerFilter,\r\n  activeFilters = [],\r\n}: NotionFilterProps<TData>) {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n\r\n  // Initialize with empty array, we'll set it in useEffect\r\n  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])\r\n\r\n  // Generate a unique ID for new filter conditions\r\n  function generateId() {\r\n    return `filter-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`\r\n  }\r\n\r\n  // Keep filter conditions in sync with activeFilters prop - only on initial mount\r\n  // We use a ref to track if this is the first render\r\n  const initialRenderRef = useRef(true)\r\n\r\n  useEffect(() => {\r\n    // Only update on the initial render or if activeFilters changes significantly\r\n    if (initialRenderRef.current) {\r\n      initialRenderRef.current = false\r\n\r\n      if (activeFilters && activeFilters.length > 0) {\r\n        // Convert activeFilters to internal FilterCondition format\r\n        const updatedFilterConditions = activeFilters.map(filter => ({\r\n          id: generateId(),\r\n          columnId: filter.fieldName,\r\n          operator: filter.operator,\r\n          value: filter.value\r\n        }))\r\n\r\n        setFilterConditions(updatedFilterConditions)\r\n      }\r\n    }\r\n  }, [activeFilters])\r\n\r\n  // Get all visible columns that can be filtered\r\n  const filterableColumns = table\r\n    .getAllColumns()\r\n    .filter(\r\n      (column) =>\r\n        // Exclude special columns\r\n        column.id !== \"select\" &&\r\n        column.id !== \"actions\" &&\r\n        column.id !== \"drag\" &&\r\n        // Only include columns that have a filter function\r\n        column.getCanFilter()\r\n    )\r\n\r\n  // Count active filters - both from table state and our filter conditions\r\n  const activeFilterCount = filterConditions.filter(f => f.value).length\r\n  const isFiltered = activeFilterCount > 0 || table.getState().columnFilters.length > 0\r\n\r\n  // Track loading state for filter operations\r\n  const [isFilterLoading, setIsFilterLoading] = useState(false)\r\n\r\n  // Add a new empty filter condition\r\n  const addFilterCondition = () => {\r\n    if (filterableColumns.length === 0) return\r\n\r\n    const newCondition: FilterCondition = {\r\n      id: generateId(),\r\n      columnId: filterableColumns[0]?.id ?? \"\",\r\n      operator: \"Contains\" as FilterOperator,\r\n      value: \"\",\r\n    }\r\n\r\n    setFilterConditions([...filterConditions, newCondition])\r\n  }\r\n\r\n  // Remove a filter condition by ID\r\n  const removeFilterCondition = (id: string) => {\r\n    setFilterConditions(filterConditions.filter(condition => condition.id !== id))\r\n  }\r\n\r\n  // Update a specific filter condition\r\n  const updateFilterCondition = (id: string, updates: Partial<FilterCondition>) => {\r\n    setFilterConditions(filterConditions.map(condition => {\r\n      if (condition.id === id) {\r\n        return { ...condition, ...updates }\r\n      }\r\n      return condition\r\n    }))\r\n  }\r\n\r\n  // Apply all filter conditions to the table\r\n  const applyFilters = () => {\r\n    // Show loading state\r\n    setIsFilterLoading(true)\r\n\r\n    // First, clear any existing filters\r\n    table.resetColumnFilters()\r\n\r\n    // If we have a server filter callback, use it\r\n    if (onServerFilter) {\r\n      // Convert our filter conditions to the format expected by the API\r\n      const apiFilterConditions = filterConditions\r\n        .filter(condition => condition.value) // Only include conditions with values\r\n        .map(condition => ({\r\n          fieldName: condition.columnId,\r\n          operator: condition.operator,\r\n          value: condition.value\r\n        }))\r\n\r\n      // Call the server filter callback\r\n      if (apiFilterConditions.length > 0) {\r\n        onServerFilter(apiFilterConditions)\r\n      }\r\n    } else {\r\n      // Otherwise, apply filters client-side\r\n      filterConditions.forEach(condition => {\r\n        const column = table.getColumn(condition.columnId)\r\n        if (column && condition.value) {\r\n          column.setFilterValue({\r\n            operator: condition.operator,\r\n            value: condition.value\r\n          })\r\n        }\r\n      })\r\n    }\r\n\r\n    // Hide the filter dropdown\r\n    setIsOpen(false)\r\n\r\n    // Reset loading state after a short delay\r\n    setTimeout(() => {\r\n      setIsFilterLoading(false)\r\n    }, 500)\r\n  }\r\n\r\n  // Clear all filters\r\n  const clearFilters = () => {\r\n    setFilterConditions([])\r\n    table.resetColumnFilters()\r\n\r\n    // If we have a server filter callback, call it with empty conditions\r\n    if (onServerFilter) {\r\n      onServerFilter([])\r\n    }\r\n  }\r\n\r\n  // Get a user-friendly column name\r\n  const getColumnDisplayName = (columnId: string) => {\r\n    const column = table.getColumn(columnId)\r\n\r\n    // Try to get the display name from the meta property first\r\n    if (column?.columnDef?.meta?.displayName) {\r\n      return column.columnDef.meta.displayName\r\n    }\r\n\r\n    // If no meta.displayName, try to get the header\r\n    if (column?.columnDef?.header) {\r\n      // If header is a function, it might be a React component\r\n      // In this case, we'll use the column ID with first letter capitalized\r\n      if (typeof column.columnDef.header === 'function') {\r\n        return columnId.charAt(0).toUpperCase() + columnId.slice(1)\r\n      }\r\n\r\n      // If header is a string, use it directly\r\n      return column.columnDef.header.toString()\r\n    }\r\n\r\n    // Fallback to the column ID with first letter capitalized\r\n    return columnId.charAt(0).toUpperCase() + columnId.slice(1)\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-wrap items-center justify-between gap-2\">\r\n      {onSearch && (\r\n        <div className=\"w-full sm:w-auto sm:max-w-[250px]\">\r\n          <Search\r\n            onUpdate={(value) => {\r\n              if (onSearch) onSearch(value);\r\n            }}\r\n            value={searchValue || ''}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex items-center gap-2 ml-auto\">\r\n        <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n          <PopoverTrigger asChild>\r\n            <Button\r\n              variant={isFiltered ? \"default\" : \"outline\"}\r\n              size=\"sm\"\r\n              className=\"h-8 gap-1\"\r\n            >\r\n              <RiFilterLine className=\"h-3.5 w-3.5\" />\r\n              <span>Filter</span>\r\n              {isFiltered && (\r\n                <span className=\"ml-1 rounded-full bg-primary/20 px-1 text-xs font-medium\">\r\n                  {activeFilterCount}\r\n                </span>\r\n              )}\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-[600px] p-3\" align=\"end\">\r\n            <div className=\"space-y-4\">\r\n              <div className=\"space-y-2\">\r\n                <h4 className=\"font-medium leading-none\">Filter</h4>\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  Filter records by specific conditions\r\n                </p>\r\n              </div>\r\n\r\n              {filterConditions.length > 0 ? (\r\n                <div className=\"space-y-2\">\r\n                  {filterConditions.map((condition) => (\r\n                    <div key={condition.id} className=\"rounded-md border p-3\">\r\n                      <div className=\"grid grid-cols-3 gap-3\">\r\n                        <div className=\"col-span-1\">\r\n                          <div className=\"flex items-center justify-between mb-1\">\r\n                            <Label className=\"text-xs font-medium\">Column</Label>\r\n                          </div>\r\n                          <Select\r\n                            value={condition.columnId}\r\n                            onValueChange={(value) => updateFilterCondition(condition.id, { columnId: value })}\r\n                          >\r\n                            <SelectTrigger className=\"w-full\">\r\n                              <SelectValue placeholder=\"Select column\" />\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                              {filterableColumns.map((column) => (\r\n                                <SelectItem key={column.id} value={column.id}>\r\n                                  {getColumnDisplayName(column.id)}\r\n                                </SelectItem>\r\n                              ))}\r\n                            </SelectContent>\r\n                          </Select>\r\n                        </div>\r\n\r\n                        <div className=\"col-span-1\">\r\n                          <div className=\"flex items-center justify-between mb-1\">\r\n                            <Label className=\"text-xs font-medium\">Operator</Label>\r\n                          </div>\r\n                          <Select\r\n                            value={condition.operator}\r\n                            onValueChange={(value) => updateFilterCondition(condition.id, { operator: value as FilterOperator })}\r\n                          >\r\n                            <SelectTrigger className=\"w-full\">\r\n                              <SelectValue placeholder=\"Select operator\" />\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                              {operatorOptions.map((option) => (\r\n                                <SelectItem key={option.value} value={option.value}>\r\n                                  {option.label}\r\n                                </SelectItem>\r\n                              ))}\r\n                            </SelectContent>\r\n                          </Select>\r\n                        </div>\r\n\r\n                        <div className=\"col-span-1\">\r\n                          <div className=\"flex\">\r\n                            <div>\r\n                              <div className=\"flex items-center justify-between mb-1\">\r\n                                <Label className=\"text-xs font-medium\">Value</Label>\r\n                              </div>\r\n                              <Input\r\n                                placeholder=\"Enter value\"\r\n                                className=\"w-full\"\r\n                                value={condition.value}\r\n                                onChange={(e) => updateFilterCondition(condition.id, { value: e.target.value })}\r\n                              />\r\n                            </div>\r\n                            <div className=\"col-span-1 flex items-end justify-end\">\r\n                              <Button\r\n                                variant=\"ghost\"\r\n                                size=\"sm\"\r\n                                className=\"h-8 w-8 p-0\"\r\n                                onClick={() => removeFilterCondition(condition.id)}\r\n                              >\r\n                                <RiCloseLine className=\"h-4 w-4\" />\r\n                              </Button>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              ) : (\r\n                <div className=\"flex h-20 items-center justify-center rounded-md border border-dashed\">\r\n                  <p className=\"text-sm text-muted-foreground\">No filters added</p>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"flex items-center gap-2\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"h-8 gap-1\"\r\n                  onClick={addFilterCondition}\r\n                >\r\n                  <RiAddLine className=\"h-3.5 w-3.5\" />\r\n                  <span>Add filter</span>\r\n                </Button>\r\n                {filterConditions.length > 0 && (\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    className=\"h-8\"\r\n                    onClick={clearFilters}\r\n                  >\r\n                    Clear all\r\n                  </Button>\r\n                )}\r\n              </div>\r\n\r\n              <Button\r\n                className=\"w-full\"\r\n                onClick={applyFilters}\r\n                disabled={filterConditions.length === 0 || isFilterLoading}\r\n              >\r\n                {isFilterLoading ? (\r\n                  <>\r\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                    Applying...\r\n                  </>\r\n                ) : (\r\n                  \"Apply filters\"\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n\r\n        {isFiltered && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={clearFilters}\r\n            className=\"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500\"\r\n          >\r\n            Clear filters\r\n          </Button>\r\n        )}\r\n        <ViewOptions table={table} />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\n"], "names": ["__iconNode", "LoaderCircle", "createLucideIcon", "operatorOptions", "NotionFilter", "table", "onSearch", "searchValue", "onServerFilter", "activeFilters", "isOpen", "setIsOpen", "useState", "filterConditions", "setFilterConditions", "generateId", "initialRenderRef", "useRef", "useEffect", "updatedFilterConditions", "filter", "filterableColumns", "column", "activeFilterCount", "f", "isFiltered", "isFilterLoading", "setIsFilterLoading", "addFilterCondition", "newCondition", "removeFilterCondition", "id", "condition", "updateFilterCondition", "updates", "applyFilters", "apiFilterConditions", "clearFilters", "getColumnDisplayName", "columnId", "jsxs", "jsx", "Search", "value", "Popover", "PopoverTrigger", "<PERSON><PERSON>", "RiFilterLine", "PopoverC<PERSON>nt", "Label", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "option", "Input", "e", "RiCloseLine", "RiAddLine", "Fragment", "Loader2", "ViewOptions"], "mappings": "sWAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,EAAa,CAAC,CAAC,OAAQ,CAAE,EAAG,8BAA+B,IAAK,QAAQ,CAAE,CAAC,EAC3EC,EAAeC,EAAiB,gBAAiBF,CAAU,EC+B3DG,EAA8D,CAClE,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,aAAc,MAAO,WAAY,EAC1C,CAAE,MAAO,WAAY,MAAO,UAAW,EACvC,CAAE,MAAO,cAAe,MAAO,YAAa,EAC5C,CAAE,MAAO,YAAa,MAAO,UAAW,EACxC,CAAE,MAAO,eAAgB,MAAO,aAAc,EAC9C,CAAE,MAAO,wBAAyB,MAAO,oBAAqB,EAC9D,CAAE,MAAO,YAAa,MAAO,UAAW,EACxC,CAAE,MAAO,qBAAsB,MAAO,iBAAkB,EACxD,CAAE,MAAO,WAAY,MAAO,SAAU,EACtC,CAAE,MAAO,eAAgB,MAAO,YAAa,EAC7C,CAAE,MAAO,UAAW,MAAO,QAAS,EACpC,CAAE,MAAO,cAAe,MAAO,WAAY,CAC7C,EAEO,SAASC,EAAoB,CAClC,MAAAC,EACA,SAAAC,EACA,YAAAC,EAAc,GACd,eAAAC,EACA,cAAAC,EAAgB,CAAA,CAClB,EAA6B,CAC3B,KAAM,CAACC,EAAQC,CAAS,EAAIC,EAAAA,SAAS,EAAK,EAGpC,CAACC,EAAkBC,CAAmB,EAAIF,EAAAA,SAA4B,CAAA,CAAE,EAG9E,SAASG,GAAa,CACpB,MAAO,UAAU,KAAK,IAAI,CAAC,IAAI,KAAK,OAAS,EAAA,SAAS,EAAE,EAAE,UAAU,EAAG,CAAC,CAAC,EAAA,CAKrE,MAAAC,EAAmBC,SAAO,EAAI,EAEpCC,EAAAA,UAAU,IAAM,CAEd,GAAIF,EAAiB,UACnBA,EAAiB,QAAU,GAEvBP,GAAiBA,EAAc,OAAS,GAAG,CAEvC,MAAAU,EAA0BV,EAAc,IAAeW,IAAA,CAC3D,GAAIL,EAAW,EACf,SAAUK,EAAO,UACjB,SAAUA,EAAO,SACjB,MAAOA,EAAO,KAAA,EACd,EAEFN,EAAoBK,CAAuB,CAAA,CAE/C,EACC,CAACV,CAAa,CAAC,EAGZ,MAAAY,EAAoBhB,EACvB,cAAA,EACA,OACEiB,GAECA,EAAO,KAAO,UACdA,EAAO,KAAO,WACdA,EAAO,KAAO,QAEdA,EAAO,aAAa,CACxB,EAGIC,EAAoBV,EAAiB,OAAYW,GAAAA,EAAE,KAAK,EAAE,OAC1DC,EAAaF,EAAoB,GAAKlB,EAAM,WAAW,cAAc,OAAS,EAG9E,CAACqB,EAAiBC,CAAkB,EAAIf,EAAAA,SAAS,EAAK,EAGtDgB,EAAqB,IAAM,CAC3B,GAAAP,EAAkB,SAAW,EAAG,OAEpC,MAAMQ,EAAgC,CACpC,GAAId,EAAW,EACf,SAAUM,EAAkB,CAAC,GAAG,IAAM,GACtC,SAAU,WACV,MAAO,EACT,EAEAP,EAAoB,CAAC,GAAGD,EAAkBgB,CAAY,CAAC,CACzD,EAGMC,EAAyBC,GAAe,CAC5CjB,EAAoBD,EAAiB,OAAOmB,GAAaA,EAAU,KAAOD,CAAE,CAAC,CAC/E,EAGME,EAAwB,CAACF,EAAYG,IAAsC,CAC3DpB,EAAAD,EAAiB,IAAiBmB,GAChDA,EAAU,KAAOD,EACZ,CAAE,GAAGC,EAAW,GAAGE,CAAQ,EAE7BF,CACR,CAAC,CACJ,EAGMG,EAAe,IAAM,CAQzB,GANAR,EAAmB,EAAI,EAGvBtB,EAAM,mBAAmB,EAGrBG,EAAgB,CAEZ,MAAA4B,EAAsBvB,EACzB,OAAOmB,GAAaA,EAAU,KAAK,EACnC,IAAkBA,IAAA,CACjB,UAAWA,EAAU,SACrB,SAAUA,EAAU,SACpB,MAAOA,EAAU,KAAA,EACjB,EAGAI,EAAoB,OAAS,GAC/B5B,EAAe4B,CAAmB,CACpC,MAGAvB,EAAiB,QAAqBmB,GAAA,CACpC,MAAMV,EAASjB,EAAM,UAAU2B,EAAU,QAAQ,EAC7CV,GAAUU,EAAU,OACtBV,EAAO,eAAe,CACpB,SAAUU,EAAU,SACpB,MAAOA,EAAU,KAAA,CAClB,CACH,CACD,EAIHrB,EAAU,EAAK,EAGf,WAAW,IAAM,CACfgB,EAAmB,EAAK,GACvB,GAAG,CACR,EAGMU,EAAe,IAAM,CACzBvB,EAAoB,CAAA,CAAE,EACtBT,EAAM,mBAAmB,EAGrBG,GACFA,EAAe,CAAA,CAAE,CAErB,EAGM8B,EAAwBC,GAAqB,CAC3C,MAAAjB,EAASjB,EAAM,UAAUkC,CAAQ,EAGnC,OAAAjB,GAAQ,WAAW,MAAM,YACpBA,EAAO,UAAU,KAAK,YAI3BA,GAAQ,WAAW,OAGjB,OAAOA,EAAO,UAAU,QAAW,WAC9BiB,EAAS,OAAO,CAAC,EAAE,cAAgBA,EAAS,MAAM,CAAC,EAIrDjB,EAAO,UAAU,OAAO,SAAS,EAInCiB,EAAS,OAAO,CAAC,EAAE,cAAgBA,EAAS,MAAM,CAAC,CAC5D,EAGE,OAAAC,EAAA,KAAC,MAAI,CAAA,UAAU,oDACZ,SAAA,CACClC,GAAAmC,EAAA,IAAC,MAAI,CAAA,UAAU,oCACb,SAAAA,EAAA,IAACC,EAAA,CACC,SAAWC,GAAU,CACfrC,KAAmBqC,CAAK,CAC9B,EACA,MAAOpC,GAAe,EAAA,CAAA,EAE1B,EAGFiC,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAA,EAAA,KAACI,EAAQ,CAAA,KAAMlC,EAAQ,aAAcC,EACnC,SAAA,CAAC8B,EAAAA,IAAAI,EAAA,CAAe,QAAO,GACrB,SAAAL,EAAA,KAACM,EAAA,CACC,QAASrB,EAAa,UAAY,UAClC,KAAK,KACL,UAAU,YAEV,SAAA,CAACgB,EAAAA,IAAAM,EAAA,CAAa,UAAU,aAAc,CAAA,EACtCN,EAAAA,IAAC,QAAK,SAAM,QAAA,CAAA,EACXhB,GACCgB,EAAA,IAAC,OAAK,CAAA,UAAU,2DACb,SACHlB,CAAA,CAAA,CAAA,CAAA,CAAA,EAGN,EACAkB,EAAAA,IAACO,GAAe,UAAU,gBAAgB,MAAM,MAC9C,SAAAR,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2BAA2B,SAAM,SAAA,EAC9CA,EAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,SAE7C,uCAAA,CAAA,CAAA,EACF,EAEC5B,EAAiB,OAAS,QACxB,MAAI,CAAA,UAAU,YACZ,SAAiBA,EAAA,IAAKmB,SACpB,MAAuB,CAAA,UAAU,wBAChC,SAACQ,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,yCACb,SAAAA,EAAA,IAACQ,GAAM,UAAU,sBAAsB,kBAAM,CAC/C,CAAA,EACAT,EAAA,KAACU,EAAA,CACC,MAAOlB,EAAU,SACjB,cAAgBW,GAAUV,EAAsBD,EAAU,GAAI,CAAE,SAAUW,EAAO,EAEjF,SAAA,CAAAF,EAAAA,IAACU,GAAc,UAAU,SACvB,eAACC,EAAY,CAAA,YAAY,gBAAgB,CAC3C,CAAA,QACCC,EACE,CAAA,SAAAhC,EAAkB,IAAKC,SACrBgC,EAA2B,CAAA,MAAOhC,EAAO,GACvC,WAAqBA,EAAO,EAAE,GADhBA,EAAO,EAExB,CACD,CACH,CAAA,CAAA,CAAA,CAAA,CACF,EACF,EAEAkB,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,yCACb,SAAAA,EAAA,IAACQ,GAAM,UAAU,sBAAsB,oBAAQ,CACjD,CAAA,EACAT,EAAA,KAACU,EAAA,CACC,MAAOlB,EAAU,SACjB,cAAgBW,GAAUV,EAAsBD,EAAU,GAAI,CAAE,SAAUW,EAAyB,EAEnG,SAAA,CAAAF,EAAAA,IAACU,GAAc,UAAU,SACvB,eAACC,EAAY,CAAA,YAAY,kBAAkB,CAC7C,CAAA,QACCC,EACE,CAAA,SAAAlD,EAAgB,IAAKoD,GACnBd,EAAAA,IAAAa,EAAA,CAA8B,MAAOC,EAAO,MAC1C,SAAOA,EAAA,OADOA,EAAO,KAExB,CACD,CACH,CAAA,CAAA,CAAA,CAAA,CACF,EACF,QAEC,MAAI,CAAA,UAAU,aACb,SAACf,EAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,yCACb,SAAAA,EAAA,IAACQ,GAAM,UAAU,sBAAsB,iBAAK,CAC9C,CAAA,EACAR,EAAA,IAACe,EAAA,CACC,YAAY,cACZ,UAAU,SACV,MAAOxB,EAAU,MACjB,SAAWyB,GAAMxB,EAAsBD,EAAU,GAAI,CAAE,MAAOyB,EAAE,OAAO,KAAO,CAAA,CAAA,CAAA,CAChF,EACF,EACAhB,EAAAA,IAAC,MAAI,CAAA,UAAU,wCACb,SAAAA,EAAA,IAACK,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cACV,QAAS,IAAMhB,EAAsBE,EAAU,EAAE,EAEjD,SAAAS,EAAAA,IAACiB,EAAY,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,CAErC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CArEQ,EAAA1B,EAAU,EAsEpB,CACD,EACH,EAEAS,EAAAA,IAAC,MAAI,CAAA,UAAU,wEACb,SAACA,EAAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,2BAAgB,CAAA,EAC/D,EAGFD,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAAA,EAAA,KAACM,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,YACV,QAASlB,EAET,SAAA,CAACa,EAAAA,IAAAkB,EAAA,CAAU,UAAU,aAAc,CAAA,EACnClB,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,EACC5B,EAAiB,OAAS,GACzB4B,EAAA,IAACK,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,MACV,QAAST,EACV,SAAA,WAAA,CAAA,CAED,EAEJ,EAEAI,EAAA,IAACK,EAAA,CACC,UAAU,SACV,QAASX,EACT,SAAUtB,EAAiB,SAAW,GAAKa,EAE1C,WAEGc,EAAAA,KAAAoB,EAAA,SAAA,CAAA,SAAA,CAACnB,EAAAA,IAAAoB,EAAA,CAAQ,UAAU,2BAA4B,CAAA,EAAE,aAAA,CAAA,CAEnD,EAEA,eAAA,CAAA,CAEJ,CAAA,CACF,CACF,CAAA,CAAA,EACF,EAECpC,GACCgB,EAAA,IAACK,EAAA,CACC,QAAQ,QACR,QAAST,EACT,UAAU,6HACX,SAAA,eAAA,CAED,EAEFI,MAACqB,GAAY,MAAAzD,CAAc,CAAA,CAAA,CAC7B,CAAA,CAAA,EACF,CAEJ", "x_google_ignoreList": [0]}