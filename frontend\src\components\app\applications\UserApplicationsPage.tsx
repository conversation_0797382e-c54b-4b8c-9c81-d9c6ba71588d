import React, { useState, useMemo } from 'react'
import { useCurrentUserRoleApplications } from '@/lib/hooks/useCurrentUserRoleApplications'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Search, ExternalLink, Globe, Grid, List, Filter } from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

type ViewMode = 'grid' | 'list'
type SortOption = 'name' | 'url' | 'recent'

export const UserApplicationsPage: React.FC = () => {
    const apps = useCurrentUserRoleApplications()
    const [searchQuery, setSearchQuery] = useState('')
    const [viewMode, setViewMode] = useState<ViewMode>('grid')
    const [sortBy, setSortBy] = useState<SortOption>('name')
    const [showOnlyAccessible, setShowOnlyAccessible] = useState(false)

    // Filter and sort applications
    const filteredAndSortedApps = useMemo(() => {
        let filtered = apps

        // Apply search filter
        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase()
            filtered = filtered.filter(app => 
                app.applicationName.toLowerCase().includes(query) ||
                (app.clientUrl && app.clientUrl.toLowerCase().includes(query))
            )
        }

        // Apply accessibility filter
        if (showOnlyAccessible) {
            filtered = filtered.filter(app => app.clientUrl)
        }

        // Apply sorting
        const sorted = [...filtered].sort((a, b) => {
            switch (sortBy) {
                case 'name':
                    return a.applicationName.localeCompare(b.applicationName)
                case 'url':
                    return (a.clientUrl || '').localeCompare(b.clientUrl || '')
                case 'recent':
                    // For now, just sort by name since we don't have recent access data
                    return a.applicationName.localeCompare(b.applicationName)
                default:
                    return 0
            }
        })

        return sorted
    }, [apps, searchQuery, sortBy, showOnlyAccessible])

    const handleCardClick = (app: typeof apps[0]) => {
        if (app.clientUrl) {
            window.open(app.clientUrl, '_blank', 'noopener,noreferrer')
        }
    }

    const accessibleAppsCount = apps.filter(app => app.clientUrl).length

    if (!apps.length) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="text-center py-16">
                    <Globe className="mx-auto h-16 w-16 text-gray-400 mb-6" />
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">No Applications Available</h1>
                    <p className="text-gray-500 max-w-md mx-auto">
                        You don't have access to any applications yet. Contact your administrator to get access to applications.
                    </p>
                </div>
            </div>
        )
    }

    return (
        <div className="container mx-auto px-4 py-8">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">My Applications</h1>
                <p className="text-gray-600">
                    Access all applications available to you based on your roles and permissions.
                </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <Globe className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Total Applications</p>
                                <p className="text-2xl font-bold text-gray-900">{apps.length}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <ExternalLink className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Accessible</p>
                                <p className="text-2xl font-bold text-gray-900">{accessibleAppsCount}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <Filter className="h-6 w-6 text-purple-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Filtered Results</p>
                                <p className="text-2xl font-bold text-gray-900">{filteredAndSortedApps.length}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Controls */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
                {/* Search */}
                <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                        type="text"
                        placeholder="Search applications..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                    />
                </div>

                {/* Filters and View Controls */}
                <div className="flex items-center gap-3">
                    {/* Sort */}
                    <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
                        <SelectTrigger className="w-40">
                            <SelectValue placeholder="Sort by" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="name">Name</SelectItem>
                            <SelectItem value="url">URL</SelectItem>
                            <SelectItem value="recent">Recent</SelectItem>
                        </SelectContent>
                    </Select>

                    {/* Filter Toggle */}
                    <Button
                        variant={showOnlyAccessible ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowOnlyAccessible(!showOnlyAccessible)}
                    >
                        <Filter className="h-4 w-4 mr-2" />
                        Accessible Only
                    </Button>

                    {/* View Mode Toggle */}
                    <div className="flex border rounded-lg">
                        <Button
                            variant={viewMode === 'grid' ? "default" : "ghost"}
                            size="sm"
                            onClick={() => setViewMode('grid')}
                            className="rounded-r-none"
                        >
                            <Grid className="h-4 w-4" />
                        </Button>
                        <Button
                            variant={viewMode === 'list' ? "default" : "ghost"}
                            size="sm"
                            onClick={() => setViewMode('list')}
                            className="rounded-l-none"
                        >
                            <List className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>

            {/* Results count */}
            {(searchQuery || showOnlyAccessible) && (
                <div className="text-sm text-gray-600 mb-4">
                    Showing {filteredAndSortedApps.length} of {apps.length} applications
                </div>
            )}

            {/* Applications Display */}
            {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filteredAndSortedApps.map((app) => (
                        <Card 
                            key={app.applicationId}
                            className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${
                                app.clientUrl 
                                    ? 'hover:border-blue-300 focus-within:ring-2 focus-within:ring-blue-500' 
                                    : 'opacity-75 cursor-not-allowed'
                            }`}
                            onClick={() => handleCardClick(app)}
                            tabIndex={app.clientUrl ? 0 : -1}
                            onKeyDown={(e) => {
                                if ((e.key === 'Enter' || e.key === ' ') && app.clientUrl) {
                                    e.preventDefault()
                                    handleCardClick(app)
                                }
                            }}
                            role={app.clientUrl ? "button" : "presentation"}
                            aria-label={app.clientUrl ? `Open ${app.applicationName}` : app.applicationName}
                        >
                            <CardHeader className="pb-3">
                                <div className="flex items-start justify-between">
                                    <CardTitle className="text-lg font-medium line-clamp-2 flex-1">
                                        {app.applicationName}
                                    </CardTitle>
                                    {app.clientUrl && (
                                        <ExternalLink className="h-5 w-5 text-gray-400 ml-2 flex-shrink-0" />
                                    )}
                                </div>
                            </CardHeader>
                            
                            <CardContent className="pt-0">
                                {app.clientUrl ? (
                                    <CardDescription className="text-sm text-blue-600 truncate">
                                        {app.clientUrl}
                                    </CardDescription>
                                ) : (
                                    <Badge variant="secondary" className="text-xs">
                                        No URL configured
                                    </Badge>
                                )}
                            </CardContent>
                        </Card>
                    ))}
                </div>
            ) : (
                <div className="space-y-3">
                    {filteredAndSortedApps.map((app) => (
                        <Card 
                            key={app.applicationId}
                            className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                                app.clientUrl 
                                    ? 'hover:border-blue-300 focus-within:ring-2 focus-within:ring-blue-500' 
                                    : 'opacity-75 cursor-not-allowed'
                            }`}
                            onClick={() => handleCardClick(app)}
                            tabIndex={app.clientUrl ? 0 : -1}
                            onKeyDown={(e) => {
                                if ((e.key === 'Enter' || e.key === ' ') && app.clientUrl) {
                                    e.preventDefault()
                                    handleCardClick(app)
                                }
                            }}
                            role={app.clientUrl ? "button" : "presentation"}
                            aria-label={app.clientUrl ? `Open ${app.applicationName}` : app.applicationName}
                        >
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex-1">
                                        <h3 className="font-medium text-lg text-gray-900">{app.applicationName}</h3>
                                        {app.clientUrl ? (
                                            <p className="text-sm text-blue-600 mt-1">{app.clientUrl}</p>
                                        ) : (
                                            <Badge variant="secondary" className="text-xs mt-2">
                                                No URL configured
                                            </Badge>
                                        )}
                                    </div>
                                    {app.clientUrl && (
                                        <ExternalLink className="h-5 w-5 text-gray-400 ml-4 flex-shrink-0" />
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}

            {/* No results message */}
            {filteredAndSortedApps.length === 0 && (searchQuery || showOnlyAccessible) && (
                <div className="text-center py-16">
                    <Search className="mx-auto h-16 w-16 text-gray-400 mb-6" />
                    <h3 className="text-xl font-medium text-gray-900 mb-4">No applications found</h3>
                    <p className="text-gray-500 max-w-md mx-auto mb-6">
                        Try adjusting your search terms or filters to find the applications you're looking for.
                    </p>
                    <Button 
                        variant="outline" 
                        onClick={() => {
                            setSearchQuery('')
                            setShowOnlyAccessible(false)
                        }}
                    >
                        Clear filters
                    </Button>
                </div>
            )}
        </div>
    )
}
