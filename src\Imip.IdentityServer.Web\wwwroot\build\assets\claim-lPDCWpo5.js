import{r as u,j as e,u as $,f as M,h as K}from"./vendor-B0b15ZrB.js";import{u as F,d as X,Q as N,p as Z,t as ee,a as Q,T as te,B as f,b as se,D as ie,c as ae,e as ne,f as H,g as re,A as le}from"./app-layout-D_A4XD_6.js";import{$ as oe}from"./App-De6zOdMU.js";import{A as ce,a as de,b as ue,c as pe,d as me,e as he,f as xe,g as ge,T as ye}from"./TableSkeleton-DgDki6RL.js";import{h as fe}from"./handleApiError-DnmY5fx8.js";import{p as je,l as Se,a as Ce,_ as Te,D as P,b as ve}from"./DataTableColumnHeader-CSMG3Uqi.js";import{N as be}from"./NotionFilter-B-J2qhpm.js";import{g as De,e as Ne}from"./query-utils-extended-wVOeERM5.js";import{F as z,a as p}from"./FormField-POW7SsfI.js";import{u as U,D as B,a as we,b as E,c as G,d as L,I as y,C as w,e as J}from"./index.esm-DqIqfoOW.js";import{M as W,c as O}from"./filterFunctions-EQ3oUkRA.js";import"./radix-BQPyiA8r.js";import"./card-Iy60I049.js";import"./badge-B7pYtUY6.js";import"./scroll-area-Dw-Gl1P6.js";const Ie=({dataId:s,onDismiss:t})=>{const{toast:r}=F(),[l,i]=u.useState(!1),h=async()=>{try{await X({path:{id:s}}),r({title:"Success",description:"Claim Type has been deleted successfully."}),t()}catch(d){const o=fe(d);r({title:o.title,description:o.description,variant:"error"})}};return u.useEffect(()=>{i(!0)},[]),e.jsx(ce,{open:l,children:e.jsxs(de,{children:[e.jsxs(ue,{children:[e.jsx(pe,{children:"Are you absolutely sure?"}),e.jsx(me,{children:"This action cannot be undone. This will permanently delete your this claim type."})]}),e.jsxs(he,{children:[e.jsx(xe,{onClick:t,children:"Cancel"}),e.jsx(ge,{onClick:h,children:"Yes"})]})]})})},Ae=(s,t,r=[],l)=>$({queryKey:[N.GetIdentityClaimTypes,s,t,JSON.stringify(r),l],queryFn:async()=>{try{const i=De({pageIndex:s,pageSize:t,sorting:l,filterConditions:r});return(await Z({body:i})).data?.data}catch(i){const{title:h,description:d}=Ne(i,"Error loading clients");return ee({title:h,description:d,variant:"destructive"}),{items:[],totalCount:0}}}}),Y=[{label:"String",value:"0"},{label:"Int",value:"1"},{label:"Boolean",value:"2"},{label:"DateTime",value:"3"}],Fe=({children:s})=>{const{can:t}=Q(),[r,l]=u.useState(!1),{toast:i}=F(),h=M(),{handleSubmit:d,register:o,reset:m}=U(),[c,T]=u.useState([]);u.useEffect(()=>{r||(m({name:"",required:!1,isStatic:!1,regex:"",regexDescription:"",description:"",valueType:0}),T([]))},[r,m]);const g=K({mutationFn:async n=>se({body:n}),onSuccess:()=>{i({title:"Success",description:"Claim Type Created Successfully",variant:"success"}),h.invalidateQueries({queryKey:[N.GetIdentityClaimTypes]}),l(!1)},onError:n=>{i({title:n?.error?.message,description:n?.error?.details,variant:"destructive"})}}),j=n=>{const b={...n};g.mutate(b)},v=n=>{l(n)};return e.jsxs("section",{children:[e.jsx(te,{}),e.jsxs(B,{open:r,onOpenChange:v,children:[e.jsx(we,{asChild:!0,children:s}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:t("IdentityServer.ClaimTypes.Create")&&e.jsxs(f,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>l(!0),children:[e.jsx(je,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"New Claim Type"})]})}),e.jsxs(E,{className:"max-w-2xl",children:[e.jsx(G,{children:e.jsx(L,{children:"Create a New Claim Type"})}),e.jsxs("form",{onSubmit:d(j),className:"mt-2",onKeyDown:n=>{n.key==="Enter"&&n.target instanceof HTMLInputElement&&(n.preventDefault(),d(j)())},children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(z,{children:[e.jsx(p,{label:"Name",description:"The name of the claim",children:e.jsx(y,{required:!0,...o("name"),placeholder:"Claim Name"})}),e.jsx(p,{label:"Required",description:"Whether the claim is required",children:e.jsx(w,{...o("required",{setValueAs:n=>n===!0})})}),e.jsx(p,{label:"Is Static",description:"Whether the claim is static",children:e.jsx(w,{...o("isStatic",{setValueAs:n=>n===!0})})}),e.jsx(p,{label:"Regex",description:"The regex for the claim",children:e.jsx(y,{...o("regex"),placeholder:"Regex"})}),e.jsx(p,{label:"Regex Description",description:"The description for the regex",children:e.jsx(y,{...o("regexDescription"),placeholder:"Regex Description"})}),e.jsx(p,{label:"Description",description:"The description for the claim",children:e.jsx(y,{required:!0,...o("description"),placeholder:"Description"})}),e.jsx(p,{label:"Value Type",description:"The value type for the claim",children:e.jsx(W,{mode:"single",options:Y,value:c,onChange:T,placeholder:"Select value type",maxHeight:300,name:"valueType",register:o,valueAsNumber:!0})})]})}),e.jsxs(J,{className:"mt-5",children:[e.jsx(f,{variant:"ghost",onClick:n=>{n.preventDefault(),l(!1)},disabled:g.isPending,children:"Cancel"}),e.jsx(f,{type:"submit",disabled:g.isPending,children:g.isPending?"Saving...":"Save"})]})]})]})]})]})},qe=({userId:s,userDto:t,onAction:r,variant:l="dropdown"})=>{const{can:i}=Q();return l==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(ie,{children:[e.jsx(ae,{asChild:!0,children:e.jsxs(f,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(Se,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(ne,{align:"end",className:"w-[160px]",children:[i("IdentityServer.ClaimTypes.Edit")&&e.jsx(H,{className:"cursor-pointer text-sm",onClick:()=>r(s,t,"edit"),children:"Edit"}),i("IdentityServer.ClaimTypes.Delete")&&e.jsx(H,{className:"cursor-pointer text-sm text-red-500",onClick:()=>r(s,t,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[i("AbpIdentity.Users.ManagePermissions")&&e.jsxs(f,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>r(s,t,"permission"),children:[e.jsx(Ce,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),i("AbpIdentity.Users.Update")&&e.jsxs(f,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>r(s,t,"edit"),children:[e.jsx(Te,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})},ke=s=>[{id:"select",header:({table:t})=>e.jsx(w,{checked:t.getIsAllPageRowsSelected()?!0:t.getIsSomeRowsSelected()?"indeterminate":!1,onCheckedChange:()=>t.toggleAllPageRowsSelected(),className:"translate-y-0.5","aria-label":"Select all"}),cell:({row:t})=>e.jsx(w,{checked:t.getIsSelected(),onCheckedChange:()=>t.toggleSelected(),className:"translate-y-0.5","aria-label":"Select row"}),enableSorting:!1,enableHiding:!0,meta:{displayName:"Select"}},{accessorKey:"name",header:({column:t})=>e.jsx(P,{column:t,title:"Name"}),enableSorting:!0,enableHiding:!0,cell:t=>t.getValue(),filterFn:O,meta:{className:"text-left",displayName:"Name"}},{accessorKey:"description",header:({column:t})=>e.jsx(P,{column:t,title:"Description"}),enableSorting:!0,enableHiding:!0,filterFn:O,cell:t=>t.getValue(),meta:{className:"text-left",displayName:"Description"}},{accessorKey:"valueType",header:({column:t})=>e.jsx(P,{column:t,title:"Value Type"}),cell:({row:t})=>{const r=t.getValue("valueType");return{0:"String",1:"Int",2:"Boolean",3:"DateTime"}[r]||"Unknown"},enableSorting:!0,enableHiding:!0,meta:{className:"text-left",displayName:"Value Type"}},{id:"actions",header:"Actions",cell:t=>e.jsx(qe,{userId:t.row.original.id,userDto:t.row.original,onAction:s,variant:"dropdown"}),enableSorting:!1,enableHiding:!0,meta:{className:"text-right",displayName:"Action"}}],Ve=({dataEdit:s,dataId:t,onDismiss:r})=>{const[l,i]=u.useState(!1),{toast:h}=F(),d=M(),{handleSubmit:o,register:m,setValue:c}=U(),[T,g]=u.useState(s.required??!1),[j,v]=u.useState(s.isStatic??!1),[n,b]=u.useState([]),D=K({mutationFn:async a=>re({path:{id:t},body:a}),onSuccess:()=>{h({title:"Success",description:"Claim Type Updated Successfully",variant:"success"}),d.invalidateQueries({queryKey:[N.GetIdentityClaimTypes]}),A()},onError:a=>{h({title:a?.error?.message,description:a?.error?.details,variant:"destructive"})}}),I=a=>{const q={...a};D.mutate(q)},A=()=>{i(!1),r()};return u.useEffect(()=>{if(l){c("name",s.name??""),c("required",s.required??!1),c("isStatic",s.isStatic??!1),c("regex",s.regex??""),c("regexDescription",s.regexDescription??""),c("description",s.description??""),c("valueType",s.valueType??0);const a=s.valueType!==void 0&&s.valueType!==null?s.valueType.toString():"0";b([a])}},[l,s,c]),u.useEffect(()=>{i(!0)},[]),e.jsx(B,{open:l,onOpenChange:A,children:e.jsxs(E,{children:[e.jsx(G,{children:e.jsxs(L,{children:["Update a Claim Type: ",s.name]})}),e.jsxs("form",{onSubmit:o(I),className:"mt-2",onKeyDown:a=>{a.key==="Enter"&&a.target instanceof HTMLInputElement&&(a.preventDefault(),o(I)())},children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(z,{children:[e.jsx(p,{label:"Name",description:"The name of the claim",children:e.jsx(y,{required:!0,...m("name"),defaultValue:s.name??"",placeholder:"Claim Name"})}),e.jsx(p,{label:"Required",description:"Whether the claim is required",children:e.jsx(w,{checked:T,onCheckedChange:a=>{g(!!a),c("required",!!a)}})}),e.jsx(p,{label:"Is Static",description:"Whether the claim is static",children:e.jsx(w,{checked:j,onCheckedChange:a=>{v(!!a),c("isStatic",!!a)}})}),e.jsx(p,{label:"Regex",description:"The regex for the claim",children:e.jsx(y,{...m("regex"),defaultValue:s.regex??"",placeholder:"Regex"})}),e.jsx(p,{label:"Regex Description",description:"The description for the regex",children:e.jsx(y,{...m("regexDescription"),defaultValue:s.regexDescription??"",placeholder:"Regex Description"})}),e.jsx(p,{label:"Description",description:"The description for the claim",children:e.jsx(y,{required:!0,...m("description"),defaultValue:s.description??"",placeholder:"Description"})}),e.jsx(p,{label:"Value Type",description:"The value type for the claim",children:e.jsx(W,{mode:"single",options:Y,value:n,onChange:b,placeholder:"Select value type",maxHeight:300,name:"valueType",register:m,valueAsNumber:!0})})]})}),e.jsxs(J,{className:"mt-5",children:[e.jsx(f,{variant:"ghost",onClick:a=>{a.preventDefault(),i(!1)},disabled:D.isPending,type:"button",children:"Cancel"}),e.jsx(f,{type:"submit",disabled:D.isPending,children:D.isPending?"Saving...":"Save"})]})]})]})})},Re=()=>{const{toast:s}=F(),t=M(),[r,l]=u.useState(""),[i,h]=u.useState([]),[d,o]=u.useState(),[m,c]=u.useState({pageIndex:0,pageSize:10}),[T,g]=u.useState([{id:"name",desc:!1}]),{isLoading:j,data:v}=Ae(m.pageIndex,m.pageSize,i),b=ke((x,S,C)=>{o({dataId:x,dataEdit:S,dialogType:C})}),D=x=>{l(x);const C=[...i.filter(R=>R.fieldName!=="name")];x&&C.push({fieldName:"name",operator:"Contains",value:x});const k=JSON.stringify(i),V=JSON.stringify(C);k!==V&&(h(C),c(R=>({...R,pageIndex:0})))},I=x=>{c(x)},A=x=>{g(x),c(S=>({...S,pageIndex:0}))},a=()=>{t.invalidateQueries({queryKey:[N.GetIdentityClaimTypes]}),setTimeout(()=>{s({title:"Data refreshed",description:"The claims list has been refreshed.",variant:"success"})},100)};if(j)return e.jsx(ye,{rowCount:m.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const q=v?.items??[],_=v?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4",children:e.jsx(ve,{title:"Claims Types",columns:b,data:q,totalCount:_,isLoading:j,manualPagination:!0,manualSorting:!0,pageSize:m.pageSize,onPaginationChange:I,onSortingChange:A,sortingState:T,onSearch:D,searchValue:r,customFilterbar:x=>e.jsx(be,{...x,activeFilters:i,onServerFilter:S=>{const C=JSON.stringify(i),k=JSON.stringify(S);C!==k&&(h(S),c(V=>({...V,pageIndex:0})))}}),hideDefaultFilterbar:!0,onRefresh:a,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(Fe,{})}})}),d&&d.dialogType==="edit"&&e.jsx(Ve,{dataId:d.dataId,dataEdit:d.dataEdit,onDismiss:()=>{t.invalidateQueries({queryKey:[N.GetIdentityClaimTypes]}),o(null)}}),d&&d.dialogType==="delete"&&e.jsx(Ie,{dataId:d.dataId,onDismiss:()=>{t.invalidateQueries({queryKey:[N.GetIdentityClaimTypes]}),o(null)}})]})};function _e(){return e.jsxs(le,{children:[e.jsx(oe,{title:"Claim"}),e.jsx(Re,{})]})}export{_e as default};
//# sourceMappingURL=claim-lPDCWpo5.js.map
