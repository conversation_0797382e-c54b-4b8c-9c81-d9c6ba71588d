# Troubleshooting Production Migration Issues

## Overview
This document provides solutions for common issues encountered during production database migration in the GitLab CI/CD pipeline.

## Common Issues and Solutions

### 1. Pod Stuck in "ContainerCreating" Status

**Symptoms:**
- Migration job pod remains in "ContainerCreating" status for extended periods
- <PERSON><PERSON> never transitions to "Running" state
- GitLab CI pipeline times out waiting for pod to start

**Common Causes:**
1. **PersistentVolumeClaim (PVC) not bound**
2. **Missing host directory for PersistentVolume**
3. **Node affinity issues**
4. **Missing secrets or configmaps**
5. **Image pull issues**

**Solutions:**

#### A. Check PVC Status
```bash
kubectl get pvc -n imip-identity-prod imip-identity-data-protection
kubectl describe pvc -n imip-identity-prod imip-identity-data-protection
```

#### B. Ensure Host Directory Exists
The PersistentVolume uses a hostPath that must exist on the target node:
```bash
# Run on the production node (imprdapp27)
sudo mkdir -p /mnt/data/identity-data-protection
sudo chmod 755 /mnt/data/identity-data-protection
```

#### C. Check Node Availability
```bash
kubectl get nodes
kubectl describe node imprdapp27
```

#### D. Verify Required Resources
```bash
# Check secrets
kubectl get secret -n imip-identity-prod imip-identity-secrets
kubectl get secret -n imip-identity-prod imip-identity-certificate
kubectl get secret -n imip-identity-prod gitlab-registry-credentials

# Check configmap
kubectl get configmap -n imip-identity-prod imip-identity-config

# Check PV
kubectl get pv imip-identity-data-protection-pv-prod
```

### 2. Image Pull Issues

**Symptoms:**
- Pod shows "ImagePullBackOff" or "ErrImagePull" status
- Cannot pull Docker image from GitLab registry

**Solutions:**

#### A. Verify Registry Credentials
```bash
kubectl get secret gitlab-registry-credentials -n imip-identity-prod -o yaml
```

#### B. Test Image Pull Manually
```bash
# On the runner machine
echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
docker pull $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA
```

#### C. Recreate Registry Secret
```bash
kubectl delete secret gitlab-registry-credentials -n imip-identity-prod
kubectl create secret docker-registry gitlab-registry-credentials \
  --namespace=imip-identity-prod \
  --docker-server=registry.gitlab.com \
  --docker-username=$CI_REGISTRY_USER \
  --docker-password=$GITLAB_REGISTRY_TOKEN \
  --docker-email=$GITLAB_USER_EMAIL
```

### 3. Volume Mount Issues

**Symptoms:**
- Pod fails to start due to volume mount errors
- "FailedMount" events in pod description

**Solutions:**

#### A. Check Volume Configuration
```bash
kubectl describe pod -n imip-identity-prod -l app=imip-identity-db-migrator
```

#### B. Verify PV Node Affinity
Ensure the PV is configured to use the correct node:
```yaml
nodeAffinity:
  required:
    nodeSelectorTerms:
      - matchExpressions:
          - key: kubernetes.io/hostname
            operator: In
            values:
              - imprdapp27
```

### 4. Resource Constraints

**Symptoms:**
- Pod cannot be scheduled
- "Insufficient resources" errors

**Solutions:**

#### A. Check Node Resources
```bash
kubectl describe node imprdapp27 | grep -A 10 "Allocated resources"
```

#### B. Adjust Resource Requests
Modify the job configuration to use fewer resources if needed:
```yaml
resources:
  requests:
    cpu: "100m"
    memory: "128Mi"
  limits:
    cpu: "300m"
    memory: "256Mi"
```

## Automated Fix Script

Use the provided script to automatically check and fix common issues:

```bash
chmod +x scripts/fix-prod-migration.sh
./scripts/fix-prod-migration.sh
```

## Manual Debugging Steps

### 1. Check Pod Status
```bash
kubectl get pods -n imip-identity-prod -l app=imip-identity-db-migrator
```

### 2. Get Pod Details
```bash
POD_NAME=$(kubectl get pods -n imip-identity-prod -l app=imip-identity-db-migrator -o name | head -n 1)
kubectl describe $POD_NAME -n imip-identity-prod
```

### 3. Check Pod Logs
```bash
kubectl logs $POD_NAME -n imip-identity-prod
```

### 4. Check Events
```bash
kubectl get events -n imip-identity-prod --sort-by='.lastTimestamp'
```

### 5. Clean Up Failed Jobs
```bash
kubectl delete jobs -n imip-identity-prod -l app=imip-identity-db-migrator
```

## Prevention

### 1. Pre-deployment Checks
- Ensure all required secrets and configmaps exist
- Verify node availability and resources
- Test image accessibility
- Confirm directory structure on target nodes

### 2. Monitoring
- Set up alerts for pod failures
- Monitor resource usage on production nodes
- Track PVC binding status

### 3. Regular Maintenance
- Clean up old completed jobs
- Monitor disk space on nodes
- Update registry credentials as needed

## Contact

For additional support, check the GitLab CI logs and contact the DevOps team with:
- Pod name and namespace
- Error messages from `kubectl describe pod`
- Recent events from the namespace
- Node resource status
