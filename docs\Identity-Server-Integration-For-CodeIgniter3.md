# Identity Server Integration Guide for CodeIgniter 3 Applications

This guide provides simplified instructions for integrating an ABP Framework Identity Server with a CodeIgniter 3 application, focusing on essential components.

## Overview

ABP Framework's Identity Server provides centralized authentication and authorization. This guide will help you integrate your CodeIgniter 3 application with an existing ABP Identity Server for:

- User authentication
- Token validation
- Permission-based authorization

## Prerequisites

- An existing ABP Framework application with Identity Server configured
- A CodeIgniter 3 application
- PHP 5.6 or later
- Composer

## Implementation Steps

### 1. Install Required Packages

First, install the necessary packages for JWT handling:

```bash
composer require firebase/php-jwt
composer require guzzlehttp/guzzle
```

### 2. Create a JWT Helper

Create a new helper file `application/helpers/jwt_helper.php`:

```php
<?php defined('BASEPATH') OR exit('No direct script access allowed');

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Validate a JWT token against the Identity Server
 *
 * @param string $token The JWT token to validate
 * @return object|null The JWT payload if valid, null otherwise
 */
function validate_token($token) {
    $CI =& get_instance();
    $CI->load->config('identity_server');
    
    try {
        // Get the JWKS from the Identity Server
        $jwksUri = $CI->config->item('identity_server_url') . '/.well-known/openid-configuration/jwks';
        
        // Use cache to avoid frequent requests
        $cacheKey = 'identity_server_jwks';
        $jwks = $CI->cache->get($cacheKey);
        
        if ($jwks === FALSE) {
            $client = new Client();
            $response = $client->get($jwksUri);
            $jwks = json_decode($response->getBody(), true);
            
            // Cache the JWKS for 1 hour
            $CI->cache->save($cacheKey, $jwks, 3600);
        }
        
        if (empty($jwks) || !isset($jwks['keys']) || empty($jwks['keys'])) {
            return null;
        }
        
        // Parse the token header to get the key ID (kid)
        $tokenParts = explode('.', $token);
        if (count($tokenParts) !== 3) {
            return null;
        }
        
        $header = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $tokenParts[0])), true);
        if (!isset($header['kid'])) {
            return null;
        }
        
        // Find the matching key in the JWKS
        $key = null;
        foreach ($jwks['keys'] as $jwk) {
            if ($jwk['kid'] === $header['kid']) {
                $key = $jwk;
                break;
            }
        }
        
        if (!$key) {
            return null;
        }
        
        // Convert the JWK to a key that can be used with the JWT library
        $pem = jwk_to_pem($key);
        if (!$pem) {
            return null;
        }
        
        // Decode and validate the token
        $payload = JWT::decode($token, new Key($pem, $header['alg']));
        
        // Validate the audience and issuer
        if (!isset($payload->aud) || !in_array($CI->config->item('identity_server_audience'), (array)$payload->aud)) {
            return null;
        }
        
        if (!isset($payload->iss) || $payload->iss !== $CI->config->item('identity_server_url')) {
            return null;
        }
        
        // Check if the token is expired
        if (!isset($payload->exp) || $payload->exp < time()) {
            return null;
        }
        
        return $payload;
    } catch (Exception $e) {
        log_message('error', 'JWT validation error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Convert a JWK to a PEM format key
 *
 * @param array $jwk The JWK to convert
 * @return string|null The PEM key if successful, null otherwise
 */
function jwk_to_pem($jwk) {
    if ($jwk['kty'] !== 'RSA' || !isset($jwk['n']) || !isset($jwk['e'])) {
        return null;
    }
    
    // This is a simplified implementation
    // For production, use a proper JWK to PEM converter library
    $modulus = JWT::urlsafeB64Decode($jwk['n']);
    $exponent = JWT::urlsafeB64Decode($jwk['e']);
    
    $modulus = bin2hex($modulus);
    $exponent = bin2hex($exponent);
    
    // Use OpenSSL to create a public key from modulus and exponent
    $modulus = pack('H*', $modulus);
    $exponent = pack('H*', $exponent);
    
    $rsa = [
        'modulus' => $modulus,
        'exponent' => $exponent
    ];
    
    $rsaKey = openssl_pkey_new([
        'rsa' => [
            'n' => $rsa['modulus'],
            'e' => $rsa['exponent']
        ]
    ]);
    
    if ($rsaKey === false) {
        return null;
    }
    
    $details = openssl_pkey_get_details($rsaKey);
    return $details['key'];
}

/**
 * Get user permissions from the Identity Server
 *
 * @param string $token The JWT token to use for authentication
 * @return array The user's permissions
 */
function get_user_permissions($token) {
    $CI =& get_instance();
    $CI->load->config('identity_server');
    
    try {
        $client = new Client([
            'base_uri' => $CI->config->item('identity_server_url'),
            'headers' => [
                'Authorization' => 'Bearer ' . $token
            ]
        ]);
        
        $response = $client->get('/api/abp/application-configuration');
        $appConfig = json_decode($response->getBody(), true);
        
        if (isset($appConfig['auth']['grantedPolicies'])) {
            return $appConfig['auth']['grantedPolicies'];
        }
        
        return [];
    } catch (Exception $e) {
        log_message('error', 'Error getting user permissions: ' . $e->getMessage());
        return [];
    }
}

/**
 * Check if the user has a specific permission
 *
 * @param string $permission The permission to check
 * @param array $permissions The user's permissions
 * @return bool True if the user has the permission, false otherwise
 */
function has_permission($permission, $permissions) {
    return isset($permissions[$permission]) && $permissions[$permission] === true;
}
```

### 3. Create an Authentication Library

Create a new library file `application/libraries/Auth.php`:

```php
<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Auth {
    protected $CI;
    protected $token;
    protected $user;
    protected $permissions = [];
    
    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->helper('jwt');
        $this->CI->load->config('identity_server');
    }
    
    /**
     * Authenticate the user using a JWT token
     *
     * @param string $token The JWT token
     * @return bool True if authentication was successful, false otherwise
     */
    public function authenticate($token = null) {
        if ($token === null) {
            // Try to get the token from the Authorization header
            $headers = $this->CI->input->request_headers();
            
            if (isset($headers['Authorization'])) {
                $auth = $headers['Authorization'];
                if (strpos($auth, 'Bearer ') === 0) {
                    $token = substr($auth, 7);
                }
            }
        }
        
        if (empty($token)) {
            return false;
        }
        
        $payload = validate_token($token);
        if ($payload === null) {
            return false;
        }
        
        $this->token = $token;
        $this->user = $payload;
        $this->permissions = get_user_permissions($token);
        
        return true;
    }
    
    /**
     * Get the authenticated user
     *
     * @return object|null The authenticated user, or null if not authenticated
     */
    public function user() {
        return $this->user;
    }
    
    /**
     * Check if the user is authenticated
     *
     * @return bool True if the user is authenticated, false otherwise
     */
    public function is_authenticated() {
        return $this->user !== null;
    }
    
    /**
     * Check if the user has a specific permission
     *
     * @param string $permission The permission to check
     * @return bool True if the user has the permission, false otherwise
     */
    public function has_permission($permission) {
        return has_permission($permission, $this->permissions);
    }
    
    /**
     * Get all user permissions
     *
     * @return array The user's permissions
     */
    public function get_permissions() {
        return $this->permissions;
    }
}
```

### 4. Create an Authentication Middleware

Create a new hook file `application/hooks/AuthHook.php`:

```php
<?php defined('BASEPATH') OR exit('No direct script access allowed');

class AuthHook {
    protected $CI;
    
    public function __construct() {
        $this->CI =& get_instance();
    }
    
    /**
     * Check if the user is authenticated
     */
    public function auth_check() {
        // Skip authentication for public routes
        $public_routes = [
            'auth/login',
            'auth/token'
        ];
        
        $uri = $this->CI->uri->uri_string();
        
        if (in_array($uri, $public_routes)) {
            return;
        }
        
        $this->CI->load->library('auth');
        
        if (!$this->CI->auth->authenticate()) {
            // User is not authenticated
            $this->send_unauthorized_response();
            exit;
        }
    }
    
    /**
     * Send an unauthorized response
     */
    protected function send_unauthorized_response() {
        $this->CI->output->set_status_header(401);
        $this->CI->output->set_content_type('application/json');
        $this->CI->output->set_output(json_encode([
            'error' => [
                'code' => 'Unauthorized',
                'message' => 'You are not authenticated',
                'details' => 'Please provide a valid authentication token'
            ]
        ]));
    }
}
```

### 5. Create a Permission Middleware

Create a new hook file `application/hooks/PermissionHook.php`:

```php
<?php defined('BASEPATH') OR exit('No direct script access allowed');

class PermissionHook {
    protected $CI;
    protected $route_permissions = [
        'products/create' => 'YourApp.Products.Create',
        'products/update' => 'YourApp.Products.Update',
        'products/delete' => 'YourApp.Products.Delete'
    ];
    
    public function __construct() {
        $this->CI =& get_instance();
    }
    
    /**
     * Check if the user has permission for the current route
     */
    public function permission_check() {
        $uri = $this->CI->uri->uri_string();
        
        // Skip permission check if no permission is required for this route
        if (!isset($this->route_permissions[$uri])) {
            return;
        }
        
        $this->CI->load->library('auth');
        
        // Check if the user has the required permission
        $permission = $this->route_permissions[$uri];
        if (!$this->CI->auth->has_permission($permission)) {
            // User does not have the required permission
            $this->send_forbidden_response($permission);
            exit;
        }
    }
    
    /**
     * Send a forbidden response
     *
     * @param string $permission The permission that was denied
     */
    protected function send_forbidden_response($permission) {
        $this->CI->output->set_status_header(403);
        $this->CI->output->set_content_type('application/json');
        $this->CI->output->set_output(json_encode([
            'error' => [
                'code' => 'Forbidden',
                'message' => 'You do not have permission to access this resource',
                'details' => "The required permission '{$permission}' is not granted for this operation"
            ]
        ]));
    }
}
```

### 6. Configure the Identity Server

Create a new config file `application/config/identity_server.php`:

```php
<?php defined('BASEPATH') OR exit('No direct script access allowed');

$config['identity_server_url'] = 'https://your-abp-identity-server.com';
$config['identity_server_audience'] = 'YourApiResourceName';
$config['identity_server_client_id'] = 'your_client_id';
$config['identity_server_client_secret'] = 'your_client_secret';
$config['identity_server_scope'] = 'YourApiResourceName';
```

### 7. Configure Hooks

Update your `application/config/hooks.php` file:

```php
<?php defined('BASEPATH') OR exit('No direct script access allowed');

// Authentication hook
$hook['post_controller_constructor'][] = [
    'class'    => 'AuthHook',
    'function' => 'auth_check',
    'filename' => 'AuthHook.php',
    'filepath' => 'hooks'
];

// Permission hook
$hook['post_controller_constructor'][] = [
    'class'    => 'PermissionHook',
    'function' => 'permission_check',
    'filename' => 'PermissionHook.php',
    'filepath' => 'hooks'
];
```

### 8. Create an Authentication Controller

Create a new controller file `application/controllers/Auth.php`:

```php
<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Auth extends CI_Controller {
    public function __construct() {
        parent::__construct();
        $this->load->config('identity_server');
        $this->load->library('auth');
    }
    
    /**
     * Get a token from the Identity Server
     */
    public function token() {
        // Validate the request
        $this->form_validation->set_rules('username', 'Username', 'required');
        $this->form_validation->set_rules('password', 'Password', 'required');
        
        if ($this->form_validation->run() === FALSE) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode([
                'error' => [
                    'code' => 'ValidationFailed',
                    'message' => 'Validation failed',
                    'details' => validation_errors()
                ]
            ]));
            return;
        }
        
        try {
            $client = new GuzzleHttp\Client();
            
            $response = $client->post($this->config->item('identity_server_url') . '/connect/token', [
                'form_params' => [
                    'client_id' => $this->config->item('identity_server_client_id'),
                    'client_secret' => $this->config->item('identity_server_client_secret'),
                    'grant_type' => 'password',
                    'username' => $this->input->post('username'),
                    'password' => $this->input->post('password'),
                    'scope' => $this->config->item('identity_server_scope')
                ]
            ]);
            
            $this->output->set_content_type('application/json');
            $this->output->set_output($response->getBody());
        } catch (Exception $e) {
            $this->output->set_status_header(500);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode([
                'error' => [
                    'code' => 'AuthenticationFailed',
                    'message' => 'Failed to authenticate with the Identity Server',
                    'details' => $e->getMessage()
                ]
            ]));
        }
    }
    
    /**
     * Get the current user
     */
    public function user() {
        if (!$this->auth->is_authenticated()) {
            $this->output->set_status_header(401);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode([
                'error' => [
                    'code' => 'Unauthorized',
                    'message' => 'You are not authenticated',
                    'details' => 'Please provide a valid authentication token'
                ]
            ]));
            return;
        }
        
        $user = $this->auth->user();
        $permissions = $this->auth->get_permissions();
        
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode([
            'id' => $user->sub,
            'name' => $user->name ?? 'Unknown',
            'email' => $user->email ?? '',
            'permissions' => $permissions
        ]));
    }
}
```

### 9. Create a Sample Protected Controller

Create a new controller file `application/controllers/Products.php`:

```php
<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Products extends CI_Controller {
    public function __construct() {
        parent::__construct();
        $this->load->library('auth');
    }
    
    /**
     * Get all products
     */
    public function index() {
        // Sample data
        $products = [
            ['id' => 1, 'name' => 'Product 1', 'price' => 10.99],
            ['id' => 2, 'name' => 'Product 2', 'price' => 19.99],
            ['id' => 3, 'name' => 'Product 3', 'price' => 5.99]
        ];
        
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($products));
    }
    
    /**
     * Create a new product
     * Requires the YourApp.Products.Create permission
     */
    public function create() {
        // Validate the request
        $this->form_validation->set_rules('name', 'Name', 'required');
        $this->form_validation->set_rules('price', 'Price', 'required|numeric');
        
        if ($this->form_validation->run() === FALSE) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode([
                'error' => [
                    'code' => 'ValidationFailed',
                    'message' => 'Validation failed',
                    'details' => validation_errors()
                ]
            ]));
            return;
        }
        
        // In a real application, you would save the product to the database
        $product = [
            'id' => 4,
            'name' => $this->input->post('name'),
            'price' => $this->input->post('price')
        ];
        
        $this->output->set_status_header(201);
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($product));
    }
    
    /**
     * Update a product
     * Requires the YourApp.Products.Update permission
     */
    public function update($id) {
        // Validate the request
        $this->form_validation->set_rules('name', 'Name', 'required');
        $this->form_validation->set_rules('price', 'Price', 'required|numeric');
        
        if ($this->form_validation->run() === FALSE) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode([
                'error' => [
                    'code' => 'ValidationFailed',
                    'message' => 'Validation failed',
                    'details' => validation_errors()
                ]
            ]));
            return;
        }
        
        // In a real application, you would update the product in the database
        $product = [
            'id' => $id,
            'name' => $this->input->post('name'),
            'price' => $this->input->post('price')
        ];
        
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($product));
    }
    
    /**
     * Delete a product
     * Requires the YourApp.Products.Delete permission
     */
    public function delete($id) {
        // In a real application, you would delete the product from the database
        
        $this->output->set_status_header(204);
    }
}
```

### 10. Configure Routes

Update your `application/config/routes.php` file:

```php
<?php defined('BASEPATH') OR exit('No direct script access allowed');

// Authentication routes
$route['auth/token'] = 'auth/token';
$route['auth/user'] = 'auth/user';

// Product routes
$route['products'] = 'products/index';
$route['products/create'] = 'products/create';
$route['products/update/(:num)'] = 'products/update/$1';
$route['products/delete/(:num)'] = 'products/delete/$1';
```

## Testing the Integration

### 1. Obtaining a Token from the ABP Identity Server

You can use your CodeIgniter application's `/auth/token` endpoint to obtain a token:

```bash
curl -X POST "http://your-codeigniter-app.com/auth/token" \
  -d "username=your_username&password=your_password"
```

### 2. Making Authenticated Requests

To make an authenticated request to your API:

```bash
curl -X GET "http://your-codeigniter-app.com/products" \
  -H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IkYyNjZCQzA5RkE5..."
```

## Troubleshooting

### Common Issues and Solutions

1. **Token validation fails**
   - Ensure the Identity Server URL is correct
   - Check that the audience matches the API resource name in the ABP Identity Server
   - Verify that the token is not expired

2. **Permission checks fail**
   - Ensure the user has the required permissions in the ABP Identity Server
   - Check that the permission names match exactly between your API and the ABP Identity Server

3. **Cannot connect to the ABP Identity Server**
   - Check network connectivity
   - Ensure the ABP Identity Server is running
   - Verify that CORS is configured correctly on the ABP Identity Server

## Conclusion

By following this guide, you've integrated your CodeIgniter 3 application with an ABP Framework Identity Server. Your application now supports:

- User authentication using JWT tokens
- Permission-based authorization using the ABP permission system
- Proper handling of authentication and authorization errors

This integration allows you to leverage the robust identity management features of ABP Framework's Identity Server while maintaining the flexibility of a CodeIgniter 3 application.
