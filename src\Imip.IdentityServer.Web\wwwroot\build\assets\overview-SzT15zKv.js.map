{"version": 3, "file": "overview-SzT15zKv.js", "sources": ["../../../../../frontend/node_modules/.pnpm/@tabler+icons-react@3.34.0_react@19.1.0/node_modules/@tabler/icons-react/dist/esm/icons/IconActivity.mjs", "../../../../../frontend/node_modules/.pnpm/@tabler+icons-react@3.34.0_react@19.1.0/node_modules/@tabler/icons-react/dist/esm/icons/IconAlertTriangle.mjs", "../../../../../frontend/node_modules/.pnpm/@tabler+icons-react@3.34.0_react@19.1.0/node_modules/@tabler/icons-react/dist/esm/icons/IconCircleCheck.mjs", "../../../../../frontend/node_modules/.pnpm/@tabler+icons-react@3.34.0_react@19.1.0/node_modules/@tabler/icons-react/dist/esm/icons/IconServer.mjs", "../../../../../frontend/node_modules/.pnpm/@tabler+icons-react@3.34.0_react@19.1.0/node_modules/@tabler/icons-react/dist/esm/icons/IconShield.mjs", "../../../../../frontend/node_modules/.pnpm/@tabler+icons-react@3.34.0_react@19.1.0/node_modules/@tabler/icons-react/dist/esm/icons/IconTrendingDown.mjs", "../../../../../frontend/node_modules/.pnpm/@tabler+icons-react@3.34.0_react@19.1.0/node_modules/@tabler/icons-react/dist/esm/icons/IconTrendingUp.mjs", "../../../../../frontend/node_modules/.pnpm/@tabler+icons-react@3.34.0_react@19.1.0/node_modules/@tabler/icons-react/dist/esm/icons/IconUsers.mjs", "../../../../../frontend/src/components/ui/alert.tsx", "../../../../../frontend/node_modules/.pnpm/@radix-ui+react-progress@1._fa1ba230d1dfc5fe2a0cedd9ff78309e/node_modules/@radix-ui/react-progress/dist/index.mjs", "../../../../../frontend/src/components/ui/progress.tsx", "../../../../../frontend/src/lib/hooks/useCurrentUserRoleApplications.ts", "../../../../../frontend/src/components/dashboard/UserRoleApplicationsGrid.tsx", "../../../../../frontend/src/components/dashboard/overview.tsx"], "sourcesContent": ["/**\n * @license @tabler/icons-react v3.34.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createReactComponent from '../createReactComponent.mjs';\n\nvar IconActivity = createReactComponent(\"outline\", \"activity\", \"IconActivity\", [[\"path\", { \"d\": \"M3 12h4l3 8l4 -16l3 8h4\", \"key\": \"svg-0\" }]]);\n\nexport { IconActivity as default };\n//# sourceMappingURL=IconActivity.mjs.map\n", "/**\n * @license @tabler/icons-react v3.34.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createReactComponent from '../createReactComponent.mjs';\n\nvar IconAlertTriangle = createReactComponent(\"outline\", \"alert-triangle\", \"IconAlertTriangle\", [[\"path\", { \"d\": \"M12 9v4\", \"key\": \"svg-0\" }], [\"path\", { \"d\": \"M10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0z\", \"key\": \"svg-1\" }], [\"path\", { \"d\": \"M12 16h.01\", \"key\": \"svg-2\" }]]);\n\nexport { IconAlertTriangle as default };\n//# sourceMappingURL=IconAlertTriangle.mjs.map\n", "/**\n * @license @tabler/icons-react v3.34.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createReactComponent from '../createReactComponent.mjs';\n\nvar IconCircleCheck = createReactComponent(\"outline\", \"circle-check\", \"IconCircleCheck\", [[\"path\", { \"d\": \"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\", \"key\": \"svg-0\" }], [\"path\", { \"d\": \"M9 12l2 2l4 -4\", \"key\": \"svg-1\" }]]);\n\nexport { IconCircleCheck as default };\n//# sourceMappingURL=IconCircleCheck.mjs.map\n", "/**\n * @license @tabler/icons-react v3.34.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createReactComponent from '../createReactComponent.mjs';\n\nvar IconServer = createReactComponent(\"outline\", \"server\", \"IconServer\", [[\"path\", { \"d\": \"M3 4m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v2a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z\", \"key\": \"svg-0\" }], [\"path\", { \"d\": \"M3 12m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v2a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z\", \"key\": \"svg-1\" }], [\"path\", { \"d\": \"M7 8l0 .01\", \"key\": \"svg-2\" }], [\"path\", { \"d\": \"M7 16l0 .01\", \"key\": \"svg-3\" }]]);\n\nexport { IconServer as default };\n//# sourceMappingURL=IconServer.mjs.map\n", "/**\n * @license @tabler/icons-react v3.34.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createReactComponent from '../createReactComponent.mjs';\n\nvar IconShield = createReactComponent(\"outline\", \"shield\", \"IconShield\", [[\"path\", { \"d\": \"M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3\", \"key\": \"svg-0\" }]]);\n\nexport { IconShield as default };\n//# sourceMappingURL=IconShield.mjs.map\n", "/**\n * @license @tabler/icons-react v3.34.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createReactComponent from '../createReactComponent.mjs';\n\nvar IconTrendingDown = createReactComponent(\"outline\", \"trending-down\", \"IconTrendingDown\", [[\"path\", { \"d\": \"M3 7l6 6l4 -4l8 8\", \"key\": \"svg-0\" }], [\"path\", { \"d\": \"M21 10l0 7l-7 0\", \"key\": \"svg-1\" }]]);\n\nexport { IconTrendingDown as default };\n//# sourceMappingURL=IconTrendingDown.mjs.map\n", "/**\n * @license @tabler/icons-react v3.34.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createReactComponent from '../createReactComponent.mjs';\n\nvar IconTrendingUp = createReactComponent(\"outline\", \"trending-up\", \"IconTrendingUp\", [[\"path\", { \"d\": \"M3 17l6 -6l4 4l8 -8\", \"key\": \"svg-0\" }], [\"path\", { \"d\": \"M14 7l7 0l0 7\", \"key\": \"svg-1\" }]]);\n\nexport { IconTrendingUp as default };\n//# sourceMappingURL=IconTrendingUp.mjs.map\n", "/**\n * @license @tabler/icons-react v3.34.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createReactComponent from '../createReactComponent.mjs';\n\nvar IconUsers = createReactComponent(\"outline\", \"users\", \"IconUsers\", [[\"path\", { \"d\": \"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0\", \"key\": \"svg-0\" }], [\"path\", { \"d\": \"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2\", \"key\": \"svg-1\" }], [\"path\", { \"d\": \"M16 3.13a4 4 0 0 1 0 7.75\", \"key\": \"svg-2\" }], [\"path\", { \"d\": \"M21 21v-2a4 4 0 0 0 -3 -3.85\", \"key\": \"svg-3\" }]]);\n\nexport { IconUsers as default };\n//# sourceMappingURL=IconUsers.mjs.map\n", "import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n", "\"use client\";\n\n// src/progress.tsx\nimport * as React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PROGRESS_NAME = \"Progress\";\nvar DEFAULT_MAX = 100;\nvar [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\nvar [ProgressProvider, useProgressContext] = createProgressContext(PROGRESS_NAME);\nvar Progress = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, \"Progress\"));\n    }\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, \"Progress\"));\n    }\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : void 0;\n    return /* @__PURE__ */ jsx(ProgressProvider, { scope: __scopeProgress, value, max, children: /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"aria-valuemax\": max,\n        \"aria-valuemin\": 0,\n        \"aria-valuenow\": isNumber(value) ? value : void 0,\n        \"aria-valuetext\": valueLabel,\n        role: \"progressbar\",\n        \"data-state\": getProgressState(value, max),\n        \"data-value\": value ?? void 0,\n        \"data-max\": max,\n        ...progressProps,\n        ref: forwardedRef\n      }\n    ) });\n  }\n);\nProgress.displayName = PROGRESS_NAME;\nvar INDICATOR_NAME = \"ProgressIndicator\";\nvar ProgressIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"data-state\": getProgressState(context.value, context.max),\n        \"data-value\": context.value ?? void 0,\n        \"data-max\": context.max,\n        ...indicatorProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nProgressIndicator.displayName = INDICATOR_NAME;\nfunction defaultGetValueLabel(value, max) {\n  return `${Math.round(value / max * 100)}%`;\n}\nfunction getProgressState(value, maxValue) {\n  return value == null ? \"indeterminate\" : value === maxValue ? \"complete\" : \"loading\";\n}\nfunction isNumber(value) {\n  return typeof value === \"number\";\n}\nfunction isValidMaxNumber(max) {\n  return isNumber(max) && !isNaN(max) && max > 0;\n}\nfunction isValidValueNumber(value, max) {\n  return isNumber(value) && !isNaN(value) && value <= max && value >= 0;\n}\nfunction getInvalidMaxError(propValue, componentName) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\nfunction getInvalidValueError(propValue, componentName) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\nvar Root = Progress;\nvar Indicator = ProgressIndicator;\nexport {\n  Indicator,\n  Progress,\n  ProgressIndicator,\n  Root,\n  createProgressScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\r\n  return (\r\n    <ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn(\r\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n      />\r\n    </ProgressPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Progress }\r\n", "import { useAppConfig } from './useAppConfig'\r\n\r\nexport type UserRoleApplication = {\r\n  applicationId: string\r\n  applicationName: string\r\n  clientUrl: string | null\r\n}\r\n\r\nexport const useCurrentUserRoleApplications = (): UserRoleApplication[] => {\r\n  const { data } = useAppConfig()\r\n  return (\r\n    (data?.extraProperties?.userRoleApplications as UserRoleApplication[]) ?? []\r\n  )\r\n} ", "import { useCurrentUserRoleApplications } from '@/lib/hooks/useCurrentUserRoleApplications'\r\nimport React from 'react'\r\n\r\nexport const UserRoleApplicationsGrid: React.FC = () => {\r\n    const apps = useCurrentUserRoleApplications()\r\n\r\n    if (!apps.length) return null\r\n\r\n    return (\r\n        <div>\r\n            <h3 className=\"text-lg font-semibold mb-2\">Your Accessible Applications</h3>\r\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\">\r\n                {apps.map((app) => (\r\n                    <a\r\n                        key={app.applicationId}\r\n                        href={app.clientUrl || undefined}\r\n                        target={app.clientUrl ? '_blank' : undefined}\r\n                        rel=\"noopener noreferrer\"\r\n                        tabIndex={0}\r\n                        aria-label={app.applicationName}\r\n                        className=\"block p-4 rounded-lg border border-gray-200 shadow hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition\"\r\n                    >\r\n                        <div className=\"font-medium truncate\">{app.applicationName}</div>\r\n                        {app.clientUrl && (\r\n                            <div className=\"text-xs text-blue-600 truncate mt-1\">{app.clientUrl}</div>\r\n                        )}\r\n                    </a>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    )\r\n} ", "import {\r\n  getApiDashboardAnalyticsHourlyActivity,\r\n  getApiDashboardClientsCount,\r\n  getApiDashboardClientsTopLogins,\r\n  getApiDashboardLoginsFailedCount,\r\n  getApiDashboardRolesDistribution,\r\n  getApiDashboardSecurityOverview,\r\n  getApiDashboardSecurityThreats,\r\n  getApiDashboardSystemHealth,\r\n  getApiDashboardUsersActivity,\r\n  getApiDashboardUsersCount,\r\n  postApiSecurityLogsMyLogs\r\n} from '@/client/sdk.gen';\r\nimport type {\r\n  ClientLoginCountDto,\r\n  HourlyActivityDto,\r\n  RoleDistributionDto,\r\n  SecurityLogDto,\r\n  SecurityOverviewDto,\r\n  SecurityThreatsDto,\r\n  SystemHealthDto,\r\n  UserActivityDto\r\n} from '@/client/types.gen';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  Card,\r\n  CardAction,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle\r\n} from '@/components/ui/card';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { useCurrentUser } from '@/lib/hooks/useCurrentUser';\r\nimport {\r\n  IconActivity,\r\n  IconAlertTriangle,\r\n  IconCircleCheck,\r\n  IconServer,\r\n  IconShield,\r\n  IconTrendingDown,\r\n  IconTrendingUp,\r\n  IconUsers\r\n} from '@tabler/icons-react';\r\nimport { useEffect, useState } from 'react';\r\nimport { UserRoleApplicationsGrid } from './UserRoleApplicationsGrid';\r\n\r\ninterface DashboardData {\r\n  usersCount: number;\r\n  clientsCount: number;\r\n  failedLoginsCount: number;\r\n  securityOverview: SecurityOverviewDto | null;\r\n  userActivity: UserActivityDto | null;\r\n  systemHealth: SystemHealthDto | null;\r\n  securityThreats: SecurityThreatsDto | null;\r\n  hourlyActivity: HourlyActivityDto[];\r\n  rolesDistribution: RoleDistributionDto[];\r\n  topClients: ClientLoginCountDto[];\r\n}\r\n\r\nexport default function OverViewPage() {\r\n  const [data, setData] = useState<DashboardData>({\r\n    usersCount: 0,\r\n    clientsCount: 0,\r\n    failedLoginsCount: 0,\r\n    securityOverview: null,\r\n    userActivity: null,\r\n    systemHealth: null,\r\n    securityThreats: null,\r\n    hourlyActivity: [],\r\n    rolesDistribution: [],\r\n    topClients: []\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const currentUser = useCurrentUser();\r\n  const isAdmin = currentUser?.roles?.includes('admin');\r\n\r\n  // --- New: Non-admin user activity widget state ---\r\n  const [userLogs, setUserLogs] = useState<SecurityLogDto[]>([]);\r\n  const [userLogsLoading, setUserLogsLoading] = useState(false);\r\n  const [userLogsError, setUserLogsError] = useState<string | null>(null);\r\n\r\n  const fetchDashboardData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const [\r\n        usersCount,\r\n        clientsCount,\r\n        failedLoginsCount,\r\n        securityOverview,\r\n        userActivity,\r\n        systemHealth,\r\n        securityThreats,\r\n        hourlyActivity,\r\n        rolesDistribution,\r\n        topClients\r\n      ] = await Promise.all([\r\n        getApiDashboardUsersCount(),\r\n        getApiDashboardClientsCount(),\r\n        getApiDashboardLoginsFailedCount(),\r\n        getApiDashboardSecurityOverview(),\r\n        getApiDashboardUsersActivity(),\r\n        getApiDashboardSystemHealth(),\r\n        getApiDashboardSecurityThreats(),\r\n        getApiDashboardAnalyticsHourlyActivity(),\r\n        getApiDashboardRolesDistribution(),\r\n        getApiDashboardClientsTopLogins()\r\n      ]);\r\n\r\n      setData({\r\n        usersCount: usersCount.data ?? 0,\r\n        clientsCount: clientsCount.data ?? 0,\r\n        failedLoginsCount: failedLoginsCount.data ?? 0,\r\n        securityOverview: securityOverview.data ?? null,\r\n        userActivity: userActivity.data ?? null,\r\n        systemHealth: systemHealth.data ?? null,\r\n        securityThreats: securityThreats.data ?? null,\r\n        hourlyActivity: hourlyActivity.data ?? [],\r\n        rolesDistribution: rolesDistribution.data ?? [],\r\n        topClients: topClients.data ?? []\r\n      });\r\n    } catch (err) {\r\n      setError('Failed to load dashboard data');\r\n      console.error('Dashboard data fetch error:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!isAdmin && currentUser?.id) {\r\n      setUserLogsLoading(true);\r\n      postApiSecurityLogsMyLogs({\r\n        body: {\r\n          action: 'Login',\r\n          startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n          endDate: new Date().toISOString(),\r\n          maxResultCount: 20,\r\n          sort: [{ field: 'CreationTime', desc: true }],\r\n        },\r\n      })\r\n        .then((res) => {\r\n          setUserLogs(res.data?.data?.items ?? []);\r\n        })\r\n        .catch(() => setUserLogsError('Failed to load your activity'))\r\n        .finally(() => setUserLogsLoading(false));\r\n    }\r\n  }, [isAdmin, currentUser?.id]);\r\n\r\n  const getThreatLevelColor = (level: string) => {\r\n    switch (level?.toLowerCase()) {\r\n      case 'low': return 'bg-green-500';\r\n      case 'medium': return 'bg-yellow-500';\r\n      case 'high': return 'bg-orange-500';\r\n      case 'critical': return 'bg-red-500';\r\n      default: return 'bg-gray-500';\r\n    }\r\n  };\r\n\r\n  // --- New: Non-admin widget rendering ---\r\n  if (!isAdmin) {\r\n    // Calculate stats\r\n    const lastLogin = userLogs.find((log) => !log.identity);\r\n    const successfulLogins = userLogs.filter((log) => !log.identity).length;\r\n    const failedLogins = userLogs.filter((log) => !!log.identity).length;\r\n    const lastLoginTime = lastLogin?.creationTime ? new Date(lastLogin.creationTime).toLocaleString() : 'N/A';\r\n    const lastLoginDevice = lastLogin?.browserInfo || 'N/A';\r\n\r\n    return (\r\n      <div className=\"flex flex-1 flex-col space-y-4\">\r\n        <h2 className=\"text-2xl font-bold tracking-tight\">Your Recent Activity</h2>\r\n        {userLogsError && (\r\n          <Alert variant=\"destructive\">\r\n            <IconAlertTriangle className=\"h-4 w-4\" />\r\n            <AlertDescription>{userLogsError}</AlertDescription>\r\n          </Alert>\r\n        )}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Last Login</CardTitle>\r\n            <CardDescription>Most recent successful login</CardDescription>\r\n          </CardHeader>\r\n          <div className=\"p-6 space-y-2\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"font-medium\">Time:</span>\r\n              <span>{userLogsLoading ? <Skeleton className=\"h-4 w-24\" /> : lastLoginTime}</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"font-medium\">Device/Browser:</span>\r\n              <span>{userLogsLoading ? <Skeleton className=\"h-4 w-24\" /> : lastLoginDevice}</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"font-medium\">Successful logins (7d):</span>\r\n              <span>{userLogsLoading ? <Skeleton className=\"h-4 w-12\" /> : successfulLogins}</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"font-medium\">Failed logins (7d):</span>\r\n              <span>{userLogsLoading ? <Skeleton className=\"h-4 w-12\" /> : failedLogins}</span>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n        <UserRoleApplicationsGrid />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex flex-1 flex-col space-y-4\">\r\n        <Alert variant=\"destructive\">\r\n          <IconAlertTriangle className=\"h-4 w-4\" />\r\n          <AlertDescription>{error}</AlertDescription>\r\n        </Alert>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className='flex flex-1 flex-col space-y-4'>\r\n      <div className='flex items-center justify-between space-y-2'>\r\n        <h2 className='text-2xl font-bold tracking-tight'>\r\n          Identity Server Dashboard 👋\r\n        </h2>\r\n        <Badge variant=\"outline\" className=\"text-sm\">\r\n          {loading ? 'Loading...' : 'Live Data'}\r\n        </Badge>\r\n      </div>\r\n\r\n      {/* Main Metrics Cards */}\r\n      <div className='*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4'>\r\n        <Card className='@container/card'>\r\n          <CardHeader>\r\n            <CardDescription className=\"flex items-center gap-2\">\r\n              <IconUsers className=\"h-4 w-4\" />\r\n              Total Users\r\n            </CardDescription>\r\n            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>\r\n              {loading ? <Skeleton className=\"h-8 w-16\" /> : data.usersCount.toLocaleString()}\r\n            </CardTitle>\r\n            <CardAction>\r\n              {!loading && data.userActivity && (\r\n                <Badge variant='outline'>\r\n                  {(data.userActivity.userRetentionRate ?? 0) > 0 ? (\r\n                    <>\r\n                      <IconTrendingUp className=\"h-3 w-3\" />\r\n                      +{data.userActivity.userRetentionRate ?? 0}%\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <IconTrendingDown className=\"h-3 w-3\" />\r\n                      {data.userActivity.userRetentionRate ?? 0}%\r\n                    </>\r\n                  )}\r\n                </Badge>\r\n              )}\r\n            </CardAction>\r\n          </CardHeader>\r\n          <CardFooter className='flex-col items-start gap-1.5 text-sm'>\r\n            <div className='line-clamp-1 flex gap-2 font-medium'>\r\n              {loading ? (\r\n                <Skeleton className=\"h-4 w-32\" />\r\n              ) : (\r\n                <>\r\n                  {data.userActivity?.newUsersToday || 0} new today\r\n                  <IconUsers className='size-4' />\r\n                </>\r\n              )}\r\n            </div>\r\n            <div className='text-muted-foreground'>\r\n              {loading ? (\r\n                <Skeleton className=\"h-4 w-24\" />\r\n              ) : (\r\n                `${data.userActivity?.activeUsersToday || 0} active today`\r\n              )}\r\n            </div>\r\n          </CardFooter>\r\n        </Card>\r\n\r\n        <Card className='@container/card'>\r\n          <CardHeader>\r\n            <CardDescription className=\"flex items-center gap-2\">\r\n              <IconShield className=\"h-4 w-4\" />\r\n              Total Clients\r\n            </CardDescription>\r\n            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>\r\n              {loading ? <Skeleton className=\"h-8 w-16\" /> : data.clientsCount.toLocaleString()}\r\n            </CardTitle>\r\n            <CardAction>\r\n              {!loading && data.securityOverview && (\r\n                <Badge variant='outline'>\r\n                  {(data.securityOverview.loginGrowthPercentage ?? 0) > 0 ? (\r\n                    <>\r\n                      <IconTrendingUp className=\"h-3 w-3\" />\r\n                      +{data.securityOverview.loginGrowthPercentage ?? 0}%\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <IconTrendingDown className=\"h-3 w-3\" />\r\n                      {data.securityOverview.loginGrowthPercentage ?? 0}%\r\n                    </>\r\n                  )}\r\n                </Badge>\r\n              )}\r\n            </CardAction>\r\n          </CardHeader>\r\n          <CardFooter className='flex-col items-start gap-1.5 text-sm'>\r\n            <div className='line-clamp-1 flex gap-2 font-medium'>\r\n              {loading ? (\r\n                <Skeleton className=\"h-4 w-32\" />\r\n              ) : (\r\n                <>\r\n                  {data.securityOverview?.totalLoginsToday || 0} logins today\r\n                  <IconActivity className='size-4' />\r\n                </>\r\n              )}\r\n            </div>\r\n            <div className='text-muted-foreground'>\r\n              {loading ? (\r\n                <Skeleton className=\"h-4 w-24\" />\r\n              ) : (\r\n                `${data.securityOverview?.successRate || 0}% success rate`\r\n              )}\r\n            </div>\r\n          </CardFooter>\r\n        </Card>\r\n\r\n        <Card className='@container/card'>\r\n          <CardHeader>\r\n            <CardDescription className=\"flex items-center gap-2\">\r\n              <IconAlertTriangle className=\"h-4 w-4\" />\r\n              Failed Logins\r\n            </CardDescription>\r\n            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>\r\n              {loading ? <Skeleton className=\"h-8 w-16\" /> : data.failedLoginsCount.toLocaleString()}\r\n            </CardTitle>\r\n            <CardAction>\r\n              {!loading && data.securityThreats && (\r\n                <Badge\r\n                  variant='outline'\r\n                  className={`${getThreatLevelColor(data.securityThreats.threatLevel || '')} text-white`}\r\n                >\r\n                  {data.securityThreats.threatLevel || 'Unknown'}\r\n                </Badge>\r\n              )}\r\n            </CardAction>\r\n          </CardHeader>\r\n          <CardFooter className='flex-col items-start gap-1.5 text-sm'>\r\n            <div className='line-clamp-1 flex gap-2 font-medium'>\r\n              {loading ? (\r\n                <Skeleton className=\"h-4 w-32\" />\r\n              ) : (\r\n                <>\r\n                  {data.securityThreats?.suspiciousActivities || 0} suspicious activities\r\n                  <IconAlertTriangle className='size-4' />\r\n                </>\r\n              )}\r\n            </div>\r\n            <div className='text-muted-foreground'>\r\n              {loading ? (\r\n                <Skeleton className=\"h-4 w-24\" />\r\n              ) : (\r\n                `${data.securityThreats?.accountLockouts || 0} account lockouts`\r\n              )}\r\n            </div>\r\n          </CardFooter>\r\n        </Card>\r\n\r\n        <Card className='@container/card'>\r\n          <CardHeader>\r\n            <CardDescription className=\"flex items-center gap-2\">\r\n              <IconServer className=\"h-4 w-4\" />\r\n              System Health\r\n            </CardDescription>\r\n            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>\r\n              {loading ? <Skeleton className=\"h-8 w-16\" /> : data.systemHealth?.systemStatus || 'Unknown'}\r\n            </CardTitle>\r\n            <CardAction>\r\n              {!loading && data.systemHealth && (\r\n                <Badge variant='outline'>\r\n                  <IconCircleCheck className=\"h-3 w-3\" />\r\n                  {data.systemHealth.uptime || 'N/A'}\r\n                </Badge>\r\n              )}\r\n            </CardAction>\r\n          </CardHeader>\r\n          <CardFooter className='flex-col items-start gap-1.5 text-sm'>\r\n            <div className='line-clamp-1 flex gap-2 font-medium'>\r\n              {loading ? (\r\n                <Skeleton className=\"h-4 w-32\" />\r\n              ) : (\r\n                <>\r\n                  Load: {data.systemHealth?.systemLoad || 'Unknown'}\r\n                  <IconActivity className='size-4' />\r\n                </>\r\n              )}\r\n            </div>\r\n            <div className='text-muted-foreground'>\r\n              {loading ? (\r\n                <Skeleton className=\"h-4 w-24\" />\r\n              ) : (\r\n                `Last backup: ${data.systemHealth?.lastBackup ? new Date(data.systemHealth.lastBackup).toLocaleDateString() : 'N/A'}`\r\n              )}\r\n            </div>\r\n          </CardFooter>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Security Overview Card */}\r\n      {!loading && data.securityOverview && (\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <IconShield className=\"h-5 w-5\" />\r\n              Security Overview\r\n            </CardTitle>\r\n            <CardDescription>\r\n              Today's security metrics and trends\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <div className=\"p-6\">\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold text-green-600\">\r\n                  {data.securityOverview.totalLoginsToday}\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Total Logins</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold text-red-600\">\r\n                  {data.securityOverview.failedLoginsToday}\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Failed Logins</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold text-blue-600\">\r\n                  {data.securityOverview.successRate}%\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Success Rate</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold text-orange-600\">\r\n                  {data.securityOverview.loginGrowthPercentage}%\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Growth</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Charts Grid */}\r\n      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7'>\r\n        <div className='col-span-4'>\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Hourly Activity</CardTitle>\r\n              <CardDescription>Login activity over the last 24 hours</CardDescription>\r\n            </CardHeader>\r\n            <div className=\"p-6\">\r\n              {loading ? (\r\n                <div className=\"space-y-2\">\r\n                  {Array.from({ length: 24 }).map((_, i) => (\r\n                    <Skeleton key={i} className=\"h-4 w-full\" />\r\n                  ))}\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  {data.hourlyActivity.map((hour) => (\r\n                    <div key={hour.hour} className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm\">{hour.hour}:00</span>\r\n                      <div className=\"flex items-center gap-4\">\r\n                        <span className=\"text-sm text-green-600\">{hour.loginCount} logins</span>\r\n                        <span className=\"text-sm text-red-600\">{hour.failedLoginCount} failed</span>\r\n                        <Progress value={hour.successRate} className=\"w-20\" />\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </Card>\r\n        </div>\r\n\r\n        <div className='col-span-4 md:col-span-3'>\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Top Clients</CardTitle>\r\n              <CardDescription>Most active applications</CardDescription>\r\n            </CardHeader>\r\n            <div className=\"p-6\">\r\n              {loading ? (\r\n                <div className=\"space-y-2\">\r\n                  {Array.from({ length: 5 }).map((_, i) => (\r\n                    <Skeleton key={i} className=\"h-8 w-full\" />\r\n                  ))}\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  {data.topClients.map((client, index) => (\r\n                    <div key={client.clientId} className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <span className=\"text-sm font-medium\">#{index + 1}</span>\r\n                        <span className=\"text-sm truncate\">{client.clientId}</span>\r\n                      </div>\r\n                      <Badge variant=\"secondary\">{client.count} logins</Badge>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </Card>\r\n        </div>\r\n\r\n        <div className='col-span-4'>\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Role Distribution</CardTitle>\r\n              <CardDescription>Users per role</CardDescription>\r\n            </CardHeader>\r\n            <div className=\"p-6\">\r\n              {loading ? (\r\n                <div className=\"space-y-2\">\r\n                  {Array.from({ length: 5 }).map((_, i) => (\r\n                    <Skeleton key={i} className=\"h-8 w-full\" />\r\n                  ))}\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  {data.rolesDistribution.map((role) => (\r\n                    <div key={role.roleName} className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm font-medium\">{role.roleName}</span>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Progress value={((role.userCount ?? 0) / data.usersCount) * 100} className=\"w-20\" />\r\n                        <Badge variant=\"outline\">{role.userCount ?? 0} users</Badge>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </Card>\r\n        </div>\r\n\r\n        <div className='col-span-4 md:col-span-3'>\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Security Recommendations</CardTitle>\r\n              <CardDescription>Based on current threats</CardDescription>\r\n            </CardHeader>\r\n            <div className=\"p-6\">\r\n              {loading ? (\r\n                <div className=\"space-y-2\">\r\n                  {Array.from({ length: 3 }).map((_, i) => (\r\n                    <Skeleton key={i} className=\"h-4 w-full\" />\r\n                  ))}\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  {data.securityThreats?.recommendations?.map((rec, index) => (\r\n                    <div key={index} className=\"flex items-start gap-2\">\r\n                      <IconAlertTriangle className=\"h-4 w-4 text-orange-500 mt-0.5\" />\r\n                      <span className=\"text-sm\">{rec}</span>\r\n                    </div>\r\n                  )) || (\r\n                      <div className=\"flex items-center gap-2 text-green-600\">\r\n                        <IconCircleCheck className=\"h-4 w-4\" />\r\n                        <span className=\"text-sm\">Security posture is good. Continue monitoring.</span>\r\n                      </div>\r\n                    )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["IconActivity", "createReactComponent", "IconAlertTriangle", "IconCircleCheck", "IconServer", "IconShield", "IconTrendingDown", "IconTrendingUp", "IconUsers", "alertVariants", "cva", "<PERSON><PERSON>", "className", "variant", "props", "jsx", "cn", "AlertDescription", "PROGRESS_NAME", "DEFAULT_MAX", "createProgressContext", "createProgressScope", "createContextScope", "ProgressProvider", "useProgressContext", "Progress", "React.forwardRef", "forwardedRef", "__scopeProgress", "valueProp", "maxProp", "getValueLabel", "defaultGetValueLabel", "progressProps", "isValidMaxNumber", "max", "isValidValueNumber", "value", "valueLabel", "isNumber", "Primitive", "getProgressState", "INDICATOR_NAME", "ProgressIndicator", "indicatorProps", "context", "maxValue", "Root", "Indicator", "ProgressPrimitive.Root", "ProgressPrimitive.Indicator", "useCurrentUserRoleApplications", "data", "useAppConfig", "UserRoleApplicationsGrid", "apps", "app", "jsxs", "OverViewPage", "setData", "useState", "loading", "setLoading", "error", "setError", "currentUser", "useCurrentUser", "isAdmin", "userLogs", "setUserLogs", "userLogsLoading", "setUserLogsLoading", "userLogsError", "setUserLogsError", "fetchDashboardData", "usersCount", "clientsCount", "failedLoginsCount", "securityOverview", "userActivity", "systemHealth", "securityThreats", "hourlyActivity", "rolesDistribution", "topClients", "getApiDashboardUsersCount", "getApiDashboardClientsCount", "getApiDashboardLoginsFailedCount", "getApiDashboardSecurityOverview", "getApiDashboardUsersActivity", "getApiDashboardSystemHealth", "getApiDashboardSecurityThreats", "getApiDashboardAnalyticsHourlyActivity", "getApiDashboardRolesDistribution", "getApiDashboardClientsTopLogins", "useEffect", "postApiSecurityLogsMyLogs", "res", "getThreatLevelColor", "level", "lastLogin", "log", "successfulLogins", "failed<PERSON>ogins", "lastLoginTime", "lastLoginDevice", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "Skeleton", "Badge", "CardAction", "Fragment", "<PERSON><PERSON><PERSON>er", "_", "i", "hour", "client", "index", "role", "rec"], "mappings": "4YAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,IAAIA,EAAeC,EAAqB,UAAW,WAAY,eAAgB,CAAC,CAAC,OAAQ,CAAE,EAAK,0BAA2B,IAAO,OAAO,CAAE,CAAC,CAAC,ECT7I;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,IAAIC,EAAoBD,EAAqB,UAAW,iBAAkB,oBAAqB,CAAC,CAAC,OAAQ,CAAE,EAAK,UAAW,IAAO,OAAS,CAAA,EAAG,CAAC,OAAQ,CAAE,EAAK,4IAA6I,IAAO,OAAS,CAAA,EAAG,CAAC,OAAQ,CAAE,EAAK,aAAc,IAAO,OAAO,CAAE,CAAC,CAAC,ECT9W;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,IAAIE,EAAkBF,EAAqB,UAAW,eAAgB,kBAAmB,CAAC,CAAC,OAAQ,CAAE,EAAK,6CAA8C,IAAO,OAAS,CAAA,EAAG,CAAC,OAAQ,CAAE,EAAK,iBAAkB,IAAO,OAAS,CAAA,CAAC,CAAC,ECT/N;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,IAAIG,GAAaH,EAAqB,UAAW,SAAU,aAAc,CAAC,CAAC,OAAQ,CAAE,EAAK,iFAAkF,IAAO,OAAS,CAAA,EAAG,CAAC,OAAQ,CAAE,EAAK,kFAAmF,IAAO,OAAO,CAAE,EAAG,CAAC,OAAQ,CAAE,EAAK,aAAc,IAAO,OAAO,CAAE,EAAG,CAAC,OAAQ,CAAE,EAAK,cAAe,IAAO,OAAS,CAAA,CAAC,CAAC,ECTvZ;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,IAAII,EAAaJ,EAAqB,UAAW,SAAU,aAAc,CAAC,CAAC,OAAQ,CAAE,EAAK,sFAAuF,IAAO,OAAO,CAAE,CAAC,CAAC,ECTnM;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,IAAIK,EAAmBL,EAAqB,UAAW,gBAAiB,mBAAoB,CAAC,CAAC,OAAQ,CAAE,EAAK,oBAAqB,IAAO,OAAS,CAAA,EAAG,CAAC,OAAQ,CAAE,EAAK,kBAAmB,IAAO,OAAS,CAAA,CAAC,CAAC,ECT1M;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,IAAIM,EAAiBN,EAAqB,UAAW,cAAe,iBAAkB,CAAC,CAAC,OAAQ,CAAE,EAAK,sBAAuB,IAAO,OAAS,CAAA,EAAG,CAAC,OAAQ,CAAE,EAAK,gBAAiB,IAAO,OAAS,CAAA,CAAC,CAAC,ECTpM;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,IAAIO,EAAYP,EAAqB,UAAW,QAAS,YAAa,CAAC,CAAC,OAAQ,CAAE,EAAK,yCAA0C,IAAO,OAAS,CAAA,EAAG,CAAC,OAAQ,CAAE,EAAK,4CAA6C,IAAO,OAAO,CAAE,EAAG,CAAC,OAAQ,CAAE,EAAK,4BAA6B,IAAO,OAAO,CAAE,EAAG,CAAC,OAAQ,CAAE,EAAK,+BAAgC,IAAO,OAAS,CAAA,CAAC,CAAC,ECJtW,MAAMQ,GAAgBC,GACpB,oOACA,CACE,SAAU,CACR,QAAS,CACP,QAAS,+BACT,YACE,mGAAA,CAEN,EACA,gBAAiB,CACf,QAAS,SAAA,CACX,CAEJ,EAEA,SAASC,EAAM,CACb,UAAAC,EACA,QAAAC,EACA,GAAGC,CACL,EAAqE,CAEjE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,QACV,KAAK,QACL,UAAWC,EAAGP,GAAc,CAAE,QAAAI,CAAS,CAAA,EAAGD,CAAS,EAClD,GAAGE,CAAA,CACN,CAEJ,CAeA,SAASG,EAAiB,CACxB,UAAAL,EACA,GAAGE,CACL,EAAgC,CAE5B,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,oBACV,UAAWC,EACT,iGACAJ,CACF,EACC,GAAGE,CAAA,CACN,CAEJ,CCxDA,IAAII,EAAgB,WAChBC,GAAc,IACd,CAACC,GAAuBC,EAAmB,EAAIC,GAAmBJ,CAAa,EAC/E,CAACK,GAAkBC,EAAkB,EAAIJ,GAAsBF,CAAa,EAC5EO,EAAWC,EAAgB,WAC7B,CAACZ,EAAOa,IAAiB,CACvB,KAAM,CACJ,gBAAAC,EACA,MAAOC,EAAY,KACnB,IAAKC,EACL,cAAAC,EAAgBC,GAChB,GAAGC,CACT,EAAQnB,GACCgB,GAAWA,IAAY,IAAOI,EAAiBJ,CAAO,EAG3D,MAAMK,EAAMD,EAAiBJ,CAAO,EAAIA,EAAUX,GAC9CU,IAAc,MAASO,EAAmBP,EAAWM,CAAG,EAG5D,MAAME,EAAQD,EAAmBP,EAAWM,CAAG,EAAIN,EAAY,KACzDS,EAAaC,EAASF,CAAK,EAAIN,EAAcM,EAAOF,CAAG,EAAI,OACjE,OAAuBpB,EAAG,IAACQ,GAAkB,CAAE,MAAOK,EAAiB,MAAAS,EAAO,IAAAF,EAAK,SAA0BpB,EAAG,IAC9GyB,EAAU,IACV,CACE,gBAAiBL,EACjB,gBAAiB,EACjB,gBAAiBI,EAASF,CAAK,EAAIA,EAAQ,OAC3C,iBAAkBC,EAClB,KAAM,cACN,aAAcG,EAAiBJ,EAAOF,CAAG,EACzC,aAAcE,GAAS,OACvB,WAAYF,EACZ,GAAGF,EACH,IAAKN,CACb,CACA,EAAO,CACP,CACA,EACAF,EAAS,YAAcP,EACvB,IAAIwB,EAAiB,oBACjBC,EAAoBjB,EAAgB,WACtC,CAACZ,EAAOa,IAAiB,CACvB,KAAM,CAAE,gBAAAC,EAAiB,GAAGgB,CAAc,EAAK9B,EACzC+B,EAAUrB,GAAmBkB,EAAgBd,CAAe,EAClE,OAAuBb,EAAG,IACxByB,EAAU,IACV,CACE,aAAcC,EAAiBI,EAAQ,MAAOA,EAAQ,GAAG,EACzD,aAAcA,EAAQ,OAAS,OAC/B,WAAYA,EAAQ,IACpB,GAAGD,EACH,IAAKjB,CACb,CACK,CACL,CACA,EACAgB,EAAkB,YAAcD,EAChC,SAASV,GAAqBK,EAAOF,EAAK,CACxC,MAAO,GAAG,KAAK,MAAME,EAAQF,EAAM,GAAG,CAAC,GACzC,CACA,SAASM,EAAiBJ,EAAOS,EAAU,CACzC,OAAOT,GAAS,KAAO,gBAAkBA,IAAUS,EAAW,WAAa,SAC7E,CACA,SAASP,EAASF,EAAO,CACvB,OAAO,OAAOA,GAAU,QAC1B,CACA,SAASH,EAAiBC,EAAK,CAC7B,OAAOI,EAASJ,CAAG,GAAK,CAAC,MAAMA,CAAG,GAAKA,EAAM,CAC/C,CACA,SAASC,EAAmBC,EAAOF,EAAK,CACtC,OAAOI,EAASF,CAAK,GAAK,CAAC,MAAMA,CAAK,GAAKA,GAASF,GAAOE,GAAS,CACtE,CAYA,IAAIU,GAAOtB,EACPuB,GAAYL,ECvFhB,SAASlB,EAAS,CAChB,UAAAb,EACA,MAAAyB,EACA,GAAGvB,CACL,EAAwD,CAEpD,OAAAC,EAAA,IAACkC,GAAA,CACC,YAAU,WACV,UAAWjC,EACT,iEACAJ,CACF,EACC,GAAGE,EAEJ,SAAAC,EAAA,IAACmC,GAAA,CACC,YAAU,qBACV,UAAU,iDACV,MAAO,CAAE,UAAW,eAAe,KAAOb,GAAS,EAAE,IAAK,CAAA,CAAA,CAC5D,CACF,CAEJ,CClBO,MAAMc,GAAiC,IAA6B,CACnE,KAAA,CAAE,KAAAC,CAAK,EAAIC,GAAa,EAE3B,OAAAD,GAAM,iBAAiB,sBAAkD,CAAC,CAE/E,ECVaE,GAAqC,IAAM,CACpD,MAAMC,EAAOJ,GAA+B,EAExC,OAACI,EAAK,cAGL,MACG,CAAA,SAAA,CAACxC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAA4B,+BAAA,QACtE,MAAI,CAAA,UAAU,uDACV,SAAKwC,EAAA,IAAKC,GACPC,EAAA,KAAC,IAAA,CAEG,KAAMD,EAAI,WAAa,OACvB,OAAQA,EAAI,UAAY,SAAW,OACnC,IAAI,sBACJ,SAAU,EACV,aAAYA,EAAI,gBAChB,UAAU,qIAEV,SAAA,CAAAzC,EAAA,IAAC,MAAI,CAAA,UAAU,uBAAwB,SAAAyC,EAAI,gBAAgB,EAC1DA,EAAI,WACDzC,EAAA,IAAC,OAAI,UAAU,sCAAuC,WAAI,SAAU,CAAA,CAAA,CAAA,EAVnEyC,EAAI,aAAA,CAahB,CACL,CAAA,CAAA,EACJ,EAvBqB,IAyB7B,EC+BA,SAAwBE,IAAe,CACrC,KAAM,CAACN,EAAMO,CAAO,EAAIC,WAAwB,CAC9C,WAAY,EACZ,aAAc,EACd,kBAAmB,EACnB,iBAAkB,KAClB,aAAc,KACd,aAAc,KACd,gBAAiB,KACjB,eAAgB,CAAC,EACjB,kBAAmB,CAAC,EACpB,WAAY,CAAA,CAAC,CACd,EACK,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAwB,IAAI,EAEhDK,EAAcC,GAAe,EAC7BC,EAAUF,GAAa,OAAO,SAAS,OAAO,EAG9C,CAACG,EAAUC,CAAW,EAAIT,EAAAA,SAA2B,CAAA,CAAE,EACvD,CAACU,EAAiBC,CAAkB,EAAIX,EAAAA,SAAS,EAAK,EACtD,CAACY,EAAeC,CAAgB,EAAIb,EAAAA,SAAwB,IAAI,EAEhEc,EAAqB,SAAY,CACjC,GAAA,CACFZ,EAAW,EAAI,EACfE,EAAS,IAAI,EAEP,KAAA,CACJW,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GACAC,GACAC,EAAA,EACE,MAAM,QAAQ,IAAI,CACpBC,GAA0B,EAC1BC,GAA4B,EAC5BC,GAAiC,EACjCC,GAAgC,EAChCC,GAA6B,EAC7BC,GAA4B,EAC5BC,GAA+B,EAC/BC,GAAuC,EACvCC,GAAiC,EACjCC,GAAgC,CAAA,CACjC,EAEOnC,EAAA,CACN,WAAYgB,EAAW,MAAQ,EAC/B,aAAcC,EAAa,MAAQ,EACnC,kBAAmBC,EAAkB,MAAQ,EAC7C,iBAAkBC,EAAiB,MAAQ,KAC3C,aAAcC,EAAa,MAAQ,KACnC,aAAcC,EAAa,MAAQ,KACnC,gBAAiBC,EAAgB,MAAQ,KACzC,eAAgBC,GAAe,MAAQ,CAAC,EACxC,kBAAmBC,GAAkB,MAAQ,CAAC,EAC9C,WAAYC,GAAW,MAAQ,CAAA,CAAC,CACjC,OACW,CACZpB,EAAS,+BAA+B,CAAA,QAExC,CACAF,EAAW,EAAK,CAAA,CAEpB,EAEAiC,EAAAA,UAAU,IAAM,CACKrB,EAAA,CACrB,EAAG,EAAE,EAELqB,EAAAA,UAAU,IAAM,CACV,CAAC5B,GAAWF,GAAa,KAC3BM,EAAmB,EAAI,EACGyB,GAAA,CACxB,KAAM,CACJ,OAAQ,QACR,UAAW,IAAI,KAAK,KAAK,IAAI,EAAI,EAAI,GAAK,GAAK,GAAK,GAAI,EAAE,YAAY,EACtE,QAAS,IAAI,KAAK,EAAE,YAAY,EAChC,eAAgB,GAChB,KAAM,CAAC,CAAE,MAAO,eAAgB,KAAM,EAAM,CAAA,CAAA,CAC9C,CACD,EACE,KAAMC,GAAQ,CACb5B,EAAY4B,EAAI,MAAM,MAAM,OAAS,CAAA,CAAE,CAAA,CACxC,EACA,MAAM,IAAMxB,EAAiB,8BAA8B,CAAC,EAC5D,QAAQ,IAAMF,EAAmB,EAAK,CAAC,EAE3C,EAAA,CAACJ,EAASF,GAAa,EAAE,CAAC,EAEvB,MAAAiC,EAAuBC,GAAkB,CACrC,OAAAA,GAAO,YAAe,EAAA,CAC5B,IAAK,MAAc,MAAA,eACnB,IAAK,SAAiB,MAAA,gBACtB,IAAK,OAAe,MAAA,gBACpB,IAAK,WAAmB,MAAA,aACxB,QAAgB,MAAA,aAAA,CAEpB,EAGA,GAAI,CAAChC,EAAS,CAEZ,MAAMiC,EAAYhC,EAAS,KAAMiC,GAAQ,CAACA,EAAI,QAAQ,EAChDC,EAAmBlC,EAAS,OAAQiC,GAAQ,CAACA,EAAI,QAAQ,EAAE,OAC3DE,EAAenC,EAAS,OAAQiC,GAAQ,CAAC,CAACA,EAAI,QAAQ,EAAE,OACxDG,EAAgBJ,GAAW,aAAe,IAAI,KAAKA,EAAU,YAAY,EAAE,eAAA,EAAmB,MAC9FK,EAAkBL,GAAW,aAAe,MAGhD,OAAA3C,EAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAAC1C,EAAA,IAAA,KAAA,CAAG,UAAU,oCAAoC,SAAoB,uBAAA,EACrEyD,GACCf,EAAA,KAAC9C,EAAM,CAAA,QAAQ,cACb,SAAA,CAACI,EAAAA,IAAAb,EAAA,CAAkB,UAAU,SAAU,CAAA,EACvCa,EAAAA,IAACE,GAAkB,SAAcuD,CAAA,CAAA,CAAA,EACnC,SAEDkC,EACC,CAAA,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAA5F,EAAAA,IAAC6F,GAAU,SAAU,YAAA,CAAA,EACrB7F,EAAAA,IAAC8F,GAAgB,SAA4B,8BAAA,CAAA,CAAA,EAC/C,EACApD,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0BACb,SAAA,CAAC1C,EAAA,IAAA,OAAA,CAAK,UAAU,cAAc,SAAK,QAAA,EACnCA,EAAAA,IAAC,QAAM,SAAkBuD,EAAAvD,EAAAA,IAAC+F,GAAS,UAAU,UAAW,CAAA,EAAKN,CAAc,CAAA,CAAA,EAC7E,EACA/C,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAC1C,EAAA,IAAA,OAAA,CAAK,UAAU,cAAc,SAAe,kBAAA,EAC7CA,EAAAA,IAAC,QAAM,SAAkBuD,EAAAvD,EAAAA,IAAC+F,GAAS,UAAU,UAAW,CAAA,EAAKL,CAAgB,CAAA,CAAA,EAC/E,EACAhD,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAC1C,EAAA,IAAA,OAAA,CAAK,UAAU,cAAc,SAAuB,0BAAA,EACrDA,EAAAA,IAAC,QAAM,SAAkBuD,EAAAvD,EAAAA,IAAC+F,GAAS,UAAU,UAAW,CAAA,EAAKR,CAAiB,CAAA,CAAA,EAChF,EACA7C,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAC1C,EAAA,IAAA,OAAA,CAAK,UAAU,cAAc,SAAmB,sBAAA,EACjDA,EAAAA,IAAC,QAAM,SAAkBuD,EAAAvD,EAAAA,IAAC+F,GAAS,UAAU,UAAW,CAAA,EAAKP,CAAa,CAAA,CAAA,CAC5E,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QACCjD,GAAyB,CAAA,CAAA,CAAA,EAC5B,CAAA,CAIJ,OAAIS,QAEC,MAAI,CAAA,UAAU,iCACb,SAACN,EAAA,KAAA9C,EAAA,CAAM,QAAQ,cACb,SAAA,CAACI,EAAAA,IAAAb,EAAA,CAAkB,UAAU,SAAU,CAAA,EACvCa,EAAAA,IAACE,GAAkB,SAAM8C,CAAA,CAAA,CAAA,CAAA,CAC3B,CACF,CAAA,EAKFN,EAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8CACb,SAAA,CAAC1C,EAAA,IAAA,KAAA,CAAG,UAAU,oCAAoC,SAElD,+BAAA,EACAA,EAAAA,IAACgG,GAAM,QAAQ,UAAU,UAAU,UAChC,SAAAlD,EAAU,aAAe,WAC5B,CAAA,CAAA,EACF,EAGAJ,EAAAA,KAAC,MAAI,CAAA,UAAU,qNACb,SAAA,CAACA,EAAAA,KAAAiD,EAAA,CAAK,UAAU,kBACd,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAClD,EAAAA,KAAAoD,EAAA,CAAgB,UAAU,0BACzB,SAAA,CAAC9F,EAAAA,IAAAP,EAAA,CAAU,UAAU,SAAU,CAAA,EAAE,aAAA,EAEnC,EACCO,EAAA,IAAA6F,EAAA,CAAU,UAAU,6DAClB,SAAU/C,EAAA9C,EAAA,IAAC+F,EAAS,CAAA,UAAU,WAAW,EAAK1D,EAAK,WAAW,iBACjE,EACCrC,MAAAiG,EAAA,CACE,SAAC,CAAAnD,GAAWT,EAAK,cAChBrC,EAAAA,IAACgG,EAAM,CAAA,QAAQ,UACX,UAAK3D,EAAA,aAAa,mBAAqB,GAAK,EAE1CK,EAAA,KAAAwD,WAAA,CAAA,SAAA,CAAClG,EAAAA,IAAAR,EAAA,CAAe,UAAU,SAAU,CAAA,EAAE,IACpC6C,EAAK,aAAa,mBAAqB,EAAE,GAAA,CAAA,CAC7C,EAGEK,EAAAA,KAAAwD,EAAA,SAAA,CAAA,SAAA,CAAClG,EAAAA,IAAAT,EAAA,CAAiB,UAAU,SAAU,CAAA,EACrC8C,EAAK,aAAa,mBAAqB,EAAE,GAAA,CAC5C,CAAA,CAEJ,CAAA,CAEJ,CAAA,CAAA,EACF,EACAK,EAAAA,KAACyD,EAAW,CAAA,UAAU,uCACpB,SAAA,CAACnG,EAAA,IAAA,MAAA,CAAI,UAAU,sCACZ,SAAA8C,QACEiD,EAAS,CAAA,UAAU,UAAW,CAAA,EAG5BrD,EAAAA,KAAAwD,EAAAA,SAAA,CAAA,SAAA,CAAA7D,EAAK,cAAc,eAAiB,EAAE,aACvCrC,EAAAA,IAACP,EAAU,CAAA,UAAU,QAAS,CAAA,CAAA,CAAA,CAChC,CAEJ,CAAA,EACCO,MAAA,MAAA,CAAI,UAAU,wBACZ,WACEA,EAAA,IAAA+F,EAAA,CAAS,UAAU,UAAW,CAAA,EAE/B,GAAG1D,EAAK,cAAc,kBAAoB,CAAC,eAE/C,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAK,EAAAA,KAACiD,EAAK,CAAA,UAAU,kBACd,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAClD,EAAAA,KAAAoD,EAAA,CAAgB,UAAU,0BACzB,SAAA,CAAC9F,EAAAA,IAAAV,EAAA,CAAW,UAAU,SAAU,CAAA,EAAE,eAAA,EAEpC,EACCU,EAAA,IAAA6F,EAAA,CAAU,UAAU,6DAClB,SAAU/C,EAAA9C,EAAA,IAAC+F,EAAS,CAAA,UAAU,WAAW,EAAK1D,EAAK,aAAa,iBACnE,EACCrC,MAAAiG,EAAA,CACE,SAAC,CAAAnD,GAAWT,EAAK,kBAChBrC,EAAAA,IAACgG,EAAM,CAAA,QAAQ,UACX,UAAK3D,EAAA,iBAAiB,uBAAyB,GAAK,EAElDK,EAAA,KAAAwD,WAAA,CAAA,SAAA,CAAClG,EAAAA,IAAAR,EAAA,CAAe,UAAU,SAAU,CAAA,EAAE,IACpC6C,EAAK,iBAAiB,uBAAyB,EAAE,GAAA,CAAA,CACrD,EAGEK,EAAAA,KAAAwD,EAAA,SAAA,CAAA,SAAA,CAAClG,EAAAA,IAAAT,EAAA,CAAiB,UAAU,SAAU,CAAA,EACrC8C,EAAK,iBAAiB,uBAAyB,EAAE,GAAA,CACpD,CAAA,CAEJ,CAAA,CAEJ,CAAA,CAAA,EACF,EACAK,EAAAA,KAACyD,EAAW,CAAA,UAAU,uCACpB,SAAA,CAACnG,EAAA,IAAA,MAAA,CAAI,UAAU,sCACZ,SAAA8C,QACEiD,EAAS,CAAA,UAAU,UAAW,CAAA,EAG5BrD,EAAAA,KAAAwD,EAAAA,SAAA,CAAA,SAAA,CAAA7D,EAAK,kBAAkB,kBAAoB,EAAE,gBAC9CrC,EAAAA,IAACf,EAAa,CAAA,UAAU,QAAS,CAAA,CAAA,CAAA,CACnC,CAEJ,CAAA,EACCe,MAAA,MAAA,CAAI,UAAU,wBACZ,WACEA,EAAA,IAAA+F,EAAA,CAAS,UAAU,UAAW,CAAA,EAE/B,GAAG1D,EAAK,kBAAkB,aAAe,CAAC,gBAE9C,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAK,EAAAA,KAACiD,EAAK,CAAA,UAAU,kBACd,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAClD,EAAAA,KAAAoD,EAAA,CAAgB,UAAU,0BACzB,SAAA,CAAC9F,EAAAA,IAAAb,EAAA,CAAkB,UAAU,SAAU,CAAA,EAAE,eAAA,EAE3C,EACCa,EAAA,IAAA6F,EAAA,CAAU,UAAU,6DAClB,SAAU/C,EAAA9C,EAAA,IAAC+F,EAAS,CAAA,UAAU,WAAW,EAAK1D,EAAK,kBAAkB,iBACxE,EACCrC,EAAA,IAAAiG,EAAA,CACE,SAAC,CAAAnD,GAAWT,EAAK,iBAChBrC,EAAA,IAACgG,EAAA,CACC,QAAQ,UACR,UAAW,GAAGb,EAAoB9C,EAAK,gBAAgB,aAAe,EAAE,CAAC,cAExE,SAAAA,EAAK,gBAAgB,aAAe,SAAA,CAAA,CAG3C,CAAA,CAAA,EACF,EACAK,EAAAA,KAACyD,EAAW,CAAA,UAAU,uCACpB,SAAA,CAACnG,EAAA,IAAA,MAAA,CAAI,UAAU,sCACZ,SAAA8C,QACEiD,EAAS,CAAA,UAAU,UAAW,CAAA,EAG5BrD,EAAAA,KAAAwD,EAAAA,SAAA,CAAA,SAAA,CAAA7D,EAAK,iBAAiB,sBAAwB,EAAE,yBACjDrC,EAAAA,IAACb,EAAkB,CAAA,UAAU,QAAS,CAAA,CAAA,CAAA,CACxC,CAEJ,CAAA,EACCa,MAAA,MAAA,CAAI,UAAU,wBACZ,WACEA,EAAA,IAAA+F,EAAA,CAAS,UAAU,UAAW,CAAA,EAE/B,GAAG1D,EAAK,iBAAiB,iBAAmB,CAAC,mBAEjD,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAK,EAAAA,KAACiD,EAAK,CAAA,UAAU,kBACd,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAClD,EAAAA,KAAAoD,EAAA,CAAgB,UAAU,0BACzB,SAAA,CAAC9F,EAAAA,IAAAX,GAAA,CAAW,UAAU,SAAU,CAAA,EAAE,eAAA,EAEpC,EACCW,EAAA,IAAA6F,EAAA,CAAU,UAAU,6DAClB,SAAU/C,EAAA9C,MAAC+F,EAAS,CAAA,UAAU,UAAW,CAAA,EAAK1D,EAAK,cAAc,cAAgB,UACpF,EACArC,EAAAA,IAACiG,GACE,SAAC,CAAAnD,GAAWT,EAAK,cAChBK,EAAAA,KAACsD,EAAM,CAAA,QAAQ,UACb,SAAA,CAAChG,EAAAA,IAAAZ,EAAA,CAAgB,UAAU,SAAU,CAAA,EACpCiD,EAAK,aAAa,QAAU,KAAA,CAAA,CAC/B,CAEJ,CAAA,CAAA,EACF,EACAK,EAAAA,KAACyD,EAAW,CAAA,UAAU,uCACpB,SAAA,CAACnG,EAAA,IAAA,MAAA,CAAI,UAAU,sCACZ,SAAA8C,QACEiD,EAAS,CAAA,UAAU,UAAW,CAAA,EAE7BrD,EAAAA,KAAAwD,EAAAA,SAAA,CAAA,SAAA,CAAA,SACO7D,EAAK,cAAc,YAAc,UACxCrC,EAAAA,IAACf,EAAa,CAAA,UAAU,QAAS,CAAA,CAAA,CAAA,CACnC,CAEJ,CAAA,EACAe,EAAAA,IAAC,OAAI,UAAU,wBACZ,WACEA,EAAAA,IAAA+F,EAAA,CAAS,UAAU,UAAA,CAAW,EAE/B,gBAAgB1D,EAAK,cAAc,WAAa,IAAI,KAAKA,EAAK,aAAa,UAAU,EAAE,qBAAuB,KAAK,EAEvH,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGC,CAACS,GAAWT,EAAK,yBACfsD,EACC,CAAA,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAClD,EAAAA,KAAAmD,EAAA,CAAU,UAAU,0BACnB,SAAA,CAAC7F,EAAAA,IAAAV,EAAA,CAAW,UAAU,SAAU,CAAA,EAAE,mBAAA,EAEpC,EACAU,EAAAA,IAAC8F,GAAgB,SAEjB,qCAAA,CAAA,CAAA,EACF,QACC,MAAI,CAAA,UAAU,MACb,SAACpD,EAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAAA1C,MAAC,MAAI,CAAA,UAAU,oCACZ,SAAAqC,EAAK,iBAAiB,iBACzB,EACCrC,EAAA,IAAA,MAAA,CAAI,UAAU,gCAAgC,SAAY,cAAA,CAAA,CAAA,EAC7D,EACA0C,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAA1C,MAAC,MAAI,CAAA,UAAU,kCACZ,SAAAqC,EAAK,iBAAiB,kBACzB,EACCrC,EAAA,IAAA,MAAA,CAAI,UAAU,gCAAgC,SAAa,eAAA,CAAA,CAAA,EAC9D,EACA0C,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACZ,SAAA,CAAAL,EAAK,iBAAiB,YAAY,GAAA,EACrC,EACCrC,EAAA,IAAA,MAAA,CAAI,UAAU,gCAAgC,SAAY,cAAA,CAAA,CAAA,EAC7D,EACA0C,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qCACZ,SAAA,CAAAL,EAAK,iBAAiB,sBAAsB,GAAA,EAC/C,EACCrC,EAAA,IAAA,MAAA,CAAI,UAAU,gCAAgC,SAAM,QAAA,CAAA,CAAA,CACvD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,EAIF0C,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAA1C,MAAC,MAAI,CAAA,UAAU,aACb,SAAA0C,EAAA,KAACiD,EACC,CAAA,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAA5F,EAAAA,IAAC6F,GAAU,SAAe,iBAAA,CAAA,EAC1B7F,EAAAA,IAAC8F,GAAgB,SAAqC,uCAAA,CAAA,CAAA,EACxD,QACC,MAAI,CAAA,UAAU,MACZ,SACChD,EAAA9C,EAAA,IAAC,OAAI,UAAU,YACZ,SAAM,MAAA,KAAK,CAAE,OAAQ,GAAI,EAAE,IAAI,CAACoG,EAAGC,IACjCrG,EAAA,IAAA+F,EAAA,CAAiB,UAAU,YAAb,EAAAM,CAA0B,CAC1C,EACH,QAEC,MAAI,CAAA,UAAU,YACZ,SAAAhE,EAAK,eAAe,IAAKiE,GACvB5D,OAAA,MAAA,CAAoB,UAAU,oCAC7B,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,UAAW,SAAA,CAAK4D,EAAA,KAAK,KAAA,EAAG,EACxC5D,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,yBAA0B,SAAA,CAAK4D,EAAA,WAAW,SAAA,EAAO,EACjE5D,EAAAA,KAAC,OAAK,CAAA,UAAU,uBAAwB,SAAA,CAAK4D,EAAA,iBAAiB,SAAA,EAAO,QACpE5F,EAAS,CAAA,MAAO4F,EAAK,YAAa,UAAU,MAAO,CAAA,CAAA,CACtD,CAAA,CAAA,CAAA,EANQA,EAAK,IAOf,CACD,CACH,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECtG,MAAA,MAAA,CAAI,UAAU,2BACb,gBAAC2F,EACC,CAAA,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAA5F,EAAAA,IAAC6F,GAAU,SAAW,aAAA,CAAA,EACtB7F,EAAAA,IAAC8F,GAAgB,SAAwB,0BAAA,CAAA,CAAA,EAC3C,QACC,MAAI,CAAA,UAAU,MACZ,SACChD,EAAA9C,EAAA,IAAC,OAAI,UAAU,YACZ,SAAM,MAAA,KAAK,CAAE,OAAQ,CAAA,CAAG,EAAE,IAAI,CAACoG,EAAGC,IAChCrG,MAAA+F,EAAA,CAAiB,UAAU,cAAbM,CAA0B,CAC1C,CACH,CAAA,QAEC,MAAI,CAAA,UAAU,YACZ,SAAKhE,EAAA,WAAW,IAAI,CAACkE,EAAQC,IAC3B9D,OAAA,MAAA,CAA0B,UAAU,oCACnC,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0BACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,sBAAsB,SAAA,CAAA,IAAE8D,EAAQ,CAAA,EAAE,EACjDxG,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAoB,WAAO,QAAS,CAAA,CAAA,EACtD,EACA0C,EAAAA,KAACsD,EAAM,CAAA,QAAQ,YAAa,SAAA,CAAOO,EAAA,MAAM,SAAA,CAAO,CAAA,CAAA,CAAA,EALxCA,EAAO,QAMjB,CACD,CACH,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECvG,MAAA,MAAA,CAAI,UAAU,aACb,gBAAC2F,EACC,CAAA,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAA5F,EAAAA,IAAC6F,GAAU,SAAiB,mBAAA,CAAA,EAC5B7F,EAAAA,IAAC8F,GAAgB,SAAc,gBAAA,CAAA,CAAA,EACjC,QACC,MAAI,CAAA,UAAU,MACZ,SACChD,EAAA9C,EAAA,IAAC,OAAI,UAAU,YACZ,SAAM,MAAA,KAAK,CAAE,OAAQ,EAAG,EAAE,IAAI,CAACoG,EAAGC,IAChCrG,EAAA,IAAA+F,EAAA,CAAiB,UAAU,YAAb,EAAAM,CAA0B,CAC1C,EACH,QAEC,MAAI,CAAA,UAAU,YACZ,SAAAhE,EAAK,kBAAkB,IAAKoE,GAC1B/D,OAAA,MAAA,CAAwB,UAAU,oCACjC,SAAA,CAAA1C,EAAA,IAAC,OAAK,CAAA,UAAU,sBAAuB,SAAAyG,EAAK,SAAS,EACrD/D,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAC1C,EAAAA,IAAAU,EAAA,CAAS,OAAS+F,EAAK,WAAa,GAAKpE,EAAK,WAAc,IAAK,UAAU,MAAO,CAAA,EACnFK,EAAAA,KAACsD,EAAM,CAAA,QAAQ,UAAW,SAAA,CAAAS,EAAK,WAAa,EAAE,QAAA,CAAM,CAAA,CAAA,CACtD,CAAA,CAAA,CAAA,EALQA,EAAK,QAMf,CACD,CACH,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECzG,MAAA,MAAA,CAAI,UAAU,2BACb,gBAAC2F,EACC,CAAA,SAAA,CAAAjD,OAACkD,EACC,CAAA,SAAA,CAAA5F,EAAAA,IAAC6F,GAAU,SAAwB,0BAAA,CAAA,EACnC7F,EAAAA,IAAC8F,GAAgB,SAAwB,0BAAA,CAAA,CAAA,EAC3C,QACC,MAAI,CAAA,UAAU,MACZ,SACChD,EAAA9C,MAAC,OAAI,UAAU,YACZ,eAAM,KAAK,CAAE,OAAQ,CAAG,CAAA,EAAE,IAAI,CAACoG,EAAGC,IACjCrG,EAAAA,IAAC+F,EAAiB,CAAA,UAAU,cAAbM,CAA0B,CAC1C,EACH,EAEArG,MAAC,OAAI,UAAU,YACZ,WAAK,iBAAiB,iBAAiB,IAAI,CAAC0G,EAAKF,IAC/C9D,OAAA,MAAA,CAAgB,UAAU,yBACzB,SAAA,CAAC1C,EAAAA,IAAAb,EAAA,CAAkB,UAAU,gCAAiC,CAAA,EAC7Da,EAAA,IAAA,OAAA,CAAK,UAAU,UAAW,SAAI0G,CAAA,CAAA,CAAA,GAFvBF,CAGV,CACD,GACI9D,EAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAC1C,EAAAA,IAAAZ,EAAA,CAAgB,UAAU,SAAU,CAAA,EACpCY,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAA8C,gDAAA,CAAA,CAAA,CAC1E,CAAA,CAEN,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 9]}