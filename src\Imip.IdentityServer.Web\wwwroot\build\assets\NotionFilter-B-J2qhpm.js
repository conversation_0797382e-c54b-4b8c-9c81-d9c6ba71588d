import{r as n,j as e}from"./vendor-B0b15ZrB.js";import{h as D,B as r,L as x,S as y,k as w,l as E,m as I,I as F}from"./app-layout-D_A4XD_6.js";import{I as V}from"./index.esm-DqIqfoOW.js";import{P as A,c as z,t as P,e as G,f as R,p as W,V as U}from"./DataTableColumnHeader-CSMG3Uqi.js";import{S as B}from"./TableSkeleton-DgDki6RL.js";import"./App-De6zOdMU.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],_=D("loader-circle",M),$=[{label:"Equals",value:"Equals"},{label:"Not Equals",value:"NotEquals"},{label:"Contains",value:"Contains"},{label:"Starts With",value:"StartsWith"},{label:"Ends With",value:"EndsWith"},{label:"Greater Than",value:"GreaterThan"},{label:"Greater Than or Equal",value:"GreaterThanOrEqual"},{label:"Less Than",value:"LessThan"},{label:"Less Than or Equal",value:"LessThanOrEqual"},{label:"Is Empty",value:"IsEmpty"},{label:"Is Not Empty",value:"IsNotEmpty"},{label:"Is Null",value:"IsNull"},{label:"Is Not Null",value:"IsNotNull"}];function Z({table:t,onSearch:o,searchValue:S="",onServerFilter:d,activeFilters:c=[]}){const[T,f]=n.useState(!1),[a,i]=n.useState([]);function j(){return`filter-${Date.now()}-${Math.random().toString(36).substring(2,9)}`}const v=n.useRef(!0);n.useEffect(()=>{if(v.current&&(v.current=!1,c&&c.length>0)){const s=c.map(l=>({id:j(),columnId:l.fieldName,operator:l.operator,value:l.value}));i(s)}},[c]);const m=t.getAllColumns().filter(s=>s.id!=="select"&&s.id!=="actions"&&s.id!=="drag"&&s.getCanFilter()),N=a.filter(s=>s.value).length,u=N>0||t.getState().columnFilters.length>0,[g,C]=n.useState(!1),L=()=>{if(m.length===0)return;const s={id:j(),columnId:m[0]?.id??"",operator:"Contains",value:""};i([...a,s])},k=s=>{i(a.filter(l=>l.id!==s))},h=(s,l)=>{i(a.map(p=>p.id===s?{...p,...l}:p))},q=()=>{if(C(!0),t.resetColumnFilters(),d){const s=a.filter(l=>l.value).map(l=>({fieldName:l.columnId,operator:l.operator,value:l.value}));s.length>0&&d(s)}else a.forEach(s=>{const l=t.getColumn(s.columnId);l&&s.value&&l.setFilterValue({operator:s.operator,value:s.value})});f(!1),setTimeout(()=>{C(!1)},500)},b=()=>{i([]),t.resetColumnFilters(),d&&d([])},O=s=>{const l=t.getColumn(s);return l?.columnDef?.meta?.displayName?l.columnDef.meta.displayName:l?.columnDef?.header?typeof l.columnDef.header=="function"?s.charAt(0).toUpperCase()+s.slice(1):l.columnDef.header.toString():s.charAt(0).toUpperCase()+s.slice(1)};return e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[o&&e.jsx("div",{className:"w-full sm:w-auto sm:max-w-[250px]",children:e.jsx(B,{onUpdate:s=>{o&&o(s)},value:S||""})}),e.jsxs("div",{className:"flex items-center gap-2 ml-auto",children:[e.jsxs(A,{open:T,onOpenChange:f,children:[e.jsx(z,{asChild:!0,children:e.jsxs(r,{variant:u?"default":"outline",size:"sm",className:"h-8 gap-1",children:[e.jsx(P,{className:"h-3.5 w-3.5"}),e.jsx("span",{children:"Filter"}),u&&e.jsx("span",{className:"ml-1 rounded-full bg-primary/20 px-1 text-xs font-medium",children:N})]})}),e.jsx(G,{className:"w-[600px] p-3",align:"end",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium leading-none",children:"Filter"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Filter records by specific conditions"})]}),a.length>0?e.jsx("div",{className:"space-y-2",children:a.map(s=>e.jsx("div",{className:"rounded-md border p-3",children:e.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[e.jsxs("div",{className:"col-span-1",children:[e.jsx("div",{className:"flex items-center justify-between mb-1",children:e.jsx(x,{className:"text-xs font-medium",children:"Column"})}),e.jsxs(y,{value:s.columnId,onValueChange:l=>h(s.id,{columnId:l}),children:[e.jsx(w,{className:"w-full",children:e.jsx(E,{placeholder:"Select column"})}),e.jsx(I,{children:m.map(l=>e.jsx(F,{value:l.id,children:O(l.id)},l.id))})]})]}),e.jsxs("div",{className:"col-span-1",children:[e.jsx("div",{className:"flex items-center justify-between mb-1",children:e.jsx(x,{className:"text-xs font-medium",children:"Operator"})}),e.jsxs(y,{value:s.operator,onValueChange:l=>h(s.id,{operator:l}),children:[e.jsx(w,{className:"w-full",children:e.jsx(E,{placeholder:"Select operator"})}),e.jsx(I,{children:$.map(l=>e.jsx(F,{value:l.value,children:l.label},l.value))})]})]}),e.jsx("div",{className:"col-span-1",children:e.jsxs("div",{className:"flex",children:[e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center justify-between mb-1",children:e.jsx(x,{className:"text-xs font-medium",children:"Value"})}),e.jsx(V,{placeholder:"Enter value",className:"w-full",value:s.value,onChange:l=>h(s.id,{value:l.target.value})})]}),e.jsx("div",{className:"col-span-1 flex items-end justify-end",children:e.jsx(r,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>k(s.id),children:e.jsx(R,{className:"h-4 w-4"})})})]})})]})},s.id))}):e.jsx("div",{className:"flex h-20 items-center justify-center rounded-md border border-dashed",children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"No filters added"})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(r,{variant:"ghost",size:"sm",className:"h-8 gap-1",onClick:L,children:[e.jsx(W,{className:"h-3.5 w-3.5"}),e.jsx("span",{children:"Add filter"})]}),a.length>0&&e.jsx(r,{variant:"ghost",size:"sm",className:"h-8",onClick:b,children:"Clear all"})]}),e.jsx(r,{className:"w-full",onClick:q,disabled:a.length===0||g,children:g?e.jsxs(e.Fragment,{children:[e.jsx(_,{className:"mr-2 h-4 w-4 animate-spin"}),"Applying..."]}):"Apply filters"})]})})]}),u&&e.jsx(r,{variant:"ghost",onClick:b,className:"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500",children:"Clear filters"}),e.jsx(U,{table:t})]})]})}export{Z as N};
//# sourceMappingURL=NotionFilter-B-J2qhpm.js.map
