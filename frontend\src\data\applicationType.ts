import { Cog, Computer, Globe, Smartphone, Tv } from "lucide-react";

export const APPLICATION_TYPES = [
  {
    value: "web",
    option: "Web Application",
    description: "Server-side web applications running on a web server (e.g., ASP.NET, Node.js, PHP)",
    icon: Globe
  },
  {
    value: "spa",
    option: "Single Page Application",
    description: "JavaScript applications running in the browser (e.g., React, Angular, Vue.js)",
    icon: Computer
  },
  {
    value: "native",
    option: "Native Application",
    description: "Desktop or mobile applications running natively on devices",
    icon: Smartphone
  },
  {
    value: "device",
    option: "Device Application",
    description: "IoT devices, smart TVs, or other limited-input devices",
    icon: Tv
  },
  {
    value: "machine",
    option: "Machine-to-Machine",
    description: "Service accounts, daemons, or backend services that run without user interaction",
    icon: Cog
  }
] as const;