'use client'

import { type OpenIddictApplicationDto } from '@/client'
import { type ColumnDef } from '@tanstack/react-table'
import { ClientActions } from './ClientActions'
import { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'
import { Badge } from '@/components/ui/badge'
import { customFilterFunction } from '@/components/data-table/filterFunctions'

// Type for the callback function to handle user actions
type UserActionCallback = (dataId: string, dataEdit: OpenIddictApplicationDto, dialogType: 'edit' | 'permission' | 'delete') => void

// Function to create user columns with the action callback
export const getUserColumns = (
  handleUserAction: UserActionCallback
): ColumnDef<OpenIddictApplicationDto>[] => [
    {
      accessorKey: 'clientId',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Client ID" />
      ),
      enableSorting: true,
      filterFn: customFilterFunction,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Client ID",
      },
    },
    {
      accessorKey: 'displayName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Display Name" />
      ),
      enableSorting: true,
      filterFn: customFilterFunction,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Display Name",
      },
    },
    {
      accessorKey: 'applicationType',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Application Type" />
      ),
      enableSorting: true,
      filterFn: customFilterFunction,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Application Type",
      },
    },
    // {
    //   accessorKey: 'clientType',
    //   header: ({ column }) => (
    //     <DataTableColumnHeader column={column} title="Client Type" />
    //   ),
    //   enableSorting: true,
    // filterFn: customFilterFunction,
    //   enableHiding: true,
    //   cell: (info) => info.getValue(),
    //   meta: {
    //     className: "text-left",
    //     displayName: "Client Type",
    //   },
    // },
    {
      accessorKey: 'consentType',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Consent Type" />
      ),
      enableSorting: true,
      filterFn: customFilterFunction,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Consent Type",
      },
    },
    {
      accessorKey: 'redirectUris',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Redirect URIs" />
      ),
      enableSorting: true,
      filterFn: customFilterFunction,
      enableHiding: true,
      cell: (info) => {
        const resources = info.getValue() as string[];
        return (
          <div className="flex flex-wrap gap-1">
            {resources?.map((resource) => (
              <Badge key={resource} variant="secondary">
                {resource}
              </Badge>
            ))}
          </div>
        );
      },
      meta: {
        className: "text-left",
        displayName: "Redirect URIs",
      },
    },
    {
      id: 'actions',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Actions" />
      ),
      enableSorting: false,
      enableHiding: true,
      cell: (info) => (
        <ClientActions
          dataId={info.row.original.id!}
          dataEdit={info.row.original}
          onAction={handleUserAction}
          variant="dropdown" // Use "dropdown" for the first image style or "buttons" for the second image style
        />
      ),
      meta: {
        className: "text-right",
        displayName: "Actions",
      },
    },
  ]
