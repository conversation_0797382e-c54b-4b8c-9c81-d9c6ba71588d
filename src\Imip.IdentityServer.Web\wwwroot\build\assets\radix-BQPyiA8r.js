import{r as i,j as p,R as Mo,a as Ba,g as Wa,b as Ne,c as Va}from"./vendor-B0b15ZrB.js";function Ha(e,t){const n=i.createContext(t),o=s=>{const{children:a,...c}=s,l=i.useMemo(()=>c,Object.values(c));return p.jsx(n.Provider,{value:l,children:a})};o.displayName=e+"Provider";function r(s){const a=i.useContext(n);if(a)return a;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[o,r]}function ee(e,t=[]){let n=[];function o(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];const u=d=>{const{scope:m,children:h,...x}=d,v=m?.[e]?.[l]||c,g=i.useMemo(()=>x,Object.values(x));return p.jsx(v.Provider,{value:g,children:h})};u.displayName=s+"Provider";function f(d,m){const h=m?.[e]?.[l]||c,x=i.useContext(h);if(x)return x;if(a!==void 0)return a;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,f]}const r=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=c?.[e]||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return r.scopeName=e,[o,Ua(r,...t)]}function Ua(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(s){const a=o.reduce((c,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...c,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}function no(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function It(...e){return t=>{let n=!1;const o=e.map(r=>{const s=no(r,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let r=0;r<o.length;r++){const s=o[r];typeof s=="function"?s():no(e[r],null)}}}}function L(...e){return i.useCallback(It(...e),e)}function R(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),n===!1||!r.defaultPrevented)return t?.(r)}}var z=globalThis?.document?i.useLayoutEffect:()=>{},Ga=Mo[" useId ".trim().toString()]||(()=>{}),Ka=0;function re(e){const[t,n]=i.useState(Ga());return z(()=>{n(o=>o??String(Ka++))},[e]),e||(t?`radix-${t}`:"")}var za=Mo[" useInsertionEffect ".trim().toString()]||z;function we({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){const[r,s,a]=Ya({defaultProp:t,onChange:n}),c=e!==void 0,l=c?e:r;{const f=i.useRef(e!==void 0);i.useEffect(()=>{const d=f.current;if(d!==c){const m=d?"controlled":"uncontrolled",h=c?"controlled":"uncontrolled"}f.current=c},[c,o])}const u=i.useCallback(f=>{if(c){const d=Xa(f)?f(e):f;d!==e&&a.current?.(d)}else s(f)},[c,e,s,a]);return[l,u]}function Ya({defaultProp:e,onChange:t}){const[n,o]=i.useState(e),r=i.useRef(n),s=i.useRef(t);return za(()=>{s.current=t},[t]),i.useEffect(()=>{r.current!==n&&(s.current?.(n),r.current=n)},[n,r]),[n,o,s]}function Xa(e){return typeof e=="function"}var rt=Ba();const qa=Wa(rt);function Re(e){const t=Za(e),n=i.forwardRef((o,r)=>{const{children:s,...a}=o,c=i.Children.toArray(s),l=c.find(Ja);if(l){const u=l.props.children,f=c.map(d=>d===l?i.Children.count(u)>1?i.Children.only(null):i.isValidElement(u)?u.props.children:null:d);return p.jsx(t,{...a,ref:r,children:i.isValidElement(u)?i.cloneElement(u,void 0,f):null})}return p.jsx(t,{...a,ref:r,children:s})});return n.displayName=`${e}.Slot`,n}var lf=Re("Slot");function Za(e){const t=i.forwardRef((n,o)=>{const{children:r,...s}=n;if(i.isValidElement(r)){const a=ti(r),c=ei(s,r.props);return r.type!==i.Fragment&&(c.ref=o?It(o,a):a),i.cloneElement(r,c)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Io=Symbol("radix.slottable");function Qa(e){const t=({children:n})=>p.jsx(p.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Io,t}function Ja(e){return i.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Io}function ei(e,t){const n={...t};for(const o in t){const r=e[o],s=t[o];/^on[A-Z]/.test(o)?r&&s?n[o]=(...c)=>{const l=s(...c);return r(...c),l}:r&&(n[o]=r):o==="style"?n[o]={...r,...s}:o==="className"&&(n[o]=[r,s].filter(Boolean).join(" "))}return{...e,...n}}function ti(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var ni=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],T=ni.reduce((e,t)=>{const n=Re(`Primitive.${t}`),o=i.forwardRef((r,s)=>{const{asChild:a,...c}=r,l=a?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),p.jsx(l,{...c,ref:s})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function To(e,t){e&&rt.flushSync(()=>e.dispatchEvent(t))}function ce(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>t.current?.(...n),[])}function oi(e,t=globalThis?.document){const n=ce(e);i.useEffect(()=>{const o=r=>{r.key==="Escape"&&n(r)};return t.addEventListener("keydown",o,{capture:!0}),()=>t.removeEventListener("keydown",o,{capture:!0})},[n,t])}var ri="DismissableLayer",pn="dismissableLayer.update",si="dismissableLayer.pointerDownOutside",ai="dismissableLayer.focusOutside",oo,No=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ze=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:a,onDismiss:c,...l}=e,u=i.useContext(No),[f,d]=i.useState(null),m=f?.ownerDocument??globalThis?.document,[,h]=i.useState({}),x=L(t,E=>d(E)),v=Array.from(u.layers),[g]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),y=v.indexOf(g),w=f?v.indexOf(f):-1,C=u.layersWithOutsidePointerEventsDisabled.size>0,b=w>=y,S=ci(E=>{const _=E.target,O=[...u.branches].some(D=>D.contains(_));!b||O||(r?.(E),a?.(E),E.defaultPrevented||c?.())},m),M=li(E=>{const _=E.target;[...u.branches].some(D=>D.contains(_))||(s?.(E),a?.(E),E.defaultPrevented||c?.())},m);return oi(E=>{w===u.layers.size-1&&(o?.(E),!E.defaultPrevented&&c&&(E.preventDefault(),c()))},m),i.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(oo=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),ro(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=oo)}},[f,m,n,u]),i.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),ro())},[f,u]),i.useEffect(()=>{const E=()=>h({});return document.addEventListener(pn,E),()=>document.removeEventListener(pn,E)},[]),p.jsx(T.div,{...l,ref:x,style:{pointerEvents:C?b?"auto":"none":void 0,...e.style},onFocusCapture:R(e.onFocusCapture,M.onFocusCapture),onBlurCapture:R(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:R(e.onPointerDownCapture,S.onPointerDownCapture)})});ze.displayName=ri;var ii="DismissableLayerBranch",Do=i.forwardRef((e,t)=>{const n=i.useContext(No),o=i.useRef(null),r=L(t,o);return i.useEffect(()=>{const s=o.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),p.jsx(T.div,{...e,ref:r})});Do.displayName=ii;function ci(e,t=globalThis?.document){const n=ce(e),o=i.useRef(!1),r=i.useRef(()=>{});return i.useEffect(()=>{const s=c=>{if(c.target&&!o.current){let l=function(){Oo(si,n,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",r.current),r.current=l,t.addEventListener("click",r.current,{once:!0})):l()}else t.removeEventListener("click",r.current);o.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",s),t.removeEventListener("click",r.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}function li(e,t=globalThis?.document){const n=ce(e),o=i.useRef(!1);return i.useEffect(()=>{const r=s=>{s.target&&!o.current&&Oo(ai,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",r),()=>t.removeEventListener("focusin",r)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function ro(){const e=new CustomEvent(pn);document.dispatchEvent(e)}function Oo(e,t,n,{discrete:o}){const r=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),o?To(r,s):r.dispatchEvent(s)}var uf=ze,df=Do,en="focusScope.autoFocusOnMount",tn="focusScope.autoFocusOnUnmount",so={bubbles:!1,cancelable:!0},ui="FocusScope",st=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:o=!1,onMountAutoFocus:r,onUnmountAutoFocus:s,...a}=e,[c,l]=i.useState(null),u=ce(r),f=ce(s),d=i.useRef(null),m=L(t,v=>l(v)),h=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(o){let v=function(C){if(h.paused||!c)return;const b=C.target;c.contains(b)?d.current=b:Se(d.current,{select:!0})},g=function(C){if(h.paused||!c)return;const b=C.relatedTarget;b!==null&&(c.contains(b)||Se(d.current,{select:!0}))},y=function(C){if(document.activeElement===document.body)for(const S of C)S.removedNodes.length>0&&Se(c)};document.addEventListener("focusin",v),document.addEventListener("focusout",g);const w=new MutationObserver(y);return c&&w.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",v),document.removeEventListener("focusout",g),w.disconnect()}}},[o,c,h.paused]),i.useEffect(()=>{if(c){io.add(h);const v=document.activeElement;if(!c.contains(v)){const y=new CustomEvent(en,so);c.addEventListener(en,u),c.dispatchEvent(y),y.defaultPrevented||(di(hi(ko(c)),{select:!0}),document.activeElement===v&&Se(c))}return()=>{c.removeEventListener(en,u),setTimeout(()=>{const y=new CustomEvent(tn,so);c.addEventListener(tn,f),c.dispatchEvent(y),y.defaultPrevented||Se(v??document.body,{select:!0}),c.removeEventListener(tn,f),io.remove(h)},0)}}},[c,u,f,h]);const x=i.useCallback(v=>{if(!n&&!o||h.paused)return;const g=v.key==="Tab"&&!v.altKey&&!v.ctrlKey&&!v.metaKey,y=document.activeElement;if(g&&y){const w=v.currentTarget,[C,b]=fi(w);C&&b?!v.shiftKey&&y===b?(v.preventDefault(),n&&Se(C,{select:!0})):v.shiftKey&&y===C&&(v.preventDefault(),n&&Se(b,{select:!0})):y===w&&v.preventDefault()}},[n,o,h.paused]);return p.jsx(T.div,{tabIndex:-1,...a,ref:m,onKeyDown:x})});st.displayName=ui;function di(e,{select:t=!1}={}){const n=document.activeElement;for(const o of e)if(Se(o,{select:t}),document.activeElement!==n)return}function fi(e){const t=ko(e),n=ao(t,e),o=ao(t.reverse(),e);return[n,o]}function ko(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ao(e,t){for(const n of e)if(!pi(n,{upTo:t}))return n}function pi(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function vi(e){return e instanceof HTMLInputElement&&"select"in e}function Se(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&vi(e)&&t&&e.select()}}var io=mi();function mi(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=co(e,t),e.unshift(t)},remove(t){e=co(e,t),e[0]?.resume()}}}function co(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function hi(e){return e.filter(t=>t.tagName!=="A")}var gi="Portal",at=i.forwardRef((e,t)=>{const{container:n,...o}=e,[r,s]=i.useState(!1);z(()=>s(!0),[]);const a=n||r&&globalThis?.document?.body;return a?qa.createPortal(p.jsx(T.div,{...o,ref:t}),a):null});at.displayName=gi;function xi(e,t){return i.useReducer((n,o)=>t[n][o]??n,e)}var te=e=>{const{present:t,children:n}=e,o=wi(t),r=typeof n=="function"?n({present:o.isPresent}):i.Children.only(n),s=L(o.ref,yi(r));return typeof n=="function"||o.isPresent?i.cloneElement(r,{ref:s}):null};te.displayName="Presence";function wi(e){const[t,n]=i.useState(),o=i.useRef(null),r=i.useRef(e),s=i.useRef("none"),a=e?"mounted":"unmounted",[c,l]=xi(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=pt(o.current);s.current=c==="mounted"?u:"none"},[c]),z(()=>{const u=o.current,f=r.current;if(f!==e){const m=s.current,h=pt(u);e?l("MOUNT"):h==="none"||u?.display==="none"?l("UNMOUNT"):l(f&&m!==h?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,l]),z(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,d=h=>{const v=pt(o.current).includes(h.animationName);if(h.target===t&&v&&(l("ANIMATION_END"),!r.current)){const g=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=g)})}},m=h=>{h.target===t&&(s.current=pt(o.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(u=>{o.current=u?getComputedStyle(u):null,n(u)},[])}}function pt(e){return e?.animationName||"none"}function yi(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var nn=0;function Tt(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??lo()),document.body.insertAdjacentElement("beforeend",e[1]??lo()),nn++,()=>{nn===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),nn--}},[])}function lo(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var fe=function(){return fe=Object.assign||function(t){for(var n,o=1,r=arguments.length;o<r;o++){n=arguments[o];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},fe.apply(this,arguments)};function Lo(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}function Ci(e,t,n){if(n||arguments.length===2)for(var o=0,r=t.length,s;o<r;o++)(s||!(o in t))&&(s||(s=Array.prototype.slice.call(t,0,o)),s[o]=t[o]);return e.concat(s||Array.prototype.slice.call(t))}var wt="right-scroll-bar-position",yt="width-before-scroll-bar",bi="with-scroll-bars-hidden",Si="--removed-body-scroll-bar-size";function on(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Ei(e,t){var n=i.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(o){var r=n.value;r!==o&&(n.value=o,n.callback(o,r))}}}})[0];return n.callback=t,n.facade}var Ri=typeof window<"u"?i.useLayoutEffect:i.useEffect,uo=new WeakMap;function Pi(e,t){var n=Ei(null,function(o){return e.forEach(function(r){return on(r,o)})});return Ri(function(){var o=uo.get(n);if(o){var r=new Set(o),s=new Set(e),a=n.current;r.forEach(function(c){s.has(c)||on(c,null)}),s.forEach(function(c){r.has(c)||on(c,a)})}uo.set(n,e)},[e]),n}function Ai(e){return e}function _i(e,t){t===void 0&&(t=Ai);var n=[],o=!1,r={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var a=t(s,o);return n.push(a),function(){n=n.filter(function(c){return c!==a})}},assignSyncMedium:function(s){for(o=!0;n.length;){var a=n;n=[],a.forEach(s)}n={push:function(c){return s(c)},filter:function(){return n}}},assignMedium:function(s){o=!0;var a=[];if(n.length){var c=n;n=[],c.forEach(s),a=n}var l=function(){var f=a;a=[],f.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(f){a.push(f),u()},filter:function(f){return a=a.filter(f),n}}}};return r}function Mi(e){e===void 0&&(e={});var t=_i(null);return t.options=fe({async:!0,ssr:!1},e),t}var jo=function(e){var t=e.sideCar,n=Lo(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=t.read();if(!o)throw new Error("Sidecar medium not found");return i.createElement(o,fe({},n))};jo.isSideCarExport=!0;function Ii(e,t){return e.useMedium(t),jo}var Fo=Mi(),rn=function(){},Nt=i.forwardRef(function(e,t){var n=i.useRef(null),o=i.useState({onScrollCapture:rn,onWheelCapture:rn,onTouchMoveCapture:rn}),r=o[0],s=o[1],a=e.forwardProps,c=e.children,l=e.className,u=e.removeScrollBar,f=e.enabled,d=e.shards,m=e.sideCar,h=e.noRelative,x=e.noIsolation,v=e.inert,g=e.allowPinchZoom,y=e.as,w=y===void 0?"div":y,C=e.gapMode,b=Lo(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=m,M=Pi([n,t]),E=fe(fe({},b),r);return i.createElement(i.Fragment,null,f&&i.createElement(S,{sideCar:Fo,removeScrollBar:u,shards:d,noRelative:h,noIsolation:x,inert:v,setCallbacks:s,allowPinchZoom:!!g,lockRef:n,gapMode:C}),a?i.cloneElement(i.Children.only(c),fe(fe({},E),{ref:M})):i.createElement(w,fe({},E,{className:l,ref:M}),c))});Nt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Nt.classNames={fullWidth:yt,zeroRight:wt};var Ti=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Ni(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Ti();return t&&e.setAttribute("nonce",t),e}function Di(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Oi(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var ki=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Ni())&&(Di(t,n),Oi(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Li=function(){var e=ki();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},$o=function(){var e=Li(),t=function(n){var o=n.styles,r=n.dynamic;return e(o,r),null};return t},ji={left:0,top:0,right:0,gap:0},sn=function(e){return parseInt(e||"",10)||0},Fi=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],o=t[e==="padding"?"paddingTop":"marginTop"],r=t[e==="padding"?"paddingRight":"marginRight"];return[sn(n),sn(o),sn(r)]},$i=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return ji;var t=Fi(e),n=document.documentElement.clientWidth,o=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,o-n+t[2]-t[0])}},Bi=$o(),Ve="data-scroll-locked",Wi=function(e,t,n,o){var r=e.left,s=e.top,a=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(bi,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(c,"px ").concat(o,`;
  }
  body[`).concat(Ve,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(o,";"),n==="margin"&&`
    padding-left: `.concat(r,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(a,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(o,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(wt,` {
    right: `).concat(c,"px ").concat(o,`;
  }
  
  .`).concat(yt,` {
    margin-right: `).concat(c,"px ").concat(o,`;
  }
  
  .`).concat(wt," .").concat(wt,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(yt," .").concat(yt,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(Ve,`] {
    `).concat(Si,": ").concat(c,`px;
  }
`)},fo=function(){var e=parseInt(document.body.getAttribute(Ve)||"0",10);return isFinite(e)?e:0},Vi=function(){i.useEffect(function(){return document.body.setAttribute(Ve,(fo()+1).toString()),function(){var e=fo()-1;e<=0?document.body.removeAttribute(Ve):document.body.setAttribute(Ve,e.toString())}},[])},Hi=function(e){var t=e.noRelative,n=e.noImportant,o=e.gapMode,r=o===void 0?"margin":o;Vi();var s=i.useMemo(function(){return $i(r)},[r]);return i.createElement(Bi,{styles:Wi(s,!t,r,n?"":"!important")})},vn=!1;if(typeof window<"u")try{var vt=Object.defineProperty({},"passive",{get:function(){return vn=!0,!0}});window.addEventListener("test",vt,vt),window.removeEventListener("test",vt,vt)}catch{vn=!1}var $e=vn?{passive:!1}:!1,Ui=function(e){return e.tagName==="TEXTAREA"},Bo=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Ui(e)&&n[t]==="visible")},Gi=function(e){return Bo(e,"overflowY")},Ki=function(e){return Bo(e,"overflowX")},po=function(e,t){var n=t.ownerDocument,o=t;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var r=Wo(e,o);if(r){var s=Vo(e,o),a=s[1],c=s[2];if(a>c)return!0}o=o.parentNode}while(o&&o!==n.body);return!1},zi=function(e){var t=e.scrollTop,n=e.scrollHeight,o=e.clientHeight;return[t,n,o]},Yi=function(e){var t=e.scrollLeft,n=e.scrollWidth,o=e.clientWidth;return[t,n,o]},Wo=function(e,t){return e==="v"?Gi(t):Ki(t)},Vo=function(e,t){return e==="v"?zi(t):Yi(t)},Xi=function(e,t){return e==="h"&&t==="rtl"?-1:1},qi=function(e,t,n,o,r){var s=Xi(e,window.getComputedStyle(t).direction),a=s*o,c=n.target,l=t.contains(c),u=!1,f=a>0,d=0,m=0;do{if(!c)break;var h=Vo(e,c),x=h[0],v=h[1],g=h[2],y=v-g-s*x;(x||y)&&Wo(e,c)&&(d+=y,m+=x);var w=c.parentNode;c=w&&w.nodeType===Node.DOCUMENT_FRAGMENT_NODE?w.host:w}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return(f&&Math.abs(d)<1||!f&&Math.abs(m)<1)&&(u=!0),u},mt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},vo=function(e){return[e.deltaX,e.deltaY]},mo=function(e){return e&&"current"in e?e.current:e},Zi=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Qi=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Ji=0,Be=[];function ec(e){var t=i.useRef([]),n=i.useRef([0,0]),o=i.useRef(),r=i.useState(Ji++)[0],s=i.useState($o)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(r));var v=Ci([e.lockRef.current],(e.shards||[]).map(mo),!0).filter(Boolean);return v.forEach(function(g){return g.classList.add("allow-interactivity-".concat(r))}),function(){document.body.classList.remove("block-interactivity-".concat(r)),v.forEach(function(g){return g.classList.remove("allow-interactivity-".concat(r))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(v,g){if("touches"in v&&v.touches.length===2||v.type==="wheel"&&v.ctrlKey)return!a.current.allowPinchZoom;var y=mt(v),w=n.current,C="deltaX"in v?v.deltaX:w[0]-y[0],b="deltaY"in v?v.deltaY:w[1]-y[1],S,M=v.target,E=Math.abs(C)>Math.abs(b)?"h":"v";if("touches"in v&&E==="h"&&M.type==="range")return!1;var _=po(E,M);if(!_)return!0;if(_?S=E:(S=E==="v"?"h":"v",_=po(E,M)),!_)return!1;if(!o.current&&"changedTouches"in v&&(C||b)&&(o.current=S),!S)return!0;var O=o.current||S;return qi(O,g,v,O==="h"?C:b)},[]),l=i.useCallback(function(v){var g=v;if(!(!Be.length||Be[Be.length-1]!==s)){var y="deltaY"in g?vo(g):mt(g),w=t.current.filter(function(S){return S.name===g.type&&(S.target===g.target||g.target===S.shadowParent)&&Zi(S.delta,y)})[0];if(w&&w.should){g.cancelable&&g.preventDefault();return}if(!w){var C=(a.current.shards||[]).map(mo).filter(Boolean).filter(function(S){return S.contains(g.target)}),b=C.length>0?c(g,C[0]):!a.current.noIsolation;b&&g.cancelable&&g.preventDefault()}}},[]),u=i.useCallback(function(v,g,y,w){var C={name:v,delta:g,target:y,should:w,shadowParent:tc(y)};t.current.push(C),setTimeout(function(){t.current=t.current.filter(function(b){return b!==C})},1)},[]),f=i.useCallback(function(v){n.current=mt(v),o.current=void 0},[]),d=i.useCallback(function(v){u(v.type,vo(v),v.target,c(v,e.lockRef.current))},[]),m=i.useCallback(function(v){u(v.type,mt(v),v.target,c(v,e.lockRef.current))},[]);i.useEffect(function(){return Be.push(s),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:m}),document.addEventListener("wheel",l,$e),document.addEventListener("touchmove",l,$e),document.addEventListener("touchstart",f,$e),function(){Be=Be.filter(function(v){return v!==s}),document.removeEventListener("wheel",l,$e),document.removeEventListener("touchmove",l,$e),document.removeEventListener("touchstart",f,$e)}},[]);var h=e.removeScrollBar,x=e.inert;return i.createElement(i.Fragment,null,x?i.createElement(s,{styles:Qi(r)}):null,h?i.createElement(Hi,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function tc(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const nc=Ii(Fo,ec);var it=i.forwardRef(function(e,t){return i.createElement(Nt,fe({},e,{ref:t,sideCar:nc}))});it.classNames=Nt.classNames;var oc=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},We=new WeakMap,ht=new WeakMap,gt={},an=0,Ho=function(e){return e&&(e.host||Ho(e.parentNode))},rc=function(e,t){return t.map(function(n){if(e.contains(n))return n;var o=Ho(n);return o&&e.contains(o)?o:null}).filter(function(n){return!!n})},sc=function(e,t,n,o){var r=rc(t,Array.isArray(e)?e:[e]);gt[n]||(gt[n]=new WeakMap);var s=gt[n],a=[],c=new Set,l=new Set(r),u=function(d){!d||c.has(d)||(c.add(d),u(d.parentNode))};r.forEach(u);var f=function(d){!d||l.has(d)||Array.prototype.forEach.call(d.children,function(m){if(c.has(m))f(m);else try{var h=m.getAttribute(o),x=h!==null&&h!=="false",v=(We.get(m)||0)+1,g=(s.get(m)||0)+1;We.set(m,v),s.set(m,g),a.push(m),v===1&&x&&ht.set(m,!0),g===1&&m.setAttribute(n,"true"),x||m.setAttribute(o,"true")}catch{}})};return f(t),c.clear(),an++,function(){a.forEach(function(d){var m=We.get(d)-1,h=s.get(d)-1;We.set(d,m),s.set(d,h),m||(ht.has(d)||d.removeAttribute(o),ht.delete(d)),h||d.removeAttribute(n)}),an--,an||(We=new WeakMap,We=new WeakMap,ht=new WeakMap,gt={})}},Dt=function(e,t,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),r=oc(e);return r?(o.push.apply(o,Array.from(r.querySelectorAll("[aria-live], script"))),sc(o,r,n,"aria-hidden")):function(){return null}},Ot="Dialog",[Uo,Go]=ee(Ot),[ac,de]=Uo(Ot),Ko=e=>{const{__scopeDialog:t,children:n,open:o,defaultOpen:r,onOpenChange:s,modal:a=!0}=e,c=i.useRef(null),l=i.useRef(null),[u,f]=we({prop:o,defaultProp:r??!1,onChange:s,caller:Ot});return p.jsx(ac,{scope:t,triggerRef:c,contentRef:l,contentId:re(),titleId:re(),descriptionId:re(),open:u,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(d=>!d),[f]),modal:a,children:n})};Ko.displayName=Ot;var zo="DialogTrigger",Yo=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(zo,n),s=L(t,r.triggerRef);return p.jsx(T.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":An(r.open),...o,ref:s,onClick:R(e.onClick,r.onOpenToggle)})});Yo.displayName=zo;var Rn="DialogPortal",[ic,Xo]=Uo(Rn,{forceMount:void 0}),qo=e=>{const{__scopeDialog:t,forceMount:n,children:o,container:r}=e,s=de(Rn,t);return p.jsx(ic,{scope:t,forceMount:n,children:i.Children.map(o,a=>p.jsx(te,{present:n||s.open,children:p.jsx(at,{asChild:!0,container:r,children:a})}))})};qo.displayName=Rn;var bt="DialogOverlay",Zo=i.forwardRef((e,t)=>{const n=Xo(bt,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=de(bt,e.__scopeDialog);return s.modal?p.jsx(te,{present:o||s.open,children:p.jsx(lc,{...r,ref:t})}):null});Zo.displayName=bt;var cc=Re("DialogOverlay.RemoveScroll"),lc=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(bt,n);return p.jsx(it,{as:cc,allowPinchZoom:!0,shards:[r.contentRef],children:p.jsx(T.div,{"data-state":An(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),De="DialogContent",Qo=i.forwardRef((e,t)=>{const n=Xo(De,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=de(De,e.__scopeDialog);return p.jsx(te,{present:o||s.open,children:s.modal?p.jsx(uc,{...r,ref:t}):p.jsx(dc,{...r,ref:t})})});Qo.displayName=De;var uc=i.forwardRef((e,t)=>{const n=de(De,e.__scopeDialog),o=i.useRef(null),r=L(t,n.contentRef,o);return i.useEffect(()=>{const s=o.current;if(s)return Dt(s)},[]),p.jsx(Jo,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:R(e.onCloseAutoFocus,s=>{s.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:R(e.onPointerDownOutside,s=>{const a=s.detail.originalEvent,c=a.button===0&&a.ctrlKey===!0;(a.button===2||c)&&s.preventDefault()}),onFocusOutside:R(e.onFocusOutside,s=>s.preventDefault())})}),dc=i.forwardRef((e,t)=>{const n=de(De,e.__scopeDialog),o=i.useRef(!1),r=i.useRef(!1);return p.jsx(Jo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(o.current||n.triggerRef.current?.focus(),s.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(o.current=!0,s.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const a=s.target;n.triggerRef.current?.contains(a)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&r.current&&s.preventDefault()}})}),Jo=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:s,...a}=e,c=de(De,n),l=i.useRef(null),u=L(t,l);return Tt(),p.jsxs(p.Fragment,{children:[p.jsx(st,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:s,children:p.jsx(ze,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":An(c.open),...a,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),p.jsxs(p.Fragment,{children:[p.jsx(pc,{titleId:c.titleId}),p.jsx(mc,{contentRef:l,descriptionId:c.descriptionId})]})]})}),Pn="DialogTitle",er=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(Pn,n);return p.jsx(T.h2,{id:r.titleId,...o,ref:t})});er.displayName=Pn;var tr="DialogDescription",nr=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(tr,n);return p.jsx(T.p,{id:r.descriptionId,...o,ref:t})});nr.displayName=tr;var or="DialogClose",rr=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(or,n);return p.jsx(T.button,{type:"button",...o,ref:t,onClick:R(e.onClick,()=>r.onOpenChange(!1))})});rr.displayName=or;function An(e){return e?"open":"closed"}var sr="DialogTitleWarning",[fc,ar]=Ha(sr,{contentName:De,titleName:Pn,docsSlug:"dialog"}),pc=({titleId:e})=>{const t=ar(sr),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{if(e){const o=document.getElementById(e)}},[n,e]),null},vc="DialogDescriptionWarning",mc=({contentRef:e,descriptionId:t})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${ar(vc).contentName}}.`;return i.useEffect(()=>{const r=e.current?.getAttribute("aria-describedby");if(t&&r){const s=document.getElementById(t)}},[o,e,t]),null},hc=Ko,gc=Yo,xc=qo,wc=Zo,yc=Qo,Cc=er,bc=nr,ir=rr,cr="AlertDialog",[Sc,ff]=ee(cr,[Go]),be=Go(),lr=e=>{const{__scopeAlertDialog:t,...n}=e,o=be(t);return p.jsx(hc,{...o,...n,modal:!0})};lr.displayName=cr;var Ec="AlertDialogTrigger",Rc=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=be(n);return p.jsx(gc,{...r,...o,ref:t})});Rc.displayName=Ec;var Pc="AlertDialogPortal",ur=e=>{const{__scopeAlertDialog:t,...n}=e,o=be(t);return p.jsx(xc,{...o,...n})};ur.displayName=Pc;var Ac="AlertDialogOverlay",dr=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=be(n);return p.jsx(wc,{...r,...o,ref:t})});dr.displayName=Ac;var He="AlertDialogContent",[_c,Mc]=Sc(He),Ic=Qa("AlertDialogContent"),fr=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,children:o,...r}=e,s=be(n),a=i.useRef(null),c=L(t,a),l=i.useRef(null);return p.jsx(fc,{contentName:He,titleName:pr,docsSlug:"alert-dialog",children:p.jsx(_c,{scope:n,cancelRef:l,children:p.jsxs(yc,{role:"alertdialog",...s,...r,ref:c,onOpenAutoFocus:R(r.onOpenAutoFocus,u=>{u.preventDefault(),l.current?.focus({preventScroll:!0})}),onPointerDownOutside:u=>u.preventDefault(),onInteractOutside:u=>u.preventDefault(),children:[p.jsx(Ic,{children:o}),p.jsx(Nc,{contentRef:a})]})})})});fr.displayName=He;var pr="AlertDialogTitle",vr=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=be(n);return p.jsx(Cc,{...r,...o,ref:t})});vr.displayName=pr;var mr="AlertDialogDescription",hr=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=be(n);return p.jsx(bc,{...r,...o,ref:t})});hr.displayName=mr;var Tc="AlertDialogAction",gr=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=be(n);return p.jsx(ir,{...r,...o,ref:t})});gr.displayName=Tc;var xr="AlertDialogCancel",wr=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,{cancelRef:r}=Mc(xr,n),s=be(n),a=L(t,r);return p.jsx(ir,{...s,...o,ref:a})});wr.displayName=xr;var Nc=({contentRef:e})=>{const t=`\`${He}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${He}\` by passing a \`${mr}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${He}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return i.useEffect(()=>{const n=document.getElementById(e.current?.getAttribute("aria-describedby"))},[t,e]),null},pf=lr,vf=ur,mf=dr,hf=fr,gf=gr,xf=wr,wf=vr,yf=hr;function yr(e){const t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function Cr(e){const[t,n]=i.useState(void 0);return z(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const s=r[0];let a,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;a=u.inlineSize,c=u.blockSize}else a=e.offsetWidth,c=e.offsetHeight;n({width:a,height:c})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}var kt="Checkbox",[Dc,Cf]=ee(kt),[Oc,_n]=Dc(kt);function kc(e){const{__scopeCheckbox:t,checked:n,children:o,defaultChecked:r,disabled:s,form:a,name:c,onCheckedChange:l,required:u,value:f="on",internal_do_not_use_render:d}=e,[m,h]=we({prop:n,defaultProp:r??!1,onChange:l,caller:kt}),[x,v]=i.useState(null),[g,y]=i.useState(null),w=i.useRef(!1),C=x?!!a||!!x.closest("form"):!0,b={checked:m,disabled:s,setChecked:h,control:x,setControl:v,name:c,form:a,value:f,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:Ee(r)?!1:r,isFormControl:C,bubbleInput:g,setBubbleInput:y};return p.jsx(Oc,{scope:t,...b,children:Fc(d)?d(b):o})}var br="CheckboxTrigger",Sr=i.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...o},r)=>{const{control:s,value:a,disabled:c,checked:l,required:u,setControl:f,setChecked:d,hasConsumerStoppedPropagationRef:m,isFormControl:h,bubbleInput:x}=_n(br,e),v=L(r,f),g=i.useRef(l);return i.useEffect(()=>{const y=s?.form;if(y){const w=()=>d(g.current);return y.addEventListener("reset",w),()=>y.removeEventListener("reset",w)}},[s,d]),p.jsx(T.button,{type:"button",role:"checkbox","aria-checked":Ee(l)?"mixed":l,"aria-required":u,"data-state":Ar(l),"data-disabled":c?"":void 0,disabled:c,value:a,...o,ref:v,onKeyDown:R(t,y=>{y.key==="Enter"&&y.preventDefault()}),onClick:R(n,y=>{d(w=>Ee(w)?!0:!w),x&&h&&(m.current=y.isPropagationStopped(),m.current||y.stopPropagation())})})});Sr.displayName=br;var Lc=i.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:o,checked:r,defaultChecked:s,required:a,disabled:c,value:l,onCheckedChange:u,form:f,...d}=e;return p.jsx(kc,{__scopeCheckbox:n,checked:r,defaultChecked:s,disabled:c,required:a,onCheckedChange:u,name:o,form:f,value:l,internal_do_not_use_render:({isFormControl:m})=>p.jsxs(p.Fragment,{children:[p.jsx(Sr,{...d,ref:t,__scopeCheckbox:n}),m&&p.jsx(Pr,{__scopeCheckbox:n})]})})});Lc.displayName=kt;var Er="CheckboxIndicator",jc=i.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:o,...r}=e,s=_n(Er,n);return p.jsx(te,{present:o||Ee(s.checked)||s.checked===!0,children:p.jsx(T.span,{"data-state":Ar(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t,style:{pointerEvents:"none",...e.style}})})});jc.displayName=Er;var Rr="CheckboxBubbleInput",Pr=i.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:o,hasConsumerStoppedPropagationRef:r,checked:s,defaultChecked:a,required:c,disabled:l,name:u,value:f,form:d,bubbleInput:m,setBubbleInput:h}=_n(Rr,e),x=L(n,h),v=yr(s),g=Cr(o);i.useEffect(()=>{const w=m;if(!w)return;const C=window.HTMLInputElement.prototype,S=Object.getOwnPropertyDescriptor(C,"checked").set,M=!r.current;if(v!==s&&S){const E=new Event("click",{bubbles:M});w.indeterminate=Ee(s),S.call(w,Ee(s)?!1:s),w.dispatchEvent(E)}},[m,v,s,r]);const y=i.useRef(Ee(s)?!1:s);return p.jsx(T.input,{type:"checkbox","aria-hidden":!0,defaultChecked:a??y.current,required:c,disabled:l,name:u,value:f,form:d,...t,tabIndex:-1,ref:x,style:{...t.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});Pr.displayName=Rr;function Fc(e){return typeof e=="function"}function Ee(e){return e==="indeterminate"}function Ar(e){return Ee(e)?"indeterminate":e?"checked":"unchecked"}var $c="Label",_r=i.forwardRef((e,t)=>p.jsx(T.label,{...e,ref:t,onMouseDown:n=>{n.target.closest("button, input, select, textarea")||(e.onMouseDown?.(n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));_r.displayName=$c;var bf=_r;const Bc=["top","right","bottom","left"],Pe=Math.min,Q=Math.max,St=Math.round,xt=Math.floor,ve=e=>({x:e,y:e}),Wc={left:"right",right:"left",bottom:"top",top:"bottom"},Vc={start:"end",end:"start"};function mn(e,t,n){return Q(e,Pe(t,n))}function ye(e,t){return typeof e=="function"?e(t):e}function Ce(e){return e.split("-")[0]}function Ye(e){return e.split("-")[1]}function Mn(e){return e==="x"?"y":"x"}function In(e){return e==="y"?"height":"width"}function pe(e){return["top","bottom"].includes(Ce(e))?"y":"x"}function Tn(e){return Mn(pe(e))}function Hc(e,t,n){n===void 0&&(n=!1);const o=Ye(e),r=Tn(e),s=In(r);let a=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(a=Et(a)),[a,Et(a)]}function Uc(e){const t=Et(e);return[hn(e),t,hn(t)]}function hn(e){return e.replace(/start|end/g,t=>Vc[t])}function Gc(e,t,n){const o=["left","right"],r=["right","left"],s=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?s:a;default:return[]}}function Kc(e,t,n,o){const r=Ye(e);let s=Gc(Ce(e),n==="start",o);return r&&(s=s.map(a=>a+"-"+r),t&&(s=s.concat(s.map(hn)))),s}function Et(e){return e.replace(/left|right|bottom|top/g,t=>Wc[t])}function zc(e){return{top:0,right:0,bottom:0,left:0,...e}}function Mr(e){return typeof e!="number"?zc(e):{top:e,right:e,bottom:e,left:e}}function Rt(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function ho(e,t,n){let{reference:o,floating:r}=e;const s=pe(t),a=Tn(t),c=In(a),l=Ce(t),u=s==="y",f=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,m=o[c]/2-r[c]/2;let h;switch(l){case"top":h={x:f,y:o.y-r.height};break;case"bottom":h={x:f,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:d};break;case"left":h={x:o.x-r.width,y:d};break;default:h={x:o.x,y:o.y}}switch(Ye(t)){case"start":h[a]-=m*(n&&u?-1:1);break;case"end":h[a]+=m*(n&&u?-1:1);break}return h}const Yc=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:s=[],platform:a}=n,c=s.filter(Boolean),l=await(a.isRTL==null?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:r}),{x:f,y:d}=ho(u,o,l),m=o,h={},x=0;for(let v=0;v<c.length;v++){const{name:g,fn:y}=c[v],{x:w,y:C,data:b,reset:S}=await y({x:f,y:d,initialPlacement:o,placement:m,strategy:r,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});f=w??f,d=C??d,h={...h,[g]:{...h[g],...b}},S&&x<=50&&(x++,typeof S=="object"&&(S.placement&&(m=S.placement),S.rects&&(u=S.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:r}):S.rects),{x:f,y:d}=ho(u,m,l)),v=-1)}return{x:f,y:d,placement:m,strategy:r,middlewareData:h}};async function et(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:s,rects:a,elements:c,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:m=!1,padding:h=0}=ye(t,e),x=Mr(h),g=c[m?d==="floating"?"reference":"floating":d],y=Rt(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(g)))==null||n?g:g.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(c.floating)),boundary:u,rootBoundary:f,strategy:l})),w=d==="floating"?{x:o,y:r,width:a.floating.width,height:a.floating.height}:a.reference,C=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c.floating)),b=await(s.isElement==null?void 0:s.isElement(C))?await(s.getScale==null?void 0:s.getScale(C))||{x:1,y:1}:{x:1,y:1},S=Rt(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:w,offsetParent:C,strategy:l}):w);return{top:(y.top-S.top+x.top)/b.y,bottom:(S.bottom-y.bottom+x.bottom)/b.y,left:(y.left-S.left+x.left)/b.x,right:(S.right-y.right+x.right)/b.x}}const Xc=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:s,platform:a,elements:c,middlewareData:l}=t,{element:u,padding:f=0}=ye(e,t)||{};if(u==null)return{};const d=Mr(f),m={x:n,y:o},h=Tn(r),x=In(h),v=await a.getDimensions(u),g=h==="y",y=g?"top":"left",w=g?"bottom":"right",C=g?"clientHeight":"clientWidth",b=s.reference[x]+s.reference[h]-m[h]-s.floating[x],S=m[h]-s.reference[h],M=await(a.getOffsetParent==null?void 0:a.getOffsetParent(u));let E=M?M[C]:0;(!E||!await(a.isElement==null?void 0:a.isElement(M)))&&(E=c.floating[C]||s.floating[x]);const _=b/2-S/2,O=E/2-v[x]/2-1,D=Pe(d[y],O),$=Pe(d[w],O),B=D,j=E-v[x]-$,k=E/2-v[x]/2+_,W=mn(B,k,j),N=!l.arrow&&Ye(r)!=null&&k!==W&&s.reference[x]/2-(k<B?D:$)-v[x]/2<0,F=N?k<B?k-B:k-j:0;return{[h]:m[h]+F,data:{[h]:W,centerOffset:k-W-F,...N&&{alignmentOffset:F}},reset:N}}}),qc=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:s,rects:a,initialPlacement:c,platform:l,elements:u}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:m,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:v=!0,...g}=ye(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const y=Ce(r),w=pe(c),C=Ce(c)===c,b=await(l.isRTL==null?void 0:l.isRTL(u.floating)),S=m||(C||!v?[Et(c)]:Uc(c)),M=x!=="none";!m&&M&&S.push(...Kc(c,v,x,b));const E=[c,...S],_=await et(t,g),O=[];let D=((o=s.flip)==null?void 0:o.overflows)||[];if(f&&O.push(_[y]),d){const k=Hc(r,a,b);O.push(_[k[0]],_[k[1]])}if(D=[...D,{placement:r,overflows:O}],!O.every(k=>k<=0)){var $,B;const k=((($=s.flip)==null?void 0:$.index)||0)+1,W=E[k];if(W&&(!(d==="alignment"?w!==pe(W):!1)||D.every(I=>I.overflows[0]>0&&pe(I.placement)===w)))return{data:{index:k,overflows:D},reset:{placement:W}};let N=(B=D.filter(F=>F.overflows[0]<=0).sort((F,I)=>F.overflows[1]-I.overflows[1])[0])==null?void 0:B.placement;if(!N)switch(h){case"bestFit":{var j;const F=(j=D.filter(I=>{if(M){const P=pe(I.placement);return P===w||P==="y"}return!0}).map(I=>[I.placement,I.overflows.filter(P=>P>0).reduce((P,G)=>P+G,0)]).sort((I,P)=>I[1]-P[1])[0])==null?void 0:j[0];F&&(N=F);break}case"initialPlacement":N=c;break}if(r!==N)return{reset:{placement:N}}}return{}}}};function go(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function xo(e){return Bc.some(t=>e[t]>=0)}const Zc=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=ye(e,t);switch(o){case"referenceHidden":{const s=await et(t,{...r,elementContext:"reference"}),a=go(s,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:xo(a)}}}case"escaped":{const s=await et(t,{...r,altBoundary:!0}),a=go(s,n.floating);return{data:{escapedOffsets:a,escaped:xo(a)}}}default:return{}}}}};async function Qc(e,t){const{placement:n,platform:o,elements:r}=e,s=await(o.isRTL==null?void 0:o.isRTL(r.floating)),a=Ce(n),c=Ye(n),l=pe(n)==="y",u=["left","top"].includes(a)?-1:1,f=s&&l?-1:1,d=ye(t,e);let{mainAxis:m,crossAxis:h,alignmentAxis:x}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return c&&typeof x=="number"&&(h=c==="end"?x*-1:x),l?{x:h*f,y:m*u}:{x:m*u,y:h*f}}const Jc=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:s,placement:a,middlewareData:c}=t,l=await Qc(t,e);return a===((n=c.offset)==null?void 0:n.placement)&&(o=c.arrow)!=null&&o.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:a}}}}},el=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:s=!0,crossAxis:a=!1,limiter:c={fn:g=>{let{x:y,y:w}=g;return{x:y,y:w}}},...l}=ye(e,t),u={x:n,y:o},f=await et(t,l),d=pe(Ce(r)),m=Mn(d);let h=u[m],x=u[d];if(s){const g=m==="y"?"top":"left",y=m==="y"?"bottom":"right",w=h+f[g],C=h-f[y];h=mn(w,h,C)}if(a){const g=d==="y"?"top":"left",y=d==="y"?"bottom":"right",w=x+f[g],C=x-f[y];x=mn(w,x,C)}const v=c.fn({...t,[m]:h,[d]:x});return{...v,data:{x:v.x-n,y:v.y-o,enabled:{[m]:s,[d]:a}}}}}},tl=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:s,middlewareData:a}=t,{offset:c=0,mainAxis:l=!0,crossAxis:u=!0}=ye(e,t),f={x:n,y:o},d=pe(r),m=Mn(d);let h=f[m],x=f[d];const v=ye(c,t),g=typeof v=="number"?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(l){const C=m==="y"?"height":"width",b=s.reference[m]-s.floating[C]+g.mainAxis,S=s.reference[m]+s.reference[C]-g.mainAxis;h<b?h=b:h>S&&(h=S)}if(u){var y,w;const C=m==="y"?"width":"height",b=["top","left"].includes(Ce(r)),S=s.reference[d]-s.floating[C]+(b&&((y=a.offset)==null?void 0:y[d])||0)+(b?0:g.crossAxis),M=s.reference[d]+s.reference[C]+(b?0:((w=a.offset)==null?void 0:w[d])||0)-(b?g.crossAxis:0);x<S?x=S:x>M&&(x=M)}return{[m]:h,[d]:x}}}},nl=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:s,platform:a,elements:c}=t,{apply:l=()=>{},...u}=ye(e,t),f=await et(t,u),d=Ce(r),m=Ye(r),h=pe(r)==="y",{width:x,height:v}=s.floating;let g,y;d==="top"||d==="bottom"?(g=d,y=m===(await(a.isRTL==null?void 0:a.isRTL(c.floating))?"start":"end")?"left":"right"):(y=d,g=m==="end"?"top":"bottom");const w=v-f.top-f.bottom,C=x-f.left-f.right,b=Pe(v-f[g],w),S=Pe(x-f[y],C),M=!t.middlewareData.shift;let E=b,_=S;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(_=C),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(E=w),M&&!m){const D=Q(f.left,0),$=Q(f.right,0),B=Q(f.top,0),j=Q(f.bottom,0);h?_=x-2*(D!==0||$!==0?D+$:Q(f.left,f.right)):E=v-2*(B!==0||j!==0?B+j:Q(f.top,f.bottom))}await l({...t,availableWidth:_,availableHeight:E});const O=await a.getDimensions(c.floating);return x!==O.width||v!==O.height?{reset:{rects:!0}}:{}}}};function Lt(){return typeof window<"u"}function Xe(e){return Ir(e)?(e.nodeName||"").toLowerCase():"#document"}function J(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function he(e){var t;return(t=(Ir(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ir(e){return Lt()?e instanceof Node||e instanceof J(e).Node:!1}function le(e){return Lt()?e instanceof Element||e instanceof J(e).Element:!1}function me(e){return Lt()?e instanceof HTMLElement||e instanceof J(e).HTMLElement:!1}function wo(e){return!Lt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof J(e).ShadowRoot}function ct(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=ue(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function ol(e){return["table","td","th"].includes(Xe(e))}function jt(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Nn(e){const t=Dn(),n=le(e)?ue(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function rl(e){let t=Ae(e);for(;me(t)&&!Ge(t);){if(Nn(t))return t;if(jt(t))return null;t=Ae(t)}return null}function Dn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ge(e){return["html","body","#document"].includes(Xe(e))}function ue(e){return J(e).getComputedStyle(e)}function Ft(e){return le(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Ae(e){if(Xe(e)==="html")return e;const t=e.assignedSlot||e.parentNode||wo(e)&&e.host||he(e);return wo(t)?t.host:t}function Tr(e){const t=Ae(e);return Ge(t)?e.ownerDocument?e.ownerDocument.body:e.body:me(t)&&ct(t)?t:Tr(t)}function tt(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=Tr(e),s=r===((o=e.ownerDocument)==null?void 0:o.body),a=J(r);if(s){const c=gn(a);return t.concat(a,a.visualViewport||[],ct(r)?r:[],c&&n?tt(c):[])}return t.concat(r,tt(r,[],n))}function gn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Nr(e){const t=ue(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=me(e),s=r?e.offsetWidth:n,a=r?e.offsetHeight:o,c=St(n)!==s||St(o)!==a;return c&&(n=s,o=a),{width:n,height:o,$:c}}function On(e){return le(e)?e:e.contextElement}function Ue(e){const t=On(e);if(!me(t))return ve(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:s}=Nr(t);let a=(s?St(n.width):n.width)/o,c=(s?St(n.height):n.height)/r;return(!a||!Number.isFinite(a))&&(a=1),(!c||!Number.isFinite(c))&&(c=1),{x:a,y:c}}const sl=ve(0);function Dr(e){const t=J(e);return!Dn()||!t.visualViewport?sl:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function al(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==J(e)?!1:t}function Oe(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=On(e);let a=ve(1);t&&(o?le(o)&&(a=Ue(o)):a=Ue(e));const c=al(s,n,o)?Dr(s):ve(0);let l=(r.left+c.x)/a.x,u=(r.top+c.y)/a.y,f=r.width/a.x,d=r.height/a.y;if(s){const m=J(s),h=o&&le(o)?J(o):o;let x=m,v=gn(x);for(;v&&o&&h!==x;){const g=Ue(v),y=v.getBoundingClientRect(),w=ue(v),C=y.left+(v.clientLeft+parseFloat(w.paddingLeft))*g.x,b=y.top+(v.clientTop+parseFloat(w.paddingTop))*g.y;l*=g.x,u*=g.y,f*=g.x,d*=g.y,l+=C,u+=b,x=J(v),v=gn(x)}}return Rt({width:f,height:d,x:l,y:u})}function kn(e,t){const n=Ft(e).scrollLeft;return t?t.left+n:Oe(he(e)).left+n}function Or(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:kn(e,o)),s=o.top+t.scrollTop;return{x:r,y:s}}function il(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const s=r==="fixed",a=he(o),c=t?jt(t.floating):!1;if(o===a||c&&s)return n;let l={scrollLeft:0,scrollTop:0},u=ve(1);const f=ve(0),d=me(o);if((d||!d&&!s)&&((Xe(o)!=="body"||ct(a))&&(l=Ft(o)),me(o))){const h=Oe(o);u=Ue(o),f.x=h.x+o.clientLeft,f.y=h.y+o.clientTop}const m=a&&!d&&!s?Or(a,l,!0):ve(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+f.x+m.x,y:n.y*u.y-l.scrollTop*u.y+f.y+m.y}}function cl(e){return Array.from(e.getClientRects())}function ll(e){const t=he(e),n=Ft(e),o=e.ownerDocument.body,r=Q(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=Q(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let a=-n.scrollLeft+kn(e);const c=-n.scrollTop;return ue(o).direction==="rtl"&&(a+=Q(t.clientWidth,o.clientWidth)-r),{width:r,height:s,x:a,y:c}}function ul(e,t){const n=J(e),o=he(e),r=n.visualViewport;let s=o.clientWidth,a=o.clientHeight,c=0,l=0;if(r){s=r.width,a=r.height;const u=Dn();(!u||u&&t==="fixed")&&(c=r.offsetLeft,l=r.offsetTop)}return{width:s,height:a,x:c,y:l}}function dl(e,t){const n=Oe(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,s=me(e)?Ue(e):ve(1),a=e.clientWidth*s.x,c=e.clientHeight*s.y,l=r*s.x,u=o*s.y;return{width:a,height:c,x:l,y:u}}function yo(e,t,n){let o;if(t==="viewport")o=ul(e,n);else if(t==="document")o=ll(he(e));else if(le(t))o=dl(t,n);else{const r=Dr(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return Rt(o)}function kr(e,t){const n=Ae(e);return n===t||!le(n)||Ge(n)?!1:ue(n).position==="fixed"||kr(n,t)}function fl(e,t){const n=t.get(e);if(n)return n;let o=tt(e,[],!1).filter(c=>le(c)&&Xe(c)!=="body"),r=null;const s=ue(e).position==="fixed";let a=s?Ae(e):e;for(;le(a)&&!Ge(a);){const c=ue(a),l=Nn(a);!l&&c.position==="fixed"&&(r=null),(s?!l&&!r:!l&&c.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||ct(a)&&!l&&kr(e,a))?o=o.filter(f=>f!==a):r=c,a=Ae(a)}return t.set(e,o),o}function pl(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const a=[...n==="clippingAncestors"?jt(t)?[]:fl(t,this._c):[].concat(n),o],c=a[0],l=a.reduce((u,f)=>{const d=yo(t,f,r);return u.top=Q(d.top,u.top),u.right=Pe(d.right,u.right),u.bottom=Pe(d.bottom,u.bottom),u.left=Q(d.left,u.left),u},yo(t,c,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function vl(e){const{width:t,height:n}=Nr(e);return{width:t,height:n}}function ml(e,t,n){const o=me(t),r=he(t),s=n==="fixed",a=Oe(e,!0,s,t);let c={scrollLeft:0,scrollTop:0};const l=ve(0);function u(){l.x=kn(r)}if(o||!o&&!s)if((Xe(t)!=="body"||ct(r))&&(c=Ft(t)),o){const h=Oe(t,!0,s,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else r&&u();s&&!o&&r&&u();const f=r&&!o&&!s?Or(r,c):ve(0),d=a.left+c.scrollLeft-l.x-f.x,m=a.top+c.scrollTop-l.y-f.y;return{x:d,y:m,width:a.width,height:a.height}}function cn(e){return ue(e).position==="static"}function Co(e,t){if(!me(e)||ue(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return he(e)===n&&(n=n.ownerDocument.body),n}function Lr(e,t){const n=J(e);if(jt(e))return n;if(!me(e)){let r=Ae(e);for(;r&&!Ge(r);){if(le(r)&&!cn(r))return r;r=Ae(r)}return n}let o=Co(e,t);for(;o&&ol(o)&&cn(o);)o=Co(o,t);return o&&Ge(o)&&cn(o)&&!Nn(o)?n:o||rl(e)||n}const hl=async function(e){const t=this.getOffsetParent||Lr,n=this.getDimensions,o=await n(e.floating);return{reference:ml(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function gl(e){return ue(e).direction==="rtl"}const xl={convertOffsetParentRelativeRectToViewportRelativeRect:il,getDocumentElement:he,getClippingRect:pl,getOffsetParent:Lr,getElementRects:hl,getClientRects:cl,getDimensions:vl,getScale:Ue,isElement:le,isRTL:gl};function jr(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function wl(e,t){let n=null,o;const r=he(e);function s(){var c;clearTimeout(o),(c=n)==null||c.disconnect(),n=null}function a(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:f,top:d,width:m,height:h}=u;if(c||t(),!m||!h)return;const x=xt(d),v=xt(r.clientWidth-(f+m)),g=xt(r.clientHeight-(d+h)),y=xt(f),C={rootMargin:-x+"px "+-v+"px "+-g+"px "+-y+"px",threshold:Q(0,Pe(1,l))||1};let b=!0;function S(M){const E=M[0].intersectionRatio;if(E!==l){if(!b)return a();E?a(!1,E):o=setTimeout(()=>{a(!1,1e-7)},1e3)}E===1&&!jr(u,e.getBoundingClientRect())&&a(),b=!1}try{n=new IntersectionObserver(S,{...C,root:r.ownerDocument})}catch{n=new IntersectionObserver(S,C)}n.observe(e)}return a(!0),s}function yl(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,u=On(e),f=r||s?[...u?tt(u):[],...tt(t)]:[];f.forEach(y=>{r&&y.addEventListener("scroll",n,{passive:!0}),s&&y.addEventListener("resize",n)});const d=u&&c?wl(u,n):null;let m=-1,h=null;a&&(h=new ResizeObserver(y=>{let[w]=y;w&&w.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var C;(C=h)==null||C.observe(t)})),n()}),u&&!l&&h.observe(u),h.observe(t));let x,v=l?Oe(e):null;l&&g();function g(){const y=Oe(e);v&&!jr(v,y)&&n(),v=y,x=requestAnimationFrame(g)}return n(),()=>{var y;f.forEach(w=>{r&&w.removeEventListener("scroll",n),s&&w.removeEventListener("resize",n)}),d?.(),(y=h)==null||y.disconnect(),h=null,l&&cancelAnimationFrame(x)}}const Cl=Jc,bl=el,Sl=qc,El=nl,Rl=Zc,bo=Xc,Pl=tl,Al=(e,t,n)=>{const o=new Map,r={platform:xl,...n},s={...r.platform,_c:o};return Yc(e,t,{...r,platform:s})};var _l=typeof document<"u",Ml=function(){},Ct=_l?i.useLayoutEffect:Ml;function Pt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,o,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(o=n;o--!==0;)if(!Pt(e[o],t[o]))return!1;return!0}if(r=Object.keys(e),n=r.length,n!==Object.keys(t).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(t,r[o]))return!1;for(o=n;o--!==0;){const s=r[o];if(!(s==="_owner"&&e.$$typeof)&&!Pt(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Fr(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function So(e,t){const n=Fr(e);return Math.round(t*n)/n}function ln(e){const t=i.useRef(e);return Ct(()=>{t.current=e}),t}function Il(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:r,elements:{reference:s,floating:a}={},transform:c=!0,whileElementsMounted:l,open:u}=e,[f,d]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,h]=i.useState(o);Pt(m,o)||h(o);const[x,v]=i.useState(null),[g,y]=i.useState(null),w=i.useCallback(I=>{I!==M.current&&(M.current=I,v(I))},[]),C=i.useCallback(I=>{I!==E.current&&(E.current=I,y(I))},[]),b=s||x,S=a||g,M=i.useRef(null),E=i.useRef(null),_=i.useRef(f),O=l!=null,D=ln(l),$=ln(r),B=ln(u),j=i.useCallback(()=>{if(!M.current||!E.current)return;const I={placement:t,strategy:n,middleware:m};$.current&&(I.platform=$.current),Al(M.current,E.current,I).then(P=>{const G={...P,isPositioned:B.current!==!1};k.current&&!Pt(_.current,G)&&(_.current=G,rt.flushSync(()=>{d(G)}))})},[m,t,n,$,B]);Ct(()=>{u===!1&&_.current.isPositioned&&(_.current.isPositioned=!1,d(I=>({...I,isPositioned:!1})))},[u]);const k=i.useRef(!1);Ct(()=>(k.current=!0,()=>{k.current=!1}),[]),Ct(()=>{if(b&&(M.current=b),S&&(E.current=S),b&&S){if(D.current)return D.current(b,S,j);j()}},[b,S,j,D,O]);const W=i.useMemo(()=>({reference:M,floating:E,setReference:w,setFloating:C}),[w,C]),N=i.useMemo(()=>({reference:b,floating:S}),[b,S]),F=i.useMemo(()=>{const I={position:n,left:0,top:0};if(!N.floating)return I;const P=So(N.floating,f.x),G=So(N.floating,f.y);return c?{...I,transform:"translate("+P+"px, "+G+"px)",...Fr(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:P,top:G}},[n,c,N.floating,f.x,f.y]);return i.useMemo(()=>({...f,update:j,refs:W,elements:N,floatingStyles:F}),[f,j,W,N,F])}const Tl=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:o,padding:r}=typeof e=="function"?e(n):e;return o&&t(o)?o.current!=null?bo({element:o.current,padding:r}).fn(n):{}:o?bo({element:o,padding:r}).fn(n):{}}}},Nl=(e,t)=>({...Cl(e),options:[e,t]}),Dl=(e,t)=>({...bl(e),options:[e,t]}),Ol=(e,t)=>({...Pl(e),options:[e,t]}),kl=(e,t)=>({...Sl(e),options:[e,t]}),Ll=(e,t)=>({...El(e),options:[e,t]}),jl=(e,t)=>({...Rl(e),options:[e,t]}),Fl=(e,t)=>({...Tl(e),options:[e,t]});var $l="Arrow",$r=i.forwardRef((e,t)=>{const{children:n,width:o=10,height:r=5,...s}=e;return p.jsx(T.svg,{...s,ref:t,width:o,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:p.jsx("polygon",{points:"0,0 30,0 15,10"})})});$r.displayName=$l;var Bl=$r,Ln="Popper",[Br,qe]=ee(Ln),[Wl,Wr]=Br(Ln),Vr=e=>{const{__scopePopper:t,children:n}=e,[o,r]=i.useState(null);return p.jsx(Wl,{scope:t,anchor:o,onAnchorChange:r,children:n})};Vr.displayName=Ln;var Hr="PopperAnchor",Ur=i.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...r}=e,s=Wr(Hr,n),a=i.useRef(null),c=L(t,a);return i.useEffect(()=>{s.onAnchorChange(o?.current||a.current)}),o?null:p.jsx(T.div,{...r,ref:c})});Ur.displayName=Hr;var jn="PopperContent",[Vl,Hl]=Br(jn),Gr=i.forwardRef((e,t)=>{const{__scopePopper:n,side:o="bottom",sideOffset:r=0,align:s="center",alignOffset:a=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:d="partial",hideWhenDetached:m=!1,updatePositionStrategy:h="optimized",onPlaced:x,...v}=e,g=Wr(jn,n),[y,w]=i.useState(null),C=L(t,A=>w(A)),[b,S]=i.useState(null),M=Cr(b),E=M?.width??0,_=M?.height??0,O=o+(s!=="center"?"-"+s:""),D=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},$=Array.isArray(u)?u:[u],B=$.length>0,j={padding:D,boundary:$.filter(Gl),altBoundary:B},{refs:k,floatingStyles:W,placement:N,isPositioned:F,middlewareData:I}=Il({strategy:"fixed",placement:O,whileElementsMounted:(...A)=>yl(...A,{animationFrame:h==="always"}),elements:{reference:g.anchor},middleware:[Nl({mainAxis:r+_,alignmentAxis:a}),l&&Dl({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?Ol():void 0,...j}),l&&kl({...j}),Ll({...j,apply:({elements:A,rects:V,availableWidth:X,availableHeight:H})=>{const{width:U,height:K}=V.reference,oe=A.floating.style;oe.setProperty("--radix-popper-available-width",`${X}px`),oe.setProperty("--radix-popper-available-height",`${H}px`),oe.setProperty("--radix-popper-anchor-width",`${U}px`),oe.setProperty("--radix-popper-anchor-height",`${K}px`)}}),b&&Fl({element:b,padding:c}),Kl({arrowWidth:E,arrowHeight:_}),m&&jl({strategy:"referenceHidden",...j})]}),[P,G]=Yr(N),Y=ce(x);z(()=>{F&&Y?.()},[F,Y]);const ae=I.arrow?.x,ge=I.arrow?.y,ne=I.arrow?.centerOffset!==0,[xe,Z]=i.useState();return z(()=>{y&&Z(window.getComputedStyle(y).zIndex)},[y]),p.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:F?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:xe,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:p.jsx(Vl,{scope:n,placedSide:P,onArrowChange:S,arrowX:ae,arrowY:ge,shouldHideArrow:ne,children:p.jsx(T.div,{"data-side":P,"data-align":G,...v,ref:C,style:{...v.style,animation:F?void 0:"none"}})})})});Gr.displayName=jn;var Kr="PopperArrow",Ul={top:"bottom",right:"left",bottom:"top",left:"right"},zr=i.forwardRef(function(t,n){const{__scopePopper:o,...r}=t,s=Hl(Kr,o),a=Ul[s.placedSide];return p.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:p.jsx(Bl,{...r,ref:n,style:{...r.style,display:"block"}})})});zr.displayName=Kr;function Gl(e){return e!==null}var Kl=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:o,middlewareData:r}=t,a=r.arrow?.centerOffset!==0,c=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[u,f]=Yr(n),d={start:"0%",center:"50%",end:"100%"}[f],m=(r.arrow?.x??0)+c/2,h=(r.arrow?.y??0)+l/2;let x="",v="";return u==="bottom"?(x=a?d:`${m}px`,v=`${-l}px`):u==="top"?(x=a?d:`${m}px`,v=`${o.floating.height+l}px`):u==="right"?(x=`${-l}px`,v=a?d:`${h}px`):u==="left"&&(x=`${o.floating.width+l}px`,v=a?d:`${h}px`),{data:{x,y:v}}}});function Yr(e){const[t,n="center"]=e.split("-");return[t,n]}var Fn=Vr,$t=Ur,$n=Gr,Bn=zr,Bt="Popover",[Xr,Sf]=ee(Bt,[qe]),lt=qe(),[zl,_e]=Xr(Bt),qr=e=>{const{__scopePopover:t,children:n,open:o,defaultOpen:r,onOpenChange:s,modal:a=!1}=e,c=lt(t),l=i.useRef(null),[u,f]=i.useState(!1),[d,m]=we({prop:o,defaultProp:r??!1,onChange:s,caller:Bt});return p.jsx(Fn,{...c,children:p.jsx(zl,{scope:t,contentId:re(),triggerRef:l,open:d,onOpenChange:m,onOpenToggle:i.useCallback(()=>m(h=>!h),[m]),hasCustomAnchor:u,onCustomAnchorAdd:i.useCallback(()=>f(!0),[]),onCustomAnchorRemove:i.useCallback(()=>f(!1),[]),modal:a,children:n})})};qr.displayName=Bt;var Zr="PopoverAnchor",Yl=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=_e(Zr,n),s=lt(n),{onCustomAnchorAdd:a,onCustomAnchorRemove:c}=r;return i.useEffect(()=>(a(),()=>c()),[a,c]),p.jsx($t,{...s,...o,ref:t})});Yl.displayName=Zr;var Qr="PopoverTrigger",Jr=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=_e(Qr,n),s=lt(n),a=L(t,r.triggerRef),c=p.jsx(T.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":rs(r.open),...o,ref:a,onClick:R(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?c:p.jsx($t,{asChild:!0,...s,children:c})});Jr.displayName=Qr;var Wn="PopoverPortal",[Xl,ql]=Xr(Wn,{forceMount:void 0}),es=e=>{const{__scopePopover:t,forceMount:n,children:o,container:r}=e,s=_e(Wn,t);return p.jsx(Xl,{scope:t,forceMount:n,children:p.jsx(te,{present:n||s.open,children:p.jsx(at,{asChild:!0,container:r,children:o})})})};es.displayName=Wn;var Ke="PopoverContent",ts=i.forwardRef((e,t)=>{const n=ql(Ke,e.__scopePopover),{forceMount:o=n.forceMount,...r}=e,s=_e(Ke,e.__scopePopover);return p.jsx(te,{present:o||s.open,children:s.modal?p.jsx(Ql,{...r,ref:t}):p.jsx(Jl,{...r,ref:t})})});ts.displayName=Ke;var Zl=Re("PopoverContent.RemoveScroll"),Ql=i.forwardRef((e,t)=>{const n=_e(Ke,e.__scopePopover),o=i.useRef(null),r=L(t,o),s=i.useRef(!1);return i.useEffect(()=>{const a=o.current;if(a)return Dt(a)},[]),p.jsx(it,{as:Zl,allowPinchZoom:!0,children:p.jsx(ns,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:R(e.onCloseAutoFocus,a=>{a.preventDefault(),s.current||n.triggerRef.current?.focus()}),onPointerDownOutside:R(e.onPointerDownOutside,a=>{const c=a.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0,u=c.button===2||l;s.current=u},{checkForDefaultPrevented:!1}),onFocusOutside:R(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1})})})}),Jl=i.forwardRef((e,t)=>{const n=_e(Ke,e.__scopePopover),o=i.useRef(!1),r=i.useRef(!1);return p.jsx(ns,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(o.current||n.triggerRef.current?.focus(),s.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(o.current=!0,s.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const a=s.target;n.triggerRef.current?.contains(a)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&r.current&&s.preventDefault()}})}),ns=i.forwardRef((e,t)=>{const{__scopePopover:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:s,disableOutsidePointerEvents:a,onEscapeKeyDown:c,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:f,...d}=e,m=_e(Ke,n),h=lt(n);return Tt(),p.jsx(st,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:s,children:p.jsx(ze,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:f,onEscapeKeyDown:c,onPointerDownOutside:l,onFocusOutside:u,onDismiss:()=>m.onOpenChange(!1),children:p.jsx($n,{"data-state":rs(m.open),role:"dialog",id:m.contentId,...h,...d,ref:t,style:{...d.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),os="PopoverClose",eu=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=_e(os,n);return p.jsx(T.button,{type:"button",...o,ref:t,onClick:R(e.onClick,()=>r.onOpenChange(!1))})});eu.displayName=os;var tu="PopoverArrow",nu=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=lt(n);return p.jsx(Bn,{...r,...o,ref:t})});nu.displayName=tu;function rs(e){return e?"open":"closed"}var Ef=qr,Rf=Jr,Pf=es,Af=ts;function Eo(e,[t,n]){return Math.min(n,Math.max(t,e))}function Vn(e){const t=e+"CollectionProvider",[n,o]=ee(t),[r,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=v=>{const{scope:g,children:y}=v,w=Ne.useRef(null),C=Ne.useRef(new Map).current;return p.jsx(r,{scope:g,itemMap:C,collectionRef:w,children:y})};a.displayName=t;const c=e+"CollectionSlot",l=Re(c),u=Ne.forwardRef((v,g)=>{const{scope:y,children:w}=v,C=s(c,y),b=L(g,C.collectionRef);return p.jsx(l,{ref:b,children:w})});u.displayName=c;const f=e+"CollectionItemSlot",d="data-radix-collection-item",m=Re(f),h=Ne.forwardRef((v,g)=>{const{scope:y,children:w,...C}=v,b=Ne.useRef(null),S=L(g,b),M=s(f,y);return Ne.useEffect(()=>(M.itemMap.set(b,{ref:b,...C}),()=>void M.itemMap.delete(b))),p.jsx(m,{[d]:"",ref:S,children:w})});h.displayName=f;function x(v){const g=s(e+"CollectionConsumer",v);return Ne.useCallback(()=>{const w=g.collectionRef.current;if(!w)return[];const C=Array.from(w.querySelectorAll(`[${d}]`));return Array.from(g.itemMap.values()).sort((M,E)=>C.indexOf(M.ref.current)-C.indexOf(E.ref.current))},[g.collectionRef,g.itemMap])}return[{Provider:a,Slot:u,ItemSlot:h},x,o]}var ou=i.createContext(void 0);function Wt(e){const t=i.useContext(ou);return e||t||"ltr"}var ss=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ru="VisuallyHidden",as=i.forwardRef((e,t)=>p.jsx(T.span,{...e,ref:t,style:{...ss,...e.style}}));as.displayName=ru;var _f=as,su=[" ","Enter","ArrowUp","ArrowDown"],au=[" ","Enter"],ke="Select",[Vt,Ht,iu]=Vn(ke),[Ze,Mf]=ee(ke,[iu,qe]),Ut=qe(),[cu,Me]=Ze(ke),[lu,uu]=Ze(ke),is=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:r,onOpenChange:s,value:a,defaultValue:c,onValueChange:l,dir:u,name:f,autoComplete:d,disabled:m,required:h,form:x}=e,v=Ut(t),[g,y]=i.useState(null),[w,C]=i.useState(null),[b,S]=i.useState(!1),M=Wt(u),[E,_]=we({prop:o,defaultProp:r??!1,onChange:s,caller:ke}),[O,D]=we({prop:a,defaultProp:c,onChange:l,caller:ke}),$=i.useRef(null),B=g?x||!!g.closest("form"):!0,[j,k]=i.useState(new Set),W=Array.from(j).map(N=>N.props.value).join(";");return p.jsx(Fn,{...v,children:p.jsxs(cu,{required:h,scope:t,trigger:g,onTriggerChange:y,valueNode:w,onValueNodeChange:C,valueNodeHasChildren:b,onValueNodeHasChildrenChange:S,contentId:re(),value:O,onValueChange:D,open:E,onOpenChange:_,dir:M,triggerPointerDownPosRef:$,disabled:m,children:[p.jsx(Vt.Provider,{scope:t,children:p.jsx(lu,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(N=>{k(F=>new Set(F).add(N))},[]),onNativeOptionRemove:i.useCallback(N=>{k(F=>{const I=new Set(F);return I.delete(N),I})},[]),children:n})}),B?p.jsxs(Ns,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:d,value:O,onChange:N=>D(N.target.value),disabled:m,form:x,children:[O===void 0?p.jsx("option",{value:""}):null,Array.from(j)]},W):null]})})};is.displayName=ke;var cs="SelectTrigger",ls=i.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...r}=e,s=Ut(n),a=Me(cs,n),c=a.disabled||o,l=L(t,a.onTriggerChange),u=Ht(n),f=i.useRef("touch"),[d,m,h]=Os(v=>{const g=u().filter(C=>!C.disabled),y=g.find(C=>C.value===a.value),w=ks(g,v,y);w!==void 0&&a.onValueChange(w.value)}),x=v=>{c||(a.onOpenChange(!0),h()),v&&(a.triggerPointerDownPosRef.current={x:Math.round(v.pageX),y:Math.round(v.pageY)})};return p.jsx($t,{asChild:!0,...s,children:p.jsx(T.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":Ds(a.value)?"":void 0,...r,ref:l,onClick:R(r.onClick,v=>{v.currentTarget.focus(),f.current!=="mouse"&&x(v)}),onPointerDown:R(r.onPointerDown,v=>{f.current=v.pointerType;const g=v.target;g.hasPointerCapture(v.pointerId)&&g.releasePointerCapture(v.pointerId),v.button===0&&v.ctrlKey===!1&&v.pointerType==="mouse"&&(x(v),v.preventDefault())}),onKeyDown:R(r.onKeyDown,v=>{const g=d.current!=="";!(v.ctrlKey||v.altKey||v.metaKey)&&v.key.length===1&&m(v.key),!(g&&v.key===" ")&&su.includes(v.key)&&(x(),v.preventDefault())})})})});ls.displayName=cs;var us="SelectValue",ds=i.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,children:s,placeholder:a="",...c}=e,l=Me(us,n),{onValueNodeHasChildrenChange:u}=l,f=s!==void 0,d=L(t,l.onValueNodeChange);return z(()=>{u(f)},[u,f]),p.jsx(T.span,{...c,ref:d,style:{pointerEvents:"none"},children:Ds(l.value)?p.jsx(p.Fragment,{children:a}):s})});ds.displayName=us;var du="SelectIcon",fs=i.forwardRef((e,t)=>{const{__scopeSelect:n,children:o,...r}=e;return p.jsx(T.span,{"aria-hidden":!0,...r,ref:t,children:o||"▼"})});fs.displayName=du;var fu="SelectPortal",ps=e=>p.jsx(at,{asChild:!0,...e});ps.displayName=fu;var Le="SelectContent",vs=i.forwardRef((e,t)=>{const n=Me(Le,e.__scopeSelect),[o,r]=i.useState();if(z(()=>{r(new DocumentFragment)},[]),!n.open){const s=o;return s?rt.createPortal(p.jsx(ms,{scope:e.__scopeSelect,children:p.jsx(Vt.Slot,{scope:e.__scopeSelect,children:p.jsx("div",{children:e.children})})}),s):null}return p.jsx(hs,{...e,ref:t})});vs.displayName=Le;var ie=10,[ms,Ie]=Ze(Le),pu="SelectContentImpl",vu=Re("SelectContent.RemoveScroll"),hs=i.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:r,onEscapeKeyDown:s,onPointerDownOutside:a,side:c,sideOffset:l,align:u,alignOffset:f,arrowPadding:d,collisionBoundary:m,collisionPadding:h,sticky:x,hideWhenDetached:v,avoidCollisions:g,...y}=e,w=Me(Le,n),[C,b]=i.useState(null),[S,M]=i.useState(null),E=L(t,A=>b(A)),[_,O]=i.useState(null),[D,$]=i.useState(null),B=Ht(n),[j,k]=i.useState(!1),W=i.useRef(!1);i.useEffect(()=>{if(C)return Dt(C)},[C]),Tt();const N=i.useCallback(A=>{const[V,...X]=B().map(K=>K.ref.current),[H]=X.slice(-1),U=document.activeElement;for(const K of A)if(K===U||(K?.scrollIntoView({block:"nearest"}),K===V&&S&&(S.scrollTop=0),K===H&&S&&(S.scrollTop=S.scrollHeight),K?.focus(),document.activeElement!==U))return},[B,S]),F=i.useCallback(()=>N([_,C]),[N,_,C]);i.useEffect(()=>{j&&F()},[j,F]);const{onOpenChange:I,triggerPointerDownPosRef:P}=w;i.useEffect(()=>{if(C){let A={x:0,y:0};const V=H=>{A={x:Math.abs(Math.round(H.pageX)-(P.current?.x??0)),y:Math.abs(Math.round(H.pageY)-(P.current?.y??0))}},X=H=>{A.x<=10&&A.y<=10?H.preventDefault():C.contains(H.target)||I(!1),document.removeEventListener("pointermove",V),P.current=null};return P.current!==null&&(document.addEventListener("pointermove",V),document.addEventListener("pointerup",X,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",V),document.removeEventListener("pointerup",X,{capture:!0})}}},[C,I,P]),i.useEffect(()=>{const A=()=>I(!1);return window.addEventListener("blur",A),window.addEventListener("resize",A),()=>{window.removeEventListener("blur",A),window.removeEventListener("resize",A)}},[I]);const[G,Y]=Os(A=>{const V=B().filter(U=>!U.disabled),X=V.find(U=>U.ref.current===document.activeElement),H=ks(V,A,X);H&&setTimeout(()=>H.ref.current.focus())}),ae=i.useCallback((A,V,X)=>{const H=!W.current&&!X;(w.value!==void 0&&w.value===V||H)&&(O(A),H&&(W.current=!0))},[w.value]),ge=i.useCallback(()=>C?.focus(),[C]),ne=i.useCallback((A,V,X)=>{const H=!W.current&&!X;(w.value!==void 0&&w.value===V||H)&&$(A)},[w.value]),xe=o==="popper"?xn:gs,Z=xe===xn?{side:c,sideOffset:l,align:u,alignOffset:f,arrowPadding:d,collisionBoundary:m,collisionPadding:h,sticky:x,hideWhenDetached:v,avoidCollisions:g}:{};return p.jsx(ms,{scope:n,content:C,viewport:S,onViewportChange:M,itemRefCallback:ae,selectedItem:_,onItemLeave:ge,itemTextRefCallback:ne,focusSelectedItem:F,selectedItemText:D,position:o,isPositioned:j,searchRef:G,children:p.jsx(it,{as:vu,allowPinchZoom:!0,children:p.jsx(st,{asChild:!0,trapped:w.open,onMountAutoFocus:A=>{A.preventDefault()},onUnmountAutoFocus:R(r,A=>{w.trigger?.focus({preventScroll:!0}),A.preventDefault()}),children:p.jsx(ze,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:A=>A.preventDefault(),onDismiss:()=>w.onOpenChange(!1),children:p.jsx(xe,{role:"listbox",id:w.contentId,"data-state":w.open?"open":"closed",dir:w.dir,onContextMenu:A=>A.preventDefault(),...y,...Z,onPlaced:()=>k(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:R(y.onKeyDown,A=>{const V=A.ctrlKey||A.altKey||A.metaKey;if(A.key==="Tab"&&A.preventDefault(),!V&&A.key.length===1&&Y(A.key),["ArrowUp","ArrowDown","Home","End"].includes(A.key)){let H=B().filter(U=>!U.disabled).map(U=>U.ref.current);if(["ArrowUp","End"].includes(A.key)&&(H=H.slice().reverse()),["ArrowUp","ArrowDown"].includes(A.key)){const U=A.target,K=H.indexOf(U);H=H.slice(K+1)}setTimeout(()=>N(H)),A.preventDefault()}})})})})})})});hs.displayName=pu;var mu="SelectItemAlignedPosition",gs=i.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...r}=e,s=Me(Le,n),a=Ie(Le,n),[c,l]=i.useState(null),[u,f]=i.useState(null),d=L(t,E=>f(E)),m=Ht(n),h=i.useRef(!1),x=i.useRef(!0),{viewport:v,selectedItem:g,selectedItemText:y,focusSelectedItem:w}=a,C=i.useCallback(()=>{if(s.trigger&&s.valueNode&&c&&u&&v&&g&&y){const E=s.trigger.getBoundingClientRect(),_=u.getBoundingClientRect(),O=s.valueNode.getBoundingClientRect(),D=y.getBoundingClientRect();if(s.dir!=="rtl"){const U=D.left-_.left,K=O.left-U,oe=E.left-K,Te=E.width+oe,Zt=Math.max(Te,_.width),Qt=window.innerWidth-ie,Jt=Eo(K,[ie,Math.max(ie,Qt-Zt)]);c.style.minWidth=Te+"px",c.style.left=Jt+"px"}else{const U=_.right-D.right,K=window.innerWidth-O.right-U,oe=window.innerWidth-E.right-K,Te=E.width+oe,Zt=Math.max(Te,_.width),Qt=window.innerWidth-ie,Jt=Eo(K,[ie,Math.max(ie,Qt-Zt)]);c.style.minWidth=Te+"px",c.style.right=Jt+"px"}const $=m(),B=window.innerHeight-ie*2,j=v.scrollHeight,k=window.getComputedStyle(u),W=parseInt(k.borderTopWidth,10),N=parseInt(k.paddingTop,10),F=parseInt(k.borderBottomWidth,10),I=parseInt(k.paddingBottom,10),P=W+N+j+I+F,G=Math.min(g.offsetHeight*5,P),Y=window.getComputedStyle(v),ae=parseInt(Y.paddingTop,10),ge=parseInt(Y.paddingBottom,10),ne=E.top+E.height/2-ie,xe=B-ne,Z=g.offsetHeight/2,A=g.offsetTop+Z,V=W+N+A,X=P-V;if(V<=ne){const U=$.length>0&&g===$[$.length-1].ref.current;c.style.bottom="0px";const K=u.clientHeight-v.offsetTop-v.offsetHeight,oe=Math.max(xe,Z+(U?ge:0)+K+F),Te=V+oe;c.style.height=Te+"px"}else{const U=$.length>0&&g===$[0].ref.current;c.style.top="0px";const oe=Math.max(ne,W+v.offsetTop+(U?ae:0)+Z)+X;c.style.height=oe+"px",v.scrollTop=V-ne+v.offsetTop}c.style.margin=`${ie}px 0`,c.style.minHeight=G+"px",c.style.maxHeight=B+"px",o?.(),requestAnimationFrame(()=>h.current=!0)}},[m,s.trigger,s.valueNode,c,u,v,g,y,s.dir,o]);z(()=>C(),[C]);const[b,S]=i.useState();z(()=>{u&&S(window.getComputedStyle(u).zIndex)},[u]);const M=i.useCallback(E=>{E&&x.current===!0&&(C(),w?.(),x.current=!1)},[C,w]);return p.jsx(gu,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:M,children:p.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:b},children:p.jsx(T.div,{...r,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...r.style}})})})});gs.displayName=mu;var hu="SelectPopperPosition",xn=i.forwardRef((e,t)=>{const{__scopeSelect:n,align:o="start",collisionPadding:r=ie,...s}=e,a=Ut(n);return p.jsx($n,{...a,...s,ref:t,align:o,collisionPadding:r,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});xn.displayName=hu;var[gu,Hn]=Ze(Le,{}),wn="SelectViewport",xs=i.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...r}=e,s=Ie(wn,n),a=Hn(wn,n),c=L(t,s.onViewportChange),l=i.useRef(0);return p.jsxs(p.Fragment,{children:[p.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),p.jsx(Vt.Slot,{scope:n,children:p.jsx(T.div,{"data-radix-select-viewport":"",role:"presentation",...r,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...r.style},onScroll:R(r.onScroll,u=>{const f=u.currentTarget,{contentWrapper:d,shouldExpandOnScrollRef:m}=a;if(m?.current&&d){const h=Math.abs(l.current-f.scrollTop);if(h>0){const x=window.innerHeight-ie*2,v=parseFloat(d.style.minHeight),g=parseFloat(d.style.height),y=Math.max(v,g);if(y<x){const w=y+h,C=Math.min(x,w),b=w-C;d.style.height=C+"px",d.style.bottom==="0px"&&(f.scrollTop=b>0?b:0,d.style.justifyContent="flex-end")}}}l.current=f.scrollTop})})})]})});xs.displayName=wn;var ws="SelectGroup",[xu,wu]=Ze(ws),ys=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=re();return p.jsx(xu,{scope:n,id:r,children:p.jsx(T.div,{role:"group","aria-labelledby":r,...o,ref:t})})});ys.displayName=ws;var Cs="SelectLabel",bs=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=wu(Cs,n);return p.jsx(T.div,{id:r.id,...o,ref:t})});bs.displayName=Cs;var At="SelectItem",[yu,Ss]=Ze(At),Es=i.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:r=!1,textValue:s,...a}=e,c=Me(At,n),l=Ie(At,n),u=c.value===o,[f,d]=i.useState(s??""),[m,h]=i.useState(!1),x=L(t,w=>l.itemRefCallback?.(w,o,r)),v=re(),g=i.useRef("touch"),y=()=>{r||(c.onValueChange(o),c.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return p.jsx(yu,{scope:n,value:o,disabled:r,textId:v,isSelected:u,onItemTextChange:i.useCallback(w=>{d(C=>C||(w?.textContent??"").trim())},[]),children:p.jsx(Vt.ItemSlot,{scope:n,value:o,disabled:r,textValue:f,children:p.jsx(T.div,{role:"option","aria-labelledby":v,"data-highlighted":m?"":void 0,"aria-selected":u&&m,"data-state":u?"checked":"unchecked","aria-disabled":r||void 0,"data-disabled":r?"":void 0,tabIndex:r?void 0:-1,...a,ref:x,onFocus:R(a.onFocus,()=>h(!0)),onBlur:R(a.onBlur,()=>h(!1)),onClick:R(a.onClick,()=>{g.current!=="mouse"&&y()}),onPointerUp:R(a.onPointerUp,()=>{g.current==="mouse"&&y()}),onPointerDown:R(a.onPointerDown,w=>{g.current=w.pointerType}),onPointerMove:R(a.onPointerMove,w=>{g.current=w.pointerType,r?l.onItemLeave?.():g.current==="mouse"&&w.currentTarget.focus({preventScroll:!0})}),onPointerLeave:R(a.onPointerLeave,w=>{w.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:R(a.onKeyDown,w=>{l.searchRef?.current!==""&&w.key===" "||(au.includes(w.key)&&y(),w.key===" "&&w.preventDefault())})})})})});Es.displayName=At;var Qe="SelectItemText",Rs=i.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,...s}=e,a=Me(Qe,n),c=Ie(Qe,n),l=Ss(Qe,n),u=uu(Qe,n),[f,d]=i.useState(null),m=L(t,y=>d(y),l.onItemTextChange,y=>c.itemTextRefCallback?.(y,l.value,l.disabled)),h=f?.textContent,x=i.useMemo(()=>p.jsx("option",{value:l.value,disabled:l.disabled,children:h},l.value),[l.disabled,l.value,h]),{onNativeOptionAdd:v,onNativeOptionRemove:g}=u;return z(()=>(v(x),()=>g(x)),[v,g,x]),p.jsxs(p.Fragment,{children:[p.jsx(T.span,{id:l.textId,...s,ref:m}),l.isSelected&&a.valueNode&&!a.valueNodeHasChildren?rt.createPortal(s.children,a.valueNode):null]})});Rs.displayName=Qe;var Ps="SelectItemIndicator",As=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return Ss(Ps,n).isSelected?p.jsx(T.span,{"aria-hidden":!0,...o,ref:t}):null});As.displayName=Ps;var yn="SelectScrollUpButton",_s=i.forwardRef((e,t)=>{const n=Ie(yn,e.__scopeSelect),o=Hn(yn,e.__scopeSelect),[r,s]=i.useState(!1),a=L(t,o.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollTop>0;s(u)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?p.jsx(Is,{...e,ref:a,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop-l.offsetHeight)}}):null});_s.displayName=yn;var Cn="SelectScrollDownButton",Ms=i.forwardRef((e,t)=>{const n=Ie(Cn,e.__scopeSelect),o=Hn(Cn,e.__scopeSelect),[r,s]=i.useState(!1),a=L(t,o.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollHeight-l.clientHeight,f=Math.ceil(l.scrollTop)<u;s(f)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?p.jsx(Is,{...e,ref:a,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop+l.offsetHeight)}}):null});Ms.displayName=Cn;var Is=i.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...r}=e,s=Ie("SelectScrollButton",n),a=i.useRef(null),c=Ht(n),l=i.useCallback(()=>{a.current!==null&&(window.clearInterval(a.current),a.current=null)},[]);return i.useEffect(()=>()=>l(),[l]),z(()=>{c().find(f=>f.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[c]),p.jsx(T.div,{"aria-hidden":!0,...r,ref:t,style:{flexShrink:0,...r.style},onPointerDown:R(r.onPointerDown,()=>{a.current===null&&(a.current=window.setInterval(o,50))}),onPointerMove:R(r.onPointerMove,()=>{s.onItemLeave?.(),a.current===null&&(a.current=window.setInterval(o,50))}),onPointerLeave:R(r.onPointerLeave,()=>{l()})})}),Cu="SelectSeparator",Ts=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return p.jsx(T.div,{"aria-hidden":!0,...o,ref:t})});Ts.displayName=Cu;var bn="SelectArrow",bu=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=Ut(n),s=Me(bn,n),a=Ie(bn,n);return s.open&&a.position==="popper"?p.jsx(Bn,{...r,...o,ref:t}):null});bu.displayName=bn;var Su="SelectBubbleInput",Ns=i.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{const r=i.useRef(null),s=L(o,r),a=yr(t);return i.useEffect(()=>{const c=r.current;if(!c)return;const l=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(l,"value").set;if(a!==t&&f){const d=new Event("change",{bubbles:!0});f.call(c,t),c.dispatchEvent(d)}},[a,t]),p.jsx(T.select,{...n,style:{...ss,...n.style},ref:s,defaultValue:t})});Ns.displayName=Su;function Ds(e){return e===""||e===void 0}function Os(e){const t=ce(e),n=i.useRef(""),o=i.useRef(0),r=i.useCallback(a=>{const c=n.current+a;t(c),function l(u){n.current=u,window.clearTimeout(o.current),u!==""&&(o.current=window.setTimeout(()=>l(""),1e3))}(c)},[t]),s=i.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,r,s]}function ks(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let a=Eu(e,Math.max(s,0));r.length===1&&(a=a.filter(u=>u!==n));const l=a.find(u=>u.textValue.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function Eu(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var If=is,Tf=ls,Nf=ds,Df=fs,Of=ps,kf=vs,Lf=xs,jf=ys,Ff=bs,$f=Es,Bf=Rs,Wf=As,Vf=_s,Hf=Ms,Uf=Ts,un="rovingFocusGroup.onEntryFocus",Ru={bubbles:!1,cancelable:!0},ut="RovingFocusGroup",[Sn,Ls,Pu]=Vn(ut),[Au,Gt]=ee(ut,[Pu]),[_u,Mu]=Au(ut),js=i.forwardRef((e,t)=>p.jsx(Sn.Provider,{scope:e.__scopeRovingFocusGroup,children:p.jsx(Sn.Slot,{scope:e.__scopeRovingFocusGroup,children:p.jsx(Iu,{...e,ref:t})})}));js.displayName=ut;var Iu=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:o,loop:r=!1,dir:s,currentTabStopId:a,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...d}=e,m=i.useRef(null),h=L(t,m),x=Wt(s),[v,g]=we({prop:a,defaultProp:c??null,onChange:l,caller:ut}),[y,w]=i.useState(!1),C=ce(u),b=Ls(n),S=i.useRef(!1),[M,E]=i.useState(0);return i.useEffect(()=>{const _=m.current;if(_)return _.addEventListener(un,C),()=>_.removeEventListener(un,C)},[C]),p.jsx(_u,{scope:n,orientation:o,dir:x,loop:r,currentTabStopId:v,onItemFocus:i.useCallback(_=>g(_),[g]),onItemShiftTab:i.useCallback(()=>w(!0),[]),onFocusableItemAdd:i.useCallback(()=>E(_=>_+1),[]),onFocusableItemRemove:i.useCallback(()=>E(_=>_-1),[]),children:p.jsx(T.div,{tabIndex:y||M===0?-1:0,"data-orientation":o,...d,ref:h,style:{outline:"none",...e.style},onMouseDown:R(e.onMouseDown,()=>{S.current=!0}),onFocus:R(e.onFocus,_=>{const O=!S.current;if(_.target===_.currentTarget&&O&&!y){const D=new CustomEvent(un,Ru);if(_.currentTarget.dispatchEvent(D),!D.defaultPrevented){const $=b().filter(N=>N.focusable),B=$.find(N=>N.active),j=$.find(N=>N.id===v),W=[B,j,...$].filter(Boolean).map(N=>N.ref.current);Bs(W,f)}}S.current=!1}),onBlur:R(e.onBlur,()=>w(!1))})})}),Fs="RovingFocusGroupItem",$s=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:o=!0,active:r=!1,tabStopId:s,children:a,...c}=e,l=re(),u=s||l,f=Mu(Fs,n),d=f.currentTabStopId===u,m=Ls(n),{onFocusableItemAdd:h,onFocusableItemRemove:x,currentTabStopId:v}=f;return i.useEffect(()=>{if(o)return h(),()=>x()},[o,h,x]),p.jsx(Sn.ItemSlot,{scope:n,id:u,focusable:o,active:r,children:p.jsx(T.span,{tabIndex:d?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:R(e.onMouseDown,g=>{o?f.onItemFocus(u):g.preventDefault()}),onFocus:R(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:R(e.onKeyDown,g=>{if(g.key==="Tab"&&g.shiftKey){f.onItemShiftTab();return}if(g.target!==g.currentTarget)return;const y=Du(g,f.orientation,f.dir);if(y!==void 0){if(g.metaKey||g.ctrlKey||g.altKey||g.shiftKey)return;g.preventDefault();let C=m().filter(b=>b.focusable).map(b=>b.ref.current);if(y==="last")C.reverse();else if(y==="prev"||y==="next"){y==="prev"&&C.reverse();const b=C.indexOf(g.currentTarget);C=f.loop?Ou(C,b+1):C.slice(b+1)}setTimeout(()=>Bs(C))}}),children:typeof a=="function"?a({isCurrentTabStop:d,hasTabStop:v!=null}):a})})});$s.displayName=Fs;var Tu={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Nu(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Du(e,t,n){const o=Nu(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return Tu[o]}function Bs(e,t=!1){const n=document.activeElement;for(const o of e)if(o===n||(o.focus({preventScroll:t}),document.activeElement!==n))return}function Ou(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var Ws=js,Vs=$s,En=["Enter"," "],ku=["ArrowDown","PageUp","Home"],Hs=["ArrowUp","PageDown","End"],Lu=[...ku,...Hs],ju={ltr:[...En,"ArrowRight"],rtl:[...En,"ArrowLeft"]},Fu={ltr:["ArrowLeft"],rtl:["ArrowRight"]},dt="Menu",[nt,$u,Bu]=Vn(dt),[je,Us]=ee(dt,[Bu,qe,Gt]),Kt=qe(),Gs=Gt(),[Wu,Fe]=je(dt),[Vu,ft]=je(dt),Ks=e=>{const{__scopeMenu:t,open:n=!1,children:o,dir:r,onOpenChange:s,modal:a=!0}=e,c=Kt(t),[l,u]=i.useState(null),f=i.useRef(!1),d=ce(s),m=Wt(r);return i.useEffect(()=>{const h=()=>{f.current=!0,document.addEventListener("pointerdown",x,{capture:!0,once:!0}),document.addEventListener("pointermove",x,{capture:!0,once:!0})},x=()=>f.current=!1;return document.addEventListener("keydown",h,{capture:!0}),()=>{document.removeEventListener("keydown",h,{capture:!0}),document.removeEventListener("pointerdown",x,{capture:!0}),document.removeEventListener("pointermove",x,{capture:!0})}},[]),p.jsx(Fn,{...c,children:p.jsx(Wu,{scope:t,open:n,onOpenChange:d,content:l,onContentChange:u,children:p.jsx(Vu,{scope:t,onClose:i.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:f,dir:m,modal:a,children:o})})})};Ks.displayName=dt;var Hu="MenuAnchor",Un=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=Kt(n);return p.jsx($t,{...r,...o,ref:t})});Un.displayName=Hu;var Gn="MenuPortal",[Uu,zs]=je(Gn,{forceMount:void 0}),Ys=e=>{const{__scopeMenu:t,forceMount:n,children:o,container:r}=e,s=Fe(Gn,t);return p.jsx(Uu,{scope:t,forceMount:n,children:p.jsx(te,{present:n||s.open,children:p.jsx(at,{asChild:!0,container:r,children:o})})})};Ys.displayName=Gn;var se="MenuContent",[Gu,Kn]=je(se),Xs=i.forwardRef((e,t)=>{const n=zs(se,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,s=Fe(se,e.__scopeMenu),a=ft(se,e.__scopeMenu);return p.jsx(nt.Provider,{scope:e.__scopeMenu,children:p.jsx(te,{present:o||s.open,children:p.jsx(nt.Slot,{scope:e.__scopeMenu,children:a.modal?p.jsx(Ku,{...r,ref:t}):p.jsx(zu,{...r,ref:t})})})})}),Ku=i.forwardRef((e,t)=>{const n=Fe(se,e.__scopeMenu),o=i.useRef(null),r=L(t,o);return i.useEffect(()=>{const s=o.current;if(s)return Dt(s)},[]),p.jsx(zn,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:R(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),zu=i.forwardRef((e,t)=>{const n=Fe(se,e.__scopeMenu);return p.jsx(zn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Yu=Re("MenuContent.ScrollLock"),zn=i.forwardRef((e,t)=>{const{__scopeMenu:n,loop:o=!1,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:a,disableOutsidePointerEvents:c,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:m,onDismiss:h,disableOutsideScroll:x,...v}=e,g=Fe(se,n),y=ft(se,n),w=Kt(n),C=Gs(n),b=$u(n),[S,M]=i.useState(null),E=i.useRef(null),_=L(t,E,g.onContentChange),O=i.useRef(0),D=i.useRef(""),$=i.useRef(0),B=i.useRef(null),j=i.useRef("right"),k=i.useRef(0),W=x?it:i.Fragment,N=x?{as:Yu,allowPinchZoom:!0}:void 0,F=P=>{const G=D.current+P,Y=b().filter(A=>!A.disabled),ae=document.activeElement,ge=Y.find(A=>A.ref.current===ae)?.textValue,ne=Y.map(A=>A.textValue),xe=ad(ne,G,ge),Z=Y.find(A=>A.textValue===xe)?.ref.current;(function A(V){D.current=V,window.clearTimeout(O.current),V!==""&&(O.current=window.setTimeout(()=>A(""),1e3))})(G),Z&&setTimeout(()=>Z.focus())};i.useEffect(()=>()=>window.clearTimeout(O.current),[]),Tt();const I=i.useCallback(P=>j.current===B.current?.side&&cd(P,B.current?.area),[]);return p.jsx(Gu,{scope:n,searchRef:D,onItemEnter:i.useCallback(P=>{I(P)&&P.preventDefault()},[I]),onItemLeave:i.useCallback(P=>{I(P)||(E.current?.focus(),M(null))},[I]),onTriggerLeave:i.useCallback(P=>{I(P)&&P.preventDefault()},[I]),pointerGraceTimerRef:$,onPointerGraceIntentChange:i.useCallback(P=>{B.current=P},[]),children:p.jsx(W,{...N,children:p.jsx(st,{asChild:!0,trapped:r,onMountAutoFocus:R(s,P=>{P.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:p.jsx(ze,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:m,onDismiss:h,children:p.jsx(Ws,{asChild:!0,...C,dir:y.dir,orientation:"vertical",loop:o,currentTabStopId:S,onCurrentTabStopIdChange:M,onEntryFocus:R(l,P=>{y.isUsingKeyboardRef.current||P.preventDefault()}),preventScrollOnEntryFocus:!0,children:p.jsx($n,{role:"menu","aria-orientation":"vertical","data-state":da(g.open),"data-radix-menu-content":"",dir:y.dir,...w,...v,ref:_,style:{outline:"none",...v.style},onKeyDown:R(v.onKeyDown,P=>{const Y=P.target.closest("[data-radix-menu-content]")===P.currentTarget,ae=P.ctrlKey||P.altKey||P.metaKey,ge=P.key.length===1;Y&&(P.key==="Tab"&&P.preventDefault(),!ae&&ge&&F(P.key));const ne=E.current;if(P.target!==ne||!Lu.includes(P.key))return;P.preventDefault();const Z=b().filter(A=>!A.disabled).map(A=>A.ref.current);Hs.includes(P.key)&&Z.reverse(),rd(Z)}),onBlur:R(e.onBlur,P=>{P.currentTarget.contains(P.target)||(window.clearTimeout(O.current),D.current="")}),onPointerMove:R(e.onPointerMove,ot(P=>{const G=P.target,Y=k.current!==P.clientX;if(P.currentTarget.contains(G)&&Y){const ae=P.clientX>k.current?"right":"left";j.current=ae,k.current=P.clientX}}))})})})})})})});Xs.displayName=se;var Xu="MenuGroup",Yn=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return p.jsx(T.div,{role:"group",...o,ref:t})});Yn.displayName=Xu;var qu="MenuLabel",qs=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return p.jsx(T.div,{...o,ref:t})});qs.displayName=qu;var _t="MenuItem",Ro="menu.itemSelect",zt=i.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:o,...r}=e,s=i.useRef(null),a=ft(_t,e.__scopeMenu),c=Kn(_t,e.__scopeMenu),l=L(t,s),u=i.useRef(!1),f=()=>{const d=s.current;if(!n&&d){const m=new CustomEvent(Ro,{bubbles:!0,cancelable:!0});d.addEventListener(Ro,h=>o?.(h),{once:!0}),To(d,m),m.defaultPrevented?u.current=!1:a.onClose()}};return p.jsx(Zs,{...r,ref:l,disabled:n,onClick:R(e.onClick,f),onPointerDown:d=>{e.onPointerDown?.(d),u.current=!0},onPointerUp:R(e.onPointerUp,d=>{u.current||d.currentTarget?.click()}),onKeyDown:R(e.onKeyDown,d=>{const m=c.searchRef.current!=="";n||m&&d.key===" "||En.includes(d.key)&&(d.currentTarget.click(),d.preventDefault())})})});zt.displayName=_t;var Zs=i.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:o=!1,textValue:r,...s}=e,a=Kn(_t,n),c=Gs(n),l=i.useRef(null),u=L(t,l),[f,d]=i.useState(!1),[m,h]=i.useState("");return i.useEffect(()=>{const x=l.current;x&&h((x.textContent??"").trim())},[s.children]),p.jsx(nt.ItemSlot,{scope:n,disabled:o,textValue:r??m,children:p.jsx(Vs,{asChild:!0,...c,focusable:!o,children:p.jsx(T.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...s,ref:u,onPointerMove:R(e.onPointerMove,ot(x=>{o?a.onItemLeave(x):(a.onItemEnter(x),x.defaultPrevented||x.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:R(e.onPointerLeave,ot(x=>a.onItemLeave(x))),onFocus:R(e.onFocus,()=>d(!0)),onBlur:R(e.onBlur,()=>d(!1))})})})}),Zu="MenuCheckboxItem",Qs=i.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:o,...r}=e;return p.jsx(oa,{scope:e.__scopeMenu,checked:n,children:p.jsx(zt,{role:"menuitemcheckbox","aria-checked":Mt(n)?"mixed":n,...r,ref:t,"data-state":qn(n),onSelect:R(r.onSelect,()=>o?.(Mt(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Qs.displayName=Zu;var Js="MenuRadioGroup",[Qu,Ju]=je(Js,{value:void 0,onValueChange:()=>{}}),ea=i.forwardRef((e,t)=>{const{value:n,onValueChange:o,...r}=e,s=ce(o);return p.jsx(Qu,{scope:e.__scopeMenu,value:n,onValueChange:s,children:p.jsx(Yn,{...r,ref:t})})});ea.displayName=Js;var ta="MenuRadioItem",na=i.forwardRef((e,t)=>{const{value:n,...o}=e,r=Ju(ta,e.__scopeMenu),s=n===r.value;return p.jsx(oa,{scope:e.__scopeMenu,checked:s,children:p.jsx(zt,{role:"menuitemradio","aria-checked":s,...o,ref:t,"data-state":qn(s),onSelect:R(o.onSelect,()=>r.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});na.displayName=ta;var Xn="MenuItemIndicator",[oa,ed]=je(Xn,{checked:!1}),ra=i.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:o,...r}=e,s=ed(Xn,n);return p.jsx(te,{present:o||Mt(s.checked)||s.checked===!0,children:p.jsx(T.span,{...r,ref:t,"data-state":qn(s.checked)})})});ra.displayName=Xn;var td="MenuSeparator",sa=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return p.jsx(T.div,{role:"separator","aria-orientation":"horizontal",...o,ref:t})});sa.displayName=td;var nd="MenuArrow",aa=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=Kt(n);return p.jsx(Bn,{...r,...o,ref:t})});aa.displayName=nd;var od="MenuSub",[Gf,ia]=je(od),Je="MenuSubTrigger",ca=i.forwardRef((e,t)=>{const n=Fe(Je,e.__scopeMenu),o=ft(Je,e.__scopeMenu),r=ia(Je,e.__scopeMenu),s=Kn(Je,e.__scopeMenu),a=i.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:l}=s,u={__scopeMenu:e.__scopeMenu},f=i.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return i.useEffect(()=>f,[f]),i.useEffect(()=>{const d=c.current;return()=>{window.clearTimeout(d),l(null)}},[c,l]),p.jsx(Un,{asChild:!0,...u,children:p.jsx(Zs,{id:r.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":r.contentId,"data-state":da(n.open),...e,ref:It(t,r.onTriggerChange),onClick:d=>{e.onClick?.(d),!(e.disabled||d.defaultPrevented)&&(d.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:R(e.onPointerMove,ot(d=>{s.onItemEnter(d),!d.defaultPrevented&&!e.disabled&&!n.open&&!a.current&&(s.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:R(e.onPointerLeave,ot(d=>{f();const m=n.content?.getBoundingClientRect();if(m){const h=n.content?.dataset.side,x=h==="right",v=x?-5:5,g=m[x?"left":"right"],y=m[x?"right":"left"];s.onPointerGraceIntentChange({area:[{x:d.clientX+v,y:d.clientY},{x:g,y:m.top},{x:y,y:m.top},{x:y,y:m.bottom},{x:g,y:m.bottom}],side:h}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(d),d.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:R(e.onKeyDown,d=>{const m=s.searchRef.current!=="";e.disabled||m&&d.key===" "||ju[o.dir].includes(d.key)&&(n.onOpenChange(!0),n.content?.focus(),d.preventDefault())})})})});ca.displayName=Je;var la="MenuSubContent",ua=i.forwardRef((e,t)=>{const n=zs(se,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,s=Fe(se,e.__scopeMenu),a=ft(se,e.__scopeMenu),c=ia(la,e.__scopeMenu),l=i.useRef(null),u=L(t,l);return p.jsx(nt.Provider,{scope:e.__scopeMenu,children:p.jsx(te,{present:o||s.open,children:p.jsx(nt.Slot,{scope:e.__scopeMenu,children:p.jsx(zn,{id:c.contentId,"aria-labelledby":c.triggerId,...r,ref:u,align:"start",side:a.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{a.isUsingKeyboardRef.current&&l.current?.focus(),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:R(e.onFocusOutside,f=>{f.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:R(e.onEscapeKeyDown,f=>{a.onClose(),f.preventDefault()}),onKeyDown:R(e.onKeyDown,f=>{const d=f.currentTarget.contains(f.target),m=Fu[a.dir].includes(f.key);d&&m&&(s.onOpenChange(!1),c.trigger?.focus(),f.preventDefault())})})})})})});ua.displayName=la;function da(e){return e?"open":"closed"}function Mt(e){return e==="indeterminate"}function qn(e){return Mt(e)?"indeterminate":e?"checked":"unchecked"}function rd(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function sd(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function ad(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let a=sd(e,Math.max(s,0));r.length===1&&(a=a.filter(u=>u!==n));const l=a.find(u=>u.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function id(e,t){const{x:n,y:o}=e;let r=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const c=t[s],l=t[a],u=c.x,f=c.y,d=l.x,m=l.y;f>o!=m>o&&n<(d-u)*(o-f)/(m-f)+u&&(r=!r)}return r}function cd(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return id(n,t)}function ot(e){return t=>t.pointerType==="mouse"?e(t):void 0}var ld=Ks,ud=Un,dd=Ys,fd=Xs,pd=Yn,vd=qs,md=zt,hd=Qs,gd=ea,xd=na,wd=ra,yd=sa,Cd=aa,bd=ca,Sd=ua,Yt="DropdownMenu",[Ed,Kf]=ee(Yt,[Us]),q=Us(),[Rd,fa]=Ed(Yt),pa=e=>{const{__scopeDropdownMenu:t,children:n,dir:o,open:r,defaultOpen:s,onOpenChange:a,modal:c=!0}=e,l=q(t),u=i.useRef(null),[f,d]=we({prop:r,defaultProp:s??!1,onChange:a,caller:Yt});return p.jsx(Rd,{scope:t,triggerId:re(),triggerRef:u,contentId:re(),open:f,onOpenChange:d,onOpenToggle:i.useCallback(()=>d(m=>!m),[d]),modal:c,children:p.jsx(ld,{...l,open:f,onOpenChange:d,dir:o,modal:c,children:n})})};pa.displayName=Yt;var va="DropdownMenuTrigger",ma=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:o=!1,...r}=e,s=fa(va,n),a=q(n);return p.jsx(ud,{asChild:!0,...a,children:p.jsx(T.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...r,ref:It(t,s.triggerRef),onPointerDown:R(e.onPointerDown,c=>{!o&&c.button===0&&c.ctrlKey===!1&&(s.onOpenToggle(),s.open||c.preventDefault())}),onKeyDown:R(e.onKeyDown,c=>{o||(["Enter"," "].includes(c.key)&&s.onOpenToggle(),c.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});ma.displayName=va;var Pd="DropdownMenuPortal",ha=e=>{const{__scopeDropdownMenu:t,...n}=e,o=q(t);return p.jsx(dd,{...o,...n})};ha.displayName=Pd;var ga="DropdownMenuContent",xa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=fa(ga,n),s=q(n),a=i.useRef(!1);return p.jsx(fd,{id:r.contentId,"aria-labelledby":r.triggerId,...s,...o,ref:t,onCloseAutoFocus:R(e.onCloseAutoFocus,c=>{a.current||r.triggerRef.current?.focus(),a.current=!1,c.preventDefault()}),onInteractOutside:R(e.onInteractOutside,c=>{const l=c.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,f=l.button===2||u;(!r.modal||f)&&(a.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});xa.displayName=ga;var Ad="DropdownMenuGroup",wa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(pd,{...r,...o,ref:t})});wa.displayName=Ad;var _d="DropdownMenuLabel",ya=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(vd,{...r,...o,ref:t})});ya.displayName=_d;var Md="DropdownMenuItem",Ca=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(md,{...r,...o,ref:t})});Ca.displayName=Md;var Id="DropdownMenuCheckboxItem",Td=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(hd,{...r,...o,ref:t})});Td.displayName=Id;var Nd="DropdownMenuRadioGroup",Dd=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(gd,{...r,...o,ref:t})});Dd.displayName=Nd;var Od="DropdownMenuRadioItem",kd=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(xd,{...r,...o,ref:t})});kd.displayName=Od;var Ld="DropdownMenuItemIndicator",jd=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(wd,{...r,...o,ref:t})});jd.displayName=Ld;var Fd="DropdownMenuSeparator",ba=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(yd,{...r,...o,ref:t})});ba.displayName=Fd;var $d="DropdownMenuArrow",Bd=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(Cd,{...r,...o,ref:t})});Bd.displayName=$d;var Wd="DropdownMenuSubTrigger",Vd=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(bd,{...r,...o,ref:t})});Vd.displayName=Wd;var Hd="DropdownMenuSubContent",Ud=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return p.jsx(Sd,{...r,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ud.displayName=Hd;var zf=pa,Yf=ma,Xf=ha,qf=xa,Zf=wa,Qf=ya,Jf=Ca,ep=ba,Xt="Collapsible",[Gd,tp]=ee(Xt),[Kd,Zn]=Gd(Xt),Sa=i.forwardRef((e,t)=>{const{__scopeCollapsible:n,open:o,defaultOpen:r,disabled:s,onOpenChange:a,...c}=e,[l,u]=we({prop:o,defaultProp:r??!1,onChange:a,caller:Xt});return p.jsx(Kd,{scope:n,disabled:s,contentId:re(),open:l,onOpenToggle:i.useCallback(()=>u(f=>!f),[u]),children:p.jsx(T.div,{"data-state":Jn(l),"data-disabled":s?"":void 0,...c,ref:t})})});Sa.displayName=Xt;var Ea="CollapsibleTrigger",zd=i.forwardRef((e,t)=>{const{__scopeCollapsible:n,...o}=e,r=Zn(Ea,n);return p.jsx(T.button,{type:"button","aria-controls":r.contentId,"aria-expanded":r.open||!1,"data-state":Jn(r.open),"data-disabled":r.disabled?"":void 0,disabled:r.disabled,...o,ref:t,onClick:R(e.onClick,r.onOpenToggle)})});zd.displayName=Ea;var Qn="CollapsibleContent",Yd=i.forwardRef((e,t)=>{const{forceMount:n,...o}=e,r=Zn(Qn,e.__scopeCollapsible);return p.jsx(te,{present:n||r.open,children:({present:s})=>p.jsx(Xd,{...o,ref:t,present:s})})});Yd.displayName=Qn;var Xd=i.forwardRef((e,t)=>{const{__scopeCollapsible:n,present:o,children:r,...s}=e,a=Zn(Qn,n),[c,l]=i.useState(o),u=i.useRef(null),f=L(t,u),d=i.useRef(0),m=d.current,h=i.useRef(0),x=h.current,v=a.open||c,g=i.useRef(v),y=i.useRef(void 0);return i.useEffect(()=>{const w=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(w)},[]),z(()=>{const w=u.current;if(w){y.current=y.current||{transitionDuration:w.style.transitionDuration,animationName:w.style.animationName},w.style.transitionDuration="0s",w.style.animationName="none";const C=w.getBoundingClientRect();d.current=C.height,h.current=C.width,g.current||(w.style.transitionDuration=y.current.transitionDuration,w.style.animationName=y.current.animationName),l(o)}},[a.open,o]),p.jsx(T.div,{"data-state":Jn(a.open),"data-disabled":a.disabled?"":void 0,id:a.contentId,hidden:!v,...s,ref:f,style:{"--radix-collapsible-content-height":m?`${m}px`:void 0,"--radix-collapsible-content-width":x?`${x}px`:void 0,...e.style},children:v&&r})});function Jn(e){return e?"open":"closed"}var np=Sa,dn={exports:{}},fn={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Po;function qd(){if(Po)return fn;Po=1;var e=Va();function t(d,m){return d===m&&(d!==0||1/d===1/m)||d!==d&&m!==m}var n=typeof Object.is=="function"?Object.is:t,o=e.useState,r=e.useEffect,s=e.useLayoutEffect,a=e.useDebugValue;function c(d,m){var h=m(),x=o({inst:{value:h,getSnapshot:m}}),v=x[0].inst,g=x[1];return s(function(){v.value=h,v.getSnapshot=m,l(v)&&g({inst:v})},[d,h,m]),r(function(){return l(v)&&g({inst:v}),d(function(){l(v)&&g({inst:v})})},[d]),a(h),h}function l(d){var m=d.getSnapshot;d=d.value;try{var h=m();return!n(d,h)}catch{return!0}}function u(d,m){return m()}var f=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?u:c;return fn.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:f,fn}var Ao;function Zd(){return Ao||(Ao=1,dn.exports=qd()),dn.exports}var Qd=Zd();function Jd(){return Qd.useSyncExternalStore(ef,()=>!0,()=>!1)}function ef(){return()=>{}}var eo="Avatar",[tf,op]=ee(eo),[nf,Ra]=tf(eo),Pa=i.forwardRef((e,t)=>{const{__scopeAvatar:n,...o}=e,[r,s]=i.useState("idle");return p.jsx(nf,{scope:n,imageLoadingStatus:r,onImageLoadingStatusChange:s,children:p.jsx(T.span,{...o,ref:t})})});Pa.displayName=eo;var Aa="AvatarImage",of=i.forwardRef((e,t)=>{const{__scopeAvatar:n,src:o,onLoadingStatusChange:r=()=>{},...s}=e,a=Ra(Aa,n),c=rf(o,s),l=ce(u=>{r(u),a.onImageLoadingStatusChange(u)});return z(()=>{c!=="idle"&&l(c)},[c,l]),c==="loaded"?p.jsx(T.img,{...s,ref:t,src:o}):null});of.displayName=Aa;var _a="AvatarFallback",Ma=i.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:o,...r}=e,s=Ra(_a,n),[a,c]=i.useState(o===void 0);return i.useEffect(()=>{if(o!==void 0){const l=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(l)}},[o]),a&&s.imageLoadingStatus!=="loaded"?p.jsx(T.span,{...r,ref:t}):null});Ma.displayName=_a;function _o(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function rf(e,{referrerPolicy:t,crossOrigin:n}){const o=Jd(),r=i.useRef(null),s=o?(r.current||(r.current=new window.Image),r.current):null,[a,c]=i.useState(()=>_o(s,e));return z(()=>{c(_o(s,e))},[s,e]),z(()=>{const l=d=>()=>{c(d)};if(!s)return;const u=l("loaded"),f=l("error");return s.addEventListener("load",u),s.addEventListener("error",f),t&&(s.referrerPolicy=t),typeof n=="string"&&(s.crossOrigin=n),()=>{s.removeEventListener("load",u),s.removeEventListener("error",f)}},[s,n,t]),a}var rp=Pa,sp=Ma,qt="Tabs",[sf,ap]=ee(qt,[Gt]),Ia=Gt(),[af,to]=sf(qt),Ta=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,onValueChange:r,defaultValue:s,orientation:a="horizontal",dir:c,activationMode:l="automatic",...u}=e,f=Wt(c),[d,m]=we({prop:o,onChange:r,defaultProp:s??"",caller:qt});return p.jsx(af,{scope:n,baseId:re(),value:d,onValueChange:m,orientation:a,dir:f,activationMode:l,children:p.jsx(T.div,{dir:f,"data-orientation":a,...u,ref:t})})});Ta.displayName=qt;var Na="TabsList",Da=i.forwardRef((e,t)=>{const{__scopeTabs:n,loop:o=!0,...r}=e,s=to(Na,n),a=Ia(n);return p.jsx(Ws,{asChild:!0,...a,orientation:s.orientation,dir:s.dir,loop:o,children:p.jsx(T.div,{role:"tablist","aria-orientation":s.orientation,...r,ref:t})})});Da.displayName=Na;var Oa="TabsTrigger",ka=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,disabled:r=!1,...s}=e,a=to(Oa,n),c=Ia(n),l=Fa(a.baseId,o),u=$a(a.baseId,o),f=o===a.value;return p.jsx(Vs,{asChild:!0,...c,focusable:!r,active:f,children:p.jsx(T.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:l,...s,ref:t,onMouseDown:R(e.onMouseDown,d=>{!r&&d.button===0&&d.ctrlKey===!1?a.onValueChange(o):d.preventDefault()}),onKeyDown:R(e.onKeyDown,d=>{[" ","Enter"].includes(d.key)&&a.onValueChange(o)}),onFocus:R(e.onFocus,()=>{const d=a.activationMode!=="manual";!f&&!r&&d&&a.onValueChange(o)})})})});ka.displayName=Oa;var La="TabsContent",ja=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,forceMount:r,children:s,...a}=e,c=to(La,n),l=Fa(c.baseId,o),u=$a(c.baseId,o),f=o===c.value,d=i.useRef(f);return i.useEffect(()=>{const m=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(m)},[]),p.jsx(te,{present:r||f,children:({present:m})=>p.jsx(T.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":l,hidden:!m,id:u,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:d.current?"0s":void 0},children:m&&s})})});ja.displayName=La;function Fa(e,t){return`${e}-trigger-${t}`}function $a(e,t){return`${e}-content-${t}`}var ip=Ta,cp=Da,lp=ka,up=ja;export{Hf as $,gf as A,ce as B,yc as C,yf as D,z as E,Eo as F,bf as G,If as H,Tf as I,Df as J,Of as K,cp as L,kf as M,Lf as N,wc as O,T as P,jf as Q,hc as R,lf as S,lp as T,Ff as U,Nf as V,$f as W,Wf as X,Bf as Y,Uf as Z,Vf as _,xc as a,Vn as a0,we as a1,df as a2,rt as a3,uf as a4,as as a5,at as a6,To as a7,zf as a8,Yf as a9,Xf as aa,qf as ab,Qf as ac,ep as ad,Zf as ae,Jf as af,np as ag,zd as ah,Yd as ai,bc as aj,qe as ak,Fn as al,$t as am,Bn as an,ze as ao,$n as ap,Qa as aq,_f as ar,rp as as,sp as at,ip as b,It as c,up as d,Ef as e,Rf as f,Pf as g,Af as h,qa as i,pf as j,hf as k,wf as l,xf as m,vf as n,mf as o,Lc as p,jc as q,ir as r,Cc as s,gc as t,re as u,L as v,Wt as w,ee as x,te as y,R as z};
//# sourceMappingURL=radix-BQPyiA8r.js.map
