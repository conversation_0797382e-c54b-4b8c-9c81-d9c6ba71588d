'use client'

import { type OpenIddictResourceDto } from '@/client'
import { type ColumnDef } from '@tanstack/react-table'
import { Actions } from './Actions'
import { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'
import { customFilterFunction } from './filterFunctions'

// Type for the callback function to handle user actions
type UserActionCallback = (dataId: string, dataEdit: OpenIddictResourceDto, dialogType: 'edit' | 'permission' | 'delete') => void

// Function to create user columns with the action callback
export const getColumns = (
  handleUserAction: UserActionCallback
): ColumnDef<OpenIddictResourceDto>[] => [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Resource Name" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Name",
      },
      filterFn: customFilterFunction,
    },
    {
      accessorKey: 'displayName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Display Name" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Display Name",
      },
      filterFn: customFilterFunction,
    },
    {
      accessorKey: 'description',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Description",
      },
      filterFn: customFilterFunction,
    },
    {
      id: 'actions',
      header: 'Actions',
      enableHiding: true,
      cell: (info) => (
        <Actions
          dataId={info.row.original.id!}
          dataEdit={info.row.original}
          onAction={handleUserAction}
          variant="dropdown" // Use "dropdown" for the first image style or "buttons" for the second image style
        />
      ),
      meta: {
        className: "text-right",
        displayName: "Actions",
      },
    },
  ]
