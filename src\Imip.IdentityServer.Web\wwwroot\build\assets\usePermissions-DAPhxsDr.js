import{r as n,j as t,u as m}from"./vendor-B0b15ZrB.js";import{_ as f,G as r,at as l,Q as g,au as v}from"./app-layout-D_A4XD_6.js";import{C as p}from"./index.esm-DqIqfoOW.js";import{b as x,L as b,T as z,d as N}from"./radix-BQPyiA8r.js";function h({name:e,id:a,onUpdate:s,className:i,isGranted:d,disabled:u=!1}){const c=n.useCallback(()=>{s?.()},[s]);return t.jsxs("div",{className:f("flex items-center space-x-2 pb-2",i),children:[t.jsx(p,{id:a,onCheckedChange:c,checked:d,disabled:u}),t.jsx("label",{htmlFor:a,className:"text-sm font-medium leading-none",children:e})]})}const V=n.memo(h),y=l("flex items-center shrink-0",{variants:{variant:{default:"bg-accent p-1",button:"",line:"border-b border-border"},shape:{default:"",pill:""},size:{lg:"gap-2.5",md:"gap-2",sm:"gap-1.5",xs:"gap-1"}},compoundVariants:[{variant:"default",size:"lg",className:"p-1.5 gap-2.5"},{variant:"default",size:"md",className:"p-1 gap-2"},{variant:"default",size:"sm",className:"p-1 gap-1.5"},{variant:"default",size:"xs",className:"p-1 gap-1"},{variant:"default",shape:"default",size:"lg",className:"rounded-lg"},{variant:"default",shape:"default",size:"md",className:"rounded-lg"},{variant:"default",shape:"default",size:"sm",className:"rounded-md"},{variant:"default",shape:"default",size:"xs",className:"rounded-md"},{variant:"line",size:"lg",className:"gap-9"},{variant:"line",size:"md",className:"gap-8"},{variant:"line",size:"sm",className:"gap-4"},{variant:"line",size:"xs",className:"gap-4"},{variant:"default",shape:"pill",className:"rounded-full [&_[role=tab]]:rounded-full"},{variant:"button",shape:"pill",className:"rounded-full [&_[role=tab]]:rounded-full"}],defaultVariants:{variant:"default",size:"md"}}),C=l("shrink-0 cursor-pointer whitespace-nowrap inline-flex justify-center items-center font-medium ring-offset-background transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-disabled:pointer-events-none data-disabled:opacity-50 [&_svg]:shrink-0 [&_svg]:text-muted-foreground [&:hover_svg]:text-primary [&[data-state=active]_svg]:text-primary",{variants:{variant:{default:"text-muted-foreground data-[state=active]:bg-background hover:text-foreground data-[state=active]:text-foreground data-[state=active]:shadow-xs data-[state=active]:shadow-black/5",button:"focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-lg text-accent-foreground hover:text-foreground data-[state=active]:bg-accent data-[state=active]:text-foreground",line:"border-b-2 text-muted-foreground border-transparent data-[state=active]:border-primary hover:text-primary data-[state=active]:text-primary data-[state=active]:border-primary data-[state=active]:text-primary"},size:{lg:"gap-2.5 [&_svg]:size-5 text-sm",md:"gap-2 [&_svg]:size-4 text-sm",sm:"gap-1.5 [&_svg]:size-3.5 text-xs",xs:"gap-1 [&_svg]:size-3.5 text-xs"}},compoundVariants:[{variant:"default",size:"lg",className:"py-2.5 px-4 rounded-md"},{variant:"default",size:"md",className:"py-1.5 px-3 rounded-md"},{variant:"default",size:"sm",className:"py-1.5 px-2.5 rounded-sm"},{variant:"default",size:"xs",className:"py-1 px-2 rounded-sm"},{variant:"button",size:"lg",className:"py-3 px-4 rounded-lg"},{variant:"button",size:"md",className:"py-2.5 px-3 rounded-lg"},{variant:"button",size:"sm",className:"py-2 px-2.5 rounded-md"},{variant:"button",size:"xs",className:"py-1.5 px-2 rounded-md"},{variant:"line",size:"lg",className:"py-3"},{variant:"line",size:"md",className:"py-2.5"},{variant:"line",size:"sm",className:"py-2"},{variant:"line",size:"xs",className:"py-1.5"}],defaultVariants:{variant:"default",size:"md"}}),j=l("mt-2.5 focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",{variants:{variant:{default:""}},defaultVariants:{variant:"default"}}),o=n.createContext({variant:"default",size:"md"});function w({className:e,...a}){return t.jsx(x,{"data-slot":"tabs",className:r("",e),...a})}function L({className:e,variant:a="default",shape:s="default",size:i="md",...d}){return t.jsx(o.Provider,{value:{variant:a||"default",size:i||"md"},children:t.jsx(b,{"data-slot":"tabs-list",className:r(y({variant:a,shape:s,size:i}),e),...d})})}function q({className:e,...a}){const{variant:s,size:i}=n.useContext(o);return t.jsx(z,{"data-slot":"tabs-trigger",className:r(C({variant:s,size:i}),e),...a})}function E({className:e,variant:a,...s}){return t.jsx(N,{"data-slot":"tabs-content",className:r(j({variant:a}),e),...s})}const Q=(e,a)=>m({queryKey:[g.GetPermissions,e,a],queryFn:async()=>{const{data:s}=await v({query:{providerName:e,providerKey:a}});return s}});export{V as P,w as T,L as a,q as b,E as c,Q as u};
//# sourceMappingURL=usePermissions-DAPhxsDr.js.map
