import{u as W,r as o,f as Q,h as $,j as e}from"./vendor-B0b15ZrB.js";import{Q as G,ak as ye,a as le,u as O,T as be,B as N,S as oe,k as ce,l as de,m as ue,n as se,al as ve,L as ae,I as te,am as Ne,an as Se,ao as Ce,ap as we,aq as Ue,_ as ke,ar as Ee,as as Ae,R as B,U as Te,V as De,G as Re,D as Pe,c as Ie,e as Le,f as ne,A as Fe}from"./app-layout-D_A4XD_6.js";import{c as Ge,$ as qe}from"./App-De6zOdMU.js";import{L as re,v as Ve,E as _e}from"./Loader-BX7B8fV0.js";import{u as me,D as Y,a as he,b as J,c as X,d as Z,I as P,C as V,e as z,f as ie}from"./index.esm-DqIqfoOW.js";import{F as pe,a as k}from"./FormField-POW7SsfI.js";import{p as Oe,R as Me,w as Ke,l as He,a as ze,_ as Qe,D as M,V as Be,b as $e}from"./DataTableColumnHeader-CSMG3Uqi.js";import{A as We,a as Ye,b as Je,c as Xe,d as Ze,e as es,f as ss,g as as,S as ts,T as ns}from"./TableSkeleton-DgDki6RL.js";import{T as xe,a as ge,b as K,c as H,u as rs,P as is}from"./usePermissions-DAPhxsDr.js";import"./radix-BQPyiA8r.js";import"./card-Iy60I049.js";const ls=(a,s,c,p)=>W({queryKey:[G.GetUsers,a,s,c,p],queryFn:async()=>{let n=0;return a>0&&(n=a*s),(await ye({query:{MaxResultCount:s,SkipCount:n,Filter:c,Sorting:p}})).data}}),os=({children:a})=>{const{can:s}=le(),[c,p]=o.useState(!1),[n,g]=o.useState(!0),[f,C]=o.useState(!0),[w,b]=o.useState(!1),[y,S]=o.useState(0),{toast:v}=O(),T=Q(),{handleSubmit:x,register:E}=me(),D=$({mutationFn:async d=>ve({body:d}),onSuccess:()=>{v({title:"Success",description:"User Created Successfully",variant:"success"}),T.invalidateQueries({queryKey:[G.GetUsers]}),p(!1)},onError:d=>{v({title:d?.error?.message,description:d?.error?.details,variant:"destructive"})}}),h=d=>{const i={...d,isActive:n,lockoutEnabled:f,extraProperties:{...d.extraProperties??{},ConcurrentLoginPreventionMode:y,NeedLoginHris:w}};D.mutate(i)};return e.jsxs("section",{children:[e.jsx(be,{}),e.jsxs(Y,{open:c,onOpenChange:p,children:[e.jsx(he,{asChild:!0,children:a}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:s("AbpIdentity.Users.Create")&&e.jsxs(N,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>p(!0),children:[e.jsx(Oe,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Create New User"})]})}),e.jsxs(J,{children:[e.jsx(X,{children:e.jsx(Z,{children:"Create a New User"})}),e.jsxs("form",{onSubmit:x(h),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(pe,{children:[e.jsx(k,{label:"Username",description:"The username of the user",children:e.jsx(P,{required:!0,...E("userName"),placeholder:"Username"})}),e.jsx(k,{label:"Password",description:"The password of the user",children:e.jsx(P,{required:!0,type:"password",...E("password"),placeholder:"Password"})}),e.jsx(k,{label:"Name",description:"The name of the user",children:e.jsx(P,{required:!0,...E("name"),placeholder:"Name"})}),e.jsx(k,{label:"Surname",description:"The surname of the user",children:e.jsx(P,{...E("surname"),placeholder:"Surname"})}),e.jsx(k,{label:"Email",description:"The email of the user",children:e.jsx(P,{required:!0,...E("email"),placeholder:"Email"})}),e.jsx(k,{label:"Phone Number",description:"The phone number of the user",children:e.jsx(P,{...E("phoneNumber"),placeholder:"Phone Number"})}),e.jsx(k,{label:"Concurrent Login Prevention",description:"How to handle concurrent logins for this user",children:e.jsxs(oe,{onValueChange:d=>S(Number(d)),children:[e.jsx(ce,{className:"w-full",children:e.jsx(de,{placeholder:"Select mode"})}),e.jsxs(ue,{children:[e.jsx(se,{value:String(0),option:"Disabled",description:"No restriction on concurrent logins"}),e.jsx(se,{value:String(1),option:"Logout From Same Type Devices",description:"Only one session per device type"}),e.jsx(se,{value:String(2),option:"Logout From All Devices",description:"Log out other sessions on new login"})]})]})}),e.jsx(k,{label:"Need Login HRIS",description:"Require HRIS authentication for this user",children:e.jsx(V,{id:"needLoginHris",name:"needLoginHris",checked:w,onCheckedChange:d=>b(!!d.valueOf())})}),e.jsx(k,{label:"Is Active",description:"The active status of the user",children:e.jsx(V,{id:"isActive",name:"isActive",defaultChecked:!0,checked:n,onCheckedChange:d=>g(!!d.valueOf())})}),e.jsx(k,{label:"Lockout Enabled",description:"The lockout status of the user",children:e.jsx(V,{id:"lockoutEnabled",name:"lockoutEnabled",defaultChecked:!0,checked:f,onCheckedChange:d=>C(!!d.valueOf())})})]})}),e.jsxs(z,{className:"mt-5",children:[e.jsx(N,{variant:"ghost",onClick:d=>{d.preventDefault(),p(!1)},disabled:D.isPending,children:"Cancel"}),e.jsx(N,{type:"submit",disabled:D.isPending,children:D.isPending?"Saving...":"Save"})]})]})]})]})]})},cs=()=>{const{toast:a}=O(),s=Q(),[c,p]=o.useState(!1),[n,g]=o.useState(null),[f,C]=o.useState(!1),[w,b]=o.useState(!1),[y,S]=o.useState("users"),v=h=>{if(h.target.files&&h.target.files.length>0){const d=h.target.files[0];if(!d?.name.toLowerCase().endsWith(".csv")){a({title:"Invalid File Type",description:"Please select a CSV file",variant:"destructive"}),h.target.value="";return}g(d)}},T=$({mutationFn:async()=>{b(!0);try{const h=await Ne({query:{type:y}}),d=new Blob([h.data],{type:"text/csv"}),i=window.URL.createObjectURL(d),r=document.createElement("a");return r.href=i,r.download="users_import_template.csv",document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(i),document.body.removeChild(r),h.data}finally{b(!1)}},onError:h=>{a({title:h?.error?.message,description:h?.error?.details,variant:"destructive"})}}),x=$({mutationFn:async()=>{if(!n)throw new Error("No file selected");C(!0);try{return(await Se({body:{File:n,Type:y,Delimiter:";"}})).data}finally{C(!1)}},onSuccess:()=>{a({title:"Import Successful",description:"Imported successfully",variant:"success"}),s.invalidateQueries({queryKey:[G.GetUsers]}),p(!1),g(null)},onError:h=>{a({title:h?.error?.message,description:h?.error?.details,variant:"destructive"})}}),E=()=>{T.mutate()},D=()=>{if(!n){a({title:"No File Selected",description:"Please select a CSV file to upload",variant:"warning"});return}x.mutate()};return e.jsxs(Y,{open:c,onOpenChange:p,children:[e.jsx(he,{asChild:!0,children:e.jsxs(N,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",children:[e.jsx(Me,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Upload CSV"})]})}),e.jsxs(J,{className:"sm:max-w-md",children:[e.jsx(X,{children:e.jsx(Z,{children:"Upload Users CSV"})}),e.jsxs("div",{className:"flex flex-col gap-4 py-4",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx(ae,{htmlFor:"csvFile",className:"text-left",children:"Upload Type"}),e.jsxs(oe,{value:y,onValueChange:h=>S(h),children:[e.jsx(ce,{className:"w-full",children:e.jsx(de,{placeholder:"Select upload type"})}),e.jsxs(ue,{children:[e.jsx(te,{value:"users",children:"Users"}),e.jsx(te,{value:"roles",children:"Roles"}),e.jsx(te,{value:"userroles",children:"User Roles"})]})]})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx(ae,{htmlFor:"template",className:"text-left",children:"Download Template"}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(N,{variant:"outline",onClick:E,disabled:w,className:"w-full",children:[e.jsx(Ke,{className:"mr-2 h-4 w-4"}),w?"Downloading...":"Download Template"]})}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Download the template first, fill it with your data, and then upload it."})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx(ae,{htmlFor:"csvFile",className:"text-left",children:"Upload CSV File"}),e.jsx(P,{id:"csvFile",type:"file",accept:".csv",onChange:v,disabled:f}),n&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Selected file: ",n.name]})]})]}),e.jsxs(z,{children:[e.jsx(N,{variant:"ghost",onClick:()=>{p(!1),g(null)},disabled:f,children:"Cancel"}),e.jsx(N,{onClick:D,disabled:!n||f,children:f?"Uploading...":"Upload"})]})]})]})},ds=({user:{userId:a,username:s},onDismiss:c})=>{const{toast:p}=O(),[n,g]=o.useState(!1),f=async()=>{try{await Ce({path:{id:a}}),p({title:"Success",description:`User "${s}" has been deleted successfully.`}),c()}catch(C){C instanceof Error&&p({title:"Failed",description:`There was a problem when deleting the user "${s}". Kindly try again.`,variant:"destructive"})}};return o.useEffect(()=>{g(!0)},[]),e.jsx(We,{open:n,children:e.jsxs(Ye,{children:[e.jsxs(Je,{children:[e.jsx(Xe,{children:"Are you absolutely sure?"}),e.jsxs(Ze,{children:['This action cannot be undone. This will permanently delete your this user "',s,'"']})]}),e.jsxs(es,{children:[e.jsx(ss,{onClick:c,children:"Cancel"}),e.jsx(as,{onClick:f,children:"Yes"})]})]})})},us=()=>W({queryKey:[G.GetAssignableRoles],queryFn:async()=>{const{data:a}=await we();return a}}),fe=({userId:a})=>W({queryKey:[G.GetUserRoles,a],queryFn:async()=>{const{data:s}=await Ue({path:{id:a}});return s}}),F={USERS_EDIT:"user_edit",USERS_ROLE_ASSIGN:"user_role_assign",USERS_DETAILS:"user_details",USERS_RAW:"user_raw"},ms=({userDto:a,userId:s,onDismiss:c})=>{const[p,n]=o.useState(!1),{toast:g}=O(),f=Q(),{handleSubmit:C,register:w,control:b}=me({defaultValues:{...a}}),[y,S]=o.useState([]),v=fe({userId:s}),T=us(),{data:x,isLoading:E}=W({queryKey:["user-details",s],queryFn:()=>Ae({path:{id:s}})}),D=$({mutationFn:async t=>Ee({path:{id:s},body:{...a,...t}}),onSuccess:()=>{g({title:"Success",description:"Claim Type Created Successfully",variant:"success"}),f.invalidateQueries({queryKey:[G.GetUsers]}),n(!1)},onError:t=>{g({title:t?.error?.message,description:t?.error?.details,variant:"destructive"})}}),h=t=>{const u={...t};D.mutate(u)},d=()=>{n(!1),c()};o.useEffect(()=>{n(!0)},[]),o.useEffect(()=>{if(v.data?.items){const t=[];v.data.items.forEach(u=>{t.push({name:u.name,id:u.id})}),S(t)}},[v.data?.items]);const i=o.useCallback(t=>{const u=y.findIndex(l=>t.id===l.id);u!==-1?(y.splice(u,1),S([...y])):(y.push({name:t.name,id:t.id}),S([...y]))},[y]),r=t=>{t.preventDefault();const u={...a,roleNames:y?.map(l=>l.name)??[]};h(u)},m=async t=>{t.preventDefault();try{await Ge.post({url:`/api/identity/users/${s}/unlock`}),g({title:"User unlocked",description:`${a.userName} has been unlocked.`,variant:"success"}),f.invalidateQueries({queryKey:["user-details",s]})}catch(u){const l=u&&typeof u=="object"&&"message"in u?String(u.message):"An error occurred";g({title:"Failed to unlock",description:l,variant:"destructive"})}},j=x?.data?.extraProperties;return e.jsx(Y,{open:p,onOpenChange:d,children:e.jsxs(J,{size:"2xl",className:"",children:[e.jsx(X,{children:e.jsxs(Z,{children:["Update a User: ",a.userName]})}),e.jsxs(xe,{defaultValue:F.USERS_EDIT,children:[e.jsxs(ge,{className:"w-full",children:[e.jsx(K,{value:F.USERS_EDIT,children:"User Information"}),e.jsx(K,{value:F.USERS_ROLE_ASSIGN,children:"Roles"}),e.jsx(K,{value:F.USERS_DETAILS,children:"Details"}),e.jsx(K,{value:F.USERS_RAW,children:"Raw"})]}),e.jsx(H,{value:F.USERS_EDIT,children:e.jsxs("form",{onSubmit:C(h),children:[e.jsx("section",{className:"flex flex-col space-y-2 mt-4",children:e.jsxs(pe,{children:[e.jsx(k,{label:"Name",description:"The name of the user",children:e.jsx(P,{required:!0,...w("name"),defaultValue:a.name??"",placeholder:"Name"})}),e.jsx(k,{label:"Surname",description:"The surname of the user",children:e.jsx(P,{...w("surname"),defaultValue:a.surname??"",placeholder:"Surname"})}),e.jsx(k,{label:"Email",description:"The email of the user",children:e.jsx(P,{required:!0,...w("email"),defaultValue:a.email??"",placeholder:"Email"})}),e.jsx(k,{label:"Phone Number",description:"The phone number of the user",children:e.jsx(P,{...w("phoneNumber"),defaultValue:a.phoneNumber??"",placeholder:"Phone Number"})}),e.jsx(k,{label:"Is Active",description:"The active status of the user",children:e.jsx(ie,{name:"isActive",control:b,render:({field:t})=>e.jsx(V,{id:"isActive",checked:!!t.value,onCheckedChange:u=>t.onChange(!!u)})})}),e.jsx(k,{label:"Lockout Enabled",description:"The lockout status of the user",children:e.jsx(ie,{name:"lockoutEnabled",control:b,render:({field:t})=>e.jsx(V,{id:"lockoutEnabled",checked:!!t.value,onCheckedChange:u=>t.onChange(!!u)})})})]})}),e.jsxs(z,{className:"mt-5 flex items-center gap-2",children:[e.jsx(N,{variant:"outline",onClick:t=>{t.preventDefault(),d()},children:"Cancel"}),x?.data?.lockoutEnd&&e.jsx(N,{type:"button",variant:"secondary",onClick:m,children:"Unlock User"}),e.jsx(N,{type:"submit",children:"Save"})]})]})}),e.jsxs(H,{className:"max-h-[70vh] overflow-hidden flex flex-col",value:F.USERS_ROLE_ASSIGN,children:[T?.isLoading&&e.jsx(re,{}),T?.isError&&e.jsxs("div",{className:"bg-error p-10 text-3xl",children:["There was an error while fetching roles information for the ",a.userName]}),!T.isLoading&&!T.isError&&e.jsx("div",{className:"flex-1 overflow-y-auto",children:T?.data?.items?.map(t=>e.jsxs("div",{className:ke("flex items-center space-x-2 pb-5 mt-3"),children:[e.jsx(V,{id:t.id,name:t.name,checked:!!y?.find(u=>u.id===t.id),onCheckedChange:()=>{i(t)}}),e.jsx("label",{htmlFor:t.id,className:"text-sm font-medium leading-none",children:t.name})]},Ve()))}),e.jsxs(z,{className:"mt-5",children:[e.jsx(N,{variant:"outline",onClick:t=>{t.preventDefault(),d()},children:"Cancel"}),e.jsx(N,{onClick:r,children:"Save"})]})]}),e.jsx(H,{value:F.USERS_DETAILS,children:E?e.jsx(re,{}):x?e.jsxs("div",{className:"space-y-3 p-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Created by :"})," ",x.data?.creatorUserName??"-"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Creation time :"})," ",x.data?.creationTime?new Date(x.data?.creationTime).toLocaleString():"-"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Modified by :"})," ",x.data?.lastModifierUserName??"-"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Modification time :"})," ",x.data?.lastModificationTime?new Date(x.data?.lastModificationTime).toLocaleString():"-"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Password update time :"})," ",x.data?.passwordChangeTime?new Date(x.data?.passwordChangeTime).toLocaleString():"-"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Lockout end time :"})," ",x.data?.lockoutEnd?new Date(x.data?.lockoutEnd).toLocaleString():"-"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Failed access count :"})," ",x.data?.accessFailedCount??0]})]}):e.jsx("div",{className:"p-4 text-gray-500",children:"No details found."})}),e.jsx(H,{value:F.USERS_RAW,children:E?e.jsx(re,{}):x?e.jsx("div",{className:"space-y-3 p-4",children:e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Extra Properties :"}),e.jsx("div",{className:"mt-2 rounded border p-3 bg-muted text-sm",children:!j||Object.keys(j).length===0?e.jsx("div",{className:"text-gray-500",children:"No extra properties."}):e.jsx("div",{className:"grid grid-cols-1 gap-2",children:Object.entries(j).map(([t,u])=>e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("div",{className:"w-64 font-medium truncate",children:t}),e.jsx("div",{className:"flex-1 break-all",children:String(u)})]},t))})})]})}):e.jsx("div",{className:"p-4 text-gray-500",children:"No details found."})})]})]})})},hs=({userDto:a,userId:s,onDismiss:c})=>{const[p,n]=o.useState(!1),{toast:g}=O(),f=fe({userId:s}),[C,w]=o.useState(!1),{data:b}=rs(B.U,s),y=Q(),[S,v]=o.useState([]);o.useEffect(()=>(n(!0),()=>{y.invalidateQueries({queryKey:[B.U]})}),[]),o.useEffect(()=>{b?.groups&&Array.isArray(b.groups)&&v([...b.groups])},[b]),o.useEffect(()=>{if(b?.groups&&b.groups.length>0){const i=b.groups.map(r=>r.permissions?.every(m=>m.isGranted)).every(r=>r);w(i)}},[b]),o.useEffect(()=>{if(S.length>0){const i=S.map(r=>({...r,permissions:r.permissions?.map(m=>({...m,isGranted:C}))}));v(i)}},[C]);const T=o.useCallback(async i=>{i.preventDefault();const m={permissions:S?.map(R=>R.permissions.map(j=>({name:j.name,isGranted:j.isGranted}))).flat()};try{await Te({query:{providerName:B.U,providerKey:s},body:m}),g({title:"Success",description:"Permission Updated Successfully",variant:"default"}),y.invalidateQueries({queryKey:[B.U]}),x()}catch(R){R instanceof Error&&g({title:"Failed",description:"Permission update wasn't successful.",variant:"destructive"})}},[S]),x=o.useCallback(()=>{n(!1),c()},[]),E=o.useMemo(()=>f?.data?.items?f.data.items.filter(i=>i.name?.includes(De.ADMIN)).length>0:!1,[f]),[D,h]=o.useState(0),d=i=>{if(!Array.isArray(i)||i.length===0)return{parentRows:[],otherPermissions:[]};const r={},m=[],R=["Create","View","Edit","Delete"];return i.forEach(j=>{(j.name?.match(/\./g)||[]).length===1&&!R.some(u=>j.name?.endsWith("."+u))&&(r[j.name]={parent:j,children:{}})}),i.forEach(j=>{const t=j.name?.match(/^(.*)\.(Create|View|Edit|Delete)$/);if(t){const u=t[1],l=t[2];r[u]?r[u].children[l]=j:m.push(j)}else r[j.name]||m.push(j)}),{parentRows:Object.values(r),otherPermissions:m}};return e.jsx(Y,{open:p,onOpenChange:x,children:e.jsxs(J,{size:"4xl",className:"max-w-4xl max-h-[90vh] overflow-hidden flex flex-col",children:[e.jsx(X,{children:e.jsxs(Z,{children:["Permissions - ",a.userName]})}),e.jsx("form",{onSubmit:T,className:"flex-1 overflow-y-auto",children:e.jsxs("div",{className:"p-1",children:[e.jsx(is,{name:"Grant All Permissions",isGranted:C,id:"all_granted",disabled:!E,onUpdate:()=>{w(i=>!i)},className:"ml-2 mb-4"}),e.jsxs(xe,{value:D.toString(),onValueChange:i=>h(Number(i)),orientation:"vertical",className:"flex flex-col justify-stretch lg:flex-row gap-4 text-sm text-muted-foreground w-full p-4 border border-border rounded-lg",children:[e.jsx("div",{className:"lg:w-[200px] lg:shrink-0",children:e.jsx(ge,{variant:"button",className:"flex flex-col items-stretch *:justify-start sticky top-0 bg-white z-10",children:S.map((i,r)=>e.jsx(K,{value:r.toString(),children:i.displayName},i.name))})}),e.jsx("div",{className:"grow border-s border-border py-0 ps-4 overflow-y-auto",children:S.map((i,r)=>{const{parentRows:m,otherPermissions:R}=d(i.permissions??[]),j=[...m.map(l=>l.parent),...m.flatMap(l=>Object.values(l.children))],t=j.length>0&&j.every(l=>l.isGranted),u=l=>{v(I=>I.map((U,L)=>L!==r?U:{...U,permissions:U.permissions?.map(A=>{const q=m.some(ee=>ee.parent.name===A.name),_=m.some(ee=>Object.values(ee.children).some(je=>je.name===A.name));return q||_?{...A,isGranted:l}:A})}))};return e.jsxs(H,{value:r.toString(),className:"overflow-x-auto",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:t,onChange:l=>u(l.target.checked),"aria-label":"Select all permissions in this table",className:"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"}),e.jsx("span",{children:"Select All"})]}),m.length>0&&e.jsxs("table",{className:"min-w-full border mb-6",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left",children:"Name"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"Parent"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"View"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"Create"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"Edit"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"Delete"})]})}),e.jsx("tbody",{children:m.map(({parent:l,children:I})=>e.jsxs("tr",{className:"border-t",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:l.displayName??l.name}),e.jsx("td",{className:"px-4 py-2 text-center",children:e.jsx("input",{type:"checkbox",checked:l.isGranted,onChange:()=>{v(U=>U.map((L,A)=>A!==r?L:{...L,permissions:L.permissions?.map(q=>q.name===l.name?{...q,isGranted:!q.isGranted}:q)}))},"aria-label":String(l.displayName??l.name),className:"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"})}),["View","Create","Edit","Delete"].map(U=>e.jsx("td",{className:"px-4 py-2 text-center",children:I[U]?e.jsx("input",{type:"checkbox",checked:I[U].isGranted,onChange:()=>{v(L=>L.map((A,q)=>q!==r?A:{...A,permissions:A.permissions?.map(_=>_.name===I[U].name?{..._,isGranted:!_.isGranted}:_)}))},"aria-label":String(I[U].displayName??I[U].name),className:"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"}):null},U))]},l.name))})]}),R.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"font-semibold mb-2",children:"Other Permissions"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-1 gap-2",children:R.map(l=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:l.isGranted,onChange:()=>{v(I=>I.map((U,L)=>L!==r?U:{...U,permissions:U.permissions?.map(A=>A.name===l.name?{...A,isGranted:!A.isGranted}:A)}))},"aria-label":String(l.name??l.displayName),className:"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"}),e.jsx("span",{children:l.name??l.displayName})]},l.name))})]})]},i.name)})})]})]})}),e.jsxs(z,{className:"mt-4 border-t pt-4 bg-white dark:bg-gray-950",children:[e.jsx(N,{onClick:i=>{i.preventDefault(),x()},variant:"ghost",children:"Cancel"}),e.jsx(N,{onClick:T,children:"Save"})]})]})})},ps=({status:a})=>{let s="Inactive",c="bg-gray-100 text-gray-700 border-gray-200";return typeof a=="boolean"?a&&(s="Active",c="bg-green-50 text-green-700 border-green-200"):typeof a=="string"&&(s=a,a.toLowerCase()==="active"?c="bg-green-50 text-green-700 border-green-200":a.toLowerCase()==="archived"?c="bg-amber-50 text-amber-700 border-amber-200":a.toLowerCase()==="inactive"&&(c="bg-gray-100 text-gray-700 border-gray-200")),e.jsx("span",{className:Re("inline-flex items-center rounded-md border px-2 py-1 text-xs font-medium",c),children:s})},xs=({userId:a,userDto:s,onAction:c,variant:p="dropdown"})=>{const{can:n}=le();return p==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(Pe,{children:[e.jsx(Ie,{asChild:!0,children:e.jsxs(N,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(He,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(Le,{align:"end",className:"w-[160px]",children:[n("AbpIdentity.Users.ManagePermissions")&&e.jsx(ne,{className:"cursor-pointer text-sm",onClick:()=>c(a,s,"permission"),children:"Permission"}),n("AbpIdentity.Users.Update")&&e.jsx(ne,{className:"cursor-pointer text-sm",onClick:()=>c(a,s,"edit"),children:"Edit"}),n("AbpIdentity.Users.Delete")&&e.jsx(ne,{className:"cursor-pointer text-sm text-red-500",onClick:()=>c(a,s,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[n("AbpIdentity.Users.ManagePermissions")&&e.jsxs(N,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>c(a,s,"permission"),children:[e.jsx(ze,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),n("AbpIdentity.Users.Update")&&e.jsxs(N,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>c(a,s,"edit"),children:[e.jsx(Qe,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})},gs=a=>[{id:"select",header:({table:s})=>e.jsx(V,{checked:s.getIsAllPageRowsSelected()?!0:s.getIsSomeRowsSelected()?"indeterminate":!1,onCheckedChange:()=>s.toggleAllPageRowsSelected(),className:"translate-y-0.5","aria-label":"Select all"}),cell:({row:s})=>e.jsx(V,{checked:s.getIsSelected(),onCheckedChange:()=>s.toggleSelected(),className:"translate-y-0.5","aria-label":"Select row"}),enableSorting:!1,enableHiding:!1,meta:{displayName:"Select"}},{accessorKey:"userName",header:({column:s})=>e.jsx(M,{column:s,title:"Username"}),enableHiding:!0,enableSorting:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Username"}},{accessorKey:"name",header:({column:s})=>e.jsx(M,{column:s,title:"Name"}),enableHiding:!0,enableSorting:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Name"}},{accessorKey:"extraProperties.Company",header:({column:s})=>e.jsx(M,{column:s,title:"Company"}),enableHiding:!0,enableSorting:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Company"}},{accessorKey:"email",header:({column:s})=>e.jsx(M,{column:s,title:"Email"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Email"}},{accessorKey:"isActive",header:({column:s})=>e.jsx(M,{column:s,title:"Status"}),enableHiding:!0,enableSorting:!0,cell:s=>e.jsx(ps,{status:s.getValue()}),meta:{className:"text-left",displayName:"Status"}},{id:"actions",header:"Actions",enableHiding:!0,cell:s=>e.jsx(xs,{userId:s.row.original.id,userDto:s.row.original,onAction:a,variant:"dropdown"}),meta:{className:"text-right",displayName:"Actions"}}];function fs({table:a,onSearch:s,searchValue:c=""}){const p=a.getState().columnFilters.length>0;return e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[s&&e.jsx("div",{className:"w-full sm:w-auto sm:max-w-[250px]",children:e.jsx(ts,{onUpdate:s,value:c})}),e.jsxs("div",{className:"flex items-center gap-2 ml-auto",children:[p&&e.jsx(N,{variant:"ghost",onClick:()=>a.resetColumnFilters(),className:"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500",children:"Clear filters"}),e.jsx(Be,{table:a})]})]})}const js=()=>{const{toast:a}=O(),s=Q(),[c,p]=o.useState(""),[n,g]=o.useState(),[f,C]=o.useState({pageIndex:0,pageSize:10}),[w,b]=o.useState([{id:"name",desc:!1}]),y=r=>{if(!r.length)return"name asc";const m=r[0]??{id:"name",desc:!1};return`${m.id} ${m.desc?"desc":"asc"}`},{isLoading:S,data:v,isError:T}=ls(f.pageIndex,f.pageSize,c,y(w)),E=gs((r,m,R)=>{g(null),setTimeout(()=>{g({userId:r,userDto:m,dialogType:R})},0)}),D=r=>{p(r),C(m=>({...m,pageIndex:0}))},h=r=>{C(r)},d=r=>{b(r),C(m=>({...m,pageIndex:0}))},i=()=>{s.invalidateQueries({queryKey:[G.GetUsers]}),setTimeout(()=>{a({title:"Data refreshed",description:"The user list has been refreshed.",variant:"default"})},800)};return S?e.jsx(ns,{rowCount:f.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0}):T?e.jsx(_e,{}):e.jsxs(e.Fragment,{children:[n&&n.dialogType==="edit"&&e.jsx(ms,{userId:n.userId,userDto:n.userDto,onDismiss:()=>{s.invalidateQueries({queryKey:[G.GetUsers]}),g(null)}}),n&&n.dialogType==="permission"&&e.jsx(hs,{userId:n.userId,userDto:n.userDto,onDismiss:()=>g(null)}),n&&n.dialogType==="delete"&&e.jsx(ds,{user:{username:n.userDto.userName,userId:n.userId},onDismiss:()=>{s.invalidateQueries({queryKey:[G.GetUsers]}),g(null)}}),e.jsx("div",{className:"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4",children:e.jsx($e,{title:"Users Management",columns:E,data:v?.items??[],totalCount:v?.totalCount,isLoading:S,manualPagination:!0,manualSorting:!0,pageSize:f.pageSize,onPaginationChange:h,onSortingChange:d,sortingState:w,onSearch:D,searchValue:c,customFilterbar:fs,hideDefaultFilterbar:!0,onRefresh:i,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(os,{}),e.jsx(cs,{})]})}})})]})};function Ts(){return e.jsxs(Fe,{children:[e.jsx(qe,{title:"Users"}),e.jsx(js,{})]})}export{Ts as default};
//# sourceMappingURL=user-6kqXUNQx.js.map
