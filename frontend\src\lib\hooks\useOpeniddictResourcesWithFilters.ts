import {
  type PagedResultDtoOfOpenIddictApplicationDto,
  postApiOpeniddictResourcesList,
} from '@/client'
import { extractApiError } from '@/lib/query-utils'
import { generateExtendedQueryParameters } from '@/lib/query-utils-extended'
import { toast } from '@/lib/useToast'
import { useQuery } from '@tanstack/react-query'
import { QueryNames } from './QueryConstants'
import type { FilterCondition } from '@/lib/interfaces/IFilterCondition'

export const useOpeniddictResourcesWithFilters = (
  pageIndex: number,
  pageSize: number,
  filterConditions: FilterCondition[] = [],
  sorting?: string
) => {
  return useQuery({
    queryKey: [QueryNames.GetOpeniddictResources, pageIndex, pageSize, JSON.stringify(filterConditions), sorting],
    queryFn: async () => {
      try {
        // Generate query parameters using the extended utility function
        const body = generateExtendedQueryParameters({
          pageIndex,
          pageSize,
          sorting,
          filterConditions,
        })

        const response = await postApiOpeniddictResourcesList({
          body,
        })

        // Ensure we return a valid data structure even if the API response is unexpected
        return response.data?.data as PagedResultDtoOfOpenIddictApplicationDto
      } catch (error) {
        // Use the error extraction utility
        const { title, description } = extractApiError(error, 'Error loading resources')

        // Show toast notification
        toast({
          title,
          description,
          variant: 'destructive',
        })

        // Return empty data to prevent UI crashes
        return { items: [], totalCount: 0 }
      }
    },
    retry: false, // Don't retry on error
  })
}
