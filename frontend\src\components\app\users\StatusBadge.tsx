'use client'

import { cn } from '@/lib/utils'

type StatusBadgeProps = {
  status: boolean | string
}

export const StatusBadge = ({ status }: StatusBadgeProps) => {
  // Convert boolean to string status
  let statusText = 'Inactive'
  let statusClass = 'bg-gray-100 text-gray-700 border-gray-200'

  if (typeof status === 'boolean') {
    if (status) {
      statusText = 'Active'
      statusClass = 'bg-green-50 text-green-700 border-green-200'
    }
  } else if (typeof status === 'string') {
    // Handle string status values
    statusText = status

    if (status.toLowerCase() === 'active') {
      statusClass = 'bg-green-50 text-green-700 border-green-200'
    } else if (status.toLowerCase() === 'archived') {
      statusClass = 'bg-amber-50 text-amber-700 border-amber-200'
    } else if (status.toLowerCase() === 'inactive') {
      statusClass = 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  return (
    <span className={cn(
      'inline-flex items-center rounded-md border px-2 py-1 text-xs font-medium',
      statusClass
    )}>
      {statusText}
    </span>
  )
}
