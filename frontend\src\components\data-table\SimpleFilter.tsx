"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { type FilterOperator } from "@/client"
import { type Table } from "@tanstack/react-table"
import { RiAddLine, RiCloseLine, RiFilterLine } from "@remixicon/react"
import { useState } from "react"

interface SimpleFilterProps<TData> {
  table: Table<TData>
}

type FilterCondition = {
  id: string
  columnId: string
  operator: FilterOperator
  value: string
}

// Map of user-friendly operator names to actual FilterOperator values
const operatorOptions: { label: string; value: FilterOperator }[] = [
  { label: "Equals", value: "Equals" },
  { label: "Not Equals", value: "NotEquals" },
  { label: "Contains", value: "Contains" },
  { label: "Starts With", value: "StartsWith" },
  { label: "Ends With", value: "EndsWith" },
]

export function SimpleFilter<TData>({
  table,
}: SimpleFilterProps<TData>) {
  const [isOpen, setIsOpen] = useState(false)
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])

  // Generate a unique ID for new filter conditions
  function generateId() {
    return `filter-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
  }

  // Get all visible columns that can be filtered
  const filterableColumns = table
    .getAllColumns()
    .filter(
      (column) =>
        column.id !== "select" &&
        column.id !== "actions" &&
        column.id !== "drag"
    )

  const isFiltered = table.getState().columnFilters.length > 0

  // Add a new empty filter condition
  const addFilterCondition = () => {
    if (filterableColumns.length === 0) return

    const newCondition: FilterCondition = {
      id: generateId(),
      columnId: filterableColumns[0]?.id ?? "",
      operator: "Contains",
      value: "",
    }

    setFilterConditions([...filterConditions, newCondition])
  }

  // Remove a filter condition by ID
  const removeFilterCondition = (id: string) => {
    setFilterConditions(filterConditions.filter(condition => condition.id !== id))
  }

  // Update a specific filter condition
  const updateFilterCondition = (id: string, updates: Partial<FilterCondition>) => {
    setFilterConditions(filterConditions.map(condition => {
      if (condition.id === id) {
        return { ...condition, ...updates }
      }
      return condition
    }))
  }

  // Apply all filter conditions to the table
  const applyFilters = () => {
    // First, clear any existing filters
    table.resetColumnFilters()

    // Then apply each filter condition
    filterConditions.forEach(condition => {
      const column = table.getColumn(condition.columnId)
      if (column && condition.value) {
        column.setFilterValue({
          operator: condition.operator,
          value: condition.value
        })
      }
    })

    setIsOpen(false)
  }

  // Clear all filters
  const clearFilters = () => {
    setFilterConditions([])
    table.resetColumnFilters()
  }

  // Get a user-friendly column name
  const getColumnDisplayName = (columnId: string) => {
    const column = table.getColumn(columnId)
    return column?.columnDef?.header?.toString() ?? columnId
  }

  return (
    <div className="flex items-center gap-2">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={isFiltered ? "primary" : "ghost"}
            size="sm"
            className="h-8 gap-1"
          >
            <RiFilterLine className="h-3.5 w-3.5" />
            <span>Filter</span>
            {isFiltered && (
              <span className="ml-1 rounded-full bg-primary/20 px-1 text-xs font-medium">
                {table.getState().columnFilters.length}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[320px] p-3" align="end">
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium leading-none">Filter</h4>
              <p className="text-sm text-muted-foreground">
                Filter records by specific conditions
              </p>
            </div>

            <div>
              <pre className="text-xs">
                {JSON.stringify(filterConditions, null, 2)}
              </pre>
            </div>

            {filterConditions.length > 0 ? (
              <div className="space-y-2">
                {filterConditions.map((condition) => (
                  <div key={condition.id} className="flex flex-col gap-2 rounded-md border p-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs font-medium">Column</Label>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => removeFilterCondition(condition.id)}
                      >
                        <RiCloseLine className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                    <Select
                      value={condition.columnId}
                      onValueChange={(value) => updateFilterCondition(condition.id, { columnId: value })}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="Select column" />
                      </SelectTrigger>
                      <SelectContent>
                        {filterableColumns.map((column) => (
                          <SelectItem key={column.id} value={column.id}>
                            {getColumnDisplayName(column.id)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Label className="text-xs font-medium">Operator</Label>
                    <Select
                      value={condition.operator}
                      onValueChange={(value) => updateFilterCondition(condition.id, { operator: value as FilterOperator })}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="Select operator" />
                      </SelectTrigger>
                      <SelectContent>
                        {operatorOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Label className="text-xs font-medium">Value</Label>
                    <Input
                      className="h-8"
                      placeholder="Enter value"
                      value={condition.value}
                      onChange={(e) => updateFilterCondition(condition.id, { value: e.target.value })}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex h-20 items-center justify-center rounded-md border border-dashed">
                <p className="text-sm text-muted-foreground">No filters added</p>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 gap-1"
                onClick={addFilterCondition}
              >
                <RiAddLine className="h-3.5 w-3.5" />
                <span>Add filter</span>
              </Button>
              {filterConditions.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8"
                  onClick={clearFilters}
                >
                  Clear all
                </Button>
              )}
            </div>

            <Button
              className="w-full"
              onClick={applyFilters}
              disabled={filterConditions.length === 0}
            >
              Apply filters
            </Button>
          </div>
        </PopoverContent>
      </Popover>

      {isFiltered && (
        <Button
          variant="ghost"
          onClick={clearFilters}
          className="border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500"
        >
          Clear filters
        </Button>
      )}
    </div>
  )
}
