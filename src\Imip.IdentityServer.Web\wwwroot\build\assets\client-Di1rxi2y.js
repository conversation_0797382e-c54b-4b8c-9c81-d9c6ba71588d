import{u as Q,r as m,f as E,h as B,j as e}from"./vendor-B0b15ZrB.js";import{h as A,Q as q,i as ue,j as he,a as J,u as z,T as Y,B as g,S as D,k as P,l as O,m as F,n as M,o as me,q as xe,r as ye,D as ge,c as je,e as fe,f as G,A as Ce}from"./app-layout-D_A4XD_6.js";import{$ as be}from"./App-De6zOdMU.js";import{u as W,D as Z,a as ve,b as X,c as ee,d as ie,I as y,e as te}from"./index.esm-DqIqfoOW.js";import{F as se,a as o}from"./FormField-POW7SsfI.js";import{M as _,c as L}from"./filterFunctions-EQ3oUkRA.js";import{C as Se}from"./cog-e8-W8VUH.js";import{L as Te}from"./lock-CJDOiBy7.js";import{p as Ne,d as H,l as Ae,a as we,_ as ke,D as I,b as Ue}from"./DataTableColumnHeader-CSMG3Uqi.js";import{A as qe,a as Ie,b as De,c as Pe,d as Oe,e as Fe,f as Me,g as Re,T as Le}from"./TableSkeleton-DgDki6RL.js";import{N as Ve}from"./NotionFilter-B-J2qhpm.js";import{u as _e}from"./useOpeniddictApplications-BhfnMR4u.js";import{B as $e}from"./badge-B7pYtUY6.js";import"./radix-BQPyiA8r.js";import"./scroll-area-Dw-Gl1P6.js";import"./card-Iy60I049.js";import"./query-utils-extended-wVOeERM5.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=[["rect",{width:"14",height:"8",x:"5",y:"2",rx:"2",key:"wc9tft"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",key:"w68u3i"}],["path",{d:"M6 18h2",key:"rwmk9e"}],["path",{d:"M12 18h6",key:"aqd8w3"}]],He=A("computer",ze);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ee=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],Ge=A("globe",Ee);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]],Qe=A("lock-open",Ke);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]],Je=A("shield-check",Be);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ye=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M9.1 9a3 3 0 0 1 5.82 1c0 2-3 3-3 3",key:"mhlwft"}],["path",{d:"M12 17h.01",key:"p32p05"}]],We=A("shield-question",Ye);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ze=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],Xe=A("smartphone",Ze);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ei=[["path",{d:"m17 2-5 5-5-5",key:"16satq"}],["rect",{width:"20",height:"15",x:"2",y:"7",rx:"2",key:"1e6viu"}]],ii=A("tv",ei);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ti=[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],si=A("user-check",ti),ne=[{value:"web",option:"Web Application",description:"Server-side web applications running on a web server (e.g., ASP.NET, Node.js, PHP)",icon:Ge},{value:"spa",option:"Single Page Application",description:"JavaScript applications running in the browser (e.g., React, Angular, Vue.js)",icon:He},{value:"native",option:"Native Application",description:"Desktop or mobile applications running natively on devices",icon:Xe},{value:"device",option:"Device Application",description:"IoT devices, smart TVs, or other limited-input devices",icon:ii},{value:"machine",option:"Machine-to-Machine",description:"Service accounts, daemons, or backend services that run without user interaction",icon:Se}],ae=[{value:"public",option:"Public",description:"Clients that cannot maintain the confidentiality of their credentials",icon:Qe},{value:"confidential",option:"Confidential",description:"Clients capable of maintaining the confidentiality of their credentials",icon:Te}],re=[{value:"implicit",option:"Implicit",description:"Consent is assumed without requiring explicit user approval",icon:Je},{value:"explicit",option:"Explicit",description:"Requires explicit user approval before granting access",icon:si},{value:"hybrid",option:"Hybrid",description:"Requires explicit user approval before granting access",icon:We}],le=()=>Q({queryKey:[q.GetOpeniddictApplicationsPermissions],queryFn:async()=>(await ue()).data?.data}),oe=()=>Q({queryKey:[q.GetOpeniddictRequirements],queryFn:async()=>(await he()).data?.data});function ce(n){if(!n)return"";const i=n.split(":");if(i.length!==2)return n;const[r,a]=i;if(!a||!r)return n;switch(r){case"ept":return`${a.charAt(0).toUpperCase()}${a.slice(1)} Endpoint`;case"gt":return`${a.split("_").map(l=>l.charAt(0).toUpperCase()+l.slice(1)).join(" ")} Grant`;case"rst":return`${a.charAt(0).toUpperCase()}${a.slice(1)} Response Type`;case"scp":return`${a.charAt(0).toUpperCase()}${a.slice(1)} Scope`;default:return n}}function de(){const n=["swift","bright","clever","dynamic","efficient","flexible","global","innovative","logical","modern","nimble","optimal","precise","quick","reliable","secure","stable","tech","unified","virtual","wise","active","agile","bold","central","digital"],i=["app","api","system","platform","service","portal","hub","core","base","cloud","data","engine","framework","grid","interface","logic","matrix","network","object","project","resource","solution","tool","utility","vision","workspace"],r=n[Math.floor(Math.random()*n.length)],a=i[Math.floor(Math.random()*i.length)],l=Math.floor(Math.random()*1e3);return`${r}${a}${l}`}function ni(){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let i="";for(let r=0;r<32;r++)i+=n.charAt(Math.floor(Math.random()*n.length));return i}function $(n){return Array.isArray(n)?n.filter(i=>i&&i.length>0):n?String(n).split(",").map(i=>i.trim()).filter(i=>i.length>0):[]}const ai=({children:n})=>{const{can:i}=J(),[r,a]=m.useState(!1),{toast:l}=z(),f=E(),{handleSubmit:p,register:c,setValue:d,reset:x}=W(),[w,b]=m.useState([]),[V,R]=m.useState([]),{data:k,isLoading:v}=le(),{data:S,isLoading:T}=oe(),U=()=>{x({applicationType:"",clientType:"",clientId:"",clientSecret:"",displayName:"",consentType:"",redirectUris:[],postLogoutRedirectUris:[],permissions:[],requirements:[]})},u=k?Array.isArray(k)?k.map(t=>({value:t,label:ce(t)})):[]:[],C=S?Array.isArray(S)?S.map(t=>({value:t.value,label:t.label})):[]:[],h=B({mutationFn:async t=>me({body:t}),onSuccess:()=>{l({title:"Success",description:"Client Created Successfully",variant:"success"}),f.invalidateQueries({queryKey:[q.GetOpeniddictApplications]}),a(!1),U()},onError:t=>{l({title:t?.error?.message,description:t?.error?.details,variant:"destructive"})}}),j=t=>{const s={...t,redirectUris:$(t.redirectUris),postLogoutRedirectUris:$(t.postLogoutRedirectUris),permissions:w};h.mutate(s)},N=t=>{t&&U(),a(t)};return e.jsxs("section",{children:[e.jsx(Y,{}),e.jsxs(Z,{open:r,onOpenChange:N,children:[e.jsx(ve,{asChild:!0,children:n}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:i("AbpIdentity.Users.Create")&&e.jsxs(g,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>N(!0),children:[e.jsx(Ne,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Create New Client"})]})}),e.jsxs(X,{style:{maxWidth:"800px"},children:[e.jsx(ee,{children:e.jsx(ie,{children:"Create a New Client"})}),e.jsxs("form",{onSubmit:p(j),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(se,{children:[e.jsx(o,{label:"Application Type",description:"The type of the application",children:e.jsxs(D,{...c("applicationType"),onValueChange:t=>d("applicationType",t),children:[e.jsx(P,{className:"w-full",clearable:!0,onClear:()=>d("applicationType",""),children:e.jsx(O,{placeholder:"Select application type"})}),e.jsx(F,{children:ne.map(t=>e.jsx(M,{value:t.value,option:t.option,description:t.description,icon:t.icon,clearable:!0},t.value))})]})}),e.jsx(o,{label:"Client Type",description:"The type of the client",children:e.jsxs(D,{...c("clientType"),onValueChange:t=>d("clientType",t),children:[e.jsx(P,{className:"w-full",children:e.jsx(O,{placeholder:"Select client type"})}),e.jsx(F,{children:ae.map(t=>e.jsx(M,{value:t.value,option:t.option,description:t.description,icon:t.icon},t.value))})]})}),e.jsx(o,{label:"Client Id",description:"The client id of the client",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{required:!0,...c("clientId"),placeholder:"Client Id"}),e.jsx(g,{type:"button",size:"sm",variant:"secondary",onClick:()=>d("clientId",de()),title:"Generate Client ID",children:e.jsx(H,{className:"size-4"})})]})}),e.jsx(o,{label:"Client Secret",description:"The client secret of the client",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{required:!0,...c("clientSecret"),placeholder:"Client Secret"}),e.jsx(g,{type:"button",variant:"secondary",size:"sm",onClick:()=>d("clientSecret",ni()),title:"Generate Client Secret",children:e.jsx(H,{className:"size-4"})})]})}),e.jsx(o,{label:"Display name",description:"The display name of the application",children:e.jsx(y,{placeholder:"Display name",...c("displayName")})}),e.jsx(o,{label:"Consent type",description:"The consent type of the client",children:e.jsxs(D,{...c("consentType"),onValueChange:t=>d("consentType",t),children:[e.jsx(P,{className:"w-full",children:e.jsx(O,{placeholder:"Select consent type"})}),e.jsx(F,{children:re.map(t=>e.jsx(M,{value:t.value,option:t.option,description:t.description,icon:t.icon},t.value))})]})}),e.jsx(o,{label:"Client Uri",description:"The client uri of the client",children:e.jsx(y,{placeholder:"Client Uri",...c("clientUri")})}),e.jsx(o,{label:"Redirect Uris",description:"The redirect uris of the client",children:e.jsx(y,{placeholder:"Redirect Uris",...c("redirectUris")})}),e.jsx(o,{label:"Post logout redirect Uris",description:"The post logout redirect uris of the client",children:e.jsx(y,{placeholder:"Post logout redirect Uris",...c("postLogoutRedirectUris")})}),e.jsx(o,{label:"Permissions",description:"The permissions of the client",children:e.jsx(_,{options:u,value:w,onChange:t=>{b(t),d("permissions",t)},placeholder:v?"Loading permissions...":"Select permissions",disabled:v,maxHeight:300})}),e.jsx(o,{label:"Requirements",description:"The requirements of the client",children:e.jsx(_,{mode:"single",options:C,value:V,onChange:t=>{R(t),d("requirements",t)},placeholder:T?"Loading requirements...":"Select requirements",disabled:T,maxHeight:300})})]})}),e.jsxs(te,{className:"mt-5",children:[e.jsx(g,{variant:"ghost",onClick:t=>{t.preventDefault(),a(!1)},disabled:h.isPending,children:"Cancel"}),e.jsx(g,{type:"submit",disabled:h.isPending,children:h.isPending?"Saving...":"Save"})]})]})]})]})]})},ri=({dataId:n,dataEdit:i,onDismiss:r})=>{const{toast:a}=z(),[l,f]=m.useState(!1),p=async()=>{try{await xe({path:{id:n}}),a({title:"Success",description:`Client "${i.displayName}" has been deleted successfully.`}),r()}catch(c){c instanceof Error&&a({title:"Failed",description:`There was a problem when deleting the client "${i.displayName}". Kindly try again.`,variant:"destructive"})}};return m.useEffect(()=>{f(!0)},[]),e.jsx(qe,{open:l,children:e.jsxs(Ie,{children:[e.jsxs(De,{children:[e.jsx(Pe,{children:"Are you absolutely sure?"}),e.jsxs(Oe,{children:['This action cannot be undone. This will permanently delete your this client "',i.displayName,'"']})]}),e.jsxs(Fe,{children:[e.jsx(Me,{onClick:r,children:"Cancel"}),e.jsx(Re,{onClick:p,children:"Yes"})]})]})})},K=({dataEdit:n,dataId:i,onDismiss:r})=>{const[a,l]=m.useState(!0),{toast:f}=z(),p=E(),{handleSubmit:c,register:d,setValue:x,reset:w}=W(),[b,V]=m.useState(n.permissions??[]),[R,k]=m.useState(n.requirements??[]),{data:v,isLoading:S}=le(),{data:T,isLoading:U}=oe(),u=()=>{w({applicationType:"",clientType:"",clientId:"",clientSecret:"",displayName:"",consentType:"",redirectUris:[],postLogoutRedirectUris:[],permissions:[],requirements:[]})},C=v?Array.isArray(v)?v.map(s=>({value:s,label:ce(s)})):[]:[],h=T?Array.isArray(T)?T.map(s=>({value:s.value,label:s.label})):[]:[],j=B({mutationFn:async s=>ye({path:{id:i},body:s}),onSuccess:()=>{f({title:"Success",description:"Client Created Successfully",variant:"success"}),p.invalidateQueries({queryKey:[q.GetOpeniddictApplications]}),u(),l(!1),r()},onError:s=>{f({title:s?.error?.message,description:s?.error?.details,variant:"destructive"})}}),N=s=>{const pe={...s,applicationType:s.applicationType??n.applicationType??"",clientType:s.clientType??n.clientType??"",permissions:b,redirectUris:$(s.redirectUris),postLogoutRedirectUris:$(s.postLogoutRedirectUris)};j.mutate(pe)},t=s=>{s&&(u(),r()),l(s)};return e.jsxs("section",{children:[e.jsx(Y,{}),e.jsx(Z,{open:a,onOpenChange:t,children:e.jsxs(X,{style:{maxWidth:"800px"},children:[e.jsx(ee,{children:e.jsx(ie,{children:"Edit Client"})}),e.jsxs("form",{onSubmit:c(N),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(se,{children:[e.jsx(o,{label:"Application Type",description:"The type of the application",children:e.jsxs(D,{defaultValue:n.applicationType??"",onValueChange:s=>x("applicationType",s),children:[e.jsx(P,{className:"w-full",clearable:!0,onClear:()=>x("applicationType",""),children:e.jsx(O,{placeholder:"Select application type"})}),e.jsx(F,{children:ne.map(s=>e.jsx(M,{value:s.value,option:s.option,description:s.description,icon:s.icon,clearable:!0},s.value))})]})}),e.jsx(o,{label:"Client Type",description:"The type of the client",children:e.jsxs(D,{defaultValue:n.clientType??"",onValueChange:s=>x("clientType",s),children:[e.jsx(P,{className:"w-full",children:e.jsx(O,{placeholder:"Select client type"})}),e.jsx(F,{children:ae.map(s=>e.jsx(M,{value:s.value,option:s.option,description:s.description,icon:s.icon},s.value))})]})}),e.jsx(o,{label:"Client Id",description:"The client id of the client",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{required:!0,...d("clientId"),defaultValue:n.clientId??"",placeholder:"Client Id"}),e.jsx(g,{type:"button",variant:"secondary",onClick:de,title:"Generate Client ID",children:e.jsx(H,{className:"size-4"})})]})}),e.jsx(o,{label:"Display name",description:"The display name of the application",children:e.jsx(y,{placeholder:"Display name",...d("displayName"),defaultValue:n.displayName??""})}),e.jsx(o,{label:"Consent type",description:"The consent type of the client",children:e.jsxs(D,{...d("consentType"),defaultValue:n.consentType??"",children:[e.jsx(P,{className:"w-full",children:e.jsx(O,{placeholder:"Select consent type"})}),e.jsx(F,{children:re.map(s=>e.jsx(M,{value:s.value,option:s.option,description:s.description,icon:s.icon},s.value))})]})}),e.jsx(o,{label:"Client Uri",description:"The client uri of the client",children:e.jsx(y,{placeholder:"Client Uri",...d("clientUri"),defaultValue:n.clientUri??""})}),e.jsx(o,{label:"Redirect Uris",description:"The redirect uris of the client",children:e.jsx(y,{placeholder:"Redirect Uris",...d("redirectUris"),defaultValue:n.redirectUris??""})}),e.jsx(o,{label:"Post logout redirect Uris",description:"The post logout redirect uris of the client",children:e.jsx(y,{placeholder:"Post logout redirect Uris",...d("postLogoutRedirectUris"),defaultValue:n.postLogoutRedirectUris??""})}),e.jsx(o,{label:"Permissions",description:"The permissions of the client",children:e.jsx(_,{options:C,value:b,onChange:s=>{V(s),x("permissions",s)},placeholder:S?"Loading permissions...":"Select permissions",disabled:S,maxHeight:300})}),e.jsx(o,{label:"Requirements",description:"The requirements of the client",children:e.jsx(_,{mode:"single",options:h,value:R,onChange:s=>{k(s),x("requirements",s)},placeholder:U?"Loading requirements...":"Select requirements",disabled:U,maxHeight:300})})]})}),e.jsxs(te,{className:"mt-5",children:[e.jsx(g,{variant:"ghost",onClick:s=>{s.preventDefault(),l(!1)},disabled:j.isPending,children:"Cancel"}),e.jsx(g,{type:"submit",disabled:j.isPending,children:j.isPending?"Saving...":"Save"})]})]})]})})]})},li=({dataId:n,dataEdit:i,onAction:r,variant:a="dropdown"})=>{const{can:l}=J();return a==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(ge,{children:[e.jsx(je,{asChild:!0,children:e.jsxs(g,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(Ae,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(fe,{align:"end",className:"w-[160px]",children:[l("IdentityServer.OpenIddictApplications.Edit")&&e.jsx(G,{className:"cursor-pointer text-sm",onClick:()=>r(n,i,"edit"),children:"Edit"}),l("IdentityServer.OpenIddictApplications.Delete")&&e.jsx(G,{className:"cursor-pointer text-sm text-red-500",onClick:()=>r(n,i,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[l("AbpIdentity.Users.ManagePermissions")&&e.jsxs(g,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>r(n,i,"permission"),children:[e.jsx(we,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),l("AbpIdentity.Users.Update")&&e.jsxs(g,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>r(n,i,"edit"),children:[e.jsx(ke,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})},oi=n=>[{accessorKey:"clientId",header:({column:i})=>e.jsx(I,{column:i,title:"Client ID"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>i.getValue(),meta:{className:"text-left",displayName:"Client ID"}},{accessorKey:"displayName",header:({column:i})=>e.jsx(I,{column:i,title:"Display Name"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>i.getValue(),meta:{className:"text-left",displayName:"Display Name"}},{accessorKey:"applicationType",header:({column:i})=>e.jsx(I,{column:i,title:"Application Type"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>i.getValue(),meta:{className:"text-left",displayName:"Application Type"}},{accessorKey:"consentType",header:({column:i})=>e.jsx(I,{column:i,title:"Consent Type"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>i.getValue(),meta:{className:"text-left",displayName:"Consent Type"}},{accessorKey:"redirectUris",header:({column:i})=>e.jsx(I,{column:i,title:"Redirect URIs"}),enableSorting:!0,filterFn:L,enableHiding:!0,cell:i=>{const r=i.getValue();return e.jsx("div",{className:"flex flex-wrap gap-1",children:r?.map(a=>e.jsx($e,{variant:"secondary",children:a},a))})},meta:{className:"text-left",displayName:"Redirect URIs"}},{id:"actions",header:({column:i})=>e.jsx(I,{column:i,title:"Actions"}),enableSorting:!1,enableHiding:!0,cell:i=>e.jsx(li,{dataId:i.row.original.id,dataEdit:i.row.original,onAction:n,variant:"dropdown"}),meta:{className:"text-right",displayName:"Actions"}}],ci=()=>{const{toast:n}=z(),i=E(),[r,a]=m.useState(""),[l,f]=m.useState([]),[p,c]=m.useState(),[d,x]=m.useState({pageIndex:0,pageSize:10}),{isLoading:w,data:b}=_e(d.pageIndex,d.pageSize,l);m.useEffect(()=>{c(null)},[b]);const R=oi((u,C,h)=>{c(null),c({dataId:u,dataEdit:C,dialogType:h})}),k=u=>{a(u);const h=[...l.filter(t=>t.fieldName!=="displayName")];u&&h.push({fieldName:"displayName",operator:"Contains",value:u});const j=JSON.stringify(l),N=JSON.stringify(h);j!==N&&(f(h),x(t=>({...t,pageIndex:0})))},v=u=>{x(u)},S=()=>{i.invalidateQueries({queryKey:[q.GetOpeniddictApplications]}),n({title:"Data refreshed",description:"The client list has been refreshed.",variant:"success"})};if(w)return e.jsx(Le,{rowCount:d.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const T=b?.items??[],U=b?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4",children:e.jsx(Ue,{title:"Clients Management",columns:R,data:T,totalCount:U,isLoading:w,manualPagination:!0,pageSize:d.pageSize,onPaginationChange:v,onSearch:k,searchValue:r,customFilterbar:u=>e.jsx(Ve,{...u,activeFilters:l,onServerFilter:C=>{const h=JSON.stringify(l),j=JSON.stringify(C);h!==j&&(f(C),x(N=>({...N,pageIndex:0})))}}),hideDefaultFilterbar:!0,onRefresh:S,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(ai,{})}})}),p&&p.dialogType==="edit"&&e.jsx(K,{dataEdit:p.dataEdit,dataId:p.dataId,onDismiss:()=>{i.invalidateQueries({queryKey:[q.GetOpeniddictApplications]}),c(null)}}),p&&p.dialogType==="permission"&&e.jsx(K,{dataEdit:p.dataEdit,dataId:p.dataId,onDismiss:()=>c(null)}),p&&p.dialogType==="delete"&&e.jsx(ri,{dataEdit:p.dataEdit,dataId:p.dataId,onDismiss:()=>{i.invalidateQueries({queryKey:[q.GetOpeniddictApplications]}),c(null)}})]})};function wi(){return e.jsxs(Ce,{children:[e.jsx(be,{title:"Client"}),e.jsx(ci,{})]})}export{wi as default};
//# sourceMappingURL=client-Di1rxi2y.js.map
