# Application Configuration Caching Fix

## Problem
The `/api/abp/application-configuration` endpoint sometimes returns empty `grantedPolicies: {}` when running multiple Kubernetes replicas, causing client applications to lose permission information intermittently.

## Root Cause
- ABP Framework's `ApplicationConfigurationAppService` uses internal caching
- Cache is not properly synchronized across multiple pods
- Different pods return different cached states for the same user
- Results in inconsistent `grantedPolicies` responses

## Solution

### 1. Enhanced Application Configuration Service
Created `EnhancedApplicationConfigurationAppService` with:
- **Redis-based caching** with user-specific cache keys
- **10-minute cache duration** with 5-minute sliding expiration
- **Automatic permission refresh** when `grantedPolicies` is empty
- **Fallback to original service** on errors

### 2. Custom Controller
Created `ApplicationConfigurationController` that:
- **Replaces the default ABP endpoint** `/api/abp/application-configuration`
- **Uses the enhanced service** with proper Redis caching
- **Maintains API compatibility** with existing clients

### 3. Key Features
- **User-specific cache keys**: `app-config:{userId}`
- **Shorter cache duration**: 10 minutes vs ABP's default longer caching
- **Permission validation**: Automatically checks common permissions when cache is empty
- **Redis synchronization**: All pods share the same cached configuration

## Expected Results

### Before Fix:
```json
{
  "auth": {
    "grantedPolicies": {}  // Sometimes empty
  }
}
```

### After Fix:
```json
{
  "auth": {
    "grantedPolicies": {
      "FeatureManagement.ManageHostFeatures": true,
      "SettingManagement.Emailing": true,
      "AbpIdentity.Roles": true,
      "WismaApp.PaymentDetails.View": true,
      // ... consistent permissions across all pods
    }
  }
}
```

## Deployment Steps

1. **Deploy the enhanced code** (includes the new service and controller)
2. **Apply Kubernetes configurations** (if any ingress changes)
3. **Restart pods** to ensure new code is loaded
4. **Test the endpoint** across multiple requests

## Testing

```bash
# Test application configuration endpoint multiple times
for i in {1..10}; do
  echo "Test $i:"
  curl -H "Authorization: Bearer $TOKEN" \
    "https://api-identity-dev.imip.co.id/api/abp/application-configuration" | \
    jq '.auth.grantedPolicies | length'
  sleep 1
done
```

Expected: Consistent non-zero count of granted policies

## Monitoring

Check Redis for application configuration cache keys:
```bash
redis-cli -h ********** -p 6379 KEYS "*app-config:*"
```

## Benefits

- ✅ **Consistent permissions** across all Kubernetes pods
- ✅ **Faster response times** with Redis caching
- ✅ **Automatic recovery** when permissions are missing
- ✅ **Backward compatibility** with existing client applications
- ✅ **Proper cache invalidation** with reasonable expiration times
