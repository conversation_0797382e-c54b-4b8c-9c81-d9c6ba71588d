import{r as i,j as e,u as _,f as O,h as J}from"./vendor-B0b15ZrB.js";import{u as K,J as ae,Q as q,K as ne,a as X,T as ie,B as w,M as le,G as re,D as ce,c as oe,e as de,f as U,N as ue,O as me,P as he,R as V,U as pe,V as xe,A as fe}from"./app-layout-D_A4XD_6.js";import{$ as ge}from"./App-De6zOdMU.js";import{A as ye,a as je,b as be,c as ve,d as Ne,e as Se,f as Ce,g as we,S as Ae,T as Pe}from"./TableSkeleton-DgDki6RL.js";import{p as ke,l as Re,a as De,_ as Te,D as $,V as Ge,b as Ie}from"./DataTableColumnHeader-CSMG3Uqi.js";import{F as Z,a as F}from"./FormField-POW7SsfI.js";import{u as ee,D as L,a as Ee,b as B,c as Q,d as H,I as se,C as M,e as z}from"./index.esm-DqIqfoOW.js";import{u as qe}from"./useOpeniddictApplications-BhfnMR4u.js";import{u as Fe,P as Me,T as Ke,a as Oe,b as Ue,c as Ve}from"./usePermissions-DAPhxsDr.js";import"./radix-BQPyiA8r.js";import"./card-Iy60I049.js";import"./query-utils-extended-wVOeERM5.js";const Le=({dataId:t,onDismiss:s})=>{const{toast:c}=K(),[d,n]=i.useState(!1),h=async()=>{try{await ae({path:{id:t}}),c({title:"Success",description:"Role has been deleted successfully."}),s()}catch(y){y instanceof Error&&c({title:"Failed",description:"There was a problem when deleting the role. Kindly try again.",variant:"destructive"})}};return i.useEffect(()=>{n(!0)},[]),e.jsx(ye,{open:d,children:e.jsxs(je,{children:[e.jsxs(be,{children:[e.jsx(ve,{children:"Are you absolutely sure?"}),e.jsx(Ne,{children:"This action cannot be undone. This will permanently delete your this role."})]}),e.jsxs(Se,{children:[e.jsx(Ce,{onClick:s,children:"Cancel"}),e.jsx(we,{onClick:h,children:"Yes"})]})]})})},Be=(t,s,c,d)=>_({queryKey:[q.GetRoles,t,s,c,d],queryFn:async()=>{let n=0;t>0&&(n=t*s);const{data:h}=await ne({query:{MaxResultCount:s,SkipCount:n,Filter:c,Sorting:d}});return h}}),Qe=({children:t})=>{const{can:s}=X(),[c,d]=i.useState(!1),{toast:n}=K(),h=O(),{handleSubmit:y,register:x,reset:v}=ee(),[,p]=i.useState([]);i.useEffect(()=>{c||(v({name:"",isDefault:!1,isPublic:!1}),p([]))},[c,v]);const f=J({mutationFn:async r=>le({body:{name:r.name??"",isDefault:r.isDefault??!1,isPublic:r.isPublic??!1}}),onSuccess:()=>{n({title:"Success",description:"Role Created Successfully",variant:"success"}),h.invalidateQueries({queryKey:[q.GetRoles]}),d(!1)},onError:r=>{n({title:r?.error?.message,description:r?.error?.details,variant:"destructive"})}}),A=r=>{const C={...r};f.mutate(C)},S=r=>{d(r)};return e.jsxs("section",{children:[e.jsx(ie,{}),e.jsxs(L,{open:c,onOpenChange:S,children:[e.jsx(Ee,{asChild:!0,children:t}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:s("IdentityServer.ClaimTypes.Create")&&e.jsxs(w,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>d(!0),children:[e.jsx(ke,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"New Role"})]})}),e.jsxs(B,{className:"max-w-2xl",children:[e.jsx(Q,{children:e.jsx(H,{children:"Create a New Role"})}),e.jsxs("form",{onSubmit:y(A),className:"mt-2",onKeyDown:r=>{r.key==="Enter"&&r.target instanceof HTMLInputElement&&(r.preventDefault(),y(A)())},children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(Z,{children:[e.jsx(F,{label:"Name",description:"The name of the claim",children:e.jsx(se,{required:!0,...x("name"),placeholder:"Claim Name"})}),e.jsx(F,{label:"Default",description:"Whether the role is default",children:e.jsx(M,{...x("isDefault",{setValueAs:r=>r===!0})})}),e.jsx(F,{label:"Public",description:"Whether the role is public",children:e.jsx(M,{...x("isPublic",{setValueAs:r=>r===!0})})})]})}),e.jsxs(z,{className:"mt-5",children:[e.jsx(w,{variant:"ghost",onClick:r=>{r.preventDefault(),d(!1)},disabled:f.isPending,children:"Cancel"}),e.jsx(w,{type:"submit",disabled:f.isPending,children:f.isPending?"Saving...":"Save"})]})]})]})]})]})},Y=({value:t,className:s})=>e.jsx("span",{className:re("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",t?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",s),children:t?"Yes":"No"}),He=({userId:t,userDto:s,onAction:c,variant:d="dropdown"})=>{const{can:n}=X();return d==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(ce,{children:[e.jsx(oe,{asChild:!0,children:e.jsxs(w,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(Re,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(de,{align:"end",className:"w-[160px]",children:[n("AbpIdentity.Roles.Update")&&e.jsx(U,{className:"cursor-pointer text-sm",onClick:()=>c(t,s,"edit"),children:"Edit"}),n("AbpIdentity.Roles.ManagePermissions")&&e.jsx(U,{className:"cursor-pointer text-sm",onClick:()=>c(t,s,"permission"),children:"Permission"}),n("AbpIdentity.Roles.ManagePermissions")&&e.jsx(U,{className:"cursor-pointer text-sm",onClick:()=>c(t,s,"application"),children:"Application"}),n("AbpIdentity.Roles.Delete")&&e.jsx(U,{className:"cursor-pointer text-sm text-red-500",onClick:()=>c(t,s,"delete"),children:"Delete"})]})]})}):e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[n("AbpIdentity.Users.ManagePermissions")&&e.jsxs(w,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>c(t,s,"permission"),children:[e.jsx(De,{className:"h-4 w-4"}),e.jsx("span",{children:"Permission"})]}),n("AbpIdentity.Users.Update")&&e.jsxs(w,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>c(t,s,"edit"),children:[e.jsx(Te,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]})},ze=t=>[{id:"select",header:({table:s})=>e.jsx(M,{checked:s.getIsAllPageRowsSelected()?!0:s.getIsSomeRowsSelected()?"indeterminate":!1,onCheckedChange:()=>s.toggleAllPageRowsSelected(),className:"translate-y-0.5","aria-label":"Select all"}),cell:({row:s})=>e.jsx(M,{checked:s.getIsSelected(),onCheckedChange:()=>s.toggleSelected(),className:"translate-y-0.5","aria-label":"Select row"}),enableSorting:!1,enableHiding:!0,meta:{displayName:"Select"}},{accessorKey:"name",header:({column:s})=>e.jsx($,{column:s,title:"Name"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Name"}},{accessorKey:"isDefault",header:({column:s})=>e.jsx($,{column:s,title:"Is Default"}),enableSorting:!0,enableHiding:!0,cell:s=>e.jsx(Y,{value:s.getValue()}),meta:{className:"text-left",displayName:"Is Default"}},{accessorKey:"isPublic",header:({column:s})=>e.jsx($,{column:s,title:"Is Public"}),enableSorting:!0,enableHiding:!0,cell:s=>e.jsx(Y,{value:s.getValue()}),meta:{className:"text-left",displayName:"Is Public"}},{id:"actions",header:"Actions",cell:s=>e.jsx(He,{userId:s.row.original.id,userDto:s.row.original,onAction:t,variant:"dropdown"}),enableSorting:!1,enableHiding:!0,meta:{className:"text-right",displayName:"Action"}}],We=({dataEdit:t,dataId:s,onDismiss:c})=>{const[d,n]=i.useState(!1),{toast:h}=K(),y=O(),{handleSubmit:x,register:v,setValue:p}=ee(),[f,A]=i.useState(t.isDefault??!1),[S,r]=i.useState(t.isPublic??!1),C=J({mutationFn:async a=>ue({path:{id:s},body:{name:a.name??t.name??"",isDefault:a.isDefault??t.isDefault??!1,isPublic:a.isPublic??t.isPublic??!1}}),onSuccess:()=>{h({title:"Success",description:"Role Updated Successfully",variant:"success"}),y.invalidateQueries({queryKey:[q.GetRoles]}),P()},onError:a=>{h({title:a?.error?.message,description:a?.error?.details,variant:"destructive"})}}),D=a=>{const l={...a};C.mutate(l)},P=()=>{n(!1),c()};return i.useEffect(()=>{d&&(p("name",t.name||""),p("isDefault",t.isDefault??!1),p("isPublic",t.isPublic??!1))},[d,t,p]),i.useEffect(()=>{n(!0)},[]),e.jsx(L,{open:d,onOpenChange:P,children:e.jsxs(B,{children:[e.jsx(Q,{children:e.jsxs(H,{children:["Update a Role: ",t.name]})}),e.jsxs("form",{onSubmit:x(D),className:"mt-2",onKeyDown:a=>{a.key==="Enter"&&a.target instanceof HTMLInputElement&&(a.preventDefault(),x(D)())},children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(Z,{children:[e.jsx(F,{label:"Name",description:"The name of the role",children:e.jsx(se,{required:!0,...v("name"),defaultValue:t.name??"",placeholder:"Role Name"})}),e.jsx(F,{label:"Default",description:"Whether the role is default",children:e.jsx(M,{checked:f,onCheckedChange:a=>{A(!!a),p("isDefault",!!a)}})}),e.jsx(F,{label:"Public",description:"Whether the role is public",children:e.jsx(M,{checked:S,onCheckedChange:a=>{r(!!a),p("isPublic",!!a)}})})]})}),e.jsxs(z,{className:"mt-5",children:[e.jsx(w,{variant:"ghost",onClick:a=>{a.preventDefault(),n(!1)},disabled:C.isPending,type:"button",children:"Cancel"}),e.jsx(w,{type:"submit",disabled:C.isPending,children:C.isPending?"Saving...":"Save"})]})]})]})})};function $e({table:t,onSearch:s,searchValue:c=""}){const d=t.getState().columnFilters.length>0;return e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[s&&e.jsx("div",{className:"w-full sm:w-auto sm:max-w-[250px]",children:e.jsx(Ae,{onUpdate:s,value:c})}),e.jsxs("div",{className:"flex items-center gap-2 ml-auto",children:[d&&e.jsx(w,{variant:"ghost",onClick:()=>t.resetColumnFilters(),className:"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500",children:"Clear filters"}),e.jsx(Ge,{table:t})]})]})}const Ye=t=>_({queryKey:["roleApplicationsByRole",t],queryFn:()=>me({path:{roleId:t}}),enabled:!!t}),_e=({roleDto:t,onDismiss:s})=>{const[c,d]=i.useState(!1),{toast:n}=K(),h=O(),{data:y,isLoading:x}=qe(0,1e3),{data:v,isLoading:p}=Ye(t.id),[f,A]=i.useState([]);i.useEffect(()=>(d(!0),()=>{h.invalidateQueries({queryKey:["roleApplicationsByRole"]})}),[h]),i.useEffect(()=>{v?.data&&A(v.data.map(a=>a.applicationId).filter(a=>a!==void 0))},[v]);const S=i.useCallback(()=>{d(!1),s()},[s]),r=i.useCallback(async a=>{a.preventDefault();try{await he({body:{roleId:t.id,applicationIds:f}}),n({title:"Success",description:"Applications Updated Successfully",variant:"default"}),h.invalidateQueries({queryKey:["roleApplicationsByRole"]}),S()}catch(l){l instanceof Error&&n({title:"Failed",description:"Application update wasn't successful.",variant:"destructive"})}},[t.id,f,n,h,S]),C=a=>{A(l=>l.includes(a)?l.filter(u=>u!==a):[...l,a])},D=i.useRef(`dialog-${Math.random().toString(36).substring(2,9)}`).current,P=x||p;return e.jsx(L,{open:c,onOpenChange:S,children:e.jsxs(B,{className:"max-h-[90vh] overflow-hidden flex flex-col",style:{maxWidth:"800px",width:"90vw"},children:[e.jsx(Q,{children:e.jsxs(H,{children:["Manage Applications - ",t.name]})}),e.jsx("form",{onSubmit:r,className:"flex-1 overflow-y-auto",children:e.jsx("div",{className:"p-4",children:P?e.jsx("div",{className:"text-center py-4",children:"Loading applications..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"text-sm text-muted-foreground mb-4",children:"Select the applications that this role can access:"}),e.jsxs("div",{className:"grid grid-cols-1 gap-3",children:[y?.items?.map(a=>e.jsxs("label",{className:"flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:f.includes(a.id||""),onChange:()=>C(a.id||""),className:"w-4 h-4 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium",children:a.displayName||a.clientId||"Unnamed Application"}),a.clientUri&&e.jsx("div",{className:"text-sm text-muted-foreground",children:a.clientUri})]})]},a.id)),(!y?.items||y.items.length===0)&&e.jsx("div",{className:"text-center py-8 text-muted-foreground",children:"No applications available"})]})]})})}),e.jsxs(z,{className:"mt-4 border-t pt-4 bg-white dark:bg-gray-950",children:[e.jsx(w,{onClick:a=>{a.preventDefault(),S()},variant:"ghost",children:"Cancel"}),e.jsx(w,{onClick:r,disabled:P,children:"Save"})]})]})},D)},Je=({roleDto:t,onDismiss:s})=>{const[c,d]=i.useState(!1),{toast:n}=K(),[h,y]=i.useState(!1),{data:x}=Fe(V.R,t.name??void 0),v=O(),[p,f]=i.useState([]),[A,S]=i.useState(0);i.useEffect(()=>(d(!0),()=>{v.invalidateQueries({queryKey:[V.R]})}),[]),i.useEffect(()=>{f(Array.isArray(x?.groups)?[...x.groups]:[])},[x]),i.useEffect(()=>{if(x?.groups&&x.groups.length>0){const l=x.groups.map(u=>u.permissions?.every(j=>j.isGranted)).every(u=>u);y(l)}},[x]),i.useEffect(()=>{if(p.length>0){const l=p.map(u=>({...u,permissions:u.permissions?.map(j=>({...j,isGranted:h}))??null}));f(l)}},[h]);const r=i.useCallback(()=>{d(!1),s()},[s]),C=i.useCallback(async l=>{if(l.preventDefault(),!p||p.length===0)return;const j={permissions:p.map(g=>(g.permissions??[]).map(m=>({name:m.name??null,isGranted:m.isGranted??!1}))).flat()};try{await pe({query:{providerKey:t.name??"",providerName:V.R},body:j}),n({title:"Success",description:"Permission Updated Successfully",variant:"default"}),v.invalidateQueries({queryKey:[V.R]}),r()}catch(g){g instanceof Error&&n({title:"Failed",description:"Permission update wasn't successful.",variant:"destructive"})}},[p,t.name,n,v,r]),D=i.useMemo(()=>t.name?.includes(xe.ADMIN)??!1,[t]),P=i.useRef(`dialog-${Math.random().toString(36).substring(2,9)}`).current,a=l=>{if(!Array.isArray(l)||l.length===0)return{parentRows:[],otherPermissions:[]};const u={},j=[],g=["Create","View","Edit","Delete"];return l.forEach(m=>{(m.name?.match(/\./g)||[]).length===1&&!g.some(I=>m.name?.endsWith("."+I))&&(u[m.name]={parent:m,children:{}})}),l.forEach(m=>{const T=m.name?.match(/^(.*)\.(Create|View|Edit|Delete)$/);if(T){const I=T[1],o=T[2];u[I]?u[I].children[o]=m:j.push(m)}else u[m.name]||j.push(m)}),{parentRows:Object.values(u),otherPermissions:j}};return e.jsx(L,{open:c,onOpenChange:r,children:e.jsxs(B,{className:"max-h-[90vh] overflow-hidden flex flex-col",style:{maxWidth:"1000px",width:"90vw"},children:[e.jsx(Q,{children:e.jsxs(H,{children:["Permissions - ",t.name]})}),e.jsx("form",{onSubmit:C,className:"flex-1 overflow-y-auto",children:e.jsxs("div",{className:"p-1",children:[e.jsx(Me,{name:"Grant All Permissions",isGranted:h,id:"all_granted",disabled:!D,onUpdate:()=>{y(l=>!l)},className:"ml-2 mb-4"}),e.jsxs(Ke,{value:A.toString(),onValueChange:l=>S(Number(l)),orientation:"vertical",className:"flex flex-col justify-stretch lg:flex-row gap-4 text-sm text-muted-foreground w-full p-4 border border-border rounded-lg",children:[e.jsx("div",{className:"lg:w-[200px] lg:shrink-0",children:e.jsx(Oe,{variant:"button",className:"flex flex-col items-stretch *:justify-start sticky top-0 bg-white z-10",children:p.map((l,u)=>e.jsx(Ue,{value:u.toString(),className:"inline-block",children:l.displayName},l.name))})}),e.jsx("div",{className:"grow border-s border-border py-0 ps-4 overflow-y-auto",children:p.map((l,u)=>{const{parentRows:j,otherPermissions:g}=a(l.permissions??[]),m=[...j.map(o=>o.parent),...j.flatMap(o=>Object.values(o.children))],T=m.length>0&&m.every(o=>o.isGranted),I=o=>{f(k=>k.map((b,R)=>R!==u?b:{...b,permissions:b.permissions?.map(N=>{const G=j.some(W=>W.parent.name===N.name),E=j.some(W=>Object.values(W.children).some(te=>te.name===N.name));return G||E?{...N,isGranted:o}:N})}))};return e.jsxs(Ve,{value:u.toString(),className:"overflow-x-auto",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:T,onChange:o=>I(o.target.checked),"aria-label":"Select all permissions in this table",className:"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"}),e.jsx("span",{children:"Select All"})]}),j.length>0&&e.jsxs("table",{className:"min-w-full border mb-6",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left",children:"Name"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"Parent"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"View"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"Create"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"Edit"}),e.jsx("th",{className:"px-4 py-2 text-center",children:"Delete"})]})}),e.jsx("tbody",{children:j.map(({parent:o,children:k})=>e.jsxs("tr",{className:"border-t",children:[e.jsx("td",{className:"px-4 py-2 font-medium",children:o.displayName??o.name}),e.jsx("td",{className:"px-4 py-2 text-center",children:e.jsx("input",{type:"checkbox",checked:o.isGranted,onChange:()=>{f(b=>b.map((R,N)=>N!==u?R:{...R,permissions:R.permissions?.map(G=>G.name===o.name?{...G,isGranted:!G.isGranted}:G)}))},"aria-label":String(o.displayName??o.name),className:"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"})}),["View","Create","Edit","Delete"].map(b=>e.jsx("td",{className:"px-4 py-2 text-center",children:k[b]?e.jsx("input",{type:"checkbox",checked:k[b].isGranted,onChange:()=>{f(R=>R.map((N,G)=>G!==u?N:{...N,permissions:N.permissions?.map(E=>E.name===k[b].name?{...E,isGranted:!E.isGranted}:E)}))},"aria-label":String(k[b].displayName??k[b].name),className:"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"}):null},b))]},o.name))})]}),g.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"font-semibold mb-2",children:"Other Permissions"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-1 gap-2",children:g.map(o=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:o.isGranted,onChange:()=>{f(k=>k.map((b,R)=>R!==u?b:{...b,permissions:b.permissions?.map(N=>N.name===o.name?{...N,isGranted:!N.isGranted}:N)}))},"aria-label":String(o.name??o.displayName),className:"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"}),e.jsx("span",{children:o.name??o.displayName})]},o.name))})]})]},l.name)})})]})]})}),e.jsxs(z,{className:"mt-4 border-t pt-4 bg-white dark:bg-gray-950",children:[e.jsx(w,{onClick:l=>{l.preventDefault(),r()},variant:"ghost",children:"Cancel"}),e.jsx(w,{onClick:C,children:"Save"})]})]})},P)},Xe=()=>{const{toast:t}=K(),s=O(),[c,d]=i.useState(""),[n,h]=i.useState(),[y,x]=i.useState({pageIndex:0,pageSize:10}),[v,p]=i.useState([{id:"name",desc:!1}]),f=g=>{if(!g.length)return"name asc";const m=g[0];return`${m?.id} ${m?.desc?"desc":"asc"}`},{isLoading:A,data:S}=Be(y.pageIndex,y.pageSize,c,f(v)),C=ze((g,m,T)=>{h({dataId:g,dataEdit:m,dialogType:T})}),D=g=>{d(g),x(m=>({...m,pageIndex:0}))},P=g=>{x(g)},a=g=>{p(g),x(m=>({...m,pageIndex:0}))},l=()=>{s.invalidateQueries({queryKey:[q.GetRoles]}),setTimeout(()=>{t({title:"Data refreshed",description:"The claims list has been refreshed.",variant:"success"})},100)};if(A)return e.jsx(Pe,{rowCount:y.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const u=S?.items??[],j=S?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4",children:e.jsx(Ie,{title:"Roles",columns:C,data:u,totalCount:j,isLoading:A,manualPagination:!0,manualSorting:!0,pageSize:y.pageSize,onPaginationChange:P,onSortingChange:a,sortingState:v,onSearch:D,searchValue:c,customFilterbar:$e,hideDefaultFilterbar:!0,onRefresh:l,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(Qe,{})}})}),n&&n.dialogType==="edit"&&e.jsx(We,{dataId:n.dataId,dataEdit:n.dataEdit,onDismiss:()=>{s.invalidateQueries({queryKey:[q.GetRoles]}),h(null)}}),n&&n.dialogType==="delete"&&e.jsx(Le,{dataId:n.dataId,onDismiss:()=>{s.invalidateQueries({queryKey:[q.GetRoles]}),h(null)}}),n&&n.dialogType==="permission"&&e.jsx(Je,{roleDto:n.dataEdit,onDismiss:()=>h(null)}),n&&n.dialogType==="application"&&e.jsx(_e,{roleDto:n.dataEdit,onDismiss:()=>h(null)})]})};function us(){return e.jsxs(fe,{children:[e.jsx(ge,{title:"Roles"}),e.jsx(Xe,{})]})}export{us as default};
//# sourceMappingURL=role-ClsarOrJ.js.map
