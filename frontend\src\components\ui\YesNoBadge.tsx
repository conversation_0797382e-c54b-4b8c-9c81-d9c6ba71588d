import React from 'react';
import { cn } from '@/lib/utils';

interface YesNoBadgeProps {
  value: boolean;
  className?: string;
}

export const YesNoBadge: React.FC<YesNoBadgeProps> = ({ value, className }) => {
  return (
    <span
      className={cn(
        "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
        value
          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
          : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
        className
      )}
    >
      {value ? 'Yes' : 'No'}
    </span>
  );
};

export default YesNoBadge; 