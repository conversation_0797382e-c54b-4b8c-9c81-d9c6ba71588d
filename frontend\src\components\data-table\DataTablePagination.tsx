import { Button } from "@/components/ui/button"
import { cx } from "@/lib/utils"
import {
  RiArrowLeftDoubleLine,
  RiArrowLeftSLine,
  RiArrowRightDoubleLine,
  RiArrowRightSLine,
} from "@remixicon/react"
import { type Table } from "@tanstack/react-table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface DataTablePaginationProps<TData> {
  table: Table<TData>
  pageSize: number
  totalCount?: number
  onPageSizeChange?: (pageSize: number) => void
}

export function DataTablePagination<TData>({
  table,
  pageSize,
  totalCount,
  onPageSizeChange,
}: DataTablePaginationProps<TData>) {
  const paginationButtons = [
    {
      icon: RiArrowLeftDoubleLine,
      onClick: () => table.setPageIndex(0),
      disabled: !table.getCanPreviousPage(),
      srText: "First page",
      mobileView: "hidden sm:block",
    },
    {
      icon: RiArrowLeftSLine,
      onClick: () => table.previousPage(),
      disabled: !table.getCanPreviousPage(),
      srText: "Previous page",
      mobileView: "",
    },
    {
      icon: RiArrowRightSLine,
      onClick: () => table.nextPage(),
      disabled: !table.getCanNextPage(),
      srText: "Next page",
      mobileView: "",
    },
    {
      icon: RiArrowRightDoubleLine,
      onClick: () => table.setPageIndex(table.getPageCount() - 1),
      disabled: !table.getCanNextPage(),
      srText: "Last page",
      mobileView: "hidden sm:block",
    },
  ]

  const displayedTotalRows = totalCount ?? table.getFilteredRowModel().rows.length
  const currentPage = table.getState().pagination.pageIndex
  const firstRowIndex = currentPage * pageSize + 1
  const lastRowIndex = Math.min(displayedTotalRows, firstRowIndex + pageSize - 1)

  const pageSizeOptions = [10, 25, 50, 100, "All"]

  const handlePageSizeChange = (value: string) => {
    const newSize = value === "All" ? displayedTotalRows : parseInt(value, 10)
    table.setPageSize(newSize)

    if (onPageSizeChange) {
      onPageSizeChange(newSize)
    }
  }

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-500">Items per page:</span>
        <Select
          value={pageSize.toString()}
          onValueChange={handlePageSizeChange}
        >
          <SelectTrigger className="h-8 w-[80px]">
            <SelectValue placeholder={pageSize.toString()} />
          </SelectTrigger>
          <SelectContent>
            {pageSizeOptions.map((option) => (
              <SelectItem key={option} value={option.toString()}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center justify-between w-full sm:w-auto">
        <div className="text-sm tabular-nums text-gray-500">
          {table.getFilteredSelectedRowModel().rows.length} of {displayedTotalRows} row(s)
          selected.
        </div>
        <div className="flex items-center gap-x-6 lg:gap-x-8">
          <p className="hidden text-sm tabular-nums text-gray-500 sm:block">
            Showing{" "}
            <span className="font-medium text-gray-900 dark:text-gray-50">
              {firstRowIndex}-{lastRowIndex}
            </span>{" "}
            of{" "}
            <span className="font-medium text-gray-900 dark:text-gray-50">
              {displayedTotalRows}
            </span>
          </p>
          <div className="flex items-center gap-x-1.5">
            {paginationButtons.map((button, index) => (
              <Button
                key={index}
                variant="secondary"
                className={cx(button.mobileView, "p-1.5")}
                onClick={() => {
                  button.onClick()
                  table.resetRowSelection()
                }}
                disabled={button.disabled}
              >
                <span className="sr-only">{button.srText}</span>
                <button.icon className="size-4 shrink-0" aria-hidden="true" />
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
