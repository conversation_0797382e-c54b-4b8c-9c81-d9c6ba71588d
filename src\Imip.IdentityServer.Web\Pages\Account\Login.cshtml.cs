﻿using Imip.IdentityServer.ActiveDirectory;
using Imip.IdentityServer.ExternalAuth;
using Imip.IdentityServer.Settings;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.Account.Web;
using Volo.Abp.Account.Web.Pages.Account;
using Volo.Abp.AspNetCore.Mvc.UI.Alerts;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Settings;
using IdentityUser = Volo.Abp.Identity.IdentityUser;

namespace Imip.IdentityServer.Web.Pages.Account;

// Add this class near the top of the file, after the existing using statements
public class CustomLoginInputModel : LoginModel.LoginInputModel
{
    public bool IsHrisPasswordChanged { get; set; } = false;
}

public class IdentityCustomLoginModel : LoginModel
{
    [BindProperty]
    public new CustomLoginInputModel LoginInput { get; set; } = new();

    protected new IAuthenticationSchemeProvider SchemeProvider { get; }
    public required IList<AuthenticationScheme> ExternalLogins { get; set; }

    // Make the Alerts property public
    public new IList<AlertMessage> Alerts => base.Alerts;

    private readonly IConfiguration _configuration;
    private readonly IExternalAuthService _externalAuthService;
    private readonly IActiveDirectoryService _activeDirectoryService;
    private readonly IdentityUserManager _userManager;
    private readonly ISettingProvider _settingProvider;
    private readonly ILogger<IdentityCustomLoginModel> _logger;
    private readonly IRepository<IdentitySession, Guid> _sessionRepository;

    public IdentityCustomLoginModel(
        IAuthenticationSchemeProvider schemeProvider,
        IOptions<AbpAccountOptions> accountOptions,
        IOptions<IdentityOptions> identityOptions,
        IdentityDynamicClaimsPrincipalContributorCache cache,
        IConfiguration configuration,
        IExternalAuthService externalAuthService,
        IActiveDirectoryService activeDirectoryService,
        IdentityUserManager userManager,
        ISettingProvider settingProvider,
        IRepository<IdentitySession, Guid> sessionRepository,
        ILogger<IdentityCustomLoginModel> logger)
        : base(schemeProvider, accountOptions, identityOptions, cache)
    {
        SchemeProvider = schemeProvider;
        _configuration = configuration;
        _externalAuthService = externalAuthService;
        _activeDirectoryService = activeDirectoryService;
        _userManager = userManager;
        _settingProvider = settingProvider;
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    public override async Task<IActionResult> OnGetAsync()
    {
        await base.OnGetAsync();

        ExternalLogins = (await SchemeProvider.GetAllSchemesAsync())
            .Where(x => x.DisplayName != null)
            .ToList();



        // Check if Active Directory is enabled
        ViewData["IsActiveDirectoryEnabled"] = await _settingProvider.GetAsync<bool>(IdentityServerSettings.ActiveDirectory.Enabled);

        // Check if auto-login is enabled
        bool autoLoginEnabled = await _settingProvider.GetAsync<bool>(IdentityServerSettings.ActiveDirectory.AutoLogin);
        if (autoLoginEnabled)
        {
            // Try to get SSO token from cookie or header
            string ssoToken = Request.Cookies["IMIP_SSO_TOKEN"];

            // If SSO token exists, try to validate it
            if (!string.IsNullOrEmpty(ssoToken))
            {
                try
                {
                    // Decode and validate the token
                    var tokenParts = ssoToken.Split('|');
                    if (tokenParts.Length == 2)
                    {
                        string username = tokenParts[0];
                        string tokenHash = tokenParts[1];

                        // Get token secret from settings
                        string? tokenSecret = await _settingProvider.GetOrNullAsync(IdentityServerSettings.ActiveDirectory.TokenSecret);
                        if (string.IsNullOrEmpty(tokenSecret))
                        {
                            tokenSecret = _configuration["ActiveDirectory:TokenSecret"];
                        }

                        // Simple validation - in production you'd use proper cryptographic validation
                        string expectedHash = ComputeHash(username + tokenSecret);

                        if (tokenHash == expectedHash)
                        {
                            // Token is valid, check if user exists in AD
                            var adResult = await _activeDirectoryService.AuthenticateAsync(username);

                            if (adResult.IsAuthenticated)
                            {
                                // Find or create user in our database
                                var user = await _userManager.FindByNameAsync(username);

                                if (user != null)
                                {
                                    // Update ActiveDirectoryLogin property if not already set
                                    if (!user.GetProperty<bool>("ActiveDirectoryLogin"))
                                    {
                                        user.SetProperty("ActiveDirectoryLogin", true);
                                        await _userManager.UpdateAsync(user);
                                    }

                                    // Sign in the user
                                    await SignInManager.SignInAsync(user, true);

                                    // Redirect to appropriate URL
                                    if (!string.IsNullOrEmpty(ReturnUrl) && Url.IsLocalUrl(ReturnUrl))
                                    {
                                        return await RedirectSafelyAsync(ReturnUrl, ReturnUrlHash);
                                    }
                                    else
                                    {
                                        var clientUrl = _configuration["App:ClientUrl"]?.TrimEnd('/');
                                        return Redirect(clientUrl ?? "http://localhost:3000");
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error during SSO auto-login");
                    // Continue to normal login page if auto-login fails
                }
            }
        }

        return Page();
    }

    private string ComputeHash(string input)
    {
        using (var sha256 = System.Security.Cryptography.SHA256.Create())
        {
            var bytes = System.Text.Encoding.UTF8.GetBytes(input);
            var hash = sha256.ComputeHash(bytes);
            return Convert.ToBase64String(hash);
        }
    }

    // Add a model for AD login
    [BindProperty]
    public AdLoginInputModel AdLoginInput { get; set; } = new AdLoginInputModel();

    public class AdLoginInputModel
    {
        [Required]
        [Display(Name = "Username")]
        public string Username { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Password")]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;

        public bool IsHrisPasswordChanged { get; set; } = false;
    }

    public async Task<IActionResult> OnPostWindowsLoginAsync()
    {
        try
        {
            // Check if Active Directory auth is enabled
            bool adEnabled = await _settingProvider.GetAsync<bool>(IdentityServerSettings.ActiveDirectory.Enabled);
            if (!adEnabled)
            {
                _logger.LogWarning("Active Directory authentication attempted but it is not enabled");
                Alerts.Add(new AlertMessage(AlertType.Warning, "Active Directory authentication is not enabled."));
                return Page();
            }

            // Try to get the current Windows identity
            string? windowsUsername = null;

            // Method 1: Try to use the ActiveDirectoryService to get the current Windows username
            windowsUsername = await _activeDirectoryService.GetCurrentWindowsUserNameAsync();

            // Method 2: Try to get from HTTP headers (if configured in Nginx)
            if (string.IsNullOrEmpty(windowsUsername))
            {
                windowsUsername = Request.Headers["X-Windows-Auth-Username"].FirstOrDefault();
            }

            // Method 3: Try to get from environment variables (if using mod_auth_kerb or similar)
            if (string.IsNullOrEmpty(windowsUsername))
            {
                windowsUsername = Environment.GetEnvironmentVariable("REMOTE_USER");
            }

            // Method 4: Try to get from a custom cookie (if set by a separate authentication service)
            if (string.IsNullOrEmpty(windowsUsername))
            {
                windowsUsername = Request.Cookies["WindowsAuthUser"];
            }

            // If we couldn't determine the Windows username through any method
            if (string.IsNullOrEmpty(windowsUsername))
            {
                // For Linux environments, we can use a fallback approach
                // Try to get the username from a configuration setting for testing/development
                windowsUsername = _configuration["ActiveDirectory:DefaultUsername"];

                if (string.IsNullOrEmpty(windowsUsername))
                {
                    _logger.LogWarning("Could not determine Windows username");
                    Alerts.Add(new AlertMessage(AlertType.Danger,
                        "Could not determine your Windows identity. Please contact your administrator."));
                    return Page();
                }
            }

            // For testing purposes, allow manual entry of username
            // This is a fallback for when automatic detection doesn't work
            if (AdLoginInput.Username != null)
            {
                windowsUsername = AdLoginInput.Username;
            }

            // Clean up the username (remove domain prefix if present)
            if (windowsUsername.Contains('\\'))
            {
                windowsUsername = windowsUsername.Split('\\').Last();
            }
            else if (windowsUsername.Contains('@'))
            {
                windowsUsername = windowsUsername.Split('@').First();
            }

            // Verify the user exists in Active Directory
            var adResult = await _activeDirectoryService.AuthenticateAsync(windowsUsername);
            if (!adResult.IsAuthenticated)
            {
                _logger.LogWarning("Active Directory verification failed for user {Username}: {ErrorMessage}",
                    windowsUsername, adResult.ErrorMessage);
                Alerts.Add(new AlertMessage(AlertType.Danger,
                    $"Active Directory verification failed: {adResult.ErrorMessage}"));
                return Page();
            }

            // Find user in our database
            var user = await _userManager.FindByNameAsync(windowsUsername);
            if (user == null)
            {
                _logger.LogWarning("Active Directory verified user {Username} not found in local database", windowsUsername);

                user = new IdentityUser(
                    Guid.NewGuid(),
                    adResult.Username,
                    adResult.Email ?? $"{adResult.Username}@{_configuration["ActiveDirectory:Domain"]}",
                    CurrentTenant.Id)
                {
                    Name = adResult.DisplayName ?? adResult.FirstName + " " + adResult.LastName,
                };

                // Store additional information in ExtraProperties
                user.SetProperty("Source", "ActiveDirectory");
                user.SetProperty("Department", "");
                user.SetProperty("Position", "");
                user.SetProperty("MustChangePassword", false);
                user.SetProperty("ActiveDirectoryLogin", true);

                // Create the user without password validation
                var createResult = await _userManager.CreateAsync(user);

                if (!createResult.Succeeded)
                {
                    _logger.LogWarning("Failed to create user from Active Directory: {Errors}",
                        string.Join(", ", createResult.Errors.Select(e => e.Description)));

                    Alerts.Add(new AlertMessage(AlertType.Danger, "Failed to create user account."));
                    return Page();
                }
            }

            // Update ActiveDirectoryLogin property if not already set
            if (!user.GetProperty<bool>("ActiveDirectoryLogin"))
            {
                user.SetProperty("ActiveDirectoryLogin", true);
                await _userManager.UpdateAsync(user);
            }

            // Sign in the user
            await SignInManager.SignInAsync(user, AdLoginInput.RememberMe);

            // Create and set SSO token if auto-login is enabled
            bool autoLoginEnabled = await _settingProvider.GetAsync<bool>(IdentityServerSettings.ActiveDirectory.AutoLogin);
            if (autoLoginEnabled)
            {
                string? tokenSecret = await _settingProvider.GetOrNullAsync(IdentityServerSettings.ActiveDirectory.TokenSecret);
                if (!string.IsNullOrEmpty(tokenSecret))
                {
                    // Create token
                    string username = windowsUsername;
                    string tokenHash = ComputeHash(username + tokenSecret);
                    string ssoToken = $"{username}|{tokenHash}";

                    // Set cookie
                    var cookieOptions = new CookieOptions
                    {
                        HttpOnly = true,
                        Secure = true,
                        SameSite = SameSiteMode.Lax,
                        Expires = DateTime.UtcNow.AddDays(30) // 30-day expiration
                    };
                    Response.Cookies.Append("IMIP_SSO_TOKEN", ssoToken, cookieOptions);
                }
            }

            // Redirect to appropriate URL
            if (string.IsNullOrEmpty(ReturnUrl))
            {
                var clientUrl = _configuration["App:ClientUrl"]?.TrimEnd('/');
                return Redirect(clientUrl ?? "http://localhost:3000");
            }

            if (!Url.IsLocalUrl(ReturnUrl))
            {
                var clientUrl = _configuration["App:ClientUrl"]?.TrimEnd('/');
                return Redirect($"{clientUrl}{ReturnUrl}");
            }

            return await RedirectSafelyAsync(ReturnUrl, ReturnUrlHash);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Windows Authentication");
            Alerts.Add(new AlertMessage(AlertType.Danger, $"Windows Authentication error: {ex.Message}"));
            return Page();
        }
    }

    private async Task<IActionResult> ProcessExternalLoginAsync(IdentityUser? user)
    {
        // If user is null, try external auth
        // Call external API for authentication
        var externalAuthResponse = await _externalAuthService.AuthenticateAsync(
            LoginInput.UserNameOrEmailAddress,
            LoginInput.Password
        );

        if (!string.IsNullOrEmpty(externalAuthResponse.Token))
        {
            if (user == null)
            {
                // Create a new user with the information from the external API
                user = new IdentityUser(
                    Guid.NewGuid(),
                    externalAuthResponse.Data.Username,
                    string.IsNullOrWhiteSpace(externalAuthResponse.Data.Email)
                        ? $"{externalAuthResponse.Data.Username}@imip.co.id"
                        : externalAuthResponse.Data.Email,
                    CurrentTenant.Id)
                {
                    Name = externalAuthResponse.Data.Name,
                };

                // Store additional information in ExtraProperties
                user.SetProperty("Company", externalAuthResponse.Data.Company);
                user.SetProperty("Department", externalAuthResponse.Data.Organization);
                user.SetProperty("Position", externalAuthResponse.Data.Position);
                user.SetProperty("Location", externalAuthResponse.Data.Location);
                user.SetProperty("Token", externalAuthResponse.Data.Token);
                user.SetProperty("MustChangePassword", false);
                user.SetProperty("ActiveDirectoryLogin", false);

                // Create the user without password validation
                var createResult = await _userManager.CreateAsync(user);

                if (!createResult.Succeeded)
                {
                    _logger.LogWarning("Failed to create user from external authentication: {Errors}",
                        string.Join(", ", createResult.Errors.Select(e => e.Description)));

                    Alerts.Add(new AlertMessage(AlertType.Danger, "Failed to create user account."));
                    return Page();
                }
            }
            else
            {
                user.SetProperty("NeedLoginHris", false);
            }

            // Update password (synchronize with HRIS) and claims for both new and existing users
            var newPasswordHash = BCrypt.Net.BCrypt.HashPassword(LoginInput.Password, 12);
            if (user is not null)
            {
                await _userManager.UpdateSecurityStampAsync(user);
                var pwdHashProperty = typeof(IdentityUser).GetProperty("PasswordHash")
                    ?? throw new InvalidOperationException("PasswordHash property not found on IdentityUser");
                pwdHashProperty.SetValue(user, newPasswordHash);
            }

            // Ensure email is set
            if (user is not null && string.IsNullOrWhiteSpace(user.Email))
            {
                var effectiveEmail = string.IsNullOrWhiteSpace(externalAuthResponse.Data.Email)
                    ? $"{externalAuthResponse.Data.Username}@imip.co.id"
                    : externalAuthResponse.Data.Email;
                var emailResult = await _userManager.SetEmailAsync(user, effectiveEmail);
                if (!emailResult.Succeeded)
                {
                    _logger.LogWarning("Failed to set email for user {UserId}: {Errors}", user.Id,
                        string.Join(", ", emailResult.Errors.Select(e => e.Description)));
                }
            }

            // Update name from external auth if available
            if (user is not null && !string.IsNullOrWhiteSpace(externalAuthResponse.Data.Name))
            {
                user.Name = externalAuthResponse.Data.Name;
                _logger.LogInformation("Updated user name from external auth: {UserId} -> {Name}", user.Id, user.Name);
            }

            if (user is not null)
            {
                await _userManager.UpdateAsync(user);
            }

            // Add claims if needed (idempotent; ABP will avoid duplicates if same value)
            if (user is not null)
            {
                // Use the updated user name (which now includes external auth name if available)
                var nameForClaim = user.Name ?? externalAuthResponse.Data.Name ?? user.UserName ?? "Unknown";
                await _userManager.AddClaimAsync(user, new Claim(ClaimTypes.Name, nameForClaim));
                await _userManager.AddClaimAsync(user, new Claim(ClaimTypes.Email, user.Email ?? ""));
            }
        }
        else
        {
            Alerts.Add(new AlertMessage(AlertType.Danger, L["InvalidUserNameOrPassword"].Value));
            return Page();
        }
        // If user exists, nothing to create here. We allow the normal password sign-in flow to continue.

        // Add this line to satisfy the compiler:
        return Page();
    }

    public override async Task<IActionResult> OnPostAsync(string action)
    {
        try
        {
            await CheckLocalLoginAsync();

            // Check if external auth is enabled
            bool externalAuthEnabled = _configuration.GetValue("ExternalAuth:Enabled", false);

            // First, try to find the user in the local database
            var user = await _userManager.FindByNameAsync(LoginInput.UserNameOrEmailAddress);

            // If user doesn't exist and external auth is enabled, try to authenticate with external API

            if (LoginInput.IsHrisPasswordChanged && externalAuthEnabled)
            {
                _logger.LogWarning("Process login cherry");
                await ProcessExternalLoginAsync(user);
            }

            // If the user requires HRIS login based on extra properties, process external login
            if (user != null && externalAuthEnabled)
            {
                var needLoginHris = user.GetProperty<bool>("NeedLoginHris");
                if (needLoginHris)
                {
                    await ProcessExternalLoginAsync(user);
                }
            }

            if (user == null && externalAuthEnabled)
            {
                await ProcessExternalLoginAsync(user);
            }

            // Now try to sign in with the local account
            var result = await SignInManager.PasswordSignInAsync(
                LoginInput.UserNameOrEmailAddress,
                LoginInput.Password,
                LoginInput.RememberMe,
                true
            );

            if (result.Succeeded)
            {
                // Fill AbpSessions
                var session = new IdentitySession(
                    Guid.NewGuid(),
                    Guid.NewGuid().ToString("N"), // SessionId
                    "Web", // Device
                    "Browser", // DeviceInfo
                    user.Id,
                    user.TenantId,
                    null, // ClientId
                    HttpContext.Connection.RemoteIpAddress?.ToString(),
                    DateTime.UtcNow,
                    DateTime.UtcNow
                );
                await _sessionRepository.InsertAsync(session);

                await _userManager.AddLoginAsync(user, new UserLoginInfo(
                    "Password", // or your provider name
                    user.Id.ToString(), // or username/email as provider key
                    "Password Login" // display name
                ));

                // If ReturnUrl is not specified, redirect to the frontend
                if (string.IsNullOrEmpty(ReturnUrl))
                {
                    var clientUrl = _configuration["App:ClientUrl"]?.TrimEnd('/');
                    return Redirect(clientUrl ?? "http://localhost:3000");
                }

                // If ReturnUrl is specified but not a local URL, redirect to the frontend
                if (!Url.IsLocalUrl(ReturnUrl))
                {
                    var clientUrl = _configuration["App:ClientUrl"]?.TrimEnd('/');
                    return Redirect($"{clientUrl}{ReturnUrl}");
                }

                return await RedirectSafelyAsync(ReturnUrl, ReturnUrlHash);
            }

            if (result.RequiresTwoFactor)
            {
                return RedirectToPage("./SendSecurityCode", new
                {
                    rememberMe = LoginInput.RememberMe,
                    returnUrl = ReturnUrl,
                    returnUrlHash = ReturnUrlHash
                });
            }

            if (result.IsLockedOut)
            {
                Alerts.Add(new AlertMessage(AlertType.Warning, L["UserLockedOutMessage"].Value));
                return Page();
            }

            if (result.IsNotAllowed)
            {
                Alerts.Add(new AlertMessage(AlertType.Warning, L["LoginIsNotAllowed"].Value));
                return Page();
            }

            Alerts.Add(new AlertMessage(AlertType.Danger, L["InvalidUserNameOrPassword"].Value));
            return Page();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login process");
            Alerts.Add(new AlertMessage(AlertType.Danger, ex.Message));
            return Page();
        }
    }
}