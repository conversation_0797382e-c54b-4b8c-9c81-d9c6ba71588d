import { type IdentityRoleDto, type RoleApplicationDto, postApiRoleApplicationsUpdate } from '@/client'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useOpeniddictApplications } from '@/lib/hooks/useOpeniddictApplications'
import { useRoleApplicationsByRole } from '@/lib/hooks/useRoleApplications'
import { useToast } from '@/lib/useToast'
import { useQueryClient } from '@tanstack/react-query'
import { type FormEvent, useCallback, useEffect, useRef, useState } from 'react'

type ManageRoleApplicationsProps = {
  roleDto: IdentityRoleDto
  onDismiss: () => void
}

export const ManageRoleApplications = ({ roleDto, onDismiss }: ManageRoleApplicationsProps) => {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Fetch all available applications (1000 limit)
  const { data: allApplications, isLoading: isLoadingApplications } = useOpeniddictApplications(0, 1000)

  // Fetch current role applications
  const { data: roleApplications, isLoading: isLoadingRoleApps } = useRoleApplicationsByRole(roleDto.id!)

  const [selectedApplications, setSelectedApplications] = useState<string[]>([])

  useEffect(() => {
    setOpen(true)
    return () => {
      void queryClient.invalidateQueries({ queryKey: ['roleApplicationsByRole'] })
    }
  }, [queryClient])

  // Update selected applications when role applications data loads
  useEffect(() => {
    if (roleApplications?.data) {
      setSelectedApplications(roleApplications.data
        .map((app: RoleApplicationDto) => app.applicationId)
        .filter((id): id is string => id !== undefined)
      )
    }
  }, [roleApplications])

  const onCloseEvent = useCallback(() => {
    setOpen(false)
    onDismiss()
  }, [onDismiss])

  const onSubmit = useCallback(
    async (e: FormEvent) => {
      e.preventDefault()

      try {
        await postApiRoleApplicationsUpdate({
          body: {
            roleId: roleDto.id!,
            applicationIds: selectedApplications
          }
        })

        toast({
          title: 'Success',
          description: 'Applications Updated Successfully',
          variant: 'default',
        })

        void queryClient.invalidateQueries({
          queryKey: ['roleApplicationsByRole'],
        })

        onCloseEvent()
      } catch (err: unknown) {
        if (err instanceof Error) {
          toast({
            title: 'Failed',
            description: "Application update wasn't successful.",
            variant: 'destructive',
          })
        }
      }
    },
    [roleDto.id, selectedApplications, toast, queryClient, onCloseEvent]
  )

  const handleApplicationToggle = (applicationId: string) => {
    setSelectedApplications(prev =>
      prev.includes(applicationId)
        ? prev.filter(id => id !== applicationId)
        : [...prev, applicationId]
    )
  }

  // Use stable keys to prevent remounting issues
  const dialogKey = useRef(`dialog-${Math.random().toString(36).substring(2, 9)}`).current
  const isLoading = isLoadingApplications || isLoadingRoleApps

  return (
    <Dialog key={dialogKey} open={open} onOpenChange={onCloseEvent}>
      <DialogContent className="max-h-[90vh] overflow-hidden flex flex-col" style={{ maxWidth: "800px", width: "90vw" }}>
        <DialogHeader>
          <DialogTitle>Manage Applications - {roleDto.name}</DialogTitle>
        </DialogHeader>
        <form onSubmit={onSubmit} className="flex-1 overflow-y-auto">
          <div className="p-4">
            {isLoading ? (
              <div className="text-center py-4">Loading applications...</div>
            ) : (
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground mb-4">
                  Select the applications that this role can access:
                </div>
                <div className="grid grid-cols-1 gap-3">
                  {allApplications?.items?.map((app) => (
                    <label key={app.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedApplications.includes(app.id || '')}
                        onChange={() => handleApplicationToggle(app.id || '')}
                        className="w-4 h-4 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"
                      />
                      <div className="flex-1">
                        <div className="font-medium">{app.displayName || app.clientId || 'Unnamed Application'}</div>
                        {app.clientUri && (
                          <div className="text-sm text-muted-foreground">{app.clientUri}</div>
                        )}
                      </div>
                    </label>
                  ))}
                  {(!allApplications?.items || allApplications.items.length === 0) && (
                    <div className="text-center py-8 text-muted-foreground">
                      No applications available
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </form>
        <DialogFooter className="mt-4 border-t pt-4 bg-white dark:bg-gray-950">
          <Button
            onClick={(e) => {
              e.preventDefault()
              onCloseEvent()
            }}
            variant="ghost"
          >
            Cancel
          </Button>
          <Button onClick={onSubmit} disabled={isLoading}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 