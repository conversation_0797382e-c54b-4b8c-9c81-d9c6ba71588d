FROM mcr.microsoft.com/dotnet/aspnet:9.0

ARG PUBLISH_DIR=web
COPY ${PUBLISH_DIR}/ app/
WORKDIR /app
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# Install LDAP libraries required for Active Directory authentication
# Make installation optional to handle network issues gracefully
RUN set +e && \
    echo "Attempting to install LDAP packages (optional)..." && \
    apt-get update -o Acquire::Retries=3 -o Acquire::http::Timeout=30 && \
    apt-get install -y --no-install-recommends \
        libldap-2.5-0 \
        libldap-common \
        libsasl2-2 \
        libsasl2-modules \
        libgssapi-krb5-2 && \
    echo "LDAP packages installed successfully!" || \
    echo "WARNING: LDAP package installation failed - Active Directory functionality may be limited" && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/* && \
    set -e

# Create directory for certificates and data protection keys
RUN mkdir -p /app/certs /app/data-protection-keys && \
    chmod -R 777 /app/data-protection-keys

# Create a new entrypoint script with proper line endings
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# Set environment\n\
echo "Setting ASPNETCORE_ENVIRONMENT to ${ASPNETCORE_ENVIRONMENT:-Production}"\n\
export ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Production}\n\
\n\
# Check for certificates\n\
if [ -d "/app/certs" ]; then\n\
    echo "Certificate directory contents:"\n\
    ls -la /app/certs\n\
\n\
    if [ -f "/app/certs/identity-server.pfx" ]; then\n\
        echo "Found certificate at /app/certs/identity-server.pfx"\n\
    else\n\
        echo "WARNING: Certificate not found at /app/certs/identity-server.pfx"\n\
        echo "This will cause the application to fail"\n\
    fi\n\
else\n\
    echo "WARNING: Certificate directory /app/certs does not exist"\n\
    echo "This will cause the application to fail"\n\
fi\n\
\n\
# Ensure data protection keys directory exists and has proper permissions\n\
echo "Setting up data protection keys directory..."\n\
mkdir -p /app/data-protection-keys\n\
chmod -R 777 /app/data-protection-keys\n\
echo "Data protection keys directory contents:"\n\
ls -la /app/data-protection-keys\n\
\n\
# Print environment for debugging\n\
echo "Current environment: $ASPNETCORE_ENVIRONMENT"\n\
echo "Current directory: $(pwd)"\n\
echo "Directory listing: $(ls -la)"\n\
\n\
# Print Redis configuration\n\
echo "Redis configuration:"\n\
echo "Redis:IsEnabled=${Redis__IsEnabled:-true}"\n\
echo "Redis:Configuration=${Redis__Configuration:-not set}"\n\
\n\
# Print Active Directory configuration\n\
echo "Active Directory configuration:"\n\
echo "ActiveDirectory:Enabled=${ActiveDirectory__Enabled:-false}"\n\
echo "ActiveDirectory:Domain=${ActiveDirectory__Domain:-not set}"\n\
echo "ActiveDirectory:LdapServer=${ActiveDirectory__LdapServer:-not set}"\n\
echo "ActiveDirectory:Port=${ActiveDirectory__Port:-389}"\n\
echo "ActiveDirectory:UseSsl=${ActiveDirectory__UseSsl:-false}"\n\
echo "ActiveDirectory:WindowsAuthEnabled=${ActiveDirectory__WindowsAuthEnabled:-false}"\n\
\n\
# Print hostname and node information\n\
echo "Pod hostname: $(hostname)"\n\
echo "Node name: ${NODE_NAME:-unknown}"\n\
\n\
# Start the application\n\
echo "Starting application..."\n\
exec dotnet Imip.IdentityServer.Web.dll' > /app/entrypoint.sh && \
chmod +x /app/entrypoint.sh

# Use exec form of ENTRYPOINT with shell to ensure proper execution
ENTRYPOINT ["/bin/bash", "/app/entrypoint.sh"]
