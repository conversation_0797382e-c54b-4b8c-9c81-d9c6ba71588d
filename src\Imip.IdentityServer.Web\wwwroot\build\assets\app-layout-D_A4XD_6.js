import{r as R,j as l,u as Xa,Q as Ja,i as Za,R as Rn,c as ne,d as An,k as br,a as In,b as X}from"./vendor-B0b15ZrB.js";import{S as it,G as eo,H as to,I as ro,J as no,V as ao,K as oo,M as so,N as io,Q as co,U as lo,W as Pn,X as kn,Y as Nn,Z as uo,_ as fo,$ as po,a0 as ho,x as Dn,a1 as Ln,y as yr,z as ae,B as Ot,P as Ae,v as Rt,a2 as vo,a3 as mo,a4 as go,a5 as zn,a6 as Bn,E as bo,a7 as yo,a8 as xo,a9 as wo,aa as _o,ab as jo,ac as So,ad as Co,ae as Oo,af as Eo,ag as To,ah as Mo,ai as Ro,R as Ao,C as Io,r as <PERSON>,s as ko,aj as No,a as Do,O as Lo,ak as $n,u as zo,al as Bo,am as $o,an as Fo,ao as Ko,ap as Vo,aq as Go,ar as qo,as as Uo,at as Ho}from"./radix-BQPyiA8r.js";import{c as B,k as Wo,q as Fn,Y as Et}from"./App-De6zOdMU.js";const Yo={GetUsers:"GetUsers",GetTenants:"GetTenants",GetRoles:"GetRoles",GetAppConfig:"GetAppConfig",GetFeatures:"GetFeatures",GetTranslations:"GetTranslations",GetPermissions:"GetPermissions",GetUserRoles:"GetUserRoles",GetEmailing:"GetEmailing",GetActiveDirectory:"GetActiveDirectory",GetAssignableRoles:"GetAssignableRoles",GetProfile:"GetProfile",GetSession:"GetSession",GetOpeniddictApplications:"GetOpeniddictApplications",GetOpeniddictApplicationsEdit:"GetOpeniddictApplicationsEdit",GetOpeniddictApplicationsPermissions:"GetOpeniddictApplicationsPermissions",GetOpeniddictApplicationsResources:"GetOpeniddictApplicationsResources",GetOpeniddictGrantTypes:"GetOpeniddictGrantTypes",GetOpeniddictResources:"GetOpeniddictResources",GetOpeniddictScopes:"GetOpeniddictScopes",GetIdentityClaims:"GetIdentityClaims",GetIdentityClaimTypes:"GetIdentityClaimTypes",GetOpeniddictResourcesAvailableResources:"GetOpeniddictResourcesAvailableResources",GetOpeniddictScopesAvailableScopes:"GetOpeniddictScopesAvailableScopes",GetOpeniddictRequirements:"GetOpeniddictRequirements"},Qo=e=>(e?.client??B).get({url:"/api/abp/application-configuration",...e}),Xo=e=>(e?.client??B).get({url:"/api/my-account/logout",...e}),md=e=>(e?.client??B).get({url:"/api/active-directory-settings",...e}),gd=e=>(e?.client??B).put({url:"/api/active-directory-settings",...e,headers:{"Content-Type":"application/json",...e?.headers}}),bd=e=>(e?.client??B).post({url:"/api/active-directory-settings/test-connection",...e,headers:{"Content-Type":"application/json",...e?.headers}}),yd=e=>(e?.client??B).post({url:"/api/identity/claim-types/list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),xd=e=>(e.client??B).delete({url:"/api/identity/claim-types/{id}",...e}),wd=e=>(e.client??B).put({url:"/api/identity/claim-types/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),_d=e=>(e?.client??B).post({url:"/api/identity/claim-types",...e,headers:{"Content-Type":"application/json",...e?.headers}}),jd=e=>B.get({url:"/api/dashboard/users/count",...e}),Sd=e=>B.get({url:"/api/dashboard/clients/count",...e}),Cd=e=>B.get({url:"/api/dashboard/logins/failed/count",...e}),Od=e=>B.get({url:"/api/dashboard/roles/distribution",...e}),Ed=e=>B.get({url:"/api/dashboard/clients/top-logins",...e}),Td=e=>B.get({url:"/api/dashboard/security/overview",...e}),Md=e=>B.get({url:"/api/dashboard/users/activity",...e}),Rd=e=>B.get({url:"/api/dashboard/security/threats",...e}),Ad=e=>B.get({url:"/api/dashboard/system/health",...e}),Id=e=>B.get({url:"/api/dashboard/analytics/hourly-activity",...e}),Pd=e=>(e?.client??B).get({url:"/api/setting-management/emailing",...e}),kd=e=>(e?.client??B).post({url:"/api/setting-management/emailing",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Nd=e=>(e?.client??B).post({url:"/api/setting-management/emailing/send-test-email",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Dd=e=>(e?.client??B).delete({url:"/api/feature-management/features",...e}),Ld=e=>(e?.client??B).get({url:"/api/feature-management/features",...e}),zd=e=>(e?.client??B).put({url:"/api/feature-management/features",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Bd=e=>(e?.client??B).post({...Wo,url:"/api/import/csv",...e,headers:{"Content-Type":null,...e?.headers}}),$d=e=>(e?.client??B).get({url:"/api/import/templates",...e}),Fd=e=>(e?.client??B).post({url:"/api/openiddict/applications/list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Kd=e=>(e.client??B).delete({url:"/api/openiddict/applications/{id}",...e}),Vd=e=>(e.client??B).put({url:"/api/openiddict/applications/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),Gd=e=>(e?.client??B).post({url:"/api/openiddict/applications",...e,headers:{"Content-Type":"application/json",...e?.headers}}),qd=e=>(e?.client??B).get({url:"/api/openiddict/applications/permissions",...e}),Ud=e=>(e?.client??B).get({url:"/api/openiddict/requirements",...e}),Hd=e=>(e?.client??B).post({url:"/api/openiddict/resources/list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Wd=e=>(e.client??B).delete({url:"/api/openiddict/resources/{id}",...e}),Yd=e=>(e.client??B).put({url:"/api/openiddict/resources/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),Qd=e=>(e?.client??B).post({url:"/api/openiddict/resources",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Xd=e=>(e?.client??B).get({url:"/api/openiddict/resources/available-resources",...e}),Jd=e=>(e?.client??B).post({url:"/api/openiddict/scopes/list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Zd=e=>(e.client??B).delete({url:"/api/openiddict/scopes/{id}",...e}),ef=e=>(e.client??B).put({url:"/api/openiddict/scopes/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),tf=e=>(e?.client??B).post({url:"/api/openiddict/scopes",...e,headers:{"Content-Type":"application/json",...e?.headers}}),rf=e=>(e?.client??B).get({url:"/api/permission-management/permissions",...e}),nf=e=>(e?.client??B).put({url:"/api/permission-management/permissions",...e,headers:{"Content-Type":"application/json",...e?.headers}}),af=e=>(e?.client??B).get({url:"/api/identity/roles",...e}),of=e=>(e?.client??B).post({url:"/api/identity/roles",...e,headers:{"Content-Type":"application/json",...e?.headers}}),sf=e=>(e.client??B).delete({url:"/api/identity/roles/{id}",...e}),cf=e=>(e.client??B).put({url:"/api/identity/roles/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),lf=e=>(e.client??B).get({url:"/api/role-applications/by-role/{roleId}",...e}),uf=e=>(e?.client??B).post({url:"/api/role-applications/update",...e,headers:{"Content-Type":"application/json",...e?.headers}}),df=e=>(e?.client??B).post({url:"/api/security-logs/my-logs",...e,headers:{"Content-Type":"application/json",...e?.headers}}),ff=e=>(e.client??B).delete({url:"/api/multi-tenancy/tenants/{id}",...e}),pf=e=>(e.client??B).put({url:"/api/multi-tenancy/tenants/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),hf=e=>(e?.client??B).get({url:"/api/multi-tenancy/tenants",...e}),vf=e=>(e.client??B).delete({url:"/api/identity/users/{id}",...e}),mf=e=>(e.client??B).put({url:"/api/identity/users/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),gf=e=>(e?.client??B).get({url:"/api/identity/users",...e}),bf=e=>(e?.client??B).post({url:"/api/identity/users",...e,headers:{"Content-Type":"application/json",...e?.headers}}),yf=e=>(e.client??B).get({url:"/api/identity/users/{id}/roles",...e}),xf=e=>(e?.client??B).get({url:"/api/identity/users/assignable-roles",...e}),wf=e=>(e.client??B).get({url:"/api/identity/user-details/{id}",...e}),Jo=1,Zo=1e6;let Bt=0;function es(){return Bt=(Bt+1)%Number.MAX_SAFE_INTEGER,Bt.toString()}const $t=new Map,Pr=e=>{if($t.has(e))return;const t=setTimeout(()=>{$t.delete(e),at({type:"REMOVE_TOAST",toastId:e})},Zo);$t.set(e,t)},ts=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Jo)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(r=>r.id===t.toast.id?{...r,...t.toast}:r)};case"DISMISS_TOAST":{const{toastId:r}=t;return r?Pr(r):e.toasts.forEach(n=>{Pr(n.id)}),{...e,toasts:e.toasts.map(n=>n.id===r||r===void 0?{...n,open:!1}:n)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(r=>r.id!==t.toastId)}}},wt=[];let _t={toasts:[]};function at(e){_t=ts(_t,e),wt.forEach(t=>{t(_t)})}function rs({...e}){const t=es(),r=a=>at({type:"UPDATE_TOAST",toast:{...a,id:t}}),n=()=>at({type:"DISMISS_TOAST",toastId:t});return at({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:a=>{a||n()}}}),{id:t,dismiss:n,update:r}}function ns(){const[e,t]=R.useState(_t);return R.useEffect(()=>(wt.push(t),()=>{const r=wt.indexOf(t);r>-1&&wt.splice(r,1)}),[e]),{...e,toast:rs,dismiss:r=>at({type:"DISMISS_TOAST",toastId:r})}}function Kn(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=Kn(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function xr(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=Kn(e))&&(n&&(n+=" "),n+=t);return n}const wr="-",as=e=>{const t=ss(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:s=>{const c=s.split(wr);return c[0]===""&&c.length!==1&&c.shift(),Vn(c,t)||os(s)},getConflictingClassGroupIds:(s,c)=>{const i=r[s]||[];return c&&n[s]?[...i,...n[s]]:i}}},Vn=(e,t)=>{if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),a=n?Vn(e.slice(1),n):void 0;if(a)return a;if(t.validators.length===0)return;const o=e.join(wr);return t.validators.find(({validator:s})=>s(o))?.classGroupId},kr=/^\[(.+)\]$/,os=e=>{if(kr.test(e)){const t=kr.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},ss=e=>{const{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(const a in r)rr(r[a],n,a,t);return n},rr=(e,t,r,n)=>{e.forEach(a=>{if(typeof a=="string"){const o=a===""?t:Nr(t,a);o.classGroupId=r;return}if(typeof a=="function"){if(is(a)){rr(a(n),t,r,n);return}t.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([o,s])=>{rr(s,Nr(t,o),r,n)})})},Nr=(e,t)=>{let r=e;return t.split(wr).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},is=e=>e.isThemeGetter,cs=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const a=(o,s)=>{r.set(o,s),t++,t>e&&(t=0,n=r,r=new Map)};return{get(o){let s=r.get(o);if(s!==void 0)return s;if((s=n.get(o))!==void 0)return a(o,s),s},set(o,s){r.has(o)?r.set(o,s):a(o,s)}}},nr="!",ar=":",ls=ar.length,us=e=>{const{prefix:t,experimentalParseClassName:r}=e;let n=a=>{const o=[];let s=0,c=0,i=0,u;for(let p=0;p<a.length;p++){let b=a[p];if(s===0&&c===0){if(b===ar){o.push(a.slice(i,p)),i=p+ls;continue}if(b==="/"){u=p;continue}}b==="["?s++:b==="]"?s--:b==="("?c++:b===")"&&c--}const f=o.length===0?a:a.substring(i),d=ds(f),g=d!==f,m=u&&u>i?u-i:void 0;return{modifiers:o,hasImportantModifier:g,baseClassName:d,maybePostfixModifierPosition:m}};if(t){const a=t+ar,o=n;n=s=>s.startsWith(a)?o(s.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:s,maybePostfixModifierPosition:void 0}}if(r){const a=n;n=o=>r({className:o,parseClassName:a})}return n},ds=e=>e.endsWith(nr)?e.substring(0,e.length-1):e.startsWith(nr)?e.substring(1):e,fs=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const a=[];let o=[];return n.forEach(s=>{s[0]==="["||t[s]?(a.push(...o.sort(),s),o=[]):o.push(s)}),a.push(...o.sort()),a}},ps=e=>({cache:cs(e.cacheSize),parseClassName:us(e),sortModifiers:fs(e),...as(e)}),hs=/\s+/,vs=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:a,sortModifiers:o}=t,s=[],c=e.trim().split(hs);let i="";for(let u=c.length-1;u>=0;u-=1){const f=c[u],{isExternal:d,modifiers:g,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:b}=r(f);if(d){i=f+(i.length>0?" "+i:i);continue}let v=!!b,w=n(v?p.substring(0,b):p);if(!w){if(!v){i=f+(i.length>0?" "+i:i);continue}if(w=n(p),!w){i=f+(i.length>0?" "+i:i);continue}v=!1}const y=o(g).join(":"),h=m?y+nr:y,j=h+w;if(s.includes(j))continue;s.push(j);const E=a(w,v);for(let M=0;M<E.length;++M){const O=E[M];s.push(h+O)}i=f+(i.length>0?" "+i:i)}return i};function ms(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=Gn(t))&&(n&&(n+=" "),n+=r);return n}const Gn=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=Gn(e[n]))&&(r&&(r+=" "),r+=t);return r};function gs(e,...t){let r,n,a,o=s;function s(i){const u=t.reduce((f,d)=>d(f),e());return r=ps(u),n=r.cache.get,a=r.cache.set,o=c,c(i)}function c(i){const u=n(i);if(u)return u;const f=vs(i,r);return a(i,f),f}return function(){return o(ms.apply(null,arguments))}}const Z=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},qn=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Un=/^\((?:(\w[\w-]*):)?(.+)\)$/i,bs=/^\d+\/\d+$/,ys=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,xs=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ws=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,_s=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,js=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,We=e=>bs.test(e),K=e=>!!e&&!Number.isNaN(Number(e)),Me=e=>!!e&&Number.isInteger(Number(e)),Ft=e=>e.endsWith("%")&&K(e.slice(0,-1)),je=e=>ys.test(e),Ss=()=>!0,Cs=e=>xs.test(e)&&!ws.test(e),Hn=()=>!1,Os=e=>_s.test(e),Es=e=>js.test(e),Ts=e=>!L(e)&&!z(e),Ms=e=>Qe(e,Qn,Hn),L=e=>qn.test(e),Le=e=>Qe(e,Xn,Cs),Kt=e=>Qe(e,ks,K),Dr=e=>Qe(e,Wn,Hn),Rs=e=>Qe(e,Yn,Es),pt=e=>Qe(e,Jn,Os),z=e=>Un.test(e),tt=e=>Xe(e,Xn),As=e=>Xe(e,Ns),Lr=e=>Xe(e,Wn),Is=e=>Xe(e,Qn),Ps=e=>Xe(e,Yn),ht=e=>Xe(e,Jn,!0),Qe=(e,t,r)=>{const n=qn.exec(e);return n?n[1]?t(n[1]):r(n[2]):!1},Xe=(e,t,r=!1)=>{const n=Un.exec(e);return n?n[1]?t(n[1]):r:!1},Wn=e=>e==="position"||e==="percentage",Yn=e=>e==="image"||e==="url",Qn=e=>e==="length"||e==="size"||e==="bg-size",Xn=e=>e==="length",ks=e=>e==="number",Ns=e=>e==="family-name",Jn=e=>e==="shadow",Ds=()=>{const e=Z("color"),t=Z("font"),r=Z("text"),n=Z("font-weight"),a=Z("tracking"),o=Z("leading"),s=Z("breakpoint"),c=Z("container"),i=Z("spacing"),u=Z("radius"),f=Z("shadow"),d=Z("inset-shadow"),g=Z("text-shadow"),m=Z("drop-shadow"),p=Z("blur"),b=Z("perspective"),v=Z("aspect"),w=Z("ease"),y=Z("animate"),h=()=>["auto","avoid","all","avoid-page","page","left","right","column"],j=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...j(),z,L],M=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto","contain","none"],C=()=>[z,L,i],T=()=>[We,"full","auto",...C()],x=()=>[Me,"none","subgrid",z,L],_=()=>["auto",{span:["full",Me,z,L]},Me,z,L],P=()=>[Me,"auto",z,L],A=()=>["auto","min","max","fr",z,L],S=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],k=()=>["auto",...C()],D=()=>[We,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],I=()=>[e,z,L],G=()=>[...j(),Lr,Dr,{position:[z,L]}],H=()=>["no-repeat",{repeat:["","x","y","space","round"]}],J=()=>["auto","cover","contain",Is,Ms,{size:[z,L]}],ee=()=>[Ft,tt,Le],q=()=>["","none","full",u,z,L],U=()=>["",K,tt,Le],se=()=>["solid","dashed","dotted","double"],et=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>[K,Ft,Lr,Dr],Ue=()=>["","none",p,z,L],ke=()=>["none",K,z,L],Ne=()=>["none",K,z,L],He=()=>[K,z,L],Ee=()=>[We,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[je],breakpoint:[je],color:[Ss],container:[je],"drop-shadow":[je],ease:["in","out","in-out"],font:[Ts],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[je],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[je],shadow:[je],spacing:["px",K],text:[je],"text-shadow":[je],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",We,L,z,v]}],container:["container"],columns:[{columns:[K,L,z,c]}],"break-after":[{"break-after":h()}],"break-before":[{"break-before":h()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:T()}],"inset-x":[{"inset-x":T()}],"inset-y":[{"inset-y":T()}],start:[{start:T()}],end:[{end:T()}],top:[{top:T()}],right:[{right:T()}],bottom:[{bottom:T()}],left:[{left:T()}],visibility:["visible","invisible","collapse"],z:[{z:[Me,"auto",z,L]}],basis:[{basis:[We,"full","auto",c,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[K,We,"auto","initial","none",L]}],grow:[{grow:["",K,z,L]}],shrink:[{shrink:["",K,z,L]}],order:[{order:[Me,"first","last","none",z,L]}],"grid-cols":[{"grid-cols":x()}],"col-start-end":[{col:_()}],"col-start":[{"col-start":P()}],"col-end":[{"col-end":P()}],"grid-rows":[{"grid-rows":x()}],"row-start-end":[{row:_()}],"row-start":[{"row-start":P()}],"row-end":[{"row-end":P()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":A()}],"auto-rows":[{"auto-rows":A()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...S(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...S()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":S()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:k()}],mx:[{mx:k()}],my:[{my:k()}],ms:[{ms:k()}],me:[{me:k()}],mt:[{mt:k()}],mr:[{mr:k()}],mb:[{mb:k()}],ml:[{ml:k()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[c,"screen",...D()]}],"min-w":[{"min-w":[c,"screen","none",...D()]}],"max-w":[{"max-w":[c,"screen","none","prose",{screen:[s]},...D()]}],h:[{h:["screen","lh",...D()]}],"min-h":[{"min-h":["screen","lh","none",...D()]}],"max-h":[{"max-h":["screen","lh",...D()]}],"font-size":[{text:["base",r,tt,Le]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,z,Kt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Ft,L]}],"font-family":[{font:[As,L,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,z,L]}],"line-clamp":[{"line-clamp":[K,"none",z,Kt]}],leading:[{leading:[o,...C()]}],"list-image":[{"list-image":["none",z,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",z,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:I()}],"text-color":[{text:I()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...se(),"wavy"]}],"text-decoration-thickness":[{decoration:[K,"from-font","auto",z,Le]}],"text-decoration-color":[{decoration:I()}],"underline-offset":[{"underline-offset":[K,"auto",z,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",z,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",z,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:G()}],"bg-repeat":[{bg:H()}],"bg-size":[{bg:J()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Me,z,L],radial:["",z,L],conic:[Me,z,L]},Ps,Rs]}],"bg-color":[{bg:I()}],"gradient-from-pos":[{from:ee()}],"gradient-via-pos":[{via:ee()}],"gradient-to-pos":[{to:ee()}],"gradient-from":[{from:I()}],"gradient-via":[{via:I()}],"gradient-to":[{to:I()}],rounded:[{rounded:q()}],"rounded-s":[{"rounded-s":q()}],"rounded-e":[{"rounded-e":q()}],"rounded-t":[{"rounded-t":q()}],"rounded-r":[{"rounded-r":q()}],"rounded-b":[{"rounded-b":q()}],"rounded-l":[{"rounded-l":q()}],"rounded-ss":[{"rounded-ss":q()}],"rounded-se":[{"rounded-se":q()}],"rounded-ee":[{"rounded-ee":q()}],"rounded-es":[{"rounded-es":q()}],"rounded-tl":[{"rounded-tl":q()}],"rounded-tr":[{"rounded-tr":q()}],"rounded-br":[{"rounded-br":q()}],"rounded-bl":[{"rounded-bl":q()}],"border-w":[{border:U()}],"border-w-x":[{"border-x":U()}],"border-w-y":[{"border-y":U()}],"border-w-s":[{"border-s":U()}],"border-w-e":[{"border-e":U()}],"border-w-t":[{"border-t":U()}],"border-w-r":[{"border-r":U()}],"border-w-b":[{"border-b":U()}],"border-w-l":[{"border-l":U()}],"divide-x":[{"divide-x":U()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":U()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...se(),"hidden","none"]}],"divide-style":[{divide:[...se(),"hidden","none"]}],"border-color":[{border:I()}],"border-color-x":[{"border-x":I()}],"border-color-y":[{"border-y":I()}],"border-color-s":[{"border-s":I()}],"border-color-e":[{"border-e":I()}],"border-color-t":[{"border-t":I()}],"border-color-r":[{"border-r":I()}],"border-color-b":[{"border-b":I()}],"border-color-l":[{"border-l":I()}],"divide-color":[{divide:I()}],"outline-style":[{outline:[...se(),"none","hidden"]}],"outline-offset":[{"outline-offset":[K,z,L]}],"outline-w":[{outline:["",K,tt,Le]}],"outline-color":[{outline:I()}],shadow:[{shadow:["","none",f,ht,pt]}],"shadow-color":[{shadow:I()}],"inset-shadow":[{"inset-shadow":["none",d,ht,pt]}],"inset-shadow-color":[{"inset-shadow":I()}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:I()}],"ring-offset-w":[{"ring-offset":[K,Le]}],"ring-offset-color":[{"ring-offset":I()}],"inset-ring-w":[{"inset-ring":U()}],"inset-ring-color":[{"inset-ring":I()}],"text-shadow":[{"text-shadow":["none",g,ht,pt]}],"text-shadow-color":[{"text-shadow":I()}],opacity:[{opacity:[K,z,L]}],"mix-blend":[{"mix-blend":[...et(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":et()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[K]}],"mask-image-linear-from-pos":[{"mask-linear-from":W()}],"mask-image-linear-to-pos":[{"mask-linear-to":W()}],"mask-image-linear-from-color":[{"mask-linear-from":I()}],"mask-image-linear-to-color":[{"mask-linear-to":I()}],"mask-image-t-from-pos":[{"mask-t-from":W()}],"mask-image-t-to-pos":[{"mask-t-to":W()}],"mask-image-t-from-color":[{"mask-t-from":I()}],"mask-image-t-to-color":[{"mask-t-to":I()}],"mask-image-r-from-pos":[{"mask-r-from":W()}],"mask-image-r-to-pos":[{"mask-r-to":W()}],"mask-image-r-from-color":[{"mask-r-from":I()}],"mask-image-r-to-color":[{"mask-r-to":I()}],"mask-image-b-from-pos":[{"mask-b-from":W()}],"mask-image-b-to-pos":[{"mask-b-to":W()}],"mask-image-b-from-color":[{"mask-b-from":I()}],"mask-image-b-to-color":[{"mask-b-to":I()}],"mask-image-l-from-pos":[{"mask-l-from":W()}],"mask-image-l-to-pos":[{"mask-l-to":W()}],"mask-image-l-from-color":[{"mask-l-from":I()}],"mask-image-l-to-color":[{"mask-l-to":I()}],"mask-image-x-from-pos":[{"mask-x-from":W()}],"mask-image-x-to-pos":[{"mask-x-to":W()}],"mask-image-x-from-color":[{"mask-x-from":I()}],"mask-image-x-to-color":[{"mask-x-to":I()}],"mask-image-y-from-pos":[{"mask-y-from":W()}],"mask-image-y-to-pos":[{"mask-y-to":W()}],"mask-image-y-from-color":[{"mask-y-from":I()}],"mask-image-y-to-color":[{"mask-y-to":I()}],"mask-image-radial":[{"mask-radial":[z,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":W()}],"mask-image-radial-to-pos":[{"mask-radial-to":W()}],"mask-image-radial-from-color":[{"mask-radial-from":I()}],"mask-image-radial-to-color":[{"mask-radial-to":I()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":j()}],"mask-image-conic-pos":[{"mask-conic":[K]}],"mask-image-conic-from-pos":[{"mask-conic-from":W()}],"mask-image-conic-to-pos":[{"mask-conic-to":W()}],"mask-image-conic-from-color":[{"mask-conic-from":I()}],"mask-image-conic-to-color":[{"mask-conic-to":I()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:G()}],"mask-repeat":[{mask:H()}],"mask-size":[{mask:J()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",z,L]}],filter:[{filter:["","none",z,L]}],blur:[{blur:Ue()}],brightness:[{brightness:[K,z,L]}],contrast:[{contrast:[K,z,L]}],"drop-shadow":[{"drop-shadow":["","none",m,ht,pt]}],"drop-shadow-color":[{"drop-shadow":I()}],grayscale:[{grayscale:["",K,z,L]}],"hue-rotate":[{"hue-rotate":[K,z,L]}],invert:[{invert:["",K,z,L]}],saturate:[{saturate:[K,z,L]}],sepia:[{sepia:["",K,z,L]}],"backdrop-filter":[{"backdrop-filter":["","none",z,L]}],"backdrop-blur":[{"backdrop-blur":Ue()}],"backdrop-brightness":[{"backdrop-brightness":[K,z,L]}],"backdrop-contrast":[{"backdrop-contrast":[K,z,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",K,z,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[K,z,L]}],"backdrop-invert":[{"backdrop-invert":["",K,z,L]}],"backdrop-opacity":[{"backdrop-opacity":[K,z,L]}],"backdrop-saturate":[{"backdrop-saturate":[K,z,L]}],"backdrop-sepia":[{"backdrop-sepia":["",K,z,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",z,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[K,"initial",z,L]}],ease:[{ease:["linear","initial",w,z,L]}],delay:[{delay:[K,z,L]}],animate:[{animate:["none",y,z,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,z,L]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:ke()}],"rotate-x":[{"rotate-x":ke()}],"rotate-y":[{"rotate-y":ke()}],"rotate-z":[{"rotate-z":ke()}],scale:[{scale:Ne()}],"scale-x":[{"scale-x":Ne()}],"scale-y":[{"scale-y":Ne()}],"scale-z":[{"scale-z":Ne()}],"scale-3d":["scale-3d"],skew:[{skew:He()}],"skew-x":[{"skew-x":He()}],"skew-y":[{"skew-y":He()}],transform:[{transform:[z,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ee()}],"translate-x":[{"translate-x":Ee()}],"translate-y":[{"translate-y":Ee()}],"translate-z":[{"translate-z":Ee()}],"translate-none":["translate-none"],accent:[{accent:I()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:I()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",z,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",z,L]}],fill:[{fill:["none",...I()]}],"stroke-w":[{stroke:[K,tt,Le,Kt]}],stroke:[{stroke:["none",...I()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Zn=gs(Ds);function $(...e){return Zn(xr(e))}function _f(...e){return Zn(xr(...e))}const jf=["focus:ring-2","focus:ring-blue-200 dark:focus:ring-blue-700/30","focus:border-blue-500 dark:focus:border-blue-700"],Sf=["outline outline-offset-2 outline-0 focus-visible:outline-2","outline-blue-500 dark:outline-blue-500"],Cf=["ring-2","border-red-500 dark:border-red-700","ring-red-200 dark:ring-red-700/30"],Of={currency:({number:e,maxFractionDigits:t=2,currency:r="USD"})=>new Intl.NumberFormat("en-US",{style:"currency",currency:r,maximumFractionDigits:t}).format(e),unit:e=>new Intl.NumberFormat("en-US",{style:"decimal"}).format(e),percentage:({number:e,decimals:t=1})=>new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:t,maximumFractionDigits:t}).format(e),million:({number:e,decimals:t=1})=>`${new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}M`},Ef={ADMIN:"admin"},Tf={U:"U",R:"R",T:"T"},vt=(e,t)=>{let r=e;const n=[];for(;r<=t;)n.push(r),r++;return n},Mf=(e,t)=>{if(e>7){const a=Math.max(2,t-1),o=Math.min(e-1,t+1);let s=vt(a,o);const c=a>2,i=e-o>1,u=5-(s.length+3);switch(!0){case(c&&!i):{s=["SPACER",...vt(a-u,a-1),...s];break}case(!c&&i):{const f=vt(o+1,o+u);s=[...s,...f,"SPACER"];break}case(c&&i):default:{s=["SPACER",...s,"SPACER"];break}}return[1,...s,e]}return vt(1,e)},zr=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Br=xr,_r=(e,t)=>r=>{var n;if(t?.variants==null)return Br(e,r?.class,r?.className);const{variants:a,defaultVariants:o}=t,s=Object.keys(a).map(u=>{const f=r?.[u],d=o?.[u];if(f===null)return null;const g=zr(f)||zr(d);return a[u][g]}),c=r&&Object.entries(r).reduce((u,f)=>{let[d,g]=f;return g===void 0||(u[d]=g),u},{}),i=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((u,f)=>{let{class:d,className:g,...m}=f;return Object.entries(m).every(p=>{let[b,v]=p;return Array.isArray(v)?v.includes({...o,...c}[b]):{...o,...c}[b]===v})?[...u,d,g]:u},[]);return Br(e,s,i,r?.class,r?.className)},Ls=_r("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",primary:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white shadow-xs hover:bg-green-700 focus-visible:ring-green-600/20 dark:focus-visible:ring-green-400/40 dark:bg-green-600/80",warning:"bg-yellow-600 text-white shadow-xs hover:bg-yellow-700 focus-visible:ring-yellow-600/20 dark:focus-visible:ring-yellow-400/40 dark:bg-yellow-600/80",info:"bg-blue-600 text-white shadow-xs hover:bg-blue-700 focus-visible:ring-blue-600/20 dark:focus-visible:ring-blue-400/40 dark:bg-blue-600/80",error:"bg-red-600 text-white shadow-xs hover:bg-red-700 focus-visible:ring-red-600/20 dark:focus-visible:ring-red-400/40 dark:bg-red-600/80"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function At({className:e,variant:t,size:r,asChild:n=!1,...a}){const o=n?it:"button";return l.jsx(o,{"data-slot":"button",className:$(Ls({variant:t,size:r,className:e})),...a})}/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zs=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Bs=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,r,n)=>n?n.toUpperCase():r.toLowerCase()),$r=e=>{const t=Bs(e);return t.charAt(0).toUpperCase()+t.slice(1)},ea=(...e)=>e.filter((t,r,n)=>!!t&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim(),$s=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Fs={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ks=R.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:a="",children:o,iconNode:s,...c},i)=>R.createElement("svg",{ref:i,...Fs,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:ea("lucide",a),...!o&&!$s(c)&&{"aria-hidden":"true"},...c},[...s.map(([u,f])=>R.createElement(u,f)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=(e,t)=>{const r=R.forwardRef(({className:n,...a},o)=>R.createElement(Ks,{ref:o,iconNode:t,className:ea(`lucide-${zs($r(e))}`,`lucide-${e}`,n),...a}));return r.displayName=$r(e),r};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vs=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],ta=Ge("check",Vs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gs=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],ra=Ge("chevron-down",Gs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qs=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Us=Ge("chevron-right",qs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hs=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Ws=Ge("chevron-up",Hs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ys=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],Qs=Ge("panel-left",Ys);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xs=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]],Js=Ge("shield-alert",Xs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zs=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],jr=Ge("x",Zs);function ei({className:e,...t}){return l.jsx(eo,{"data-slot":"label",className:$("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}function ti({...e}){return l.jsx(to,{"data-slot":"select",...e})}function Vt({...e}){return l.jsx(co,{"data-slot":"select-group",...e})}function ri({...e}){return l.jsx(ao,{"data-slot":"select-value",...e})}function ni({className:e,size:t="default",children:r,clearable:n,onClear:a,...o}){return l.jsxs(ro,{"data-slot":"select-trigger","data-size":t,className:$("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,children:[r,n&&a&&l.jsx("button",{type:"button",onClick:s=>{s.stopPropagation(),a()},className:"hover:bg-muted rounded-sm p-1 transition-colors",children:l.jsx(jr,{className:"size-3 opacity-50"})}),l.jsx(no,{asChild:!0,children:l.jsx(ra,{className:"size-4 opacity-50"})})]})}function ai({className:e,children:t,position:r="popper",...n}){return l.jsx(oo,{children:l.jsxs(so,{"data-slot":"select-content",className:$("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",r==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[l.jsx(si,{}),l.jsx(io,{className:$("p-1",r==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),l.jsx(ii,{})]})})}function Gt({className:e,...t}){return l.jsx(lo,{"data-slot":"select-label",className:$("text-muted-foreground px-2 py-1.5 text-xs",e),...t})}function qt({className:e,children:t,...r}){return l.jsxs(Pn,{"data-slot":"select-item",className:$("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[l.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:l.jsx(kn,{children:l.jsx(ta,{className:"size-4"})})}),l.jsx(Nn,{children:t})]})}function oi({className:e,...t}){return l.jsx(uo,{"data-slot":"select-separator",className:$("bg-border pointer-events-none -mx-1 my-1 h-px",e),...t})}function si({className:e,...t}){return l.jsx(fo,{"data-slot":"select-scroll-up-button",className:$("flex cursor-default items-center justify-center py-1",e),...t,children:l.jsx(Ws,{className:"size-4"})})}function ii({className:e,...t}){return l.jsx(po,{"data-slot":"select-scroll-down-button",className:$("flex cursor-default items-center justify-center py-1",e),...t,children:l.jsx(ra,{className:"size-4"})})}function Rf({className:e,option:t,description:r,icon:n,clearable:a=!0,...o}){return l.jsxs(Pn,{"data-slot":"select-item",className:$("focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-default select-none items-center justify-between rounded-sm py-1.5 pl-2 pr-8 text-sm outline-hidden data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...o,children:[l.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:a&&l.jsx(kn,{children:l.jsx(ta,{className:"size-4"})})}),l.jsxs("div",{className:"flex flex-col items-start gap-1",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[n&&l.jsx(n,{className:"size-4 shrink-0 opacity-50"}),l.jsx(Nn,{children:t})]}),l.jsx("span",{className:"text-muted-foreground text-xs",children:r})]})]})}var na="ToastProvider",[Sr,ci,li]=ho("Toast"),[aa,Af]=Dn("Toast",[li]),[ui,It]=aa(na),oa=e=>{const{__scopeToast:t,label:r="Notification",duration:n=5e3,swipeDirection:a="right",swipeThreshold:o=50,children:s}=e,[c,i]=R.useState(null),[u,f]=R.useState(0),d=R.useRef(!1),g=R.useRef(!1);return r.trim(),l.jsx(Sr.Provider,{scope:t,children:l.jsx(ui,{scope:t,label:r,duration:n,swipeDirection:a,swipeThreshold:o,toastCount:u,viewport:c,onViewportChange:i,onToastAdd:R.useCallback(()=>f(m=>m+1),[]),onToastRemove:R.useCallback(()=>f(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:g,children:s})})};oa.displayName=na;var sa="ToastViewport",di=["F8"],or="toast.viewportPause",sr="toast.viewportResume",ia=R.forwardRef((e,t)=>{const{__scopeToast:r,hotkey:n=di,label:a="Notifications ({hotkey})",...o}=e,s=It(sa,r),c=ci(r),i=R.useRef(null),u=R.useRef(null),f=R.useRef(null),d=R.useRef(null),g=Rt(t,d,s.onViewportChange),m=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),p=s.toastCount>0;R.useEffect(()=>{const v=w=>{n.length!==0&&n.every(h=>w[h]||w.code===h)&&d.current?.focus()};return document.addEventListener("keydown",v),()=>document.removeEventListener("keydown",v)},[n]),R.useEffect(()=>{const v=i.current,w=d.current;if(p&&v&&w){const y=()=>{if(!s.isClosePausedRef.current){const M=new CustomEvent(or);w.dispatchEvent(M),s.isClosePausedRef.current=!0}},h=()=>{if(s.isClosePausedRef.current){const M=new CustomEvent(sr);w.dispatchEvent(M),s.isClosePausedRef.current=!1}},j=M=>{!v.contains(M.relatedTarget)&&h()},E=()=>{v.contains(document.activeElement)||h()};return v.addEventListener("focusin",y),v.addEventListener("focusout",j),v.addEventListener("pointermove",y),v.addEventListener("pointerleave",E),window.addEventListener("blur",y),window.addEventListener("focus",h),()=>{v.removeEventListener("focusin",y),v.removeEventListener("focusout",j),v.removeEventListener("pointermove",y),v.removeEventListener("pointerleave",E),window.removeEventListener("blur",y),window.removeEventListener("focus",h)}}},[p,s.isClosePausedRef]);const b=R.useCallback(({tabbingDirection:v})=>{const y=c().map(h=>{const j=h.ref.current,E=[j,...Oi(j)];return v==="forwards"?E:E.reverse()});return(v==="forwards"?y.reverse():y).flat()},[c]);return R.useEffect(()=>{const v=d.current;if(v){const w=y=>{const h=y.altKey||y.ctrlKey||y.metaKey;if(y.key==="Tab"&&!h){const E=document.activeElement,M=y.shiftKey;if(y.target===v&&M){u.current?.focus();return}const T=b({tabbingDirection:M?"backwards":"forwards"}),x=T.findIndex(_=>_===E);Ut(T.slice(x+1))?y.preventDefault():M?u.current?.focus():f.current?.focus()}};return v.addEventListener("keydown",w),()=>v.removeEventListener("keydown",w)}},[c,b]),l.jsxs(vo,{ref:i,role:"region","aria-label":a.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:p?void 0:"none"},children:[p&&l.jsx(ir,{ref:u,onFocusFromOutsideViewport:()=>{const v=b({tabbingDirection:"forwards"});Ut(v)}}),l.jsx(Sr.Slot,{scope:r,children:l.jsx(Ae.ol,{tabIndex:-1,...o,ref:g})}),p&&l.jsx(ir,{ref:f,onFocusFromOutsideViewport:()=>{const v=b({tabbingDirection:"backwards"});Ut(v)}})]})});ia.displayName=sa;var ca="ToastFocusProxy",ir=R.forwardRef((e,t)=>{const{__scopeToast:r,onFocusFromOutsideViewport:n,...a}=e,o=It(ca,r);return l.jsx(zn,{"aria-hidden":!0,tabIndex:0,...a,ref:t,style:{position:"fixed"},onFocus:s=>{const c=s.relatedTarget;!o.viewport?.contains(c)&&n()}})});ir.displayName=ca;var ct="Toast",fi="toast.swipeStart",pi="toast.swipeMove",hi="toast.swipeCancel",vi="toast.swipeEnd",la=R.forwardRef((e,t)=>{const{forceMount:r,open:n,defaultOpen:a,onOpenChange:o,...s}=e,[c,i]=Ln({prop:n,defaultProp:a??!0,onChange:o,caller:ct});return l.jsx(yr,{present:r||c,children:l.jsx(bi,{open:c,...s,ref:t,onClose:()=>i(!1),onPause:Ot(e.onPause),onResume:Ot(e.onResume),onSwipeStart:ae(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ae(e.onSwipeMove,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:ae(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ae(e.onSwipeEnd,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),i(!1)})})})});la.displayName=ct;var[mi,gi]=aa(ct,{onClose(){}}),bi=R.forwardRef((e,t)=>{const{__scopeToast:r,type:n="foreground",duration:a,open:o,onClose:s,onEscapeKeyDown:c,onPause:i,onResume:u,onSwipeStart:f,onSwipeMove:d,onSwipeCancel:g,onSwipeEnd:m,...p}=e,b=It(ct,r),[v,w]=R.useState(null),y=Rt(t,S=>w(S)),h=R.useRef(null),j=R.useRef(null),E=a||b.duration,M=R.useRef(0),O=R.useRef(E),C=R.useRef(0),{onToastAdd:T,onToastRemove:x}=b,_=Ot(()=>{v?.contains(document.activeElement)&&b.viewport?.focus(),s()}),P=R.useCallback(S=>{!S||S===1/0||(window.clearTimeout(C.current),M.current=new Date().getTime(),C.current=window.setTimeout(_,S))},[_]);R.useEffect(()=>{const S=b.viewport;if(S){const N=()=>{P(O.current),u?.()},k=()=>{const D=new Date().getTime()-M.current;O.current=O.current-D,window.clearTimeout(C.current),i?.()};return S.addEventListener(or,k),S.addEventListener(sr,N),()=>{S.removeEventListener(or,k),S.removeEventListener(sr,N)}}},[b.viewport,E,i,u,P]),R.useEffect(()=>{o&&!b.isClosePausedRef.current&&P(E)},[o,E,b.isClosePausedRef,P]),R.useEffect(()=>(T(),()=>x()),[T,x]);const A=R.useMemo(()=>v?ha(v):null,[v]);return b.viewport?l.jsxs(l.Fragment,{children:[A&&l.jsx(yi,{__scopeToast:r,role:"status","aria-live":n==="foreground"?"assertive":"polite","aria-atomic":!0,children:A}),l.jsx(mi,{scope:r,onClose:_,children:mo.createPortal(l.jsx(Sr.ItemSlot,{scope:r,children:l.jsx(go,{asChild:!0,onEscapeKeyDown:ae(c,()=>{b.isFocusedToastEscapeKeyDownRef.current||_(),b.isFocusedToastEscapeKeyDownRef.current=!1}),children:l.jsx(Ae.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":o?"open":"closed","data-swipe-direction":b.swipeDirection,...p,ref:y,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ae(e.onKeyDown,S=>{S.key==="Escape"&&(c?.(S.nativeEvent),S.nativeEvent.defaultPrevented||(b.isFocusedToastEscapeKeyDownRef.current=!0,_()))}),onPointerDown:ae(e.onPointerDown,S=>{S.button===0&&(h.current={x:S.clientX,y:S.clientY})}),onPointerMove:ae(e.onPointerMove,S=>{if(!h.current)return;const N=S.clientX-h.current.x,k=S.clientY-h.current.y,D=!!j.current,I=["left","right"].includes(b.swipeDirection),G=["left","up"].includes(b.swipeDirection)?Math.min:Math.max,H=I?G(0,N):0,J=I?0:G(0,k),ee=S.pointerType==="touch"?10:2,q={x:H,y:J},U={originalEvent:S,delta:q};D?(j.current=q,mt(pi,d,U,{discrete:!1})):Fr(q,b.swipeDirection,ee)?(j.current=q,mt(fi,f,U,{discrete:!1}),S.target.setPointerCapture(S.pointerId)):(Math.abs(N)>ee||Math.abs(k)>ee)&&(h.current=null)}),onPointerUp:ae(e.onPointerUp,S=>{const N=j.current,k=S.target;if(k.hasPointerCapture(S.pointerId)&&k.releasePointerCapture(S.pointerId),j.current=null,h.current=null,N){const D=S.currentTarget,I={originalEvent:S,delta:N};Fr(N,b.swipeDirection,b.swipeThreshold)?mt(vi,m,I,{discrete:!0}):mt(hi,g,I,{discrete:!0}),D.addEventListener("click",G=>G.preventDefault(),{once:!0})}})})})}),b.viewport)})]}):null}),yi=e=>{const{__scopeToast:t,children:r,...n}=e,a=It(ct,t),[o,s]=R.useState(!1),[c,i]=R.useState(!1);return Si(()=>s(!0)),R.useEffect(()=>{const u=window.setTimeout(()=>i(!0),1e3);return()=>window.clearTimeout(u)},[]),c?null:l.jsx(Bn,{asChild:!0,children:l.jsx(zn,{...n,children:o&&l.jsxs(l.Fragment,{children:[a.label," ",r]})})})},xi="ToastTitle",ua=R.forwardRef((e,t)=>{const{__scopeToast:r,...n}=e;return l.jsx(Ae.div,{...n,ref:t})});ua.displayName=xi;var wi="ToastDescription",da=R.forwardRef((e,t)=>{const{__scopeToast:r,...n}=e;return l.jsx(Ae.div,{...n,ref:t})});da.displayName=wi;var _i="ToastAction",ji=R.forwardRef((e,t)=>{const{altText:r,...n}=e;return r.trim()?l.jsx(pa,{altText:r,asChild:!0,children:l.jsx(Cr,{...n,ref:t})}):null});ji.displayName=_i;var fa="ToastClose",Cr=R.forwardRef((e,t)=>{const{__scopeToast:r,...n}=e,a=gi(fa,r);return l.jsx(pa,{asChild:!0,children:l.jsx(Ae.button,{type:"button",...n,ref:t,onClick:ae(e.onClick,a.onClose)})})});Cr.displayName=fa;var pa=R.forwardRef((e,t)=>{const{__scopeToast:r,altText:n,...a}=e;return l.jsx(Ae.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...a,ref:t})});function ha(e){const t=[];return Array.from(e.childNodes).forEach(n=>{if(n.nodeType===n.TEXT_NODE&&n.textContent&&t.push(n.textContent),Ci(n)){const a=n.ariaHidden||n.hidden||n.style.display==="none",o=n.dataset.radixToastAnnounceExclude==="";if(!a)if(o){const s=n.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...ha(n))}}),t}function mt(e,t,r,{discrete:n}){const a=r.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),n?yo(a,o):a.dispatchEvent(o)}var Fr=(e,t,r=0)=>{const n=Math.abs(e.x),a=Math.abs(e.y),o=n>a;return t==="left"||t==="right"?o&&n>r:!o&&a>r};function Si(e=()=>{}){const t=Ot(e);bo(()=>{let r=0,n=0;return r=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(r),window.cancelAnimationFrame(n)}},[t])}function Ci(e){return e.nodeType===e.ELEMENT_NODE}function Oi(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const a=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||a?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function Ut(e){const t=document.activeElement;return e.some(r=>r===t?!0:(r.focus(),document.activeElement!==t))}var Ei=oa,Ti=ia,Mi=la,Ri=ua,Ai=da,Ii=Cr;const Pi=Ei;function ki({className:e,...t}){return l.jsx(Ti,{className:$("fixed top-0 right-0 z-50 flex max-h-screen w-full flex-col-reverse p-4 sm:top-auto sm:bottom-0 sm:flex-col md:max-w-[400px]",e),...t})}const Ni=_r("group pointer-events-auto relative flex w-full items-center justify-between overflow-hidden rounded-md border p-4 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:data-[swipe-direction=left]:slide-out-to-left-full data-[state=closed]:data-[swipe-direction=right]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"border-t-4 border-t-destructive border bg-background text-destructive",success:"border-t-4 border-t-green-500 border bg-background text-green-900 dark:text-green-100",error:"border-t-4 border-t-red-500 border bg-background text-red-900 dark:text-red-100",info:"border-t-4 border-t-blue-500 border bg-background text-blue-900 dark:text-blue-100",warning:"border-t-4 border-t-yellow-500 border bg-background text-yellow-900 dark:text-yellow-100"}},defaultVariants:{variant:"default"}});function Di({className:e,variant:t,...r}){return l.jsx(Mi,{className:$(Ni({variant:t}),e),...r})}function Li({className:e,asChild:t=!1,...r}){return l.jsx(Ii,{className:$(!t&&"group focus-visible:border-ring focus-visible:ring-ring/50 absolute top-3 right-3 flex size-7 items-center justify-center rounded transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:pointer-events-none",e),"toast-close":"",asChild:t,...r,children:t?r.children:l.jsx(jr,{size:16,className:"opacity-60 transition-opacity group-hover:opacity-100","aria-hidden":"true"})})}function zi({className:e,...t}){return l.jsx(Ri,{className:$("text-sm font-medium",e),...t})}function Bi({className:e,...t}){return l.jsx(Ai,{className:$("text-muted-foreground text-sm",e),...t})}function va(){const{toasts:e}=ns();return l.jsxs(Pi,{children:[e.map(function({id:t,title:r,description:n,action:a,...o}){return l.jsx(Di,{...o,children:l.jsxs("div",{className:"flex w-full justify-between gap-2",children:[l.jsxs("div",{className:"flex flex-col gap-3",children:[l.jsxs("div",{className:"space-y-1",children:[r&&l.jsx(zi,{children:r}),n&&l.jsx(Bi,{children:n})]}),l.jsx("div",{children:a})]}),l.jsx("div",{children:l.jsx(Li,{})})]})},t)}),l.jsx(ki,{className:"z-[9999]"})]})}const ma=()=>Xa({queryKey:[Yo.GetAppConfig],queryFn:async()=>{const{data:e}=await Qo();return e},staleTime:60*60*1e3}),ga=()=>{const{data:e}=ma();return{can:R.useCallback(r=>!!(e?.auth?.grantedPolicies&&e.auth.grantedPolicies[r]),[e?.auth?.grantedPolicies])}};function $i({...e}){return l.jsx(xo,{"data-slot":"dropdown-menu",...e})}function Fi({...e}){return l.jsx(wo,{"data-slot":"dropdown-menu-trigger",...e})}function Ki({className:e,sideOffset:t=4,...r}){return l.jsx(_o,{children:l.jsx(jo,{"data-slot":"dropdown-menu-content",sideOffset:t,className:$("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function Vi({...e}){return l.jsx(Oo,{"data-slot":"dropdown-menu-group",...e})}function gt({className:e,inset:t,variant:r="default",...n}){return l.jsx(Eo,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:$("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function Gi({className:e,inset:t,...r}){return l.jsx(So,{"data-slot":"dropdown-menu-label","data-inset":t,className:$("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function Kr({className:e,...t}){return l.jsx(Co,{"data-slot":"dropdown-menu-separator",className:$("bg-border -mx-1 my-1 h-px",e),...t})}const qi=new Za({defaultOptions:{queries:{staleTime:10*1e3,refetchOnWindowFocus:!0}}});function Ui({children:e}){return l.jsx(Ja,{client:qi,children:e})}const{useContext:Hi}=Rn,Wi={theme:"system",setTheme:()=>null,resolvedTheme:void 0},ba=R.createContext(Wi);function Yi(){const e=Hi(ba);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e}const{useEffect:Vr,useState:Gr}=Rn;function Qi({children:e,defaultTheme:t="system",storageKey:r="vite-ui-theme",...n}){const[a,o]=Gr(()=>localStorage.getItem(r)||t),[s,c]=Gr(()=>window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light");Vr(()=>{const u=window.document.documentElement;if(u.classList.remove("light","dark"),a==="system"){u.classList.add(s);return}u.classList.add(a)},[a,s]),Vr(()=>{const u=window.matchMedia("(prefers-color-scheme: dark)"),f=()=>{const d=window.document.documentElement;d.classList.remove("light","dark");const g=u.matches?"dark":"light";c(g),a==="system"&&d.classList.add(g)};return u.addEventListener("change",f),()=>u.removeEventListener("change",f)},[a]);const i={theme:a,setTheme:u=>{localStorage.setItem(r,u),o(u)},resolvedTheme:a==="system"?s:a};return l.jsx(ba.Provider,{...n,value:i,children:e})}/*! js-cookie v3.0.5 | MIT */function bt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)e[n]=r[n]}return e}var Xi={read:function(e){return e[0]==='"'&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function cr(e,t){function r(a,o,s){if(!(typeof document>"u")){s=bt({},t,s),typeof s.expires=="number"&&(s.expires=new Date(Date.now()+s.expires*864e5)),s.expires&&(s.expires=s.expires.toUTCString()),a=encodeURIComponent(a).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var c="";for(var i in s)s[i]&&(c+="; "+i,s[i]!==!0&&(c+="="+s[i].split(";")[0]));return document.cookie=a+"="+e.write(o,a)+c}}function n(a){if(!(typeof document>"u"||arguments.length&&!a)){for(var o=document.cookie?document.cookie.split("; "):[],s={},c=0;c<o.length;c++){var i=o[c].split("="),u=i.slice(1).join("=");try{var f=decodeURIComponent(i[0]);if(s[f]=e.read(u,f),a===f)break}catch{}}return a?s[a]:s}}return Object.create({set:r,get:n,remove:function(a,o){r(a,"",bt({},o,{expires:-1}))},withAttributes:function(a){return cr(this.converter,bt({},this.attributes,a))},withConverter:function(a){return cr(bt({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(t)},converter:{value:Object.freeze(e)}})}var ot=cr(Xi,{path:"/"});const jt="active_theme",Ji="default-scaled";function Zi(e){if(!(typeof window>"u"))try{ot.remove(jt,{path:"/"}),ot.set(jt,e,{path:"/",expires:365,sameSite:"lax"});const t=ot.get(jt)}catch{}}const ya=R.createContext(void 0);function ec({children:e,initialTheme:t}){const[r,n]=R.useState(()=>{if(typeof window<"u"){const a=ot.get(jt);if(a)return a}return t||Ji});return R.useEffect(()=>{Zi(r);const a=document.body;Array.from(a.classList).filter(s=>s.startsWith("theme-")).forEach(s=>a.classList.remove(s)),a.classList.add(`theme-${r}`),r.endsWith("-scaled")&&a.classList.add("theme-scaled")},[r]),l.jsx(ya.Provider,{value:{activeTheme:r,setActiveTheme:n},children:e})}function xa(){const e=R.useContext(ya);if(e===void 0)throw new Error("useThemeConfig must be used within an ActiveThemeProvider");return e}function tc({message:e="You don't have permission to access this page.",showBackButton:t=!0}){return l.jsx("div",{className:"flex flex-col items-center justify-center min-h-[50vh] p-6",children:l.jsxs("div",{className:"flex flex-col items-center text-center space-y-4 max-w-md",children:[l.jsx(Js,{className:"h-12 w-12 text-gray-400"}),l.jsx("h2",{className:"text-2xl font-semibold tracking-tight",children:"Access Restricted"}),l.jsx("p",{className:"text-gray-500",children:e}),t&&l.jsx(At,{onClick:()=>window.history.back(),variant:"outline",className:"mt-4",children:"Go Back"})]})})}function rc({policy:e,children:t,fallback:r,message:n="You don't have permission to access this page."}){const{can:a}=ga();return a(e)?l.jsx(l.Fragment,{children:t}):r?l.jsx(l.Fragment,{children:r}):l.jsx(tc,{message:n})}function nc({...e}){return l.jsx(To,{"data-slot":"collapsible",...e})}function ac({...e}){return l.jsx(Mo,{"data-slot":"collapsible-trigger",...e})}function oc({...e}){return l.jsx(Ro,{"data-slot":"collapsible-content",...e})}const Ht=768;function sc(){const[e,t]=R.useState(void 0);return R.useEffect(()=>{const r=window.matchMedia(`(max-width: ${Ht-1}px)`),n=()=>{t(window.innerWidth<Ht)};return r.addEventListener("change",n),t(window.innerWidth<Ht),()=>r.removeEventListener("change",n)},[]),!!e}var ic="Separator",qr="horizontal",cc=["horizontal","vertical"],wa=R.forwardRef((e,t)=>{const{decorative:r,orientation:n=qr,...a}=e,o=lc(n)?n:qr,c=r?{role:"none"}:{"aria-orientation":o==="vertical"?o:void 0,role:"separator"};return l.jsx(Ae.div,{"data-orientation":o,...c,...a,ref:t})});wa.displayName=ic;function lc(e){return cc.includes(e)}var uc=wa;function dc({className:e,orientation:t="horizontal",decorative:r=!0,...n}){return l.jsx(uc,{"data-slot":"separator",decorative:r,orientation:t,className:$("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...n})}function fc({...e}){return l.jsx(Ao,{"data-slot":"sheet",...e})}function pc({...e}){return l.jsx(Do,{"data-slot":"sheet-portal",...e})}function hc({className:e,...t}){return l.jsx(Lo,{"data-slot":"sheet-overlay",className:$("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function vc({className:e,children:t,side:r="right",...n}){return l.jsxs(pc,{children:[l.jsx(hc,{}),l.jsxs(Io,{"data-slot":"sheet-content",className:$("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",r==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",r==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",r==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",r==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...n,children:[t,l.jsxs(Po,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[l.jsx(jr,{className:"size-4"}),l.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function mc({className:e,...t}){return l.jsx("div",{"data-slot":"sheet-header",className:$("flex flex-col gap-1.5 p-4",e),...t})}function gc({className:e,...t}){return l.jsx(ko,{"data-slot":"sheet-title",className:$("text-foreground font-semibold",e),...t})}function bc({className:e,...t}){return l.jsx(No,{"data-slot":"sheet-description",className:$("text-muted-foreground text-sm",e),...t})}var[Pt,If]=Dn("Tooltip",[$n]),kt=$n(),_a="TooltipProvider",yc=700,lr="tooltip.open",[xc,Or]=Pt(_a),ja=e=>{const{__scopeTooltip:t,delayDuration:r=yc,skipDelayDuration:n=300,disableHoverableContent:a=!1,children:o}=e,s=R.useRef(!0),c=R.useRef(!1),i=R.useRef(0);return R.useEffect(()=>{const u=i.current;return()=>window.clearTimeout(u)},[]),l.jsx(xc,{scope:t,isOpenDelayedRef:s,delayDuration:r,onOpen:R.useCallback(()=>{window.clearTimeout(i.current),s.current=!1},[]),onClose:R.useCallback(()=>{window.clearTimeout(i.current),i.current=window.setTimeout(()=>s.current=!0,n)},[n]),isPointerInTransitRef:c,onPointerInTransitChange:R.useCallback(u=>{c.current=u},[]),disableHoverableContent:a,children:o})};ja.displayName=_a;var st="Tooltip",[wc,lt]=Pt(st),Sa=e=>{const{__scopeTooltip:t,children:r,open:n,defaultOpen:a,onOpenChange:o,disableHoverableContent:s,delayDuration:c}=e,i=Or(st,e.__scopeTooltip),u=kt(t),[f,d]=R.useState(null),g=zo(),m=R.useRef(0),p=s??i.disableHoverableContent,b=c??i.delayDuration,v=R.useRef(!1),[w,y]=Ln({prop:n,defaultProp:a??!1,onChange:O=>{O?(i.onOpen(),document.dispatchEvent(new CustomEvent(lr))):i.onClose(),o?.(O)},caller:st}),h=R.useMemo(()=>w?v.current?"delayed-open":"instant-open":"closed",[w]),j=R.useCallback(()=>{window.clearTimeout(m.current),m.current=0,v.current=!1,y(!0)},[y]),E=R.useCallback(()=>{window.clearTimeout(m.current),m.current=0,y(!1)},[y]),M=R.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{v.current=!0,y(!0),m.current=0},b)},[b,y]);return R.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),l.jsx(Bo,{...u,children:l.jsx(wc,{scope:t,contentId:g,open:w,stateAttribute:h,trigger:f,onTriggerChange:d,onTriggerEnter:R.useCallback(()=>{i.isOpenDelayedRef.current?M():j()},[i.isOpenDelayedRef,M,j]),onTriggerLeave:R.useCallback(()=>{p?E():(window.clearTimeout(m.current),m.current=0)},[E,p]),onOpen:j,onClose:E,disableHoverableContent:p,children:r})})};Sa.displayName=st;var ur="TooltipTrigger",Ca=R.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,a=lt(ur,r),o=Or(ur,r),s=kt(r),c=R.useRef(null),i=Rt(t,c,a.onTriggerChange),u=R.useRef(!1),f=R.useRef(!1),d=R.useCallback(()=>u.current=!1,[]);return R.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),l.jsx($o,{asChild:!0,...s,children:l.jsx(Ae.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...n,ref:i,onPointerMove:ae(e.onPointerMove,g=>{g.pointerType!=="touch"&&!f.current&&!o.isPointerInTransitRef.current&&(a.onTriggerEnter(),f.current=!0)}),onPointerLeave:ae(e.onPointerLeave,()=>{a.onTriggerLeave(),f.current=!1}),onPointerDown:ae(e.onPointerDown,()=>{a.open&&a.onClose(),u.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:ae(e.onFocus,()=>{u.current||a.onOpen()}),onBlur:ae(e.onBlur,a.onClose),onClick:ae(e.onClick,a.onClose)})})});Ca.displayName=ur;var Er="TooltipPortal",[_c,jc]=Pt(Er,{forceMount:void 0}),Oa=e=>{const{__scopeTooltip:t,forceMount:r,children:n,container:a}=e,o=lt(Er,t);return l.jsx(_c,{scope:t,forceMount:r,children:l.jsx(yr,{present:r||o.open,children:l.jsx(Bn,{asChild:!0,container:a,children:n})})})};Oa.displayName=Er;var Ye="TooltipContent",Ea=R.forwardRef((e,t)=>{const r=jc(Ye,e.__scopeTooltip),{forceMount:n=r.forceMount,side:a="top",...o}=e,s=lt(Ye,e.__scopeTooltip);return l.jsx(yr,{present:n||s.open,children:s.disableHoverableContent?l.jsx(Ta,{side:a,...o,ref:t}):l.jsx(Sc,{side:a,...o,ref:t})})}),Sc=R.forwardRef((e,t)=>{const r=lt(Ye,e.__scopeTooltip),n=Or(Ye,e.__scopeTooltip),a=R.useRef(null),o=Rt(t,a),[s,c]=R.useState(null),{trigger:i,onClose:u}=r,f=a.current,{onPointerInTransitChange:d}=n,g=R.useCallback(()=>{c(null),d(!1)},[d]),m=R.useCallback((p,b)=>{const v=p.currentTarget,w={x:p.clientX,y:p.clientY},y=Tc(w,v.getBoundingClientRect()),h=Mc(w,y),j=Rc(b.getBoundingClientRect()),E=Ic([...h,...j]);c(E),d(!0)},[d]);return R.useEffect(()=>()=>g(),[g]),R.useEffect(()=>{if(i&&f){const p=v=>m(v,f),b=v=>m(v,i);return i.addEventListener("pointerleave",p),f.addEventListener("pointerleave",b),()=>{i.removeEventListener("pointerleave",p),f.removeEventListener("pointerleave",b)}}},[i,f,m,g]),R.useEffect(()=>{if(s){const p=b=>{const v=b.target,w={x:b.clientX,y:b.clientY},y=i?.contains(v)||f?.contains(v),h=!Ac(w,s);y?g():h&&(g(),u())};return document.addEventListener("pointermove",p),()=>document.removeEventListener("pointermove",p)}},[i,f,s,u,g]),l.jsx(Ta,{...e,ref:o})}),[Cc,Oc]=Pt(st,{isInside:!1}),Ec=Go("TooltipContent"),Ta=R.forwardRef((e,t)=>{const{__scopeTooltip:r,children:n,"aria-label":a,onEscapeKeyDown:o,onPointerDownOutside:s,...c}=e,i=lt(Ye,r),u=kt(r),{onClose:f}=i;return R.useEffect(()=>(document.addEventListener(lr,f),()=>document.removeEventListener(lr,f)),[f]),R.useEffect(()=>{if(i.trigger){const d=g=>{g.target?.contains(i.trigger)&&f()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[i.trigger,f]),l.jsx(Ko,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:s,onFocusOutside:d=>d.preventDefault(),onDismiss:f,children:l.jsxs(Vo,{"data-state":i.stateAttribute,...u,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[l.jsx(Ec,{children:n}),l.jsx(Cc,{scope:r,isInside:!0,children:l.jsx(qo,{id:i.contentId,role:"tooltip",children:a||n})})]})})});Ea.displayName=Ye;var Ma="TooltipArrow",Ra=R.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,a=kt(r);return Oc(Ma,r).isInside?null:l.jsx(Fo,{...a,...n,ref:t})});Ra.displayName=Ma;function Tc(e,t){const r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(r,n,a,o)){case o:return"left";case a:return"right";case r:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function Mc(e,t,r=5){const n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return n}function Rc(e){const{top:t,right:r,bottom:n,left:a}=e;return[{x:a,y:t},{x:r,y:t},{x:r,y:n},{x:a,y:n}]}function Ac(e,t){const{x:r,y:n}=e;let a=!1;for(let o=0,s=t.length-1;o<t.length;s=o++){const c=t[o],i=t[s],u=c.x,f=c.y,d=i.x,g=i.y;f>n!=g>n&&r<(d-u)*(n-f)/(g-f)+u&&(a=!a)}return a}function Ic(e){const t=e.slice();return t.sort((r,n)=>r.x<n.x?-1:r.x>n.x?1:r.y<n.y?-1:r.y>n.y?1:0),Pc(t)}function Pc(e){if(e.length<=1)return e.slice();const t=[];for(let n=0;n<e.length;n++){const a=e[n];for(;t.length>=2;){const o=t[t.length-1],s=t[t.length-2];if((o.x-s.x)*(a.y-s.y)>=(o.y-s.y)*(a.x-s.x))t.pop();else break}t.push(a)}t.pop();const r=[];for(let n=e.length-1;n>=0;n--){const a=e[n];for(;r.length>=2;){const o=r[r.length-1],s=r[r.length-2];if((o.x-s.x)*(a.y-s.y)>=(o.y-s.y)*(a.x-s.x))r.pop();else break}r.push(a)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var kc=ja,Nc=Sa,Dc=Ca,Lc=Oa,zc=Ea,Bc=Ra;function Aa({delayDuration:e=0,...t}){return l.jsx(kc,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function $c({...e}){return l.jsx(Aa,{children:l.jsx(Nc,{"data-slot":"tooltip",...e})})}function Fc({...e}){return l.jsx(Dc,{"data-slot":"tooltip-trigger",...e})}function Kc({className:e,sideOffset:t=0,children:r,...n}){return l.jsx(Lc,{children:l.jsxs(zc,{"data-slot":"tooltip-content",sideOffset:t,className:$("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...n,children:[r,l.jsx(Bc,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const Vc="sidebar_state",Gc=60*60*24*7,qc="16rem",Uc="18rem",Hc="3rem",Wc="b",Ia=R.createContext(null);function ut(){const e=R.useContext(Ia);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function Yc({defaultOpen:e=!0,open:t,onOpenChange:r,className:n,style:a,children:o,...s}){const c=sc(),[i,u]=R.useState(!1),[f,d]=R.useState(e),g=t??f,m=R.useCallback(w=>{const y=typeof w=="function"?w(g):w;r?r(y):d(y),document.cookie=`${Vc}=${y}; path=/; max-age=${Gc}`},[r,g]),p=R.useCallback(()=>c?u(w=>!w):m(w=>!w),[c,m,u]);R.useEffect(()=>{const w=y=>{y.key===Wc&&(y.metaKey||y.ctrlKey)&&(y.preventDefault(),p())};return window.addEventListener("keydown",w),()=>window.removeEventListener("keydown",w)},[p]);const b=g?"expanded":"collapsed",v=R.useMemo(()=>({state:b,open:g,setOpen:m,isMobile:c,openMobile:i,setOpenMobile:u,toggleSidebar:p}),[b,g,m,c,i,u,p]);return l.jsx(Ia.Provider,{value:v,children:l.jsx(Aa,{delayDuration:0,children:l.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":qc,"--sidebar-width-icon":Hc,...a},className:$("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n),...s,children:o})})})}function Qc({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:n,children:a,...o}){const{isMobile:s,state:c,openMobile:i,setOpenMobile:u}=ut();return r==="none"?l.jsx("div",{"data-slot":"sidebar",className:$("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...o,children:a}):s?l.jsx(fc,{open:i,onOpenChange:u,...o,children:l.jsxs(vc,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":Uc},side:e,children:[l.jsxs(mc,{className:"sr-only",children:[l.jsx(gc,{children:"Sidebar"}),l.jsx(bc,{children:"Displays the mobile sidebar."})]}),l.jsx("div",{className:"flex h-full w-full flex-col",children:a})]})}):l.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?r:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[l.jsx("div",{"data-slot":"sidebar-gap",className:$("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),l.jsx("div",{"data-slot":"sidebar-container",className:$("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...o,children:l.jsx("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:a})})]})}function Xc({className:e,onClick:t,...r}){const{toggleSidebar:n}=ut();return l.jsxs(At,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:$("size-7",e),onClick:a=>{t?.(a),n()},...r,children:[l.jsx(Qs,{}),l.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Jc({className:e,...t}){const{toggleSidebar:r}=ut();return l.jsx("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:$("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})}function Zc({className:e,...t}){return l.jsx("main",{"data-slot":"sidebar-inset",className:$("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}function el({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:$("flex flex-col gap-2 p-2",e),...t})}function tl({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:$("flex flex-col gap-2 p-2",e),...t})}function rl({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:$("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function nl({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:$("relative flex w-full min-w-0 flex-col p-2",e),...t})}function al({className:e,asChild:t=!1,...r}){const n=t?it:"div";return l.jsx(n,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:$("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})}function Pf({className:e,...t}){return l.jsx("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:$("w-full text-sm",e),...t})}function dr({className:e,...t}){return l.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:$("flex w-full min-w-0 flex-col gap-1",e),...t})}function St({className:e,...t}){return l.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:$("group/menu-item relative",e),...t})}const ol=_r("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Ct({asChild:e=!1,isActive:t=!1,variant:r="default",size:n="default",tooltip:a,className:o,...s}){const c=e?it:"button",{isMobile:i,state:u}=ut(),f=l.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n,"data-active":t,className:$(ol({variant:r,size:n}),o),...s});return a?(typeof a=="string"&&(a={children:a}),l.jsxs($c,{children:[l.jsx(Fc,{asChild:!0,children:f}),l.jsx(Kc,{side:"right",align:"center",hidden:u!=="collapsed"||i,...a})]})):f}function sl({className:e,...t}){return l.jsx("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:$("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t})}function il({className:e,...t}){return l.jsx("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:$("group/menu-sub-item relative",e),...t})}function cl({asChild:e=!1,size:t="md",isActive:r=!1,className:n,...a}){const o=e?it:"a";return l.jsx(o,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":t,"data-active":r,className:$("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",n),...a})}/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ll={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=(e,t,r,n)=>{const a=R.forwardRef(({color:o="currentColor",size:s=24,stroke:c=2,title:i,className:u,children:f,...d},g)=>R.createElement("svg",{ref:g,...ll[e],width:s,height:s,className:["tabler-icon",`tabler-icon-${t}`,u].join(" "),strokeWidth:c,stroke:o,...d},[i&&R.createElement("title",{key:"svg-title"},i),...n.map(([m,p])=>R.createElement(m,p)),...Array.isArray(f)?f:[f]]));return a.displayName=`${r}`,a};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ul=oe("outline","app-window","IconAppWindow",[["path",{d:"M3 5m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z",key:"svg-0"}],["path",{d:"M6 8h.01",key:"svg-1"}],["path",{d:"M9 8h.01",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var dl=oe("outline","brightness","IconBrightness",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 3l0 18",key:"svg-1"}],["path",{d:"M12 9l4.65 -4.65",key:"svg-2"}],["path",{d:"M12 14.3l7.37 -7.37",key:"svg-3"}],["path",{d:"M12 19.6l8.85 -8.85",key:"svg-4"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var fl=oe("outline","chevron-right","IconChevronRight",[["path",{d:"M9 6l6 6l-6 6",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var pl=oe("outline","credit-card","IconCreditCard",[["path",{d:"M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M7 15l.01 0",key:"svg-2"}],["path",{d:"M11 15l2 0",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var hl=oe("outline","dashboard","IconDashboard",[["path",{d:"M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M13.45 11.55l2.05 -2.05",key:"svg-1"}],["path",{d:"M6.4 20a9 9 0 1 1 11.2 0z",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var vl=oe("outline","database","IconDatabase",[["path",{d:"M12 6m-8 0a8 3 0 1 0 16 0a8 3 0 1 0 -16 0",key:"svg-0"}],["path",{d:"M4 6v6a8 3 0 0 0 16 0v-6",key:"svg-1"}],["path",{d:"M4 12v6a8 3 0 0 0 16 0v-6",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ml=oe("outline","dots-vertical","IconDotsVertical",[["path",{d:"M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M12 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-1"}],["path",{d:"M12 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var gl=oe("outline","inner-shadow-top","IconInnerShadowTop",[["path",{d:"M5.636 5.636a9 9 0 1 0 12.728 12.728a9 9 0 0 0 -12.728 -12.728z",key:"svg-0"}],["path",{d:"M16.243 7.757a6 6 0 0 0 -8.486 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var bl=oe("outline","key","IconKey",[["path",{d:"M16.555 3.843l3.602 3.602a2.877 2.877 0 0 1 0 4.069l-2.643 2.643a2.877 2.877 0 0 1 -4.069 0l-.301 -.301l-6.558 6.558a2 2 0 0 1 -1.239 .578l-.175 .008h-1.172a1 1 0 0 1 -.993 -.883l-.007 -.117v-1.172a2 2 0 0 1 .467 -1.284l.119 -.13l.414 -.414h2v-2h2v-2l2.144 -2.144l-.301 -.301a2.877 2.877 0 0 1 0 -4.069l2.643 -2.643a2.877 2.877 0 0 1 4.069 0z",key:"svg-0"}],["path",{d:"M15 9h.01",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var yl=oe("outline","logout","IconLogout",[["path",{d:"M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M9 12h12l-3 -3",key:"svg-1"}],["path",{d:"M18 15l3 -3",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var xl=oe("outline","notification","IconNotification",[["path",{d:"M10 6h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3",key:"svg-0"}],["path",{d:"M17 7m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var wl=oe("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var _l=oe("outline","settings","IconSettings",[["path",{d:"M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var jl=oe("outline","user-circle","IconUserCircle",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 10m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}],["path",{d:"M6.168 18.849a4 4 0 0 1 3.832 -2.849h4a4 4 0 0 1 3.834 2.855",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Sl=oe("outline","user","IconUser",[["path",{d:"M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0",key:"svg-0"}],["path",{d:"M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Cl=oe("outline","users-group","IconUsersGroup",[["path",{d:"M10 13a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-0"}],["path",{d:"M8 21v-1a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v1",key:"svg-1"}],["path",{d:"M15 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-2"}],["path",{d:"M17 10h2a2 2 0 0 1 2 2v1",key:"svg-3"}],["path",{d:"M5 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-4"}],["path",{d:"M3 13v-1a2 2 0 0 1 2 -2h2",key:"svg-5"}]]);const Ol={name:"Identity Provider"},El=[{title:"Dashboard",url:"/admin",isActive:!1,icon:hl},{title:"Claim Types",url:"/admin/claims",isActive:!1,icon:bl,permission:"IdentityServer.ClaimTypes"},{title:"Users",url:"/admin/users",isActive:!1,icon:Sl,permission:"AbpIdentity.Users"},{title:"Roles",url:"/admin/users/roles",isActive:!1,icon:Cl,permission:"AbpIdentity.Roles"},{title:"Clients",url:"#",isActive:!1,icon:ul,permission:"IdentityServer.OpenIddictApplications",items:[{title:"Clients",url:"/admin/clients",permission:"IdentityServer.OpenIddictApplications"},{title:"Resources",url:"/admin/clients/resources",permission:"IdentityServer.OpenIddictResources"},{title:"Scopes",url:"/admin/clients/scopes",permission:"IdentityServer.OpenIddictScopes"}]},{title:"Tenants",url:"/admin/tenants",isActive:!1,icon:vl,permission:"AbpTenantManagement.Tenants"},{title:"Settings",url:"/admin/settings",isActive:!1,icon:_l,permission:"SettingManagement.Emailing"}];function Tl(){const[e,t]=R.useState(!1);return R.useEffect(()=>{const r=window.matchMedia("(max-width: 768px)");t(r.matches);const n=a=>{t(a.matches)};return r.addEventListener("change",n),()=>r.removeEventListener("change",n)},[]),{isOpen:e}}const Ml=()=>{const{data:e}=ma();return e?.currentUser};function Ur({className:e,...t}){return l.jsx(Uo,{"data-slot":"avatar",className:$("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function Hr({className:e,...t}){return l.jsx(Ho,{"data-slot":"avatar-fallback",className:$("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function Rl({user:e}){const{isMobile:t}=ut(),r=a=>a?a.slice(0,2).toUpperCase():"",n=async()=>{try{await Xo(),window.location.reload()}catch{}};return l.jsx(dr,{children:l.jsx(St,{children:l.jsxs($i,{children:[l.jsx(Fi,{asChild:!0,children:l.jsxs(Ct,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[l.jsx(Ur,{className:"h-8 w-8 rounded-lg grayscale",children:l.jsx(Hr,{className:"rounded-lg",children:r(e?.name??"")})}),l.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[l.jsx("span",{className:"truncate font-medium",children:e.name}),l.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]}),l.jsx(ml,{className:"ml-auto size-4"})]})}),l.jsxs(Ki,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:t?"bottom":"right",align:"end",sideOffset:4,children:[l.jsx(Gi,{className:"p-0 font-normal",children:l.jsxs("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[l.jsx(Ur,{className:"h-8 w-8 rounded-lg",children:l.jsx(Hr,{className:"rounded-lg",children:r(e?.name??"")})}),l.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[l.jsx("span",{className:"truncate font-medium",children:e.name}),l.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]})]})}),l.jsx(Kr,{}),l.jsxs(Vi,{children:[l.jsxs(gt,{children:[l.jsx(jl,{}),"Account"]}),l.jsxs(gt,{children:[l.jsx(pl,{}),"Billing"]}),l.jsxs(gt,{children:[l.jsx(xl,{}),"Notifications"]})]}),l.jsx(Kr,{}),l.jsxs(gt,{onClick:n,children:[l.jsx(yl,{}),"Log out"]})]})]})})})}function Al(){const{can:e}=ga(),t=Ml(),{url:r}=Fn(),n=r,{isOpen:a}=Tl();return R.useEffect(()=>{},[a]),l.jsxs(Qc,{collapsible:"icon",children:[l.jsx(el,{children:l.jsx(dr,{children:l.jsx(St,{children:l.jsx(Ct,{asChild:!0,className:"data-[slot=sidebar-menu-button]:!p-1.5",children:l.jsxs("a",{href:"#",children:[l.jsx(gl,{className:"!size-5"}),l.jsx("span",{className:"text-base font-semibold",children:Ol.name})]})})})})}),l.jsx(rl,{className:"overflow-x-hidden",children:l.jsxs(nl,{children:[l.jsx(al,{children:"Overview"}),l.jsx(dr,{children:El.map(o=>{const s=o.items?.some(c=>n.startsWith(c.url));return o?.items&&o?.items?.length>0&&(!o.permission||e(o.permission))?l.jsx(nc,{asChild:!0,defaultOpen:s||o.isActive,className:"group/collapsible",children:l.jsxs(St,{children:[l.jsx(ac,{asChild:!0,children:l.jsxs(Ct,{tooltip:o.title,isActive:n===o.url,children:[o.icon&&l.jsx(o.icon,{}),l.jsx("span",{children:o.title}),l.jsx(fl,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),l.jsx(oc,{children:l.jsx(sl,{children:o.items?.map(c=>l.jsx(il,{children:l.jsx(cl,{asChild:!0,isActive:n===c.url,children:l.jsx(Et,{href:c.url,children:l.jsx("span",{children:c.title})})})},c.title))})})]})},o.title):(!o.permission||e(o.permission))&&l.jsx(St,{children:l.jsx(Ct,{asChild:!0,tooltip:o.title,isActive:n===o.url,children:l.jsxs(Et,{href:o.url,children:[l.jsx(o.icon,{}),l.jsx("span",{children:o.title})]})})},o.title)})})]})}),l.jsx(tl,{children:t&&l.jsx(Rl,{user:t})}),l.jsx(Jc,{})]})}function Il({...e}){return l.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function Pl({className:e,...t}){return l.jsx("ol",{"data-slot":"breadcrumb-list",className:$("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function Wr({className:e,...t}){return l.jsx("li",{"data-slot":"breadcrumb-item",className:$("inline-flex items-center gap-1.5",e),...t})}function Yr({asChild:e,className:t,...r}){const n=e?it:"a";return l.jsx(n,{"data-slot":"breadcrumb-link",className:$("hover:text-foreground transition-colors",t),...r})}function kl({className:e,...t}){return l.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:$("text-foreground font-normal",e),...t})}function Qr({children:e,className:t,...r}){return l.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:$("[&>svg]:size-3.5",t),...r,children:e??l.jsx(Us,{})})}function Nl(){const{url:e}=Fn();if(e==="/")return null;const t=e.split("/").filter(Boolean),r=n=>n.split("-").map(a=>a.charAt(0).toUpperCase()+a.slice(1)).join(" ");return l.jsx(Il,{children:l.jsxs(Pl,{children:[l.jsx(Wr,{children:l.jsx(Yr,{asChild:!0,children:l.jsx(Et,{href:"/",children:"Home"})})}),l.jsx(Qr,{}),t.map((n,a)=>{const o=`/${t.slice(0,a+1).join("/")}`,s=a===t.length-1;return l.jsxs(R.Fragment,{children:[l.jsx(Wr,{children:s?l.jsx(kl,{children:r(n)}):l.jsx(Yr,{asChild:!0,children:l.jsx(Et,{href:o,children:r(n)})})}),!s&&l.jsx(Qr,{})]},o)})]})})}var ze={},V={},Xr;function qe(){if(Xr)return V;Xr=1;var e=V&&V.__assign||function(){return e=Object.assign||function(y){for(var h,j=1,E=arguments.length;j<E;j++){h=arguments[j];for(var M in h)Object.prototype.hasOwnProperty.call(h,M)&&(y[M]=h[M])}return y},e.apply(this,arguments)},t=V&&V.__createBinding||(Object.create?function(y,h,j,E){E===void 0&&(E=j),Object.defineProperty(y,E,{enumerable:!0,get:function(){return h[j]}})}:function(y,h,j,E){E===void 0&&(E=j),y[E]=h[j]}),r=V&&V.__setModuleDefault||(Object.create?function(y,h){Object.defineProperty(y,"default",{enumerable:!0,value:h})}:function(y,h){y.default=h}),n=V&&V.__importStar||function(y){if(y&&y.__esModule)return y;var h={};if(y!=null)for(var j in y)j!=="default"&&Object.prototype.hasOwnProperty.call(y,j)&&t(h,y,j);return r(h,y),h},a=V&&V.__spreadArray||function(y,h,j){if(j||arguments.length===2)for(var E=0,M=h.length,O;E<M;E++)(O||!(E in h))&&(O||(O=Array.prototype.slice.call(h,0,E)),O[E]=h[E]);return y.concat(O||Array.prototype.slice.call(h))};Object.defineProperty(V,"__esModule",{value:!0}),V.Priority=V.isModKey=V.shouldRejectKeystrokes=V.useThrottledValue=V.getScrollbarWidth=V.useIsomorphicLayout=V.noop=V.createAction=V.randomId=V.usePointerMovedSinceMount=V.useOuterClick=V.swallowEvent=void 0;var o=n(ne());function s(y){y.stopPropagation(),y.preventDefault()}V.swallowEvent=s;function c(y,h){var j=o.useRef(h);j.current=h,o.useEffect(function(){function E(M){var O,C;!((O=y.current)===null||O===void 0)&&O.contains(M.target)||M.target===((C=y.current)===null||C===void 0?void 0:C.getRootNode().host)||(M.preventDefault(),M.stopPropagation(),j.current())}return window.addEventListener("pointerdown",E,!0),function(){return window.removeEventListener("pointerdown",E,!0)}},[y])}V.useOuterClick=c;function i(){var y=o.useState(!1),h=y[0],j=y[1];return o.useEffect(function(){function E(){j(!0)}if(!h)return window.addEventListener("pointermove",E),function(){return window.removeEventListener("pointermove",E)}},[h]),h}V.usePointerMovedSinceMount=i;function u(){return Math.random().toString(36).substring(2,9)}V.randomId=u;function f(y){return e({id:u()},y)}V.createAction=f;function d(){}V.noop=d,V.useIsomorphicLayout=typeof window>"u"?d:o.useLayoutEffect;function g(){var y=document.createElement("div");y.style.visibility="hidden",y.style.overflow="scroll",document.body.appendChild(y);var h=document.createElement("div");y.appendChild(h);var j=y.offsetWidth-h.offsetWidth;return y.parentNode.removeChild(y),j}V.getScrollbarWidth=g;function m(y,h){h===void 0&&(h=100);var j=o.useState(y),E=j[0],M=j[1],O=o.useRef(Date.now());return o.useEffect(function(){if(h!==0){var C=setTimeout(function(){M(y),O.current=Date.now()},O.current-(Date.now()-h));return function(){clearTimeout(C)}}},[h,y]),h===0?y:E}V.useThrottledValue=m;function p(y){var h,j,E,M=y===void 0?{ignoreWhenFocused:[]}:y,O=M.ignoreWhenFocused,C=a(["input","textarea"],O,!0).map(function(_){return _.toLowerCase()}),T=document.activeElement,x=T&&(C.indexOf(T.tagName.toLowerCase())!==-1||((h=T.attributes.getNamedItem("role"))===null||h===void 0?void 0:h.value)==="textbox"||((j=T.attributes.getNamedItem("contenteditable"))===null||j===void 0?void 0:j.value)==="true"||((E=T.attributes.getNamedItem("contenteditable"))===null||E===void 0?void 0:E.value)==="plaintext-only");return x}V.shouldRejectKeystrokes=p;var b=typeof window>"u",v=!b&&window.navigator.platform==="MacIntel";function w(y){return v?y.metaKey:y.ctrlKey}return V.isModKey=w,V.Priority={HIGH:1,NORMAL:0,LOW:-1},V}var me={},le={},Se={},ie={},nt={exports:{}},Dl=nt.exports,Jr;function Ll(){return Jr||(Jr=1,function(e,t){(function(r,n){n(t)})(Dl,function(r){var n=typeof WeakSet=="function",a=Object.keys;function o(x,_){return x===_||x!==x&&_!==_}function s(x){return x.constructor===Object||x.constructor==null}function c(x){return!!x&&typeof x.then=="function"}function i(x){return!!(x&&x.$$typeof)}function u(){var x=[];return{add:function(_){x.push(_)},has:function(_){return x.indexOf(_)!==-1}}}var f=function(x){return x?function(){return new WeakSet}:u}(n);function d(x){return function(P){var A=x||P;return function(N,k,D){D===void 0&&(D=f());var I=!!N&&typeof N=="object",G=!!k&&typeof k=="object";if(I||G){var H=I&&D.has(N),J=G&&D.has(k);if(H||J)return H&&J;I&&D.add(N),G&&D.add(k)}return A(N,k,D)}}}function g(x,_,P,A){var S=x.length;if(_.length!==S)return!1;for(;S-- >0;)if(!P(x[S],_[S],A))return!1;return!0}function m(x,_,P,A){var S=x.size===_.size;if(S&&x.size){var N={};x.forEach(function(k,D){if(S){var I=!1,G=0;_.forEach(function(H,J){!I&&!N[G]&&(I=P(D,J,A)&&P(k,H,A),I&&(N[G]=!0)),G++}),S=I}})}return S}var p="_owner",b=Function.prototype.bind.call(Function.prototype.call,Object.prototype.hasOwnProperty);function v(x,_,P,A){var S=a(x),N=S.length;if(a(_).length!==N)return!1;if(N)for(var k=void 0;N-- >0;){if(k=S[N],k===p){var D=i(x),I=i(_);if((D||I)&&D!==I)return!1}if(!b(_,k)||!P(x[k],_[k],A))return!1}return!0}function w(x,_){return x.source===_.source&&x.global===_.global&&x.ignoreCase===_.ignoreCase&&x.multiline===_.multiline&&x.unicode===_.unicode&&x.sticky===_.sticky&&x.lastIndex===_.lastIndex}function y(x,_,P,A){var S=x.size===_.size;if(S&&x.size){var N={};x.forEach(function(k){if(S){var D=!1,I=0;_.forEach(function(G){!D&&!N[I]&&(D=P(k,G,A),D&&(N[I]=!0)),I++}),S=D}})}return S}var h=typeof Map=="function",j=typeof Set=="function";function E(x){var _=typeof x=="function"?x(P):P;function P(A,S,N){if(A===S)return!0;if(A&&S&&typeof A=="object"&&typeof S=="object"){if(s(A)&&s(S))return v(A,S,_,N);var k=Array.isArray(A),D=Array.isArray(S);return k||D?k===D&&g(A,S,_,N):(k=A instanceof Date,D=S instanceof Date,k||D?k===D&&o(A.getTime(),S.getTime()):(k=A instanceof RegExp,D=S instanceof RegExp,k||D?k===D&&w(A,S):c(A)||c(S)?A===S:h&&(k=A instanceof Map,D=S instanceof Map,k||D)?k===D&&m(A,S,_,N):j&&(k=A instanceof Set,D=S instanceof Set,k||D)?k===D&&y(A,S,_,N):v(A,S,_,N)))}return A!==A&&S!==S}return P}var M=E(),O=E(function(){return o}),C=E(d()),T=E(d(o));r.circularDeepEqual=C,r.circularShallowEqual=T,r.createCustomEqual=E,r.deepEqual=M,r.sameValueZeroEqual=o,r.shallowEqual=O,Object.defineProperty(r,"__esModule",{value:!0})})}(nt,nt.exports)),nt.exports}var Wt,Zr;function Tr(){if(Zr)return Wt;Zr=1;var e="Invariant failed";function t(r,n){if(!r)throw new Error(e)}return Wt=t,Wt}var Ce={},Be={},rt={},en;function zl(){if(en)return rt;en=1,Object.defineProperty(rt,"__esModule",{value:!0}),rt.Command=void 0;var e=function(){function t(r,n){var a=this;n===void 0&&(n={}),this.perform=function(){var o=r.perform();if(typeof o=="function"){var s=n.history;s&&(a.historyItem&&s.remove(a.historyItem),a.historyItem=s.add({perform:r.perform,negate:o}),a.history={undo:function(){return s.undo(a.historyItem)},redo:function(){return s.redo(a.historyItem)}})}}}return t}();return rt.Command=e,rt}var tn;function Pa(){if(tn)return Be;tn=1;var e=Be&&Be.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(Be,"__esModule",{value:!0}),Be.ActionImpl=void 0;var t=e(Tr()),r=zl(),n=qe(),a=function(s){var c=s.keywords,i=c===void 0?"":c,u=s.section,f=u===void 0?"":u;return(i+" "+(typeof f=="string"?f:f.name)).trim()},o=function(){function s(c,i){var u=this,f;this.priority=n.Priority.NORMAL,this.ancestors=[],this.children=[],Object.assign(this,c),this.id=c.id,this.name=c.name,this.keywords=a(c);var d=c.perform;if(this.command=d&&new r.Command({perform:function(){return d(u)}},{history:i.history}),this.perform=(f=this.command)===null||f===void 0?void 0:f.perform,c.parent){var g=i.store[c.parent];(0,t.default)(g,"attempted to create an action whos parent: "+c.parent+" does not exist in the store."),g.addChild(this)}}return s.prototype.addChild=function(c){c.ancestors.unshift(this);for(var i=this.parentActionImpl;i;)c.ancestors.unshift(i),i=i.parentActionImpl;this.children.push(c)},s.prototype.removeChild=function(c){var i=this,u=this.children.indexOf(c);u!==-1&&this.children.splice(u,1),c.children&&c.children.forEach(function(f){i.removeChild(f)})},Object.defineProperty(s.prototype,"parentActionImpl",{get:function(){return this.ancestors[this.ancestors.length-1]},enumerable:!1,configurable:!0}),s.create=function(c,i){return new s(c,i)},s}();return Be.ActionImpl=o,Be}var rn;function ka(){if(rn)return Ce;rn=1;var e=Ce&&Ce.__assign||function(){return e=Object.assign||function(o){for(var s,c=1,i=arguments.length;c<i;c++){s=arguments[c];for(var u in s)Object.prototype.hasOwnProperty.call(s,u)&&(o[u]=s[u])}return o},e.apply(this,arguments)},t=Ce&&Ce.__importDefault||function(o){return o&&o.__esModule?o:{default:o}};Object.defineProperty(Ce,"__esModule",{value:!0}),Ce.ActionInterface=void 0;var r=t(Tr()),n=Pa(),a=function(){function o(s,c){s===void 0&&(s=[]),c===void 0&&(c={}),this.actions={},this.options=c,this.add(s)}return o.prototype.add=function(s){for(var c=0;c<s.length;c++){var i=s[c];i.parent&&(0,r.default)(this.actions[i.parent],'Attempted to create action "'+i.name+'" without registering its parent "'+i.parent+'" first.'),this.actions[i.id]=n.ActionImpl.create(i,{history:this.options.historyManager,store:this.actions})}return e({},this.actions)},o.prototype.remove=function(s){var c=this;return s.forEach(function(i){var u=c.actions[i.id];if(u){for(var f=u.children;f.length;){var d=f.pop();if(!d)return;delete c.actions[d.id],d.parentActionImpl&&d.parentActionImpl.removeChild(d),d.children&&f.push.apply(f,d.children)}u.parentActionImpl&&u.parentActionImpl.removeChild(u),delete c.actions[i.id]}}),e({},this.actions)},o}();return Ce.ActionInterface=a,Ce}var $e={},nn;function Bl(){if(nn)return $e;nn=1,Object.defineProperty($e,"__esModule",{value:!0}),$e.history=$e.HistoryItemImpl=void 0;var e=qe(),t=function(){function a(o){this.perform=o.perform,this.negate=o.negate}return a.create=function(o){return new a(o)},a}();$e.HistoryItemImpl=t;var r=function(){function a(){return this.undoStack=[],this.redoStack=[],a.instance||(a.instance=this,this.init()),a.instance}return a.prototype.init=function(){var o=this;typeof window>"u"||window.addEventListener("keydown",function(s){var c;if(!(!o.redoStack.length&&!o.undoStack.length||(0,e.shouldRejectKeystrokes)())){var i=(c=s.key)===null||c===void 0?void 0:c.toLowerCase();s.metaKey&&i==="z"&&s.shiftKey?o.redo():s.metaKey&&i==="z"&&o.undo()}})},a.prototype.add=function(o){var s=t.create(o);return this.undoStack.push(s),s},a.prototype.remove=function(o){var s=this.undoStack.findIndex(function(i){return i===o});if(s!==-1){this.undoStack.splice(s,1);return}var c=this.redoStack.findIndex(function(i){return i===o});c!==-1&&this.redoStack.splice(c,1)},a.prototype.undo=function(o){if(!o){var s=this.undoStack.pop();return s?(s?.negate(),this.redoStack.push(s),s):void 0}var c=this.undoStack.findIndex(function(i){return i===o});if(c!==-1)return this.undoStack.splice(c,1),o.negate(),this.redoStack.push(o),o},a.prototype.redo=function(o){if(!o){var s=this.redoStack.pop();return s?(s?.perform(),this.undoStack.push(s),s):void 0}var c=this.redoStack.findIndex(function(i){return i===o});if(c!==-1)return this.redoStack.splice(c,1),o.perform(),this.undoStack.push(o),o},a.prototype.reset=function(){this.undoStack.splice(0),this.redoStack.splice(0)},a}(),n=new r;return $e.history=n,Object.freeze(n),$e}var Yt={},an;function Je(){return an||(an=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.VisualState=void 0,function(t){t.animatingIn="animating-in",t.showing="showing",t.animatingOut="animating-out",t.hidden="hidden"}(e.VisualState||(e.VisualState={}))}(Yt)),Yt}var on;function $l(){if(on)return ie;on=1;var e=ie&&ie.__assign||function(){return e=Object.assign||function(p){for(var b,v=1,w=arguments.length;v<w;v++){b=arguments[v];for(var y in b)Object.prototype.hasOwnProperty.call(b,y)&&(p[y]=b[y])}return p},e.apply(this,arguments)},t=ie&&ie.__createBinding||(Object.create?function(p,b,v,w){w===void 0&&(w=v),Object.defineProperty(p,w,{enumerable:!0,get:function(){return b[v]}})}:function(p,b,v,w){w===void 0&&(w=v),p[w]=b[v]}),r=ie&&ie.__setModuleDefault||(Object.create?function(p,b){Object.defineProperty(p,"default",{enumerable:!0,value:b})}:function(p,b){p.default=b}),n=ie&&ie.__importStar||function(p){if(p&&p.__esModule)return p;var b={};if(p!=null)for(var v in p)v!=="default"&&Object.prototype.hasOwnProperty.call(p,v)&&t(b,p,v);return r(b,p),b},a=ie&&ie.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(ie,"__esModule",{value:!0}),ie.useStore=void 0;var o=Ll(),s=n(ne()),c=a(Tr()),i=ka(),u=Bl(),f=Je();function d(p){var b=s.useRef(e({animations:{enterMs:200,exitMs:100}},p.options)),v=s.useMemo(function(){return new i.ActionInterface(p.actions||[],{historyManager:b.current.enableHistory?u.history:void 0})},[]),w=s.useState({searchQuery:"",currentRootActionId:null,visualState:f.VisualState.hidden,actions:e({},v.actions),activeIndex:0,disabled:!1}),y=w[0],h=w[1],j=s.useRef(y);j.current=y;var E=s.useCallback(function(){return j.current},[]),M=s.useMemo(function(){return new g(E)},[E]);s.useEffect(function(){j.current=y,M.notify()},[y,M]);var O=s.useCallback(function(T){return h(function(x){return e(e({},x),{actions:v.add(T)})}),function(){h(function(_){return e(e({},_),{actions:v.remove(T)})})}},[v]),C=s.useRef(null);return s.useMemo(function(){var T={setCurrentRootAction:function(x){h(function(_){return e(e({},_),{currentRootActionId:x})})},setVisualState:function(x){h(function(_){return e(e({},_),{visualState:typeof x=="function"?x(_.visualState):x})})},setSearch:function(x){return h(function(_){return e(e({},_),{searchQuery:x})})},registerActions:O,toggle:function(){return h(function(x){return e(e({},x),{visualState:[f.VisualState.animatingOut,f.VisualState.hidden].includes(x.visualState)?f.VisualState.animatingIn:f.VisualState.animatingOut})})},setActiveIndex:function(x){return h(function(_){return e(e({},_),{activeIndex:typeof x=="number"?x:x(_.activeIndex)})})},inputRefSetter:function(x){C.current=x},getInput:function(){return(0,c.default)(C.current,"Input ref is undefined, make sure you attach `query.inputRefSetter` to your search input."),C.current},disable:function(x){h(function(_){return e(e({},_),{disabled:x})})}};return{getState:E,query:T,options:b.current,subscribe:function(x,_){return M.subscribe(x,_)}}},[E,M,O])}ie.useStore=d;var g=function(){function p(b){this.subscribers=[],this.getState=b}return p.prototype.subscribe=function(b,v){var w=this,y=new m(function(){return b(w.getState())},v);return this.subscribers.push(y),this.unsubscribe.bind(this,y)},p.prototype.unsubscribe=function(b){if(this.subscribers.length){var v=this.subscribers.indexOf(b);if(v>-1)return this.subscribers.splice(v,1)}},p.prototype.notify=function(){this.subscribers.forEach(function(b){return b.collect()})},p}(),m=function(){function p(b,v){this.collector=b,this.onChange=v}return p.prototype.collect=function(){try{var b=this.collector();(0,o.deepEqual)(b,this.collected)||(this.collected=b,this.onChange&&this.onChange(this.collected))}catch{}},p}();return ie}var ue={},yt={},sn;function Fl(){if(sn)return yt;sn=1,Object.defineProperty(yt,"__esModule",{value:!0});var e=["Shift","Meta","Alt","Control"],t=1e3,r="keydown",n=typeof navigator=="object"&&/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"Meta":"Control";function a(i,u){return typeof i.getModifierState=="function"?i.getModifierState(u):!1}function o(i){return i.trim().split(" ").map(function(u){var f=u.split(/\b\+/),d=f.pop();return f=f.map(function(g){return g==="$mod"?n:g}),[f,d]})}function s(i,u){return/^[^A-Za-z0-9]$/.test(i.key)&&u[1]===i.key?!0:!(u[1].toUpperCase()!==i.key.toUpperCase()&&u[1]!==i.code||u[0].find(function(f){return!a(i,f)})||e.find(function(f){return!u[0].includes(f)&&u[1]!==f&&a(i,f)}))}function c(i,u,f){var d,g;f===void 0&&(f={});var m=(d=f.timeout)!==null&&d!==void 0?d:t,p=(g=f.event)!==null&&g!==void 0?g:r,b=Object.keys(u).map(function(h){return[o(h),u[h]]}),v=new Map,w=null,y=function(h){h instanceof KeyboardEvent&&(b.forEach(function(j){var E=j[0],M=j[1],O=v.get(E),C=O||E,T=C[0],x=s(h,T);x?C.length>1?v.set(E,C.slice(1)):(v.delete(E),M(h)):a(h,h.key)||v.delete(E)}),w&&clearTimeout(w),w=setTimeout(v.clear.bind(v),m))};return i.addEventListener(p,y),function(){i.removeEventListener(p,y)}}return yt.default=c,yt}var cn;function Kl(){if(cn)return ue;cn=1;var e=ue&&ue.__createBinding||(Object.create?function(v,w,y,h){h===void 0&&(h=y),Object.defineProperty(v,h,{enumerable:!0,get:function(){return w[y]}})}:function(v,w,y,h){h===void 0&&(h=y),v[h]=w[y]}),t=ue&&ue.__setModuleDefault||(Object.create?function(v,w){Object.defineProperty(v,"default",{enumerable:!0,value:w})}:function(v,w){v.default=w}),r=ue&&ue.__importStar||function(v){if(v&&v.__esModule)return v;var w={};if(v!=null)for(var y in v)y!=="default"&&Object.prototype.hasOwnProperty.call(v,y)&&e(w,v,y);return t(w,v),w},n=ue&&ue.__importDefault||function(v){return v&&v.__esModule?v:{default:v}};Object.defineProperty(ue,"__esModule",{value:!0}),ue.InternalEvents=void 0;var a=r(ne()),o=n(Fl()),s=Je(),c=Ie(),i=qe();function u(){return f(),d(),p(),b(),null}ue.InternalEvents=u;function f(){var v,w,y=(0,c.useKBar)(function(x){return{visualState:x.visualState,showing:x.visualState!==s.VisualState.hidden,disabled:x.disabled}}),h=y.query,j=y.options,E=y.visualState,M=y.showing,O=y.disabled;a.useEffect(function(){var x,_=function(){h.setVisualState(function(S){return S===s.VisualState.hidden||S===s.VisualState.animatingOut?S:s.VisualState.animatingOut})};if(O){_();return}var P=j.toggleShortcut||"$mod+k",A=(0,o.default)(window,(x={},x[P]=function(S){var N,k,D,I;S.defaultPrevented||(S.preventDefault(),h.toggle(),M?(k=(N=j.callbacks)===null||N===void 0?void 0:N.onClose)===null||k===void 0||k.call(N):(I=(D=j.callbacks)===null||D===void 0?void 0:D.onOpen)===null||I===void 0||I.call(D))},x.Escape=function(S){var N,k;M&&(S.stopPropagation(),S.preventDefault(),(k=(N=j.callbacks)===null||N===void 0?void 0:N.onClose)===null||k===void 0||k.call(N)),_()},x));return function(){A()}},[j.callbacks,j.toggleShortcut,h,M,O]);var C=a.useRef(),T=a.useCallback(function(x){var _,P,A=0;x===s.VisualState.animatingIn&&(A=((_=j.animations)===null||_===void 0?void 0:_.enterMs)||0),x===s.VisualState.animatingOut&&(A=((P=j.animations)===null||P===void 0?void 0:P.exitMs)||0),clearTimeout(C.current),C.current=setTimeout(function(){var S=!1;h.setVisualState(function(){var N=x===s.VisualState.animatingIn?s.VisualState.showing:s.VisualState.hidden;return N===s.VisualState.hidden&&(S=!0),N}),S&&h.setCurrentRootAction(null)},A)},[(v=j.animations)===null||v===void 0?void 0:v.enterMs,(w=j.animations)===null||w===void 0?void 0:w.exitMs,h]);a.useEffect(function(){switch(E){case s.VisualState.animatingIn:case s.VisualState.animatingOut:T(E);break}},[T,E])}function d(){var v=(0,c.useKBar)(function(h){return{visualState:h.visualState}}),w=v.visualState,y=v.options;a.useEffect(function(){if(!y.disableDocumentLock)if(w===s.VisualState.animatingIn){if(document.body.style.overflow="hidden",!y.disableScrollbarManagement){var h=(0,i.getScrollbarWidth)(),j=getComputedStyle(document.body)["margin-right"];j&&(h+=Number(j.replace(/\D/g,""))),document.body.style.marginRight=h+"px"}}else w===s.VisualState.hidden&&(document.body.style.removeProperty("overflow"),y.disableScrollbarManagement||document.body.style.removeProperty("margin-right"))},[y.disableDocumentLock,y.disableScrollbarManagement,w])}var g=new WeakSet;function m(v){return function(w){g.has(w)||(v(w),g.add(w))}}function p(){var v=(0,c.useKBar)(function(M){return{actions:M.actions,open:M.visualState===s.VisualState.showing,disabled:M.disabled}}),w=v.actions,y=v.query,h=v.open,j=v.options,E=v.disabled;a.useEffect(function(){var M;if(!(h||E)){for(var O=Object.keys(w).map(function(D){return w[D]}),C=[],T=0,x=O;T<x.length;T++){var _=x[T];!((M=_.shortcut)===null||M===void 0)&&M.length&&C.push(_)}C=C.sort(function(D,I){return I.shortcut.join(" ").length-D.shortcut.join(" ").length});for(var P={},A=function(D){var I=D.shortcut.join(" ");P[I]=m(function(G){var H,J,ee,q,U,se;(0,i.shouldRejectKeystrokes)()||(G.preventDefault(),!((H=D.children)===null||H===void 0)&&H.length?(y.setCurrentRootAction(D.id),y.toggle(),(ee=(J=j.callbacks)===null||J===void 0?void 0:J.onOpen)===null||ee===void 0||ee.call(J)):((q=D.command)===null||q===void 0||q.perform(),(se=(U=j.callbacks)===null||U===void 0?void 0:U.onSelectAction)===null||se===void 0||se.call(U,D)))})},S=0,N=C;S<N.length;S++){var _=N[S];A(_)}var k=(0,o.default)(window,P,{timeout:400});return function(){k()}}},[w,h,j.callbacks,y,E])}function b(){var v=a.useRef(!0),w=(0,c.useKBar)(function(E){return{isShowing:E.visualState===s.VisualState.showing||E.visualState===s.VisualState.animatingIn}}),y=w.isShowing,h=w.query,j=a.useRef(null);a.useEffect(function(){if(v.current){v.current=!1;return}if(y){j.current=document.activeElement;return}var E=document.activeElement;E?.tagName.toLowerCase()==="input"&&E.blur();var M=j.current;M&&M!==E&&M.focus()},[y]),a.useEffect(function(){function E(M){var O=h.getInput();M.target!==O&&O.focus()}if(y)return window.addEventListener("keydown",E),function(){window.removeEventListener("keydown",E)}},[y,h])}return ue}var ln;function Na(){return ln||(ln=1,function(e){var t=Se&&Se.__createBinding||(Object.create?function(i,u,f,d){d===void 0&&(d=f),Object.defineProperty(i,d,{enumerable:!0,get:function(){return u[f]}})}:function(i,u,f,d){d===void 0&&(d=f),i[d]=u[f]}),r=Se&&Se.__setModuleDefault||(Object.create?function(i,u){Object.defineProperty(i,"default",{enumerable:!0,value:u})}:function(i,u){i.default=u}),n=Se&&Se.__importStar||function(i){if(i&&i.__esModule)return i;var u={};if(i!=null)for(var f in i)f!=="default"&&Object.prototype.hasOwnProperty.call(i,f)&&t(u,i,f);return r(u,i),u};Object.defineProperty(e,"__esModule",{value:!0}),e.KBarProvider=e.KBarContext=void 0;var a=$l(),o=n(ne()),s=Kl();e.KBarContext=o.createContext({});var c=function(i){var u=(0,a.useStore)(i);return o.createElement(e.KBarContext.Provider,{value:u},o.createElement(s.InternalEvents,null),i.children)};e.KBarProvider=c}(Se)),Se}var un;function Ie(){if(un)return le;un=1;var e=le&&le.__assign||function(){return e=Object.assign||function(c){for(var i,u=1,f=arguments.length;u<f;u++){i=arguments[u];for(var d in i)Object.prototype.hasOwnProperty.call(i,d)&&(c[d]=i[d])}return c},e.apply(this,arguments)},t=le&&le.__createBinding||(Object.create?function(c,i,u,f){f===void 0&&(f=u),Object.defineProperty(c,f,{enumerable:!0,get:function(){return i[u]}})}:function(c,i,u,f){f===void 0&&(f=u),c[f]=i[u]}),r=le&&le.__setModuleDefault||(Object.create?function(c,i){Object.defineProperty(c,"default",{enumerable:!0,value:i})}:function(c,i){c.default=i}),n=le&&le.__importStar||function(c){if(c&&c.__esModule)return c;var i={};if(c!=null)for(var u in c)u!=="default"&&Object.prototype.hasOwnProperty.call(c,u)&&t(i,c,u);return r(i,c),i};Object.defineProperty(le,"__esModule",{value:!0}),le.useKBar=void 0;var a=n(ne()),o=Na();function s(c){var i=a.useContext(o.KBarContext),u=i.query,f=i.getState,d=i.subscribe,g=i.options,m=a.useRef(c?.(f())),p=a.useRef(c),b=a.useCallback(function(h){return e(e({},h),{query:u,options:g})},[u,g]),v=a.useState(b(m.current)),w=v[0],y=v[1];return a.useEffect(function(){var h;return p.current&&(h=d(function(j){return p.current(j)},function(j){return y(b(j))})),function(){h&&h()}},[b,d]),w}return le.useKBar=s,le}function Oe(e){return Array.isArray?Array.isArray(e):za(e)==="[object Array]"}function Vl(e){if(typeof e=="string")return e;let t=e+"";return t=="0"&&1/e==-1/0?"-0":t}function Gl(e){return e==null?"":Vl(e)}function we(e){return typeof e=="string"}function Da(e){return typeof e=="number"}function ql(e){return e===!0||e===!1||Ul(e)&&za(e)=="[object Boolean]"}function La(e){return typeof e=="object"}function Ul(e){return La(e)&&e!==null}function he(e){return e!=null}function Qt(e){return!e.trim().length}function za(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const Hl="Incorrect 'index' type",Wl=e=>`Invalid value for key ${e}`,Yl=e=>`Pattern length exceeds max of ${e}.`,Ql=e=>`Missing ${e} property in key`,Xl=e=>`Property 'weight' in key '${e}' must be a positive integer`,dn=Object.prototype.hasOwnProperty;class Jl{constructor(t){this._keys=[],this._keyMap={};let r=0;t.forEach(n=>{let a=Ba(n);r+=a.weight,this._keys.push(a),this._keyMap[a.id]=a,r+=a.weight}),this._keys.forEach(n=>{n.weight/=r})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Ba(e){let t=null,r=null,n=null,a=1,o=null;if(we(e)||Oe(e))n=e,t=fn(e),r=fr(e);else{if(!dn.call(e,"name"))throw new Error(Ql("name"));const s=e.name;if(n=s,dn.call(e,"weight")&&(a=e.weight,a<=0))throw new Error(Xl(s));t=fn(s),r=fr(s),o=e.getFn}return{path:t,id:r,weight:a,src:n,getFn:o}}function fn(e){return Oe(e)?e:e.split(".")}function fr(e){return Oe(e)?e.join("."):e}function Zl(e,t){let r=[],n=!1;const a=(o,s,c)=>{if(he(o))if(!s[c])r.push(o);else{let i=s[c];const u=o[i];if(!he(u))return;if(c===s.length-1&&(we(u)||Da(u)||ql(u)))r.push(Gl(u));else if(Oe(u)){n=!0;for(let f=0,d=u.length;f<d;f+=1)a(u[f],s,c+1)}else s.length&&a(u,s,c+1)}};return a(e,we(t)?t.split("."):t,0),n?r:r[0]}const eu={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},tu={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1},ru={location:0,threshold:.6,distance:100},nu={useExtendedSearch:!1,getFn:Zl,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var F={...tu,...eu,...ru,...nu};const au=/[^ ]+/g;function ou(e=1,t=3){const r=new Map,n=Math.pow(10,t);return{get(a){const o=a.match(au).length;if(r.has(o))return r.get(o);const s=1/Math.pow(o,.5*e),c=parseFloat(Math.round(s*n)/n);return r.set(o,c),c},clear(){r.clear()}}}class Mr{constructor({getFn:t=F.getFn,fieldNormWeight:r=F.fieldNormWeight}={}){this.norm=ou(r,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((r,n)=>{this._keysMap[r.id]=n})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,we(this.docs[0])?this.docs.forEach((t,r)=>{this._addString(t,r)}):this.docs.forEach((t,r)=>{this._addObject(t,r)}),this.norm.clear())}add(t){const r=this.size();we(t)?this._addString(t,r):this._addObject(t,r)}removeAt(t){this.records.splice(t,1);for(let r=t,n=this.size();r<n;r+=1)this.records[r].i-=1}getValueForItemAtKeyId(t,r){return t[this._keysMap[r]]}size(){return this.records.length}_addString(t,r){if(!he(t)||Qt(t))return;let n={v:t,i:r,n:this.norm.get(t)};this.records.push(n)}_addObject(t,r){let n={i:r,$:{}};this.keys.forEach((a,o)=>{let s=a.getFn?a.getFn(t):this.getFn(t,a.path);if(he(s)){if(Oe(s)){let c=[];const i=[{nestedArrIndex:-1,value:s}];for(;i.length;){const{nestedArrIndex:u,value:f}=i.pop();if(he(f))if(we(f)&&!Qt(f)){let d={v:f,i:u,n:this.norm.get(f)};c.push(d)}else Oe(f)&&f.forEach((d,g)=>{i.push({nestedArrIndex:g,value:d})})}n.$[o]=c}else if(we(s)&&!Qt(s)){let c={v:s,n:this.norm.get(s)};n.$[o]=c}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function $a(e,t,{getFn:r=F.getFn,fieldNormWeight:n=F.fieldNormWeight}={}){const a=new Mr({getFn:r,fieldNormWeight:n});return a.setKeys(e.map(Ba)),a.setSources(t),a.create(),a}function su(e,{getFn:t=F.getFn,fieldNormWeight:r=F.fieldNormWeight}={}){const{keys:n,records:a}=e,o=new Mr({getFn:t,fieldNormWeight:r});return o.setKeys(n),o.setIndexRecords(a),o}function xt(e,{errors:t=0,currentLocation:r=0,expectedLocation:n=0,distance:a=F.distance,ignoreLocation:o=F.ignoreLocation}={}){const s=t/e.length;if(o)return s;const c=Math.abs(n-r);return a?s+c/a:c?1:s}function iu(e=[],t=F.minMatchCharLength){let r=[],n=-1,a=-1,o=0;for(let s=e.length;o<s;o+=1){let c=e[o];c&&n===-1?n=o:!c&&n!==-1&&(a=o-1,a-n+1>=t&&r.push([n,a]),n=-1)}return e[o-1]&&o-n>=t&&r.push([n,o-1]),r}const Ke=32;function cu(e,t,r,{location:n=F.location,distance:a=F.distance,threshold:o=F.threshold,findAllMatches:s=F.findAllMatches,minMatchCharLength:c=F.minMatchCharLength,includeMatches:i=F.includeMatches,ignoreLocation:u=F.ignoreLocation}={}){if(t.length>Ke)throw new Error(Yl(Ke));const f=t.length,d=e.length,g=Math.max(0,Math.min(n,d));let m=o,p=g;const b=c>1||i,v=b?Array(d):[];let w;for(;(w=e.indexOf(t,p))>-1;){let O=xt(t,{currentLocation:w,expectedLocation:g,distance:a,ignoreLocation:u});if(m=Math.min(O,m),p=w+f,b){let C=0;for(;C<f;)v[w+C]=1,C+=1}}p=-1;let y=[],h=1,j=f+d;const E=1<<f-1;for(let O=0;O<f;O+=1){let C=0,T=j;for(;C<T;)xt(t,{errors:O,currentLocation:g+T,expectedLocation:g,distance:a,ignoreLocation:u})<=m?C=T:j=T,T=Math.floor((j-C)/2+C);j=T;let x=Math.max(1,g-T+1),_=s?d:Math.min(g+T,d)+f,P=Array(_+2);P[_+1]=(1<<O)-1;for(let S=_;S>=x;S-=1){let N=S-1,k=r[e.charAt(N)];if(b&&(v[N]=+!!k),P[S]=(P[S+1]<<1|1)&k,O&&(P[S]|=(y[S+1]|y[S])<<1|1|y[S+1]),P[S]&E&&(h=xt(t,{errors:O,currentLocation:N,expectedLocation:g,distance:a,ignoreLocation:u}),h<=m)){if(m=h,p=N,p<=g)break;x=Math.max(1,2*g-p)}}if(xt(t,{errors:O+1,currentLocation:g,expectedLocation:g,distance:a,ignoreLocation:u})>m)break;y=P}const M={isMatch:p>=0,score:Math.max(.001,h)};if(b){const O=iu(v,c);O.length?i&&(M.indices=O):M.isMatch=!1}return M}function lu(e){let t={};for(let r=0,n=e.length;r<n;r+=1){const a=e.charAt(r);t[a]=(t[a]||0)|1<<n-r-1}return t}class Fa{constructor(t,{location:r=F.location,threshold:n=F.threshold,distance:a=F.distance,includeMatches:o=F.includeMatches,findAllMatches:s=F.findAllMatches,minMatchCharLength:c=F.minMatchCharLength,isCaseSensitive:i=F.isCaseSensitive,ignoreLocation:u=F.ignoreLocation}={}){if(this.options={location:r,threshold:n,distance:a,includeMatches:o,findAllMatches:s,minMatchCharLength:c,isCaseSensitive:i,ignoreLocation:u},this.pattern=i?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const f=(g,m)=>{this.chunks.push({pattern:g,alphabet:lu(g),startIndex:m})},d=this.pattern.length;if(d>Ke){let g=0;const m=d%Ke,p=d-m;for(;g<p;)f(this.pattern.substr(g,Ke),g),g+=Ke;if(m){const b=d-Ke;f(this.pattern.substr(b),b)}}else f(this.pattern,0)}searchIn(t){const{isCaseSensitive:r,includeMatches:n}=this.options;if(r||(t=t.toLowerCase()),this.pattern===t){let p={isMatch:!0,score:0};return n&&(p.indices=[[0,t.length-1]]),p}const{location:a,distance:o,threshold:s,findAllMatches:c,minMatchCharLength:i,ignoreLocation:u}=this.options;let f=[],d=0,g=!1;this.chunks.forEach(({pattern:p,alphabet:b,startIndex:v})=>{const{isMatch:w,score:y,indices:h}=cu(t,p,b,{location:a+v,distance:o,threshold:s,findAllMatches:c,minMatchCharLength:i,includeMatches:n,ignoreLocation:u});w&&(g=!0),d+=y,w&&h&&(f=[...f,...h])});let m={isMatch:g,score:g?d/this.chunks.length:1};return g&&n&&(m.indices=f),m}}class Pe{constructor(t){this.pattern=t}static isMultiMatch(t){return pn(t,this.multiRegex)}static isSingleMatch(t){return pn(t,this.singleRegex)}search(){}}function pn(e,t){const r=e.match(t);return r?r[1]:null}class uu extends Pe{constructor(t){super(t)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(t){const r=t===this.pattern;return{isMatch:r,score:r?0:1,indices:[0,this.pattern.length-1]}}}class du extends Pe{constructor(t){super(t)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(t){const n=t.indexOf(this.pattern)===-1;return{isMatch:n,score:n?0:1,indices:[0,t.length-1]}}}class fu extends Pe{constructor(t){super(t)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(t){const r=t.startsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,this.pattern.length-1]}}}class pu extends Pe{constructor(t){super(t)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(t){const r=!t.startsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,t.length-1]}}}class hu extends Pe{constructor(t){super(t)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(t){const r=t.endsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[t.length-this.pattern.length,t.length-1]}}}class vu extends Pe{constructor(t){super(t)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(t){const r=!t.endsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,t.length-1]}}}class Ka extends Pe{constructor(t,{location:r=F.location,threshold:n=F.threshold,distance:a=F.distance,includeMatches:o=F.includeMatches,findAllMatches:s=F.findAllMatches,minMatchCharLength:c=F.minMatchCharLength,isCaseSensitive:i=F.isCaseSensitive,ignoreLocation:u=F.ignoreLocation}={}){super(t),this._bitapSearch=new Fa(t,{location:r,threshold:n,distance:a,includeMatches:o,findAllMatches:s,minMatchCharLength:c,isCaseSensitive:i,ignoreLocation:u})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class Va extends Pe{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let r=0,n;const a=[],o=this.pattern.length;for(;(n=t.indexOf(this.pattern,r))>-1;)r=n+o,a.push([n,r-1]);const s=!!a.length;return{isMatch:s,score:s?0:1,indices:a}}}const pr=[uu,Va,fu,pu,vu,hu,du,Ka],hn=pr.length,mu=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,gu="|";function bu(e,t={}){return e.split(gu).map(r=>{let n=r.trim().split(mu).filter(o=>o&&!!o.trim()),a=[];for(let o=0,s=n.length;o<s;o+=1){const c=n[o];let i=!1,u=-1;for(;!i&&++u<hn;){const f=pr[u];let d=f.isMultiMatch(c);d&&(a.push(new f(d,t)),i=!0)}if(!i)for(u=-1;++u<hn;){const f=pr[u];let d=f.isSingleMatch(c);if(d){a.push(new f(d,t));break}}}return a})}const yu=new Set([Ka.type,Va.type]);class xu{constructor(t,{isCaseSensitive:r=F.isCaseSensitive,includeMatches:n=F.includeMatches,minMatchCharLength:a=F.minMatchCharLength,ignoreLocation:o=F.ignoreLocation,findAllMatches:s=F.findAllMatches,location:c=F.location,threshold:i=F.threshold,distance:u=F.distance}={}){this.query=null,this.options={isCaseSensitive:r,includeMatches:n,minMatchCharLength:a,findAllMatches:s,ignoreLocation:o,location:c,threshold:i,distance:u},this.pattern=r?t:t.toLowerCase(),this.query=bu(this.pattern,this.options)}static condition(t,r){return r.useExtendedSearch}searchIn(t){const r=this.query;if(!r)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:a}=this.options;t=a?t:t.toLowerCase();let o=0,s=[],c=0;for(let i=0,u=r.length;i<u;i+=1){const f=r[i];s.length=0,o=0;for(let d=0,g=f.length;d<g;d+=1){const m=f[d],{isMatch:p,indices:b,score:v}=m.search(t);if(p){if(o+=1,c+=v,n){const w=m.constructor.type;yu.has(w)?s=[...s,...b]:s.push(b)}}else{c=0,o=0,s.length=0;break}}if(o){let d={isMatch:!0,score:c/o};return n&&(d.indices=s),d}}return{isMatch:!1,score:1}}}const hr=[];function wu(...e){hr.push(...e)}function vr(e,t){for(let r=0,n=hr.length;r<n;r+=1){let a=hr[r];if(a.condition(e,t))return new a(e,t)}return new Fa(e,t)}const Tt={AND:"$and",OR:"$or"},mr={PATH:"$path",PATTERN:"$val"},gr=e=>!!(e[Tt.AND]||e[Tt.OR]),_u=e=>!!e[mr.PATH],ju=e=>!Oe(e)&&La(e)&&!gr(e),vn=e=>({[Tt.AND]:Object.keys(e).map(t=>({[t]:e[t]}))});function Ga(e,t,{auto:r=!0}={}){const n=a=>{let o=Object.keys(a);const s=_u(a);if(!s&&o.length>1&&!gr(a))return n(vn(a));if(ju(a)){const i=s?a[mr.PATH]:o[0],u=s?a[mr.PATTERN]:a[i];if(!we(u))throw new Error(Wl(i));const f={keyId:fr(i),pattern:u};return r&&(f.searcher=vr(u,t)),f}let c={children:[],operator:o[0]};return o.forEach(i=>{const u=a[i];Oe(u)&&u.forEach(f=>{c.children.push(n(f))})}),c};return gr(e)||(e=vn(e)),n(e)}function Su(e,{ignoreFieldNorm:t=F.ignoreFieldNorm}){e.forEach(r=>{let n=1;r.matches.forEach(({key:a,norm:o,score:s})=>{const c=a?a.weight:null;n*=Math.pow(s===0&&c?Number.EPSILON:s,(c||1)*(t?1:o))}),r.score=n})}function Cu(e,t){const r=e.matches;t.matches=[],he(r)&&r.forEach(n=>{if(!he(n.indices)||!n.indices.length)return;const{indices:a,value:o}=n;let s={indices:a,value:o};n.key&&(s.key=n.key.src),n.idx>-1&&(s.refIndex=n.idx),t.matches.push(s)})}function Ou(e,t){t.score=e.score}function Eu(e,t,{includeMatches:r=F.includeMatches,includeScore:n=F.includeScore}={}){const a=[];return r&&a.push(Cu),n&&a.push(Ou),e.map(o=>{const{idx:s}=o,c={item:t[s],refIndex:s};return a.length&&a.forEach(i=>{i(o,c)}),c})}class Ze{constructor(t,r={},n){this.options={...F,...r},this.options.useExtendedSearch,this._keyStore=new Jl(this.options.keys),this.setCollection(t,n)}setCollection(t,r){if(this._docs=t,r&&!(r instanceof Mr))throw new Error(Hl);this._myIndex=r||$a(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){he(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const r=[];for(let n=0,a=this._docs.length;n<a;n+=1){const o=this._docs[n];t(o,n)&&(this.removeAt(n),n-=1,a-=1,r.push(o))}return r}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:r=-1}={}){const{includeMatches:n,includeScore:a,shouldSort:o,sortFn:s,ignoreFieldNorm:c}=this.options;let i=we(t)?we(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return Su(i,{ignoreFieldNorm:c}),o&&i.sort(s),Da(r)&&r>-1&&(i=i.slice(0,r)),Eu(i,this._docs,{includeMatches:n,includeScore:a})}_searchStringList(t){const r=vr(t,this.options),{records:n}=this._myIndex,a=[];return n.forEach(({v:o,i:s,n:c})=>{if(!he(o))return;const{isMatch:i,score:u,indices:f}=r.searchIn(o);i&&a.push({item:o,idx:s,matches:[{score:u,value:o,norm:c,indices:f}]})}),a}_searchLogical(t){const r=Ga(t,this.options),n=(c,i,u)=>{if(!c.children){const{keyId:d,searcher:g}=c,m=this._findMatches({key:this._keyStore.get(d),value:this._myIndex.getValueForItemAtKeyId(i,d),searcher:g});return m&&m.length?[{idx:u,item:i,matches:m}]:[]}const f=[];for(let d=0,g=c.children.length;d<g;d+=1){const m=c.children[d],p=n(m,i,u);if(p.length)f.push(...p);else if(c.operator===Tt.AND)return[]}return f},a=this._myIndex.records,o={},s=[];return a.forEach(({$:c,i})=>{if(he(c)){let u=n(r,c,i);u.length&&(o[i]||(o[i]={idx:i,item:c,matches:[]},s.push(o[i])),u.forEach(({matches:f})=>{o[i].matches.push(...f)}))}}),s}_searchObjectList(t){const r=vr(t,this.options),{keys:n,records:a}=this._myIndex,o=[];return a.forEach(({$:s,i:c})=>{if(!he(s))return;let i=[];n.forEach((u,f)=>{i.push(...this._findMatches({key:u,value:s[f],searcher:r}))}),i.length&&o.push({idx:c,item:s,matches:i})}),o}_findMatches({key:t,value:r,searcher:n}){if(!he(r))return[];let a=[];if(Oe(r))r.forEach(({v:o,i:s,n:c})=>{if(!he(o))return;const{isMatch:i,score:u,indices:f}=n.searchIn(o);i&&a.push({score:u,key:t,value:o,idx:s,norm:c,indices:f})});else{const{v:o,n:s}=r,{isMatch:c,score:i,indices:u}=n.searchIn(o);c&&a.push({score:i,key:t,value:o,norm:s,indices:u})}return a}}Ze.version="6.6.2";Ze.createIndex=$a;Ze.parseIndex=su;Ze.config=F;Ze.parseQuery=Ga;wu(xu);const Tu=Object.freeze(Object.defineProperty({__proto__:null,default:Ze},Symbol.toStringTag,{value:"Module"})),Mu=An(Tu);var mn;function Ru(){return mn||(mn=1,function(e){var t=me&&me.__createBinding||(Object.create?function(m,p,b,v){v===void 0&&(v=b),Object.defineProperty(m,v,{enumerable:!0,get:function(){return p[b]}})}:function(m,p,b,v){v===void 0&&(v=b),m[v]=p[b]}),r=me&&me.__setModuleDefault||(Object.create?function(m,p){Object.defineProperty(m,"default",{enumerable:!0,value:p})}:function(m,p){m.default=p}),n=me&&me.__importStar||function(m){if(m&&m.__esModule)return m;var p={};if(m!=null)for(var b in m)b!=="default"&&Object.prototype.hasOwnProperty.call(m,b)&&t(p,m,b);return r(p,m),p},a=me&&me.__importDefault||function(m){return m&&m.__esModule?m:{default:m}};Object.defineProperty(e,"__esModule",{value:!0}),e.useDeepMatches=e.useMatches=e.NO_GROUP=void 0;var o=n(ne()),s=Ie(),c=qe(),i=a(Mu);e.NO_GROUP={name:"none",priority:c.Priority.NORMAL};var u={keys:[{name:"name",weight:.5},{name:"keywords",getFn:function(m){var p;return((p=m.keywords)!==null&&p!==void 0?p:"").split(",")},weight:.5},"subtitle"],ignoreLocation:!0,includeScore:!0,includeMatches:!0,threshold:.2,minMatchCharLength:1};function f(m,p){return p.priority-m.priority}function d(){var m=(0,s.useKBar)(function(T){return{search:T.searchQuery,actions:T.actions,rootActionId:T.currentRootActionId}}),p=m.search,b=m.actions,v=m.rootActionId,w=o.useMemo(function(){return Object.keys(b).reduce(function(T,x){var _=b[x];if(!_.parent&&!v&&T.push(_),_.id===v)for(var P=0;P<_.children.length;P++)T.push(_.children[P]);return T},[]).sort(f)},[b,v]),y=o.useCallback(function(T){for(var x=[],_=0;_<T.length;_++)x.push(T[_]);return function P(A,S){S===void 0&&(S=x);for(var N=0;N<A.length;N++)if(A[N].children.length>0){for(var k=A[N].children,D=0;D<k.length;D++)S.push(k[D]);P(A[N].children,S)}return S}(T)},[]),h=!p,j=o.useMemo(function(){return h?w:y(w)},[y,w,h]),E=o.useMemo(function(){return new i.default(j,u)},[j]),M=g(j,p,E),O=o.useMemo(function(){for(var T,x,_={},P=[],A=[],S=0;S<M.length;S++){var N=M[S],k=N.action,D=N.score||c.Priority.NORMAL,I={name:typeof k.section=="string"?k.section:((T=k.section)===null||T===void 0?void 0:T.name)||e.NO_GROUP.name,priority:typeof k.section=="string"?D:((x=k.section)===null||x===void 0?void 0:x.priority)||0+D};_[I.name]||(_[I.name]=[],P.push(I)),_[I.name].push({priority:k.priority+D,action:k})}A=P.sort(f).map(function(ee){return{name:ee.name,actions:_[ee.name].sort(f).map(function(q){return q.action})}});for(var G=[],S=0;S<A.length;S++){var H=A[S];H.name!==e.NO_GROUP.name&&G.push(H.name);for(var J=0;J<H.actions.length;J++)G.push(H.actions[J])}return G},[M]),C=o.useMemo(function(){return v},[O]);return o.useMemo(function(){return{results:O,rootActionId:C}},[C,O])}e.useMatches=d;function g(m,p,b){var v=o.useMemo(function(){return{filtered:m,search:p}},[m,p]),w=(0,c.useThrottledValue)(v),y=w.filtered,h=w.search;return o.useMemo(function(){if(h.trim()==="")return y.map(function(M){return{score:0,action:M}});var j=[],E=b.search(h);return j=E.map(function(M){var O=M.item,C=M.score;return{score:1/((C??0)+1),action:O}}),j},[y,h,b])}e.useDeepMatches=d}(me)),me}var ge={},Xt,gn;function Au(){if(gn)return Xt;gn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,o=Object.prototype.hasOwnProperty,s=(b,v)=>{for(var w in v)t(b,w,{get:v[w],enumerable:!0})},c=(b,v,w,y)=>{if(v&&typeof v=="object"||typeof v=="function")for(let h of n(v))!o.call(b,h)&&h!==w&&t(b,h,{get:()=>v[h],enumerable:!(y=r(v,h))||y.enumerable});return b},i=(b,v,w)=>(w=b!=null?e(a(b)):{},c(!b||!b.__esModule?t(w,"default",{value:b,enumerable:!0}):w,b)),u=b=>c(t({},"__esModule",{value:!0}),b),f={};s(f,{composeRefs:()=>m,useComposedRefs:()=>p}),Xt=u(f);var d=i(ne());function g(b,v){if(typeof b=="function")return b(v);b!=null&&(b.current=v)}function m(...b){return v=>{let w=!1;const y=b.map(h=>{const j=g(h,v);return!w&&typeof j=="function"&&(w=!0),j});if(w)return()=>{for(let h=0;h<y.length;h++){const j=y[h];typeof j=="function"?j():g(b[h],null)}}}}function p(...b){return d.useCallback(m(...b),b)}return Xt}var Jt,bn;function Iu(){if(bn)return Jt;bn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,o=Object.prototype.hasOwnProperty,s=(O,C)=>{for(var T in C)t(O,T,{get:C[T],enumerable:!0})},c=(O,C,T,x)=>{if(C&&typeof C=="object"||typeof C=="function")for(let _ of n(C))!o.call(O,_)&&_!==T&&t(O,_,{get:()=>C[_],enumerable:!(x=r(C,_))||x.enumerable});return O},i=(O,C,T)=>(T=O!=null?e(a(O)):{},c(!O||!O.__esModule?t(T,"default",{value:O,enumerable:!0}):T,O)),u=O=>c(t({},"__esModule",{value:!0}),O),f={};s(f,{Root:()=>b,Slot:()=>b,Slottable:()=>h,createSlot:()=>p,createSlottable:()=>y}),Jt=u(f);var d=i(ne()),g=Au(),m=br();function p(O){const C=v(O),T=d.forwardRef((x,_)=>{const{children:P,...A}=x,S=d.Children.toArray(P),N=S.find(j);if(N){const k=N.props.children,D=S.map(I=>I===N?d.Children.count(k)>1?d.Children.only(null):d.isValidElement(k)?k.props.children:null:I);return(0,m.jsx)(C,{...A,ref:_,children:d.isValidElement(k)?d.cloneElement(k,void 0,D):null})}return(0,m.jsx)(C,{...A,ref:_,children:P})});return T.displayName=`${O}.Slot`,T}var b=p("Slot");function v(O){const C=d.forwardRef((T,x)=>{const{children:_,...P}=T;if(d.isValidElement(_)){const A=M(_),S=E(P,_.props);return _.type!==d.Fragment&&(S.ref=x?(0,g.composeRefs)(x,A):A),d.cloneElement(_,S)}return d.Children.count(_)>1?d.Children.only(null):null});return C.displayName=`${O}.SlotClone`,C}var w=Symbol("radix.slottable");function y(O){const C=({children:T})=>(0,m.jsx)(m.Fragment,{children:T});return C.displayName=`${O}.Slottable`,C.__radixId=w,C}var h=y("Slottable");function j(O){return d.isValidElement(O)&&typeof O.type=="function"&&"__radixId"in O.type&&O.type.__radixId===w}function E(O,C){const T={...C};for(const x in C){const _=O[x],P=C[x];/^on[A-Z]/.test(x)?_&&P?T[x]=(...S)=>{const N=P(...S);return _(...S),N}:_&&(T[x]=_):x==="style"?T[x]={..._,...P}:x==="className"&&(T[x]=[_,P].filter(Boolean).join(" "))}return{...O,...T}}function M(O){let C=Object.getOwnPropertyDescriptor(O.props,"ref")?.get,T=C&&"isReactWarning"in C&&C.isReactWarning;return T?O.ref:(C=Object.getOwnPropertyDescriptor(O,"ref")?.get,T=C&&"isReactWarning"in C&&C.isReactWarning,T?O.props.ref:O.props.ref||O.ref)}return Jt}var Zt,yn;function Pu(){if(yn)return Zt;yn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,o=Object.prototype.hasOwnProperty,s=(h,j)=>{for(var E in j)t(h,E,{get:j[E],enumerable:!0})},c=(h,j,E,M)=>{if(j&&typeof j=="object"||typeof j=="function")for(let O of n(j))!o.call(h,O)&&O!==E&&t(h,O,{get:()=>j[O],enumerable:!(M=r(j,O))||M.enumerable});return h},i=(h,j,E)=>(E=h!=null?e(a(h)):{},c(!h||!h.__esModule?t(E,"default",{value:h,enumerable:!0}):E,h)),u=h=>c(t({},"__esModule",{value:!0}),h),f={};s(f,{Primitive:()=>v,Root:()=>y,dispatchDiscreteCustomEvent:()=>w}),Zt=u(f);var d=i(ne()),g=i(In()),m=Iu(),p=br(),b=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],v=b.reduce((h,j)=>{const E=(0,m.createSlot)(`Primitive.${j}`),M=d.forwardRef((O,C)=>{const{asChild:T,...x}=O,_=T?E:j;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),(0,p.jsx)(_,{...x,ref:C})});return M.displayName=`Primitive.${j}`,{...h,[j]:M}},{});function w(h,j){h&&g.flushSync(()=>h.dispatchEvent(j))}var y=v;return Zt}var er,xn;function ku(){if(xn)return er;xn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,o=Object.prototype.hasOwnProperty,s=(m,p)=>{for(var b in p)t(m,b,{get:p[b],enumerable:!0})},c=(m,p,b,v)=>{if(p&&typeof p=="object"||typeof p=="function")for(let w of n(p))!o.call(m,w)&&w!==b&&t(m,w,{get:()=>p[w],enumerable:!(v=r(p,w))||v.enumerable});return m},i=(m,p,b)=>(b=m!=null?e(a(m)):{},c(!m||!m.__esModule?t(b,"default",{value:m,enumerable:!0}):b,m)),u=m=>c(t({},"__esModule",{value:!0}),m),f={};s(f,{useLayoutEffect:()=>g}),er=u(f);var d=i(ne()),g=globalThis?.document?d.useLayoutEffect:()=>{};return er}var tr,wn;function Nu(){if(wn)return tr;wn=1;var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.getPrototypeOf,o=Object.prototype.hasOwnProperty,s=(h,j)=>{for(var E in j)t(h,E,{get:j[E],enumerable:!0})},c=(h,j,E,M)=>{if(j&&typeof j=="object"||typeof j=="function")for(let O of n(j))!o.call(h,O)&&O!==E&&t(h,O,{get:()=>j[O],enumerable:!(M=r(j,O))||M.enumerable});return h},i=(h,j,E)=>(E=h!=null?e(a(h)):{},c(!h||!h.__esModule?t(E,"default",{value:h,enumerable:!0}):E,h)),u=h=>c(t({},"__esModule",{value:!0}),h),f={};s(f,{Portal:()=>w,Root:()=>y}),tr=u(f);var d=i(ne()),g=i(In()),m=Pu(),p=ku(),b=br(),v="Portal",w=d.forwardRef((h,j)=>{const{container:E,...M}=h,[O,C]=d.useState(!1);(0,p.useLayoutEffect)(()=>C(!0),[]);const T=E||O&&globalThis?.document?.body;return T?g.default.createPortal((0,b.jsx)(m.Primitive.div,{...M,ref:j}),T):null});w.displayName=v;var y=w;return tr}var _n;function Du(){if(_n)return ge;_n=1;var e=ge&&ge.__createBinding||(Object.create?function(i,u,f,d){d===void 0&&(d=f),Object.defineProperty(i,d,{enumerable:!0,get:function(){return u[f]}})}:function(i,u,f,d){d===void 0&&(d=f),i[d]=u[f]}),t=ge&&ge.__setModuleDefault||(Object.create?function(i,u){Object.defineProperty(i,"default",{enumerable:!0,value:u})}:function(i,u){i.default=u}),r=ge&&ge.__importStar||function(i){if(i&&i.__esModule)return i;var u={};if(i!=null)for(var f in i)f!=="default"&&Object.prototype.hasOwnProperty.call(i,f)&&e(u,i,f);return t(u,i),u};Object.defineProperty(ge,"__esModule",{value:!0}),ge.KBarPortal=void 0;var n=Nu(),a=r(ne()),o=Je(),s=Ie();function c(i){var u=i.children,f=i.container,d=(0,s.useKBar)(function(g){return{showing:g.visualState!==o.VisualState.hidden}}).showing;return d?a.createElement(n.Portal,{container:f},u):null}return ge.KBarPortal=c,ge}var ce={},jn;function Lu(){if(jn)return ce;jn=1;var e=ce&&ce.__assign||function(){return e=Object.assign||function(i){for(var u,f=1,d=arguments.length;f<d;f++){u=arguments[f];for(var g in u)Object.prototype.hasOwnProperty.call(u,g)&&(i[g]=u[g])}return i},e.apply(this,arguments)},t=ce&&ce.__createBinding||(Object.create?function(i,u,f,d){d===void 0&&(d=f),Object.defineProperty(i,d,{enumerable:!0,get:function(){return u[f]}})}:function(i,u,f,d){d===void 0&&(d=f),i[d]=u[f]}),r=ce&&ce.__setModuleDefault||(Object.create?function(i,u){Object.defineProperty(i,"default",{enumerable:!0,value:u})}:function(i,u){i.default=u}),n=ce&&ce.__importStar||function(i){if(i&&i.__esModule)return i;var u={};if(i!=null)for(var f in i)f!=="default"&&Object.prototype.hasOwnProperty.call(i,f)&&t(u,i,f);return r(u,i),u},a=ce&&ce.__rest||function(i,u){var f={};for(var d in i)Object.prototype.hasOwnProperty.call(i,d)&&u.indexOf(d)<0&&(f[d]=i[d]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var g=0,d=Object.getOwnPropertySymbols(i);g<d.length;g++)u.indexOf(d[g])<0&&Object.prototype.propertyIsEnumerable.call(i,d[g])&&(f[d[g]]=i[d[g]]);return f};Object.defineProperty(ce,"__esModule",{value:!0}),ce.KBarPositioner=void 0;var o=n(ne()),s={position:"fixed",display:"flex",alignItems:"flex-start",justifyContent:"center",width:"100%",inset:"0px",padding:"14vh 16px 16px"};function c(i){return i?e(e({},s),i):s}return ce.KBarPositioner=o.forwardRef(function(i,u){var f=i.style,d=i.children,g=a(i,["style","children"]);return o.createElement("div",e({ref:u,style:c(f)},g),d)}),ce}var de={},Sn;function qa(){return Sn||(Sn=1,function(e){var t=de&&de.__assign||function(){return t=Object.assign||function(d){for(var g,m=1,p=arguments.length;m<p;m++){g=arguments[m];for(var b in g)Object.prototype.hasOwnProperty.call(g,b)&&(d[b]=g[b])}return d},t.apply(this,arguments)},r=de&&de.__createBinding||(Object.create?function(d,g,m,p){p===void 0&&(p=m),Object.defineProperty(d,p,{enumerable:!0,get:function(){return g[m]}})}:function(d,g,m,p){p===void 0&&(p=m),d[p]=g[m]}),n=de&&de.__setModuleDefault||(Object.create?function(d,g){Object.defineProperty(d,"default",{enumerable:!0,value:g})}:function(d,g){d.default=g}),a=de&&de.__importStar||function(d){if(d&&d.__esModule)return d;var g={};if(d!=null)for(var m in d)m!=="default"&&Object.prototype.hasOwnProperty.call(d,m)&&r(g,d,m);return n(g,d),g},o=de&&de.__rest||function(d,g){var m={};for(var p in d)Object.prototype.hasOwnProperty.call(d,p)&&g.indexOf(p)<0&&(m[p]=d[p]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var b=0,p=Object.getOwnPropertySymbols(d);b<p.length;b++)g.indexOf(p[b])<0&&Object.prototype.propertyIsEnumerable.call(d,p[b])&&(m[p[b]]=d[p[b]]);return m};Object.defineProperty(e,"__esModule",{value:!0}),e.KBarSearch=e.getListboxItemId=e.KBAR_LISTBOX=void 0;var s=a(ne()),c=Je(),i=Ie();e.KBAR_LISTBOX="kbar-listbox";var u=function(d){return"kbar-listbox-item-"+d};e.getListboxItemId=u;function f(d){var g=(0,i.useKBar)(function(x){return{search:x.searchQuery,currentRootActionId:x.currentRootActionId,actions:x.actions,activeIndex:x.activeIndex,showing:x.visualState===c.VisualState.showing}}),m=g.query,p=g.search,b=g.actions,v=g.currentRootActionId,w=g.activeIndex,y=g.showing,h=g.options,j=s.useState(p),E=j[0],M=j[1];s.useEffect(function(){m.setSearch(E)},[E,m]);var O=d.defaultPlaceholder,C=o(d,["defaultPlaceholder"]);s.useEffect(function(){return m.setSearch(""),m.getInput().focus(),function(){return m.setSearch("")}},[v,m]);var T=s.useMemo(function(){var x=O??"Type a command or search…";return v&&b[v]?b[v].name:x},[b,v,O]);return s.createElement("input",t({},C,{ref:m.inputRefSetter,autoFocus:!0,autoComplete:"off",role:"combobox",spellCheck:"false","aria-expanded":y,"aria-controls":e.KBAR_LISTBOX,"aria-activedescendant":(0,e.getListboxItemId)(w),value:E,placeholder:T,onChange:function(x){var _,P,A;(_=d.onChange)===null||_===void 0||_.call(d,x),M(x.target.value),(A=(P=h?.callbacks)===null||P===void 0?void 0:P.onQueryChange)===null||A===void 0||A.call(P,x.target.value)},onKeyDown:function(x){var _;if((_=d.onKeyDown)===null||_===void 0||_.call(d,x),v&&!p&&x.key==="Backspace"){var P=b[v].parent;m.setCurrentRootAction(P)}}}))}e.KBarSearch=f}(de)),de}var fe={};function Ve(){return Ve=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ve.apply(this,arguments)}function zu(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,o;for(o=0;o<n.length;o++)a=n[o],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}var Bu=["bottom","height","left","right","top","width"],$u=function(t,r){return t===void 0&&(t={}),r===void 0&&(r={}),Bu.some(function(n){return t[n]!==r[n]})},Re=new Map,Ua,Fu=function e(){var t=[];Re.forEach(function(r,n){var a=n.getBoundingClientRect();$u(a,r.rect)&&(r.rect=a,t.push(r))}),t.forEach(function(r){r.callbacks.forEach(function(n){return n(r.rect)})}),Ua=window.requestAnimationFrame(e)};function Ku(e,t){return{observe:function(){var n=Re.size===0;Re.has(e)?Re.get(e).callbacks.push(t):Re.set(e,{rect:void 0,hasRectChanged:!1,callbacks:[t]}),n&&Fu()},unobserve:function(){var n=Re.get(e);if(n){var a=n.callbacks.indexOf(t);a>=0&&n.callbacks.splice(a,1),n.callbacks.length||Re.delete(e),Re.size||cancelAnimationFrame(Ua)}}}}var Mt=typeof window<"u"?X.useLayoutEffect:X.useEffect;function Vu(e,t){t===void 0&&(t={width:0,height:0});var r=X.useState(e.current),n=r[0],a=r[1],o=X.useReducer(Gu,t),s=o[0],c=o[1],i=X.useRef(!1);return Mt(function(){e.current!==n&&a(e.current)}),Mt(function(){if(n&&!i.current){i.current=!0;var u=n.getBoundingClientRect();c({rect:u})}},[n]),X.useEffect(function(){if(n){var u=Ku(n,function(f){c({rect:f})});return u.observe(),function(){u.unobserve()}}},[n]),s}function Gu(e,t){var r=t.rect;return e.height!==r.height||e.width!==r.width?r:e}var qu=function(){return 50},Uu=function(t){return t},Hu=function(t,r){var n=r?"offsetWidth":"offsetHeight";return t[n]},Ha=function(t){for(var r=Math.max(t.start-t.overscan,0),n=Math.min(t.end+t.overscan,t.size-1),a=[],o=r;o<=n;o++)a.push(o);return a};function Wu(e){var t,r=e.size,n=r===void 0?0:r,a=e.estimateSize,o=a===void 0?qu:a,s=e.overscan,c=s===void 0?1:s,i=e.paddingStart,u=i===void 0?0:i,f=e.paddingEnd,d=f===void 0?0:f,g=e.parentRef,m=e.horizontal,p=e.scrollToFn,b=e.useObserver,v=e.initialRect,w=e.onScrollElement,y=e.scrollOffsetFn,h=e.keyExtractor,j=h===void 0?Uu:h,E=e.measureSize,M=E===void 0?Hu:E,O=e.rangeExtractor,C=O===void 0?Ha:O,T=m?"width":"height",x=m?"scrollLeft":"scrollTop",_=X.useRef({scrollOffset:0,measurements:[]}),P=X.useState(0),A=P[0],S=P[1];_.current.scrollOffset=A;var N=b||Vu,k=N(g,v),D=k[T];_.current.outerSize=D;var I=X.useCallback(function(Y){g.current&&(g.current[x]=Y)},[g,x]),G=p||I;p=X.useCallback(function(Y){G(Y,I)},[I,G]);var H=X.useState({}),J=H[0],ee=H[1],q=X.useCallback(function(){return ee({})},[]),U=X.useRef([]),se=X.useMemo(function(){var Y=U.current.length>0?Math.min.apply(Math,U.current):0;U.current=[];for(var re=_.current.measurements.slice(0,Y),Q=Y;Q<n;Q++){var ve=j(Q),te=J[ve],_e=re[Q-1]?re[Q-1].end:u,ye=typeof te=="number"?te:o(Q),xe=_e+ye;re[Q]={index:Q,start:_e,size:ye,end:xe,key:ve}}return re},[o,J,u,n,j]),et=(((t=se[n-1])==null?void 0:t.end)||u)+d;_.current.measurements=se,_.current.totalSize=et;var W=w?w.current:g.current,Ue=X.useRef(y);Ue.current=y,Mt(function(){if(!W){S(0);return}var Y=function(Q){var ve=Ue.current?Ue.current(Q):W[x];S(ve)};return Y(),W.addEventListener("scroll",Y,{capture:!1,passive:!0}),function(){W.removeEventListener("scroll",Y)}},[W,x]);var ke=Qu(_.current),Ne=ke.start,He=ke.end,Ee=X.useMemo(function(){return C({start:Ne,end:He,overscan:c,size:se.length})},[Ne,He,c,se.length,C]),Rr=X.useRef(M);Rr.current=M;var Wa=X.useMemo(function(){for(var Y=[],re=function(_e,ye){var xe=Ee[_e],dt=se[xe],De=Ve(Ve({},dt),{},{measureRef:function(ft){if(ft){var Lt=Rr.current(ft,m);if(Lt!==De.size){var Ir=_.current.scrollOffset;De.start<Ir&&I(Ir+(Lt-De.size)),U.current.push(xe),ee(function(Qa){var zt;return Ve(Ve({},Qa),{},(zt={},zt[De.key]=Lt,zt))})}}}});Y.push(De)},Q=0,ve=Ee.length;Q<ve;Q++)re(Q);return Y},[Ee,I,m,se]),Ar=X.useRef(!1);Mt(function(){Ar.current&&ee({}),Ar.current=!0},[o]);var Nt=X.useCallback(function(Y,re){var Q=re===void 0?{}:re,ve=Q.align,te=ve===void 0?"start":ve,_e=_.current,ye=_e.scrollOffset,xe=_e.outerSize;te==="auto"&&(Y<=ye?te="start":Y>=ye+xe?te="end":te="start"),te==="start"?p(Y):te==="end"?p(Y-xe):te==="center"&&p(Y-xe/2)},[p]),Dt=X.useCallback(function(Y,re){var Q=re===void 0?{}:re,ve=Q.align,te=ve===void 0?"auto":ve,_e=zu(Q,["align"]),ye=_.current,xe=ye.measurements,dt=ye.scrollOffset,De=ye.outerSize,Te=xe[Math.max(0,Math.min(Y,n-1))];if(Te){if(te==="auto")if(Te.end>=dt+De)te="end";else if(Te.start<=dt)te="start";else return;var ft=te==="center"?Te.start+Te.size/2:te==="end"?Te.end:Te.start;Nt(ft,Ve({align:te},_e))}},[Nt,n]),Ya=X.useCallback(function(){for(var Y=arguments.length,re=new Array(Y),Q=0;Q<Y;Q++)re[Q]=arguments[Q];Dt.apply(void 0,re),requestAnimationFrame(function(){Dt.apply(void 0,re)})},[Dt]);return{virtualItems:Wa,totalSize:et,scrollToOffset:Nt,scrollToIndex:Ya,measure:q}}var Yu=function(t,r,n,a){for(;t<=r;){var o=(t+r)/2|0,s=n(o);if(s<a)t=o+1;else if(s>a)r=o-1;else return o}return t>0?t-1:0};function Qu(e){for(var t=e.measurements,r=e.outerSize,n=e.scrollOffset,a=t.length-1,o=function(u){return t[u].start},s=Yu(0,a,o,n),c=s;c<a&&t[c].end<n+r;)c++;return{start:s,end:c}}const Xu=Object.freeze(Object.defineProperty({__proto__:null,defaultRangeExtractor:Ha,useVirtual:Wu},Symbol.toStringTag,{value:"Module"})),Ju=An(Xu);var Cn;function Zu(){if(Cn)return fe;Cn=1;var e=fe&&fe.__assign||function(){return e=Object.assign||function(d){for(var g,m=1,p=arguments.length;m<p;m++){g=arguments[m];for(var b in g)Object.prototype.hasOwnProperty.call(g,b)&&(d[b]=g[b])}return d},e.apply(this,arguments)},t=fe&&fe.__createBinding||(Object.create?function(d,g,m,p){p===void 0&&(p=m),Object.defineProperty(d,p,{enumerable:!0,get:function(){return g[m]}})}:function(d,g,m,p){p===void 0&&(p=m),d[p]=g[m]}),r=fe&&fe.__setModuleDefault||(Object.create?function(d,g){Object.defineProperty(d,"default",{enumerable:!0,value:g})}:function(d,g){d.default=g}),n=fe&&fe.__importStar||function(d){if(d&&d.__esModule)return d;var g={};if(d!=null)for(var m in d)m!=="default"&&Object.prototype.hasOwnProperty.call(d,m)&&t(g,d,m);return r(g,d),g};Object.defineProperty(fe,"__esModule",{value:!0}),fe.KBarResults=void 0;var a=n(ne()),o=Ju,s=qa(),c=Ie(),i=qe(),u=0,f=function(d){var g=a.useRef(null),m=a.useRef(null),p=a.useRef(d.items);p.current=d.items;var b=(0,o.useVirtual)({size:p.current.length,parentRef:m}),v=(0,c.useKBar)(function(T){return{search:T.searchQuery,currentRootActionId:T.currentRootActionId,activeIndex:T.activeIndex}}),w=v.query,y=v.search,h=v.currentRootActionId,j=v.activeIndex,E=v.options;a.useEffect(function(){var T=function(x){var _;x.isComposing||(x.key==="ArrowUp"||x.ctrlKey&&x.key==="p"?(x.preventDefault(),x.stopPropagation(),w.setActiveIndex(function(P){var A=P>u?P-1:P;if(typeof p.current[A]=="string"){if(A===0)return P;A-=1}return A})):x.key==="ArrowDown"||x.ctrlKey&&x.key==="n"?(x.preventDefault(),x.stopPropagation(),w.setActiveIndex(function(P){var A=P<p.current.length-1?P+1:P;if(typeof p.current[A]=="string"){if(A===p.current.length-1)return P;A+=1}return A})):x.key==="Enter"&&(x.preventDefault(),x.stopPropagation(),(_=g.current)===null||_===void 0||_.click()))};return window.addEventListener("keydown",T,{capture:!0}),function(){return window.removeEventListener("keydown",T,{capture:!0})}},[w]);var M=b.scrollToIndex;a.useEffect(function(){M(j,{align:j<=1?"end":"auto"})},[j,M]),a.useEffect(function(){w.setActiveIndex(typeof d.items[u]=="string"?u+1:u)},[y,h,d.items,w]);var O=a.useCallback(function(T){var x,_;typeof T!="string"&&(T.command?(T.command.perform(T),w.toggle()):(w.setSearch(""),w.setCurrentRootAction(T.id)),(_=(x=E.callbacks)===null||x===void 0?void 0:x.onSelectAction)===null||_===void 0||_.call(x,T))},[w,E]),C=(0,i.usePointerMovedSinceMount)();return a.createElement("div",{ref:m,style:{maxHeight:d.maxHeight||400,position:"relative",overflow:"auto"}},a.createElement("div",{role:"listbox",id:s.KBAR_LISTBOX,style:{height:b.totalSize+"px",width:"100%"}},b.virtualItems.map(function(T){var x=p.current[T.index],_=typeof x!="string"&&{onPointerMove:function(){return C&&j!==T.index&&w.setActiveIndex(T.index)},onPointerDown:function(){return w.setActiveIndex(T.index)},onClick:function(){return O(x)}},P=T.index===j;return a.createElement("div",e({ref:P?g:null,id:(0,s.getListboxItemId)(T.index),role:"option","aria-selected":P,key:T.index,style:{position:"absolute",top:0,left:0,width:"100%",transform:"translateY("+T.start+"px)"}},_),a.cloneElement(d.onRender({item:x,active:P}),{ref:T.measureRef}))})))};return fe.KBarResults=f,fe}var be={},On;function ed(){if(On)return be;On=1;var e=be&&be.__createBinding||(Object.create?function(s,c,i,u){u===void 0&&(u=i),Object.defineProperty(s,u,{enumerable:!0,get:function(){return c[i]}})}:function(s,c,i,u){u===void 0&&(u=i),s[u]=c[i]}),t=be&&be.__setModuleDefault||(Object.create?function(s,c){Object.defineProperty(s,"default",{enumerable:!0,value:c})}:function(s,c){s.default=c}),r=be&&be.__importStar||function(s){if(s&&s.__esModule)return s;var c={};if(s!=null)for(var i in s)i!=="default"&&Object.prototype.hasOwnProperty.call(s,i)&&e(c,s,i);return t(c,s),c};Object.defineProperty(be,"__esModule",{value:!0}),be.useRegisterActions=void 0;var n=r(ne()),a=Ie();function o(s,c){c===void 0&&(c=[]);var i=(0,a.useKBar)().query,u=n.useMemo(function(){return s},c);n.useEffect(function(){if(u.length){var f=i.registerActions(u);return function(){f()}}},[i,u])}return be.useRegisterActions=o,be}var pe={},En;function td(){if(En)return pe;En=1;var e=pe&&pe.__assign||function(){return e=Object.assign||function(d){for(var g,m=1,p=arguments.length;m<p;m++){g=arguments[m];for(var b in g)Object.prototype.hasOwnProperty.call(g,b)&&(d[b]=g[b])}return d},e.apply(this,arguments)},t=pe&&pe.__createBinding||(Object.create?function(d,g,m,p){p===void 0&&(p=m),Object.defineProperty(d,p,{enumerable:!0,get:function(){return g[m]}})}:function(d,g,m,p){p===void 0&&(p=m),d[p]=g[m]}),r=pe&&pe.__setModuleDefault||(Object.create?function(d,g){Object.defineProperty(d,"default",{enumerable:!0,value:g})}:function(d,g){d.default=g}),n=pe&&pe.__importStar||function(d){if(d&&d.__esModule)return d;var g={};if(d!=null)for(var m in d)m!=="default"&&Object.prototype.hasOwnProperty.call(d,m)&&t(g,d,m);return r(g,d),g};Object.defineProperty(pe,"__esModule",{value:!0}),pe.KBarAnimator=void 0;var a=n(ne()),o=Je(),s=Ie(),c=qe(),i=[{opacity:0,transform:"scale(.99)"},{opacity:1,transform:"scale(1.01)"},{opacity:1,transform:"scale(1)"}],u=[{transform:"scale(1)"},{transform:"scale(.98)"},{transform:"scale(1)"}],f=function(d){var g,m,p=d.children,b=d.style,v=d.className,w=d.disableCloseOnOuterClick,y=(0,s.useKBar)(function(A){return{visualState:A.visualState,currentRootActionId:A.currentRootActionId}}),h=y.visualState,j=y.currentRootActionId,E=y.query,M=y.options,O=a.useRef(null),C=a.useRef(null),T=((g=M?.animations)===null||g===void 0?void 0:g.enterMs)||0,x=((m=M?.animations)===null||m===void 0?void 0:m.exitMs)||0;a.useEffect(function(){if(h!==o.VisualState.showing){var A=h===o.VisualState.animatingIn?T:x,S=O.current;S?.animate(i,{duration:A,easing:h===o.VisualState.animatingOut?"ease-in":"ease-out",direction:h===o.VisualState.animatingOut?"reverse":"normal",fill:"forwards"})}},[M,h,T,x]);var _=a.useRef();a.useEffect(function(){if(h===o.VisualState.showing){var A=O.current,S=C.current;if(!A||!S)return;var N=new ResizeObserver(function(k){for(var D=0,I=k;D<I.length;D++){var G=I[D],H=G.contentRect;_.current||(_.current=H.height),A.animate([{height:_.current+"px"},{height:H.height+"px"}],{duration:T/2,easing:"ease-out",fill:"forwards"}),_.current=H.height}});return N.observe(S),function(){N.unobserve(S)}}},[h,M,T,x]);var P=a.useRef(!0);return a.useEffect(function(){if(P.current){P.current=!1;return}var A=O.current;A&&A.animate(u,{duration:T,easing:"ease-out"})},[j,T]),(0,c.useOuterClick)(O,function(){var A,S;w||(E.setVisualState(o.VisualState.animatingOut),(S=(A=M.callbacks)===null||A===void 0?void 0:A.onClose)===null||S===void 0||S.call(A))}),a.createElement("div",{ref:O,style:e(e(e({},i[0]),b),{pointerEvents:"auto"}),className:v},a.createElement("div",{ref:C},p))};return pe.KBarAnimator=f,pe}var Fe={},Tn;function rd(){return Tn||(Tn=1,function(e){var t=Fe&&Fe.__createBinding||(Object.create?function(n,a,o,s){s===void 0&&(s=o),Object.defineProperty(n,s,{enumerable:!0,get:function(){return a[o]}})}:function(n,a,o,s){s===void 0&&(s=o),n[s]=a[o]}),r=Fe&&Fe.__exportStar||function(n,a){for(var o in n)o!=="default"&&!Object.prototype.hasOwnProperty.call(a,o)&&t(a,n,o)};Object.defineProperty(e,"__esModule",{value:!0}),r(ka(),e),r(Pa(),e)}(Fe)),Fe}var Mn;function nd(){return Mn||(Mn=1,function(e){var t=ze&&ze.__createBinding||(Object.create?function(a,o,s,c){c===void 0&&(c=s),Object.defineProperty(a,c,{enumerable:!0,get:function(){return o[s]}})}:function(a,o,s,c){c===void 0&&(c=s),a[c]=o[s]}),r=ze&&ze.__exportStar||function(a,o){for(var s in a)s!=="default"&&!Object.prototype.hasOwnProperty.call(o,s)&&t(o,a,s)};Object.defineProperty(e,"__esModule",{value:!0}),e.Priority=e.createAction=void 0;var n=qe();Object.defineProperty(e,"createAction",{enumerable:!0,get:function(){return n.createAction}}),Object.defineProperty(e,"Priority",{enumerable:!0,get:function(){return n.Priority}}),r(Ru(),e),r(Du(),e),r(Lu(),e),r(qa(),e),r(Zu(),e),r(Ie(),e),r(ed(),e),r(Na(),e),r(td(),e),r(Je(),e),r(rd(),e)}(ze)),ze}var ad=nd();function od(){const{query:e}=ad.useKBar();return l.jsx("div",{className:"w-full space-y-2",children:l.jsxs(At,{variant:"outline",className:"bg-background text-muted-foreground relative h-9 w-full justify-start rounded-[0.5rem] text-sm font-normal shadow-none sm:pr-12 md:w-40 lg:w-64",onClick:e?.toggle,children:[l.jsx(wl,{className:"mr-2 h-4 w-4"}),"Search...",l.jsxs("kbd",{className:"bg-muted pointer-events-none absolute top-[0.3rem] right-[0.3rem] hidden h-6 items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 select-none sm:flex",children:[l.jsx("span",{className:"text-xs",children:"⌘"}),"K"]})]})})}const sd=[{name:"Default",value:"default"},{name:"Blue",value:"blue"},{name:"Green",value:"green"},{name:"Amber",value:"amber"}],id=[{name:"Default",value:"default-scaled"},{name:"Blue",value:"blue-scaled"},{name:"Green",value:"green-scaled"}],cd=[{name:"Mono",value:"mono-scaled"}];function ld(){const{activeTheme:e,setActiveTheme:t}=xa(),r=n=>{t(n)};return l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(ei,{htmlFor:"theme-selector",className:"sr-only",children:"Theme"}),l.jsxs(ti,{value:e,onValueChange:r,children:[l.jsxs(ni,{id:"theme-selector",className:"justify-start *:data-[slot=select-value]:w-12",children:[l.jsx("span",{className:"text-muted-foreground hidden sm:block",children:"Select a theme:"}),l.jsx("span",{className:"text-muted-foreground block sm:hidden",children:"Theme"}),l.jsx(ri,{placeholder:"Select a theme"})]}),l.jsxs(ai,{align:"end",children:[l.jsxs(Vt,{children:[l.jsx(Gt,{children:"Default"}),sd.map(n=>l.jsx(qt,{value:n.value,children:n.name},n.name))]}),l.jsx(oi,{}),l.jsxs(Vt,{children:[l.jsx(Gt,{children:"Scaled"}),id.map(n=>l.jsx(qt,{value:n.value,children:n.name},n.name))]}),l.jsxs(Vt,{children:[l.jsx(Gt,{children:"Monospaced"}),cd.map(n=>l.jsx(qt,{value:n.value,children:n.name},n.name))]})]})]})]})}function ud(){const{setTheme:e,resolvedTheme:t}=Yi(),r=R.useCallback(n=>{const a=t==="dark"?"light":"dark",o=document.documentElement;if(!document.startViewTransition){e(a);return}n&&(o.style.setProperty("--x",`${n.clientX}px`),o.style.setProperty("--y",`${n.clientY}px`)),document.startViewTransition(()=>{e(a)})},[t,e]);return l.jsxs(At,{variant:"secondary",size:"icon",className:"group/toggle size-8",onClick:r,children:[l.jsx(dl,{}),l.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}function dd(){return l.jsxs("header",{role:"banner",className:"sticky top-0 z-50 bg-background shadow flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12",children:[l.jsxs("div",{className:"flex items-center gap-2 px-4",children:[l.jsx(Xc,{className:"-ml-1"}),l.jsx(dc,{orientation:"vertical",className:"mr-2 h-4"}),l.jsx(Nl,{})]}),l.jsxs("div",{className:"flex items-center gap-2 px-4",children:[l.jsx("div",{className:"hidden md:flex",children:l.jsx(od,{})}),l.jsx(ud,{}),l.jsx(ld,{})]})]})}function fd({children:e,policy:t,message:r}){const n=ot.get("sidebar_state")==="true",{activeTheme:a}=xa(),o=a.endsWith("-scaled");return l.jsxs("div",{className:$("bg-background font-sans antialiased",`theme-${a}`,o?"theme-scaled":""),children:[l.jsx(va,{}),l.jsxs(Yc,{defaultOpen:n,children:[l.jsx(Al,{}),l.jsxs(Zc,{children:[l.jsx(dd,{}),l.jsx("div",{className:"flex flex-1 flex-col",children:l.jsx("div",{className:"@container/main flex flex-1 flex-col gap-2",children:l.jsx("div",{className:"flex flex-col gap-4 py-4 px-4 md:gap-6 md:py-4 md:px-6",children:t!==void 0?l.jsx(rc,{policy:t,message:r,children:e}):e})})})]})]})]})}function kf(e){return l.jsx(R.StrictMode,{children:l.jsx(Ui,{children:l.jsx(Qi,{defaultTheme:"system",storageKey:"vite-ui-theme",children:l.jsxs(ec,{children:[l.jsx(va,{}),l.jsx(fd,{...e})]})})})})}export{kd as $,kf as A,At as B,Xd as C,$i as D,tf as E,ef as F,$ as G,ta as H,qt as I,sf as J,af as K,ei as L,of as M,cf as N,lf as O,uf as P,Yo as Q,Tf as R,ti as S,va as T,nf as U,Ef as V,Fd as W,jr as X,Pd as Y,Nd as Z,xr as _,ga as a,md as a0,bd as a1,gd as a2,Yc as a3,Qc as a4,rl as a5,nl as a6,Pf as a7,dr as a8,St as a9,Ls as aA,oe as aB,ma as aC,Ml as aD,df as aE,jd as aF,Sd as aG,Cd as aH,Td as aI,Md as aJ,Ad as aK,Rd as aL,Id as aM,Od as aN,Ed as aO,Ct as aa,rc as ab,Mf as ac,Us as ad,hf as ae,ff as af,Ld as ag,Dd as ah,zd as ai,pf as aj,gf as ak,bf as al,$d as am,Bd as an,vf as ao,xf as ap,yf as aq,mf as ar,wf as as,_r as at,rf as au,_f as av,jf as aw,Cf as ax,Sf as ay,Of as az,_d as b,Fi as c,xd as d,Ki as e,gt as f,wd as g,Ge as h,qd as i,Ud as j,ni as k,ri as l,ai as m,Rf as n,Gd as o,yd as p,Kd as q,Vd as r,Wd as s,rs as t,ns as u,Hd as v,Qd as w,Yd as x,Zd as y,Jd as z};
//# sourceMappingURL=app-layout-D_A4XD_6.js.map
