import { getApiOpeniddictResourcesAvailableResources } from '@/client'
import { useQuery } from '@tanstack/react-query'
import { QueryNames } from './QueryConstants'

export const useOpeniddictResourceSelect = () => {
  return useQuery({
    queryKey: [QueryNames.GetOpeniddictResourcesAvailableResources],
    queryFn: async () => {
      const response = await getApiOpeniddictResourcesAvailableResources()
      return response.data?.data
    },
  })
}
