{"version": 3, "file": "claim-lPDCWpo5.js", "sources": ["../../../../../frontend/src/components/app/claims/Delete.tsx", "../../../../../frontend/src/lib/hooks/useIdentityClaimTypes.ts", "../../../../../frontend/src/components/app/claims/variable.ts", "../../../../../frontend/src/components/app/claims/Add.tsx", "../../../../../frontend/src/components/app/claims/Actions.tsx", "../../../../../frontend/src/components/app/claims/Columns.tsx", "../../../../../frontend/src/components/app/claims/Edit.tsx", "../../../../../frontend/src/components/app/claims/List.tsx", "../../../../../frontend/src/pages/claim.tsx"], "sourcesContent": ["import { deleteApiIdentityClaimTypesById } from '@/client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\nimport { useEffect, useState } from 'react'\r\n\r\ntype DeleteUserProps = {\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\nexport const Delete = ({ dataId, onDismiss }: DeleteUserProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiIdentityClaimTypesById({\r\n        path: { id: dataId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `Claim Type has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your this claim type.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "import { postApiIdentityClaimTypesList } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { extractApiError } from '@/lib/query-utils'\r\nimport { QueryNames } from './QueryConstants'\r\nimport { generateExtendedQueryParameters } from '@/lib/query-utils-extended'\r\nimport { type FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\nimport { toast } from '@/lib/useToast'\r\n\r\nexport const useIdentityClaimTypes = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filterConditions: FilterCondition[] = [],\r\n  sorting?: string  \r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetIdentityClaimTypes,  pageIndex, pageSize, JSON.stringify(filterConditions), sorting],\r\n    queryFn: async () => {\r\n      try {\r\n        // Generate query parameters using the extended utility function\r\n        const body = generateExtendedQueryParameters({\r\n          pageIndex,\r\n          pageSize,\r\n          sorting,\r\n          filterConditions,\r\n        })\r\n\r\n        const response = await postApiIdentityClaimTypesList({\r\n          body\r\n        })\r\n\r\n        return response.data?.data\r\n      } catch (error) {\r\n       // Use the error extraction utility\r\n       const { title, description } = extractApiError(error, 'Error loading clients')\r\n\r\n       // Show toast notification\r\n       toast({\r\n         title,\r\n         description,\r\n         variant: 'destructive',\r\n       })\r\n        // Return empty data to prevent UI crashes\r\n        return { items: [], totalCount: 0 }\r\n      }\r\n    },\r\n  })\r\n}\r\n", "export const itemValueType = [\r\n  {\r\n    label: 'String',\r\n    value: '0',\r\n  },\r\n  {\r\n    label: 'Int',\r\n    value: '1',\r\n  },\r\n  {\r\n    label: 'Boolean',\r\n    value: '2',\r\n  },\r\n  {\r\n    label: 'DateTime',\r\n    value: '3',\r\n  },\r\n]\r\n", "'use client'\r\nimport { type CreateUpdateClaimTypeDto, postApiIdentityClaimTypes, type RemoteServiceErrorResponse } from '@/client'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  <PERSON>alog,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { MultiSelect } from '@/components/ui/multi-select'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { RiAddLine } from '@remixicon/react'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { itemValueType } from './variable'\r\n\r\nexport type AddClientProps = {\r\n  children?: React.ReactNode\r\n}\r\n\r\nexport const Add = ({ children }: AddClientProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, reset } = useForm<CreateUpdateClaimTypeDto>()\r\n  const [selectedValueType, setSelectedValueType] = useState<string[]>([])\r\n\r\n  // Reset form when dialog is closed\r\n  useEffect(() => {\r\n    if (!open) {\r\n      reset({\r\n        name: '',\r\n        required: false,\r\n        isStatic: false,\r\n        regex: '',\r\n        regexDescription: '',\r\n        description: '',\r\n        valueType: 0\r\n      })\r\n      setSelectedValueType([])\r\n    }\r\n  }, [open, reset])\r\n\r\n\r\n  const createDataMutation = useMutation({\r\n    mutationFn: async (dataMutation: CreateUpdateClaimTypeDto) =>\r\n      postApiIdentityClaimTypes({\r\n        body: dataMutation\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Claim Type Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n      setOpen(false)\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateClaimTypeDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: CreateUpdateClaimTypeDto = {\r\n      ...formData,\r\n    }\r\n\r\n    // Explicitly mark the promise as handled\r\n    void createDataMutation.mutate(userData)\r\n  }\r\n\r\n  const handleOpenChange = (newOpenState: boolean) => {\r\n    setOpen(newOpenState)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={handleOpenChange}>\r\n        <DialogTrigger asChild>{children}</DialogTrigger>\r\n        <section className=\"flex items-center justify-between pb-5\">\r\n          {can('IdentityServer.ClaimTypes.Create') && (\r\n            <Button size='sm' className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\" onClick={() => setOpen(true)}>\r\n              <RiAddLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"hidden truncate sm:inline\">New Claim Type</span>\r\n            </Button>\r\n          )}\r\n        </section>\r\n        <DialogContent className='max-w-2xl'>\r\n          <DialogHeader>\r\n            <DialogTitle>Create a New Claim Type</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2' onKeyDown={(e) => {\r\n            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {\r\n              e.preventDefault();\r\n              void handleSubmit(onSubmit)();\r\n            }\r\n          }}>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The name of the claim\"\r\n                >\r\n                  <Input required {...register('name')} placeholder=\"Claim Name\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Required\"\r\n                  description=\"Whether the claim is required\"\r\n                >\r\n                  <Checkbox {...register('required', {\r\n                    setValueAs: (value) => value === true\r\n                  })} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Is Static\"\r\n                  description=\"Whether the claim is static\"\r\n                >\r\n                  <Checkbox {...register('isStatic', {\r\n                    setValueAs: (value) => value === true\r\n                  })} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Regex\"\r\n                  description=\"The regex for the claim\"\r\n                >\r\n                  <Input {...register('regex')} placeholder=\"Regex\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Regex Description\"\r\n                  description=\"The description for the regex\"\r\n                >\r\n                  <Input {...register('regexDescription')} placeholder=\"Regex Description\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Description\"\r\n                  description=\"The description for the claim\"\r\n                >\r\n                  <Input required {...register('description')} placeholder=\"Description\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Value Type\"\r\n                  description=\"The value type for the claim\"\r\n                >\r\n                  <MultiSelect\r\n                    mode='single'\r\n                    options={itemValueType}\r\n                    value={selectedValueType}\r\n                    onChange={setSelectedValueType}\r\n                    placeholder=\"Select value type\"\r\n                    maxHeight={300}\r\n                    name=\"valueType\"\r\n                    register={register}\r\n                    valueAsNumber={true}\r\n                  />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createDataMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createDataMutation.isPending}>\r\n                {createDataMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type IdentityClaimTypeDto } from '@/client'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { RiMoreLine, RiPencilLine, RiShieldKeyholeLine } from '@remixicon/react'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\n\r\ntype ClaimActionProps = {\r\n  userId: string\r\n  userDto: IdentityClaimTypeDto\r\n  onAction: (dataId: string, dataEdit: IdentityClaimTypeDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n  variant?: 'dropdown' | 'buttons'\r\n}\r\n\r\nexport const Actions = ({ userId, userDto, onAction, variant = 'dropdown' }: ClaimActionProps) => {\r\n  const { can } = useGrantedPolicies()\r\n\r\n  // For dropdown menu style (first image)\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <div className=\"flex justify-end\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 w-8 p-0\"\r\n            >\r\n              <RiMoreLine className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Open menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"w-[160px]\">\r\n            {can('IdentityServer.ClaimTypes.Edit') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'edit')}\r\n              >\r\n                Edit\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('IdentityServer.ClaimTypes.Delete') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm text-red-500\"\r\n                onClick={() => onAction(userId, userDto, 'delete')}\r\n              >\r\n                Delete\r\n              </DropdownMenuItem>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // For button group style (second image)\r\n  return (\r\n    <div className=\"flex items-center justify-end gap-1\">\r\n      {can('AbpIdentity.Users.ManagePermissions') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'permission')}\r\n        >\r\n          <RiShieldKeyholeLine className=\"h-4 w-4\" />\r\n          <span>Permission</span>\r\n        </Button>\r\n      )}\r\n      {can('AbpIdentity.Users.Update') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'edit')}\r\n        >\r\n          <RiPencilLine className=\"h-4 w-4\" />\r\n          <span>Edit</span>\r\n        </Button>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type IdentityClaimTypeDto } from '@/client'\r\nimport { type ColumnDef } from '@tanstack/react-table'\r\nimport { Actions } from './Actions'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'\r\nimport { customFilterFunction } from '@/components/data-table/filterFunctions'\r\n\r\n// Type for the callback function to handle claim type actions\r\ntype ClaimActionCallback = (dataId: string, dataEdit: IdentityClaimTypeDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n\r\n// Function to create columns with the action callback\r\nexport const getColumns = (\r\n  handleUserAction: ClaimActionCallback\r\n): ColumnDef<IdentityClaimTypeDto>[] => {\r\n  return [\r\n    {\r\n      id: \"select\",\r\n      header: ({ table }) => (\r\n        <Checkbox\r\n          checked={\r\n            table.getIsAllPageRowsSelected()\r\n              ? true\r\n              : table.getIsSomeRowsSelected()\r\n                ? \"indeterminate\"\r\n                : false\r\n          }\r\n          onCheckedChange={() => table.toggleAllPageRowsSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select all\"\r\n        />\r\n      ),\r\n      cell: ({ row }) => (\r\n        <Checkbox\r\n          checked={row.getIsSelected()}\r\n          onCheckedChange={() => row.toggleSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select row\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: true,\r\n      meta: {\r\n        displayName: \"Select\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"name\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Name\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      filterFn: customFilterFunction,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Name\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"description\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Description\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      filterFn: customFilterFunction,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Description\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"valueType\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Value Type\" />\r\n      ),\r\n      cell: ({ row }) => {\r\n        const valueType = row.getValue(\"valueType\");\r\n        const valueTypeMap = {\r\n          0: \"String\",\r\n          1: \"Int\",\r\n          2: \"Boolean\",\r\n          3: \"DateTime\"\r\n        };\r\n        return valueTypeMap[valueType as keyof typeof valueTypeMap] || \"Unknown\";\r\n      },\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Value Type\",\r\n      },\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      header: \"Actions\",\r\n      cell: (info) => (\r\n        <Actions\r\n          userId={info.row.original.id!}\r\n          userDto={info.row.original}\r\n          onAction={handleUserAction}\r\n          variant=\"dropdown\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: true,\r\n      meta: {\r\n        className: \"text-right\",\r\n        displayName: \"Action\",\r\n      },\r\n    }\r\n  ];\r\n}\r\n", "import { type CreateUpdateClaimTypeDto, type IdentityClaimTypeDto, putApiIdentityClaimTypesById, type RemoteServiceErrorResponse } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Input } from '@/components/ui/input'\r\nimport { MultiSelect } from '@/components/ui/multi-select'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { itemValueType } from './variable'\r\n\r\ntype UserEditProps = {\r\n  dataEdit: IdentityClaimTypeDto\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\nexport const Edit = ({ dataEdit, dataId, onDismiss }: UserEditProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, setValue } = useForm<CreateUpdateClaimTypeDto>()\r\n  const [isRequired, setIsRequired] = useState(dataEdit.required ?? false)\r\n  const [isStatic, setIsStatic] = useState(dataEdit.isStatic ?? false)\r\n  const [selectedValueType, setSelectedValueType] = useState<string[]>([])\r\n\r\n  const updateDataMutation = useMutation({\r\n    mutationFn: async (formData: CreateUpdateClaimTypeDto) =>\r\n      putApiIdentityClaimTypesById({\r\n        path: { id: dataId },\r\n        body: formData,\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Claim Type Updated Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n      onCloseEvent()\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateClaimTypeDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: CreateUpdateClaimTypeDto = {\r\n      ...formData,\r\n    }\r\n\r\n    // Explicitly mark the promise as handled\r\n    void updateDataMutation.mutate(userData)\r\n  }\r\n\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  // Initialize form when opened\r\n  useEffect(() => {\r\n    if (open) {\r\n      // Initialize form values from dataEdit\r\n      setValue('name', dataEdit.name ?? '')\r\n      setValue('required', dataEdit.required ?? false)\r\n      setValue('isStatic', dataEdit.isStatic ?? false)\r\n      setValue('regex', dataEdit.regex ?? '')\r\n      setValue('regexDescription', dataEdit.regexDescription ?? '')\r\n      setValue('description', dataEdit.description ?? '')\r\n      setValue('valueType', dataEdit.valueType ?? 0)\r\n\r\n      // Initialize MultiSelect value\r\n      const valueTypeStr = (dataEdit.valueType !== undefined && dataEdit.valueType !== null)\r\n        ? dataEdit.valueType.toString()\r\n        : '0'\r\n      setSelectedValueType([valueTypeStr])\r\n    }\r\n  }, [open, dataEdit, setValue])\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent>\r\n        <DialogHeader>\r\n          <DialogTitle>Update a Claim Type: {dataEdit.name}</DialogTitle>\r\n        </DialogHeader>\r\n        <form\r\n          onSubmit={handleSubmit(onSubmit)}\r\n          className='mt-2'\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {\r\n              e.preventDefault();\r\n              void handleSubmit(onSubmit)();\r\n            }\r\n          }}\r\n        >\r\n          <section className=\"flex w-full flex-col space-y-2\">\r\n            <FormSection>\r\n              <FormField\r\n                label=\"Name\"\r\n                description=\"The name of the claim\"\r\n              >\r\n                <Input required {...register('name')} defaultValue={dataEdit.name ?? ''} placeholder=\"Claim Name\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Required\"\r\n                description=\"Whether the claim is required\"\r\n              >\r\n                <Checkbox\r\n                  checked={isRequired}\r\n                  onCheckedChange={(checked) => {\r\n                    setIsRequired(!!checked)\r\n                    setValue('required', !!checked)\r\n                  }}\r\n                />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Is Static\"\r\n                description=\"Whether the claim is static\"\r\n              >\r\n                <Checkbox\r\n                  checked={isStatic}\r\n                  onCheckedChange={(checked) => {\r\n                    setIsStatic(!!checked)\r\n                    setValue('isStatic', !!checked)\r\n                  }}\r\n                />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Regex\"\r\n                description=\"The regex for the claim\"\r\n              >\r\n                <Input {...register('regex')} defaultValue={dataEdit.regex ?? ''} placeholder=\"Regex\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Regex Description\"\r\n                description=\"The description for the regex\"\r\n              >\r\n                <Input {...register('regexDescription')} defaultValue={dataEdit.regexDescription ?? ''} placeholder=\"Regex Description\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Description\"\r\n                description=\"The description for the claim\"\r\n              >\r\n                <Input required {...register('description')} defaultValue={dataEdit.description ?? ''} placeholder=\"Description\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Value Type\"\r\n                description=\"The value type for the claim\"\r\n              >\r\n                <MultiSelect\r\n                  mode='single'\r\n                  options={itemValueType}\r\n                  value={selectedValueType}\r\n                  onChange={setSelectedValueType}\r\n                  placeholder=\"Select value type\"\r\n                  maxHeight={300}\r\n                  name=\"valueType\"\r\n                  register={register}\r\n                  valueAsNumber={true}\r\n                />\r\n              </FormField>\r\n            </FormSection>\r\n          </section>\r\n          <DialogFooter className=\"mt-5\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              onClick={(e) => {\r\n                e.preventDefault()\r\n                setOpen(false)\r\n              }}\r\n              disabled={updateDataMutation.isPending}\r\n              type=\"button\"\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={updateDataMutation.isPending}\r\n            >\r\n              {updateDataMutation.isPending ? 'Saving...' : 'Save'}\r\n            </Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "'use client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useState } from 'react'\r\n\r\nimport { type IdentityClaimTypeDto } from '@/client'\r\nimport { type PaginationState, type SortingState } from '@tanstack/react-table'\r\n\r\nimport { useToast } from '@/lib/useToast'\r\nimport { Delete } from './Delete'\r\n\r\nimport { DataTable } from '@/components/data-table/DataTable'\r\nimport { NotionFilter } from '@/components/data-table/NotionFilter'\r\nimport { TableSkeleton } from '@/components/ui/TableSkeleton'\r\nimport { useIdentityClaimTypes } from '@/lib/hooks/useIdentityClaimTypes'\r\nimport { type FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Add } from './Add'\r\nimport { getColumns } from './Columns'\r\nimport { Edit } from './Edit'\r\n\r\nexport const ClaimList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  const [searchStr, setSearchStr] = useState<string>('')\r\n  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])\r\n  const [userActionDialog, setUserActionDialog] = useState<{\r\n    dataId: string\r\n    dataEdit: IdentityClaimTypeDto\r\n    dialogType?: 'edit' | 'permission' | 'delete'\r\n  } | null>()\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n\r\n  // Initialize sorting state\r\n  const [sorting, setSorting] = useState<SortingState>([\r\n    { id: 'name', desc: false }\r\n  ])\r\n\r\n  const { isLoading, data } = useIdentityClaimTypes(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    filterConditions\r\n  )\r\n\r\n  // Handler for user actions (edit, permission, delete)\r\n  const handleUserAction = (dataId: string, dataEdit: IdentityClaimTypeDto, dialogType: 'edit' | 'permission' | 'delete') => {\r\n    setUserActionDialog({\r\n      dataId,\r\n      dataEdit,\r\n      dialogType,\r\n    })\r\n  }\r\n\r\n  // Get columns with the action handler\r\n  const columns = getColumns(handleUserAction)\r\n\r\n  const handleSearch = (value: string) => {\r\n    // Always update the search string for UI consistency\r\n    setSearchStr(value)\r\n\r\n    // Create a search filter condition if there's a search value\r\n    // First, remove any existing name filter\r\n    const existingFilters = filterConditions.filter(fc => fc.fieldName !== 'name')\r\n    const newFilterConditions = [...existingFilters]\r\n\r\n    // Only add the search filter if there's a value\r\n    if (value) {\r\n      newFilterConditions.push({\r\n        fieldName: 'name',\r\n        operator: 'Contains',\r\n        value: value\r\n      })\r\n    }\r\n\r\n    // Only update state if filters have changed\r\n    const currentFiltersStr = JSON.stringify(filterConditions)\r\n    const newFiltersStr = JSON.stringify(newFilterConditions)\r\n\r\n    if (currentFiltersStr !== newFiltersStr) {\r\n      setFilterConditions(newFilterConditions)\r\n      setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search\r\n    }\r\n  }\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination)\r\n  }\r\n\r\n  // Handler for sorting change\r\n  const handleSortingChange = (newSorting: SortingState) => {\r\n    setSorting(newSorting)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change\r\n  }\r\n\r\n  // Handler for refreshing the data\r\n  const handleRefresh = () => {\r\n    // Invalidate the query to fetch fresh data\r\n    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n    // Show toast notification after a short delay to match the animation\r\n    setTimeout(() => {\r\n      toast({\r\n        title: \"Data refreshed\",\r\n        description: \"The claims list has been refreshed.\",\r\n        variant: \"success\",\r\n      })\r\n    }, 100)\r\n  }\r\n\r\n  if (isLoading) return (\r\n    <TableSkeleton\r\n      rowCount={pagination.pageSize}\r\n      columnCount={4}\r\n      hasTitle={true}\r\n      hasSearch={true}\r\n      hasFilters={true}\r\n      hasPagination={true}\r\n      hasActions={true}\r\n    />\r\n  )\r\n\r\n  // Ensure we have valid data to render\r\n  const items = data?.items ?? [];\r\n  const totalCount = data?.totalCount ?? 0;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4\">\r\n        <DataTable\r\n          title=\"Claims Types\"\r\n          columns={columns}\r\n          data={items}\r\n          totalCount={totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          manualSorting={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={handlePaginationChange}\r\n          onSortingChange={handleSortingChange}\r\n          sortingState={sorting}\r\n          onSearch={handleSearch}\r\n          searchValue={searchStr}\r\n          customFilterbar={(props) => (\r\n            <NotionFilter\r\n              {...props}\r\n              activeFilters={filterConditions}\r\n              onServerFilter={(conditions) => {\r\n                // Only update if the conditions have actually changed\r\n                const currentStr = JSON.stringify(filterConditions);\r\n                const newStr = JSON.stringify(conditions);\r\n\r\n                if (currentStr !== newStr) {\r\n                  setFilterConditions(conditions)\r\n                  setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on filter change\r\n                }\r\n              }}\r\n            />\r\n          )}\r\n          hideDefaultFilterbar={true}\r\n          onRefresh={handleRefresh}\r\n          enableRowSelection={false}\r\n          actionButton={{\r\n            // label: \"Create New User\",\r\n            // We need to provide an onClick handler even though we're using a custom component\r\n            onClick: () => { /* Required but not used */ },\r\n            content: <Add />\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {userActionDialog && userActionDialog.dialogType === 'edit' && (\r\n        <Edit\r\n          dataId={userActionDialog.dataId}\r\n          dataEdit={userActionDialog.dataEdit}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'delete' && (\r\n        <Delete\r\n          dataId={userActionDialog.dataId}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { ClaimList } from '@/components/app/claims/List';\r\nimport AppLayout from '../layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Claim\" />\r\n\r\n      <ClaimList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["Delete", "dataId", "on<PERSON><PERSON><PERSON>", "toast", "useToast", "open", "<PERSON><PERSON><PERSON>", "useState", "onYesEvent", "deleteApiIdentityClaimTypesById", "err", "error", "handleApiError", "useEffect", "jsx", "AlertDialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "useIdentityClaimTypes", "pageIndex", "pageSize", "filterConditions", "sorting", "useQuery", "QueryNames", "body", "generateExtendedQueryParameters", "postApiIdentityClaimTypesList", "title", "description", "extractApiError", "itemValueType", "Add", "children", "can", "useGrantedPolicies", "queryClient", "useQueryClient", "handleSubmit", "register", "reset", "useForm", "selectedValueType", "setSelectedValueType", "createDataMutation", "useMutation", "dataMutation", "postApiIdentityClaimTypes", "onSubmit", "formData", "userData", "handleOpenChange", "newOpenState", "Toaster", "Dialog", "DialogTrigger", "<PERSON><PERSON>", "RiAddLine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "e", "FormSection", "FormField", "Input", "Checkbox", "value", "MultiSelect", "<PERSON><PERSON><PERSON><PERSON>er", "Actions", "userId", "userDto", "onAction", "variant", "DropdownMenu", "DropdownMenuTrigger", "RiMoreLine", "DropdownMenuContent", "DropdownMenuItem", "RiShieldKeyholeLine", "RiPencilLine", "getColumns", "handleUserAction", "table", "row", "column", "DataTableColumnHeader", "info", "customFilterFunction", "valueType", "Edit", "dataEdit", "setValue", "isRequired", "setIsRequired", "isStatic", "setIsStatic", "updateDataMutation", "putApiIdentityClaimTypesById", "onCloseEvent", "valueTypeStr", "checked", "ClaimList", "searchStr", "setSearchStr", "setFilterConditions", "userActionDialog", "setUserActionDialog", "pagination", "setPagination", "setSorting", "isLoading", "data", "columns", "dialogType", "handleSearch", "newFilterConditions", "fc", "currentFiltersStr", "newFiltersStr", "prev", "handlePaginationChange", "newPagination", "handleSortingChange", "newSorting", "handleRefresh", "TableSkeleton", "items", "totalCount", "Fragment", "DataTable", "props", "NotionFilter", "conditions", "currentStr", "newStr", "OverViewLayout", "AppLayout", "Head"], "mappings": "47BAmBO,MAAMA,GAAS,CAAC,CAAE,OAAAC,EAAQ,UAAAC,KAAiC,CAC1D,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EACzCC,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,EAAgC,CACpC,KAAM,CAAE,GAAIR,CAAO,CAAA,CACpB,EACKE,EAAA,CACJ,MAAO,UACP,YAAa,2CAAA,CACd,EACSD,EAAA,QACHQ,EAAc,CACf,MAAAC,EAAQC,GAAeF,CAAG,EAC1BP,EAAA,CACJ,MAAOQ,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CAEL,EAEAE,OAAAA,EAAAA,UAAU,IAAM,CACdP,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFQ,EAAA,IAAAC,GAAA,CAAY,KAAAV,EACX,SAAAW,OAACC,GACC,CAAA,SAAA,CAAAD,OAACE,GACC,CAAA,SAAA,CAAAJ,EAAAA,IAACK,IAAiB,SAAwB,0BAAA,CAAA,EAC1CL,EAAAA,IAACM,IAAuB,SAExB,kFAAA,CAAA,CAAA,EACF,SACCC,GACC,CAAA,SAAA,CAACP,EAAA,IAAAQ,GAAA,CAAkB,QAASpB,EAAW,SAAM,SAAA,EAC5CY,EAAA,IAAAS,GAAA,CAAkB,QAASf,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,ECtDagB,GAAwB,CACnCC,EACAC,EACAC,EAAsC,CAAA,EACtCC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,sBAAwBL,EAAWC,EAAU,KAAK,UAAUC,CAAgB,EAAGC,CAAO,EAC5G,QAAS,SAAY,CACf,GAAA,CAEF,MAAMG,EAAOC,GAAgC,CAC3C,UAAAP,EACA,SAAAC,EACA,QAAAE,EACA,iBAAAD,CAAA,CACD,EAMD,OAJiB,MAAMM,EAA8B,CACnD,KAAAF,CAAA,CACD,GAEe,MAAM,WACfpB,EAAO,CAEf,KAAM,CAAE,MAAAuB,EAAO,YAAAC,CAAA,EAAgBC,GAAgBzB,EAAO,uBAAuB,EAGvE,OAAAR,GAAA,CACJ,MAAA+B,EACA,YAAAC,EACA,QAAS,aAAA,CACV,EAEO,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CACpC,CACF,CACD,EC7CUE,EAAgB,CAC3B,CACE,MAAO,SACP,MAAO,GACT,EACA,CACE,MAAO,MACP,MAAO,GACT,EACA,CACE,MAAO,UACP,MAAO,GACT,EACA,CACE,MAAO,WACP,MAAO,GAAA,CAEX,ECYaC,GAAM,CAAC,CAAE,SAAAC,KAA+B,CAC7C,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAC7B,CAACpC,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBsC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,MAAAC,CAAA,EAAUC,EAAkC,EACtE,CAACC,EAAmBC,CAAoB,EAAI1C,EAAAA,SAAmB,CAAA,CAAE,EAGvEM,EAAAA,UAAU,IAAM,CACTR,IACGyC,EAAA,CACJ,KAAM,GACN,SAAU,GACV,SAAU,GACV,MAAO,GACP,iBAAkB,GAClB,YAAa,GACb,UAAW,CAAA,CACZ,EACDG,EAAqB,CAAA,CAAE,EACzB,EACC,CAAC5C,EAAMyC,CAAK,CAAC,EAGhB,MAAMI,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBC,GAA0B,CACxB,KAAMD,CAAA,CACP,EACH,UAAW,IAAM,CACTjD,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EACIuC,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,qBAAqB,EAAG,EACnFxB,EAAQ,EAAK,CACf,EACA,QAAUI,GAAoC,CACtCP,EAAA,CACJ,MAAOO,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEK4C,EAAYC,GAAuC,CAEvD,MAAMC,EAAqC,CACzC,GAAGD,CACL,EAGKL,EAAmB,OAAOM,CAAQ,CACzC,EAEMC,EAAoBC,GAA0B,CAClDpD,EAAQoD,CAAY,CACtB,EAEA,cACG,UACC,CAAA,SAAA,CAAA5C,EAAA,IAAC6C,GAAQ,EAAA,EACR3C,EAAA,KAAA4C,EAAA,CAAO,KAAAvD,EAAY,aAAcoD,EAChC,SAAA,CAAC3C,EAAAA,IAAA+C,GAAA,CAAc,QAAO,GAAE,SAAAtB,CAAS,CAAA,QAChC,UAAQ,CAAA,UAAU,yCAChB,SAAAC,EAAI,kCAAkC,GACrCxB,OAAC8C,EAAO,CAAA,KAAK,KAAK,UAAU,kCAAkC,QAAS,IAAMxD,EAAQ,EAAI,EACvF,SAAA,CAAAQ,EAAA,IAACiD,GAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/DjD,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAc,gBAAA,CAAA,CAAA,CAAA,CAC5D,CAEJ,CAAA,EACAE,EAAAA,KAACgD,EAAc,CAAA,UAAU,YACvB,SAAA,CAAAlD,MAACmD,EACC,CAAA,SAAAnD,EAAA,IAACoD,EAAY,CAAA,SAAA,yBAAuB,CAAA,EACtC,EACAlD,EAAAA,KAAC,OAAK,CAAA,SAAU4B,EAAaU,CAAQ,EAAG,UAAU,OAAO,UAAYa,GAAM,CACrEA,EAAE,MAAQ,SAAWA,EAAE,kBAAkB,mBAC3CA,EAAE,eAAe,EACZvB,EAAaU,CAAQ,EAAE,EAG9B,EAAA,SAAA,CAAAxC,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAACoD,EACC,CAAA,SAAA,CAAAtD,EAAA,IAACuD,EAAA,CACC,MAAM,OACN,YAAY,wBAEZ,SAAAvD,EAAA,IAACwD,GAAM,SAAQ,GAAE,GAAGzB,EAAS,MAAM,EAAG,YAAY,YAAa,CAAA,CAAA,CACjE,EAEA/B,EAAA,IAACuD,EAAA,CACC,MAAM,WACN,YAAY,gCAEZ,SAACvD,EAAAA,IAAAyD,EAAA,CAAU,GAAG1B,EAAS,WAAY,CACjC,WAAa2B,GAAUA,IAAU,EAAA,CAClC,CAAG,CAAA,CAAA,CACN,EAEA1D,EAAA,IAACuD,EAAA,CACC,MAAM,YACN,YAAY,8BAEZ,SAACvD,EAAAA,IAAAyD,EAAA,CAAU,GAAG1B,EAAS,WAAY,CACjC,WAAa2B,GAAUA,IAAU,EAAA,CAClC,CAAG,CAAA,CAAA,CACN,EAEA1D,EAAA,IAACuD,EAAA,CACC,MAAM,QACN,YAAY,0BAEZ,eAACC,EAAO,CAAA,GAAGzB,EAAS,OAAO,EAAG,YAAY,OAAQ,CAAA,CAAA,CACpD,EAEA/B,EAAA,IAACuD,EAAA,CACC,MAAM,oBACN,YAAY,gCAEZ,eAACC,EAAO,CAAA,GAAGzB,EAAS,kBAAkB,EAAG,YAAY,mBAAoB,CAAA,CAAA,CAC3E,EAEA/B,EAAA,IAACuD,EAAA,CACC,MAAM,cACN,YAAY,gCAEZ,SAAAvD,EAAA,IAACwD,GAAM,SAAQ,GAAE,GAAGzB,EAAS,aAAa,EAAG,YAAY,aAAc,CAAA,CAAA,CACzE,EAEA/B,EAAA,IAACuD,EAAA,CACC,MAAM,aACN,YAAY,+BAEZ,SAAAvD,EAAA,IAAC2D,EAAA,CACC,KAAK,SACL,QAASpC,EACT,MAAOW,EACP,SAAUC,EACV,YAAY,oBACZ,UAAW,IACX,KAAK,YACL,SAAAJ,EACA,cAAe,EAAA,CAAA,CACjB,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACA7B,EAAAA,KAAC0D,EAAa,CAAA,UAAU,OACtB,SAAA,CAAA5D,EAAA,IAACgD,EAAA,CACC,QAAQ,QACR,QAAUK,GAAM,CACdA,EAAE,eAAe,EACjB7D,EAAQ,EAAK,CACf,EACA,SAAU4C,EAAmB,UAC9B,SAAA,QAAA,CAED,EACApC,EAAAA,IAACgD,EAAO,CAAA,KAAK,SAAS,SAAUZ,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECpLayB,GAAU,CAAC,CAAE,OAAAC,EAAQ,QAAAC,EAAS,SAAAC,EAAU,QAAAC,EAAU,cAAmC,CAC1F,KAAA,CAAE,IAAAvC,CAAI,EAAIC,EAAmB,EAGnC,OAAIsC,IAAY,WAEXjE,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,gBAACkE,GACC,CAAA,SAAA,CAAClE,EAAAA,IAAAmE,GAAA,CAAoB,QAAO,GAC1B,SAAAjE,EAAA,KAAC8C,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cAEV,SAAA,CAAChD,EAAAA,IAAAoE,GAAA,CAAW,UAAU,SAAU,CAAA,EAC/BpE,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEvC,EACCE,EAAA,KAAAmE,GAAA,CAAoB,MAAM,MAAM,UAAU,YACxC,SAAA,CAAA3C,EAAI,gCAAgC,GACnC1B,EAAA,IAACsE,EAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,MAAM,EAChD,SAAA,MAAA,CAED,EAEDrC,EAAI,kCAAkC,GACrC1B,EAAA,IAACsE,EAAA,CACC,UAAU,sCACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,QAAQ,EAClD,SAAA,QAAA,CAAA,CAED,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAMF7D,EAAA,KAAC,MAAI,CAAA,UAAU,sCACZ,SAAA,CAAAwB,EAAI,qCAAqC,GACxCxB,EAAA,KAAC8C,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMgB,EAASF,EAAQC,EAAS,YAAY,EAErD,SAAA,CAAC/D,EAAAA,IAAAuE,GAAA,CAAoB,UAAU,SAAU,CAAA,EACzCvE,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,EAED0B,EAAI,0BAA0B,GAC7BxB,EAAA,KAAC8C,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMgB,EAASF,EAAQC,EAAS,MAAM,EAE/C,SAAA,CAAC/D,EAAAA,IAAAwE,GAAA,CAAa,UAAU,SAAU,CAAA,EAClCxE,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,CAAA,CAAA,CACZ,EAEJ,CAEJ,EC3EayE,GACXC,GAEO,CACL,CACE,GAAI,SACJ,OAAQ,CAAC,CAAE,MAAAC,CAAA,IACT3E,EAAA,IAACyD,EAAA,CACC,QACEkB,EAAM,2BACF,GACAA,EAAM,sBAAA,EACJ,gBACA,GAER,gBAAiB,IAAMA,EAAM,0BAA0B,EACvD,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,KAAM,CAAC,CAAE,IAAAC,CAAA,IACP5E,EAAA,IAACyD,EAAA,CACC,QAASmB,EAAI,cAAc,EAC3B,gBAAiB,IAAMA,EAAI,eAAe,EAC1C,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,YAAa,QAAA,CAEjB,EACA,CACE,YAAa,OACb,OAAQ,CAAC,CAAE,OAAAC,CAAA,IACR7E,EAAA,IAAA8E,EAAA,CAAsB,OAAAD,EAAgB,MAAM,OAAO,EAEtD,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,SAAUC,EACV,KAAM,CACJ,UAAW,YACX,YAAa,MAAA,CAEjB,EACA,CACE,YAAa,cACb,OAAQ,CAAC,CAAE,OAAAH,CAAA,IACR7E,EAAA,IAAA8E,EAAA,CAAsB,OAAAD,EAAgB,MAAM,cAAc,EAE7D,cAAe,GACf,aAAc,GACd,SAAUG,EACV,KAAOD,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,aAAA,CAEjB,EACA,CACE,YAAa,YACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IACR7E,EAAA,IAAA8E,EAAA,CAAsB,OAAAD,EAAgB,MAAM,aAAa,EAE5D,KAAM,CAAC,CAAE,IAAAD,KAAU,CACX,MAAAK,EAAYL,EAAI,SAAS,WAAW,EAOnC,MANc,CACnB,EAAG,SACH,EAAG,MACH,EAAG,UACH,EAAG,UACL,EACoBK,CAAsC,GAAK,SACjE,EACA,cAAe,GACf,aAAc,GACd,KAAM,CACJ,UAAW,YACX,YAAa,YAAA,CAEjB,EACA,CACE,GAAI,UACJ,OAAQ,UACR,KAAOF,GACL/E,EAAA,IAAC6D,GAAA,CACC,OAAQkB,EAAK,IAAI,SAAS,GAC1B,QAASA,EAAK,IAAI,SAClB,SAAUL,EACV,QAAQ,UAAA,CACV,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,UAAW,aACX,YAAa,QAAA,CACf,CAEJ,EC1FWQ,GAAO,CAAC,CAAE,SAAAC,EAAU,OAAAhG,EAAQ,UAAAC,KAA+B,CACtE,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBsC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,SAAAqD,CAAA,EAAanD,EAAkC,EACzE,CAACoD,EAAYC,CAAa,EAAI7F,EAAS,SAAA0F,EAAS,UAAY,EAAK,EACjE,CAACI,EAAUC,CAAW,EAAI/F,EAAS,SAAA0F,EAAS,UAAY,EAAK,EAC7D,CAACjD,EAAmBC,CAAoB,EAAI1C,EAAAA,SAAmB,CAAA,CAAE,EAEjEgG,EAAqBpD,EAAY,CACrC,WAAY,MAAOI,GACjBiD,GAA6B,CAC3B,KAAM,CAAE,GAAIvG,CAAO,EACnB,KAAMsD,CAAA,CACP,EACH,UAAW,IAAM,CACTpD,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EACIuC,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,qBAAqB,EAAG,EACtE2E,EAAA,CACf,EACA,QAAU/F,GAAoC,CACtCP,EAAA,CACJ,MAAOO,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEK4C,EAAYC,GAAuC,CAEvD,MAAMC,EAAqC,CACzC,GAAGD,CACL,EAGKgD,EAAmB,OAAO/C,CAAQ,CACzC,EAEMiD,EAAe,IAAM,CACzBnG,EAAQ,EAAK,EACHJ,EAAA,CACZ,EAGAW,OAAAA,EAAAA,UAAU,IAAM,CACd,GAAIR,EAAM,CAEC6F,EAAA,OAAQD,EAAS,MAAQ,EAAE,EAC3BC,EAAA,WAAYD,EAAS,UAAY,EAAK,EACtCC,EAAA,WAAYD,EAAS,UAAY,EAAK,EACtCC,EAAA,QAASD,EAAS,OAAS,EAAE,EAC7BC,EAAA,mBAAoBD,EAAS,kBAAoB,EAAE,EACnDC,EAAA,cAAeD,EAAS,aAAe,EAAE,EACzCC,EAAA,YAAaD,EAAS,WAAa,CAAC,EAGvC,MAAAS,EAAgBT,EAAS,YAAc,QAAaA,EAAS,YAAc,KAC7EA,EAAS,UAAU,SACnB,EAAA,IACiBhD,EAAA,CAACyD,CAAY,CAAC,CAAA,CAEpC,EAAA,CAACrG,EAAM4F,EAAUC,CAAQ,CAAC,EAE7BrF,EAAAA,UAAU,IAAM,CACdP,EAAQ,EAAI,CACd,EAAG,EAAE,QAGFsD,EAAO,CAAA,KAAAvD,EAAY,aAAcoG,EAChC,gBAACzC,EACC,CAAA,SAAA,CAAClD,EAAA,IAAAmD,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,wBAAsB+B,EAAS,IAAA,CAAA,CAAK,CACnD,CAAA,EACAjF,EAAA,KAAC,OAAA,CACC,SAAU4B,EAAaU,CAAQ,EAC/B,UAAU,OACV,UAAYa,GAAM,CACZA,EAAE,MAAQ,SAAWA,EAAE,kBAAkB,mBAC3CA,EAAE,eAAe,EACZvB,EAAaU,CAAQ,EAAE,EAEhC,EAEA,SAAA,CAAAxC,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAACoD,EACC,CAAA,SAAA,CAAAtD,EAAA,IAACuD,EAAA,CACC,MAAM,OACN,YAAY,wBAEZ,SAACvD,EAAA,IAAAwD,EAAA,CAAM,SAAQ,GAAE,GAAGzB,EAAS,MAAM,EAAG,aAAcoD,EAAS,MAAQ,GAAI,YAAY,YAAa,CAAA,CAAA,CACpG,EAEAnF,EAAA,IAACuD,EAAA,CACC,MAAM,WACN,YAAY,gCAEZ,SAAAvD,EAAA,IAACyD,EAAA,CACC,QAAS4B,EACT,gBAAkBQ,GAAY,CACdP,EAAA,CAAC,CAACO,CAAO,EACdT,EAAA,WAAY,CAAC,CAACS,CAAO,CAAA,CAChC,CAAA,CACF,CACF,EAEA7F,EAAA,IAACuD,EAAA,CACC,MAAM,YACN,YAAY,8BAEZ,SAAAvD,EAAA,IAACyD,EAAA,CACC,QAAS8B,EACT,gBAAkBM,GAAY,CAChBL,EAAA,CAAC,CAACK,CAAO,EACZT,EAAA,WAAY,CAAC,CAACS,CAAO,CAAA,CAChC,CAAA,CACF,CACF,EAEA7F,EAAA,IAACuD,EAAA,CACC,MAAM,QACN,YAAY,0BAEZ,SAAAvD,EAAAA,IAACwD,EAAO,CAAA,GAAGzB,EAAS,OAAO,EAAG,aAAcoD,EAAS,OAAS,GAAI,YAAY,OAAQ,CAAA,CAAA,CACxF,EAEAnF,EAAA,IAACuD,EAAA,CACC,MAAM,oBACN,YAAY,gCAEZ,SAAAvD,EAAAA,IAACwD,EAAO,CAAA,GAAGzB,EAAS,kBAAkB,EAAG,aAAcoD,EAAS,kBAAoB,GAAI,YAAY,mBAAoB,CAAA,CAAA,CAC1H,EAEAnF,EAAA,IAACuD,EAAA,CACC,MAAM,cACN,YAAY,gCAEZ,SAACvD,EAAA,IAAAwD,EAAA,CAAM,SAAQ,GAAE,GAAGzB,EAAS,aAAa,EAAG,aAAcoD,EAAS,aAAe,GAAI,YAAY,aAAc,CAAA,CAAA,CACnH,EAEAnF,EAAA,IAACuD,EAAA,CACC,MAAM,aACN,YAAY,+BAEZ,SAAAvD,EAAA,IAAC2D,EAAA,CACC,KAAK,SACL,QAASpC,EACT,MAAOW,EACP,SAAUC,EACV,YAAY,oBACZ,UAAW,IACX,KAAK,YACL,SAAAJ,EACA,cAAe,EAAA,CAAA,CACjB,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACA7B,EAAAA,KAAC0D,EAAa,CAAA,UAAU,OACtB,SAAA,CAAA5D,EAAA,IAACgD,EAAA,CACC,QAAQ,QACR,QAAUK,GAAM,CACdA,EAAE,eAAe,EACjB7D,EAAQ,EAAK,CACf,EACA,SAAUiG,EAAmB,UAC7B,KAAK,SACN,SAAA,QAAA,CAED,EACAzF,EAAA,IAACgD,EAAA,CACC,KAAK,SACL,SAAUyC,EAAmB,UAE5B,SAAAA,EAAmB,UAAY,YAAc,MAAA,CAAA,CAChD,CACF,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAEJ,EC9LaK,GAAY,IAAM,CACvB,KAAA,CAAE,MAAAzG,CAAM,EAAIC,EAAS,EACrBsC,EAAcC,EAAe,EAE7B,CAACkE,EAAWC,CAAY,EAAIvG,EAAAA,SAAiB,EAAE,EAC/C,CAACoB,EAAkBoF,CAAmB,EAAIxG,EAAAA,SAA4B,CAAA,CAAE,EACxE,CAACyG,EAAkBC,CAAmB,EAAI1G,WAItC,EAEJ,CAAC2G,EAAYC,CAAa,EAAI5G,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAGK,CAACqB,EAASwF,CAAU,EAAI7G,WAAuB,CACnD,CAAE,GAAI,OAAQ,KAAM,EAAM,CAAA,CAC3B,EAEK,CAAE,UAAA8G,EAAW,KAAAC,CAAA,EAAS9F,GAC1B0F,EAAW,UACXA,EAAW,SACXvF,CACF,EAYM4F,EAAUhC,GATS,CAACtF,EAAgBgG,EAAgCuB,IAAiD,CACrGP,EAAA,CAClB,OAAAhH,EACA,SAAAgG,EACA,WAAAuB,CAAA,CACD,CACH,CAG2C,EAErCC,EAAgBjD,GAAkB,CAEtCsC,EAAatC,CAAK,EAKZ,MAAAkD,EAAsB,CAAC,GADL/F,EAAiB,OAAagG,GAAAA,EAAG,YAAc,MAAM,CAC9B,EAG3CnD,GACFkD,EAAoB,KAAK,CACvB,UAAW,OACX,SAAU,WACV,MAAAlD,CAAA,CACD,EAIG,MAAAoD,EAAoB,KAAK,UAAUjG,CAAgB,EACnDkG,EAAgB,KAAK,UAAUH,CAAmB,EAEpDE,IAAsBC,IACxBd,EAAoBW,CAAmB,EACvCP,MAAuB,CAAE,GAAGW,EAAM,UAAW,GAAI,EAErD,EAEMC,EAA0BC,GAAmC,CACjEb,EAAca,CAAa,CAC7B,EAGMC,EAAuBC,GAA6B,CACxDd,EAAWc,CAAU,EACrBf,MAAuB,CAAE,GAAGW,EAAM,UAAW,GAAI,CACnD,EAGMK,EAAgB,IAAM,CAErBzF,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,qBAAqB,EAAG,EAEnF,WAAW,IAAM,CACT3B,EAAA,CACJ,MAAO,iBACP,YAAa,sCACb,QAAS,SAAA,CACV,GACA,GAAG,CACR,EAEA,GAAIkH,EACF,OAAAvG,EAAA,IAACsH,GAAA,CACC,SAAUlB,EAAW,SACrB,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CACd,EAII,MAAAmB,EAAQf,GAAM,OAAS,CAAC,EACxBgB,EAAahB,GAAM,YAAc,EAEvC,OAEItG,EAAA,KAAAuH,WAAA,CAAA,SAAA,CAACzH,EAAAA,IAAA,MAAA,CAAI,UAAU,+EACb,SAAAA,EAAA,IAAC0H,GAAA,CACC,MAAM,eACN,QAAAjB,EACA,KAAMc,EACN,WAAAC,EACA,UAAAjB,EACA,iBAAkB,GAClB,cAAe,GACf,SAAUH,EAAW,SACrB,mBAAoBa,EACpB,gBAAiBE,EACjB,aAAcrG,EACd,SAAU6F,EACV,YAAaZ,EACb,gBAAkB4B,GAChB3H,EAAA,IAAC4H,GAAA,CACE,GAAGD,EACJ,cAAe9G,EACf,eAAiBgH,GAAe,CAExB,MAAAC,EAAa,KAAK,UAAUjH,CAAgB,EAC5CkH,EAAS,KAAK,UAAUF,CAAU,EAEpCC,IAAeC,IACjB9B,EAAoB4B,CAAU,EAC9BxB,MAAuB,CAAE,GAAGW,EAAM,UAAW,GAAI,EACnD,CACF,CACF,EAEF,qBAAsB,GACtB,UAAWK,EACX,mBAAoB,GACpB,aAAc,CAGZ,QAAS,IAAM,CAA8B,EAC7C,cAAU7F,GAAI,CAAA,CAAA,CAAA,CAChB,CAAA,EAEJ,EAEC0E,GAAoBA,EAAiB,aAAe,QACnDlG,EAAA,IAACkF,GAAA,CACC,OAAQgB,EAAiB,OACzB,SAAUA,EAAiB,SAC3B,UAAW,IAAM,CACVtE,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,qBAAqB,EAAG,EACnFmF,EAAoB,IAAI,CAAA,CAC1B,CACF,EAEDD,GAAoBA,EAAiB,aAAe,UACnDlG,EAAA,IAACd,GAAA,CACC,OAAQgH,EAAiB,OACzB,UAAW,IAAM,CACVtE,EAAY,kBAAkB,CAAE,SAAU,CAACZ,EAAW,qBAAqB,EAAG,EACnFmF,EAAoB,IAAI,CAAA,CAC1B,CAAA,CACF,EAEJ,CAEJ,EC7LA,SAAwB6B,IAAiB,CACvC,cACGC,GACC,CAAA,SAAA,CAACjI,EAAAA,IAAAkI,GAAA,CAAK,MAAM,OAAQ,CAAA,QAEnBpC,GAAU,CAAA,CAAA,CAAA,EACb,CAEJ"}