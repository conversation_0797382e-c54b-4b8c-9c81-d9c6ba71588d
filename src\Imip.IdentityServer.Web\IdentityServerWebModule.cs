using Imip.IdentityServer.EntityFrameworkCore;
using Imip.IdentityServer.Localization;
using Imip.IdentityServer.MultiTenancy;
using Imip.IdentityServer.Repositories;
using Imip.IdentityServer.Web.ApplicationConfiguration;
using Imip.IdentityServer.Web.HealthChecks;
using Imip.IdentityServer.Web.Menus;
using Imip.IdentityServer.Web.Middleware;
using InertiaCore.Extensions;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using OpenIddict.Server;
using OpenIddict.Server.AspNetCore;
using OpenIddict.Validation.AspNetCore;
using StackExchange.Redis;
using System;
using System.IO;
using System.Linq;
using Volo.Abp;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.AspNetCore.Mvc.Libs;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.Caching;
using Volo.Abp.Caching.StackExchangeRedis;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity.Web;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict;
using Volo.Abp.PermissionManagement;
using Volo.Abp.PermissionManagement.Identity;
using Volo.Abp.Security.Claims;
using Volo.Abp.Studio.Client.AspNetCore;
using Volo.Abp.Swashbuckle;
using Volo.Abp.TenantManagement.Web;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;
using Volo.Abp.EventBus.RabbitMq;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Imip.IdentityServer.Domain.Services;

namespace Imip.IdentityServer.Web;

[DependsOn(
    typeof(IdentityServerHttpApiModule),
    typeof(IdentityServerApplicationModule),
    typeof(IdentityServerEntityFrameworkCoreModule),
    typeof(AbpAutofacModule),
    typeof(AbpStudioClientAspNetCoreModule),
    typeof(AbpIdentityWebModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpAccountWebOpenIddictModule),
    typeof(AbpTenantManagementWebModule),
    typeof(AbpFeatureManagementWebModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpCachingStackExchangeRedisModule)
// typeof(AbpEventBusRabbitMqModule) // Temporarily disabled due to connection issues
)]
public class IdentityServerWebModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
        {
            options.AddAssemblyResource(
                typeof(IdentityServerResource),
                typeof(IdentityServerDomainModule).Assembly,
                typeof(IdentityServerDomainSharedModule).Assembly,
                typeof(IdentityServerApplicationModule).Assembly,
                typeof(IdentityServerApplicationContractsModule).Assembly,
                typeof(IdentityServerWebModule).Assembly
            );
        });

        PreConfigure<OpenIddictBuilder>(builder =>
        {
            builder.AddValidation(options =>
            {
                options.AddAudiences("IdentityServer");
                options.UseLocalServer();
                options.UseAspNetCore();
            });
        });

        // Configure OpenIddict for both development and production
        PreConfigure<OpenIddictServerBuilder>(serverBuilder =>
        {
            // Set the issuer URI and ensure it uses HTTPS
            var authority = configuration["AuthServer:Authority"]!;
            if (!authority.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
            {
                // Force HTTPS for the authority URL
                authority = "https://" + authority.Replace("http://", "", StringComparison.OrdinalIgnoreCase);
                Console.WriteLine($"Forcing HTTPS for authority URL: {authority}");
            }

            serverBuilder.SetIssuer(new Uri(authority));
            Console.WriteLine($"OpenIddict issuer set to: {authority}");
        });


        if (!hostingEnvironment.IsDevelopment())
        {
            PreConfigure<AbpOpenIddictAspNetCoreOptions>(options =>
            {
                options.AddDevelopmentEncryptionAndSigningCertificate = false;
            });

            PreConfigure<OpenIddictServerBuilder>(serverBuilder =>
            {
                // Use production certificate
                // Always use identity-server.pfx as the primary certificate path
                var certPath = "/app/certs/identity-server.pfx";

                // Fall back to the configured path if identity-server.pfx doesn't exist
                if (!File.Exists(certPath))
                {
                    certPath = configuration["AuthServer:CertificatePath"] ?? "/app/certs/openiddict.pfx";
                }

                Console.WriteLine($"Using certificate from {certPath}");
                serverBuilder.AddProductionEncryptionAndSigningCertificate(certPath,
                    configuration["AuthServer:CertificatePassPhrase"]!);
            });
        }
    }

    private void ConfigureDataProtection(ServiceConfigurationContext context, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        var appName = configuration["App:AppName"] ?? "Imip.IdentityServer";

        var dataProtectionBuilder = context.Services.AddDataProtection()
            .SetApplicationName(appName)
            .SetDefaultKeyLifetime(TimeSpan.FromDays(90));

        if (hostingEnvironment.IsDevelopment())
        {
            // Development: Local file system
            // var keysPath = Path.Combine(hostingEnvironment.ContentRootPath, "App_Data", "DataProtection-Keys");
            // Directory.CreateDirectory(keysPath);
            // dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(keysPath));
            // Console.WriteLine($"Development: Data protection keys stored at: {keysPath}");
            // // Production/Kubernetes: Multiple storage strategies
            ConfigureProductionDataProtection(context, configuration, dataProtectionBuilder, appName);
        }
        else
        {
            // Production/Kubernetes: Multiple storage strategies
            ConfigureProductionDataProtection(context, configuration, dataProtectionBuilder, appName);
        }
    }

    private void ConfigureProductionDataProtection(ServiceConfigurationContext context, IConfiguration configuration,
        IDataProtectionBuilder dataProtectionBuilder, string appName)
    {
        var redisIsEnabled = configuration.GetValue<bool>("Redis:IsEnabled", true);
        var redisConfiguration = configuration["Redis:Configuration"];

        // Strategy 1: Try Redis first (best for Kubernetes multi-pod scenarios)
        if (redisIsEnabled && !string.IsNullOrEmpty(redisConfiguration))
        {
            try
            {
                // Persist data protection keys to Redis when configured
                context.Services.AddDataProtection()
                    .PersistKeysToStackExchangeRedis(ConnectionMultiplexer.Connect(redisConfiguration), "DataProtection-Keys");

                Configure<RedisCacheOptions>(options =>
                {
                    options.Configuration = redisConfiguration;
                    options.InstanceName = $"{appName}:DataProtection:";
                });
                Console.WriteLine("Production: Data protection keys stored in Redis (Kubernetes-ready)");

                Configure<AbpDistributedCacheOptions>(options =>
                {
                    options.KeyPrefix = $"{appName}:DataProtection:";
                });
                Console.WriteLine("Production: distributed cache stored in Redis (Kubernetes-ready)");
                return; // Redis success, we're done
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Redis data protection failed: {ex.Message}. Falling back to persistent volume.");
            }
        }

        // Strategy 2: Kubernetes Persistent Volume (fallback)
        ConfigureKubernetesPersistentStorage(dataProtectionBuilder);
    }

    private void ConfigureKubernetesPersistentStorage(IDataProtectionBuilder dataProtectionBuilder)
    {
        // Kubernetes persistent volume paths (must be configured in your deployment)
        var kubernetesPaths = new[]
        {
            "/app/data-protection-keys",  // Your current path
            "/data/keys",                 // Alternative common path
            "/persistent/data-protection-keys"  // Another alternative
        };

        foreach (var path in kubernetesPaths)
        {
            try
            {
                // Ensure directory exists
                Directory.CreateDirectory(path);

                // Test write permissions
                var testFile = Path.Combine(path, "test-write.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);

                dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(path));
                Console.WriteLine($"Production: Data protection keys stored at: {path} (Kubernetes PV)");
                return; // Success
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to use path {path}: {ex.Message}");
            }
        }

        // Strategy 3: Fallback to temporary storage (NOT recommended for production)
        Console.WriteLine("WARNING: Using temporary storage for data protection keys. Users will be logged out on pod restarts!");
        var tempPath = Path.Combine(Path.GetTempPath(), "dataprotection-keys");
        Directory.CreateDirectory(tempPath);
        dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(tempPath));
    }

    // Add this method to handle distributed cache configuration
    private void ConfigureDistributedCache(ServiceConfigurationContext context, IConfiguration configuration)
    {
        var redisIsEnabled = configuration.GetValue<bool>("Redis:IsEnabled", true);
        var redisConfiguration = configuration["Redis:Configuration"];

        Configure<AbpDistributedCacheOptions>(options =>
        {
            options.KeyPrefix = "Imip.IdentityServer.Cache:";
            // Reduce cache duration for permissions to ensure faster synchronization across pods
            options.GlobalCacheEntryOptions.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30);
            options.GlobalCacheEntryOptions.SlidingExpiration = TimeSpan.FromMinutes(10);
        });

        if (redisIsEnabled && !string.IsNullOrEmpty(redisConfiguration))
        {
            try
            {
                // Configure Redis-specific options for better performance
                Configure<RedisCacheOptions>(options =>
                {
                    options.Configuration = redisConfiguration;
                    options.InstanceName = "Imip.IdentityServer:";
                });

                // Redis cache will be configured by AbpCachingStackExchangeRedisModule
                Console.WriteLine("ABP distributed cache configured to use Redis with enhanced options");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Redis cache configuration failed: {ex.Message}. Using in-memory cache.");
            }
        }
        else
        {
            Console.WriteLine("Using in-memory distributed cache");
        }
    }

    // Also update your antiforgery configuration to be more resilient
    private void ConfigureAntiforgery(ServiceConfigurationContext context, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        // Configure ABP antiforgery options
        Configure<AbpAntiForgeryOptions>(options =>
        {
            options.TokenCookie.Expiration = TimeSpan.FromDays(365);
            // In development, allow non-HTTPS for easier debugging
            if (hostingEnvironment.IsDevelopment())
            {
                options.TokenCookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
            }
        });

        // Configure ASP.NET Core antiforgery options
        context.Services.Configure<AntiforgeryOptions>(options =>
        {
            options.Cookie.Name = ".Imip.IdentityServer.Antiforgery.V2";
            options.Cookie.HttpOnly = true;

            var requireHttps = !hostingEnvironment.IsDevelopment() &&
                              configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", true);

            options.Cookie.SecurePolicy = requireHttps ? CookieSecurePolicy.Always : CookieSecurePolicy.SameAsRequest;
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.Cookie.MaxAge = TimeSpan.FromHours(2); // Longer lifetime for development
            options.FormFieldName = "__RequestVerificationToken";
            options.HeaderName = "X-CSRF-TOKEN";
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        var appName = configuration["App:AppName"] ?? "Imip.IdentityServer";

        // Add to ConfigureServices method
        context.Services.AddOpenIddict()
          .AddValidation(options =>
          {
              options.UseAspNetCore();
              options.AddAudiences("IdentityServer");  // Validate tokens for this audience
          });

        // Replace the large Redis/DataProtection block with:
        ConfigureDataProtection(context, configuration, hostingEnvironment);
        ConfigureDistributedCache(context, configuration);
        ConfigureAntiforgery(context, configuration, hostingEnvironment);

        // Redis: register a shared ConnectionMultiplexer for DI consumers (e.g., LogoutNotificationService)
        var redisIsEnabled = configuration.GetValue<bool>("Redis:IsEnabled", true);
        var redisConfiguration = configuration["Redis:Configuration"];
        if (redisIsEnabled && !string.IsNullOrWhiteSpace(redisConfiguration))
        {
            context.Services.AddSingleton<IConnectionMultiplexer>(_ => ConnectionMultiplexer.Connect(redisConfiguration));
        }

        // Options: bind LogoutNotificationOptions from configuration
        context.Services.Configure<LogoutNotificationOptions>(configuration.GetSection("LogoutNotification"));

        // Add this registration
        context.Services.AddTransient(typeof(IIdentityDynamicRepository<,>), typeof(IdentityDynamicRepository<,>));

        // Enhanced application configuration will be handled by middleware

        // Disable client-side library check for containers
        Configure<AbpMvcLibsOptions>(options => { options.CheckLibs = false; });

        if (!configuration.GetValue<bool>("App:DisablePII"))
        {
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;
        }

        // Always configure forwarded headers to handle proxy scenarios
        Configure<ForwardedHeadersOptions>(options =>
        {
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto |
                                       ForwardedHeaders.XForwardedHost;
            // Only loopback proxies are allowed by default
            // Clear that restriction because forwarders are enabled by explicit configuration
            options.KnownNetworks.Clear();
            options.KnownProxies.Clear();
        });

        // Configure OpenIddict AspNetCore options
        Configure<OpenIddictServerAspNetCoreOptions>(options =>
        {
            // Disable HTTPS requirement when explicitly configured or in development
            if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata") || hostingEnvironment.IsDevelopment())
            {
                options.DisableTransportSecurityRequirement = true;
            }

            // Disable request caching to prevent request_id issues
            options.EnableAuthorizationRequestCaching = false;
            options.EnableEndSessionRequestCaching = false;
        });

        context.Services.AddInertia(options =>
        {
            options.RootView = "~/Views/App.cshtml";
        });

        context.Services.AddViteHelper(options =>
        {
            options.PublicDirectory = "wwwroot";
            options.ManifestFilename = "manifest.json";
            options.BuildDirectory = "build";
        });

        // Configure OpenIddict server options for token lifetimes
        Configure<OpenIddictServerOptions>(options =>
        {
            // Increase token lifetimes for better user experience
            options.AccessTokenLifetime = TimeSpan.FromHours(8);
            options.RefreshTokenLifetime = TimeSpan.FromDays(30);
        });

        // Configure cookie policy for better browser compatibility and security
        context.Services.Configure<CookiePolicyOptions>(options =>
        {
            options.MinimumSameSitePolicy = SameSiteMode.Lax;
            options.Secure = CookieSecurePolicy.Always;
            options.HttpOnly = Microsoft.AspNetCore.CookiePolicy.HttpOnlyPolicy.Always;

            // Handle SameSite=None for modern browsers that support it
            options.OnAppendCookie = cookieContext =>
            {
                if (cookieContext.CookieOptions.SameSite == SameSiteMode.None)
                {
                    cookieContext.CookieOptions.Secure = true;
                }

                // Ensure cookies have reasonable expiration
                if (!cookieContext.CookieOptions.Expires.HasValue &&
                    !cookieContext.CookieOptions.MaxAge.HasValue)
                {
                    cookieContext.CookieOptions.MaxAge = TimeSpan.FromHours(1);
                }
            };

            // Handle cookie deletion properly
            options.OnDeleteCookie = cookieContext =>
            {
                cookieContext.CookieOptions.Path = "/";
                cookieContext.CookieOptions.Secure = true;
            };
        });

        ConfigureBundles();
        ConfigureUrls(configuration);
        ConfigureHealthChecks(context);
        ConfigureAuthentication(context);
        ConfigureAutoMapper();
        ConfigureVirtualFileSystem(hostingEnvironment);
        ConfigureNavigationServices();
        ConfigureAutoApiControllers();
        ConfigureSwaggerServices(context.Services);

        Configure<PermissionManagementOptions>(options =>
        {
            options.IsDynamicPermissionStoreEnabled = true;
            // Ensure permissions are saved to database for consistency across pods
            options.SaveStaticPermissionsToDatabase = true;
            // Keep default management providers - they handle role/user permission assignments
            // Permission definitions are automatically discovered from IdentityServerPermissionDefinitionProvider
        });

        // Register the RoleApplicationConfigurationContributor
        context.Services.AddTransient<RoleApplicationConfigurationContributor>();

        Configure<AbpApplicationConfigurationOptions>(options =>
        {
            options.Contributors.Add(context.Services.GetRequiredService<RoleApplicationConfigurationContributor>());
        });
    }


    private void ConfigureHealthChecks(ServiceConfigurationContext context)
    {
        context.Services.AddIdentityServerHealthChecks();
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-scripts.js");
                    bundle.AddFiles("/global-styles.css");
                }
            );
        });
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options => { options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"]; });
    }

    private void ConfigureAuthentication(ServiceConfigurationContext context)
    {
        context.Services.ForwardIdentityAuthenticationForBearer(OpenIddictValidationAspNetCoreDefaults
            .AuthenticationScheme);

        // Configure dynamic claims to ensure session tracking works
        Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            // Enable dynamic claims which is required for session tracking
            options.IsDynamicClaimsEnabled = true;
        });

        // Configure claims principal factory options to ensure dynamic claims
        Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            // Enable session ID claims
            options.IsDynamicClaimsEnabled = true;
        });
    }

    private void ConfigureAutoMapper()
    {
        Configure<AbpAutoMapperOptions>(options => { options.AddMaps<IdentityServerWebModule>(); });
    }

    private static bool IsRunningInContainer()
    {
        // Check for container-specific environment indicators
        return File.Exists("/.dockerenv") ||
               (File.Exists("/proc/1/cgroup") && File.ReadAllText("/proc/1/cgroup").Contains("/docker/"));
    }

    private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
    {
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<IdentityServerWebModule>();

            // Only replace embedded resources with physical files in a development environment,
            // and only if we're not running in a container
            if (hostingEnvironment.IsDevelopment() && !IsRunningInContainer())
            {
                try
                {
                    var domainSharedPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.IdentityServer.Domain.Shared", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainSharedPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<IdentityServerDomainSharedModule>(domainSharedPath);
                    }

                    var domainPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.IdentityServer.Domain", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<IdentityServerDomainModule>(domainPath);
                    }

                    var contractsPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.IdentityServer.Application.Contracts", Path.DirectorySeparatorChar));
                    if (Directory.Exists(contractsPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<IdentityServerApplicationContractsModule>(
                            contractsPath);
                    }

                    var appPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.IdentityServer.Application", Path.DirectorySeparatorChar));
                    if (Directory.Exists(appPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<IdentityServerApplicationModule>(appPath);
                    }

                    var httpApiPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}..{0}src{0}Imip.IdentityServer.HttpApi", Path.DirectorySeparatorChar));
                    if (Directory.Exists(httpApiPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<IdentityServerHttpApiModule>(httpApiPath);
                    }

                    if (Directory.Exists(hostingEnvironment.ContentRootPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<IdentityServerWebModule>(hostingEnvironment
                            .ContentRootPath);
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception but continue without replacing embedded resources
                    Console.WriteLine($"Error configuring virtual file system: {ex.Message}");
                }
            }
        });
    }

    private void ConfigureNavigationServices()
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new IdentityServerMenuContributor());
        });

        Configure<AbpToolbarOptions>(options => { options.Contributors.Add(new IdentityServerToolbarContributor()); });
    }

    private void ConfigureAutoApiControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(IdentityServerApplicationModule).Assembly, opts =>
            {
                opts.RootPath = "idp";
            });
        });
    }

    private static void ConfigureSwaggerServices(IServiceCollection services)
    {
        services.AddAbpSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "IdentityServer API", Version = "v1" });
                options.DocInclusionPredicate((docName, description) => true);
                options.CustomSchemaIds(type =>
                {
                    // Handle generic types
                    if (type.IsGenericType)
                    {
                        var prefix = type.Name.Split('`')[0];
                        var genericArgs = string.Join("And", type.GetGenericArguments().Select(t =>
                        {
                            if (t.IsGenericType)
                            {
                                var nestedPrefix = t.Name.Split('`')[0];
                                var nestedArgs = string.Join("And", t.GetGenericArguments().Select(nt => nt.Name));
                                return $"{nestedPrefix}Of{nestedArgs}";
                            }

                            return t.Name;
                        }));
                        return $"{prefix}Of{genericArgs}";
                    }

                    // Handle non-generic types
                    return type.Name;
                });
            }
        );
    }


    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        var configuration = context.GetConfiguration();

        // Add health check middleware to fix hostname issues - must be first in the pipeline
        app.UseMiddleware<HealthCheckMiddleware>();

        // Apply forwarded headers middleware early in the pipeline
        app.UseForwardedHeaders();

        // Apply our custom forwarded headers middleware
        app.UseCustomForwardedHeaders();

        // Add localhost antiforgery handling middleware
        app.UseLocalhostAntiforgery();

        // Add data protection error handler early in the pipeline
        app.UseMiddleware<DataProtectionErrorHandlerMiddleware>();

        // Add application configuration cache middleware
        app.UseMiddleware<ApplicationConfigurationCacheMiddleware>();

        // Add custom OpenIddict error handler
        app.UseMiddleware<OpenIddictErrorHandlerMiddleware>();

        // Add pod info to response headers
        app.UsePodInfo();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
            app.UseHsts();
        }

        app.UseInertia();


        app.UseApiAuthResponseMiddleware();

        // Add middleware to ensure OpenIddict endpoints use HTTPS
        app.UseOpenIddictHttps();

        app.UseCorrelationId();
        app.MapAbpStaticAssets();
        app.UseAbpStudioLink();
        app.UseRouting();
        app.UseAbpSecurityHeaders();

        // Add SSO middleware before authentication
        app.UseSsoMiddleware();

        app.UseAuthentication();
        app.UseAbpOpenIddictValidation();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        // Add API session tracking middleware after authentication but before authorization
        // app.UseApiSessionTracking();

        // Add concurrent login prevention middleware
        app.UseConcurrentLoginPrevention();

        app.UseUnitOfWork();
        app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options => { options.SwaggerEndpoint("/swagger/v1/swagger.json", "IdentityServer API"); });
        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapControllerRoute(
                name: "default",
                pattern: "{controller=Home}/{action=Index}/{id?}");
        });
    }
}