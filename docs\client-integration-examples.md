# Client Integration Examples for Logout Notifications

This document provides examples of how to integrate with the Identity Server logout notification system from different client platforms.

## Overview

The Identity Server uses a dual notification system:
1. **Redis Pub/Sub** - For real-time notifications
2. **RabbitMQ** - For reliable delivery with acknowledgments

## .NET Client Example

### 1. Install Required Packages

```bash
dotnet add package StackExchange.Redis
dotnet add package RabbitMQ.Client
dotnet add package Microsoft.Extensions.Hosting
dotnet add package Microsoft.Extensions.Configuration
```

### 2. Create Logout Notification Service

```csharp
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using StackExchange.Redis;

namespace YourApp.Services;

public class LogoutNotificationService : BackgroundService
{
    private readonly ILogger<LogoutNotificationService> _logger;
    private readonly IConnectionMultiplexer _redisConnection;
    private readonly IConnection _rabbitMqConnection;
    private readonly IModel _rabbitMqChannel;
    private readonly LogoutNotificationOptions _options;

    public LogoutNotificationService(
        ILogger<LogoutNotificationService> logger,
        IConnectionMultiplexer redisConnection,
        IOptions<LogoutNotificationOptions> options)
    {
        _logger = logger;
        _redisConnection = redisConnection;
        _options = options.Value;

        // Setup RabbitMQ connection
        var factory = new ConnectionFactory
        {
            HostName = _options.RabbitMqHost,
            UserName = _options.RabbitMqUsername,
            Password = _options.RabbitMqPassword,
            Port = _options.RabbitMqPort
        };
        _rabbitMqConnection = factory.CreateConnection();
        _rabbitMqChannel = _rabbitMqConnection.CreateModel();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Subscribe to Redis Pub/Sub
        await SubscribeToRedisAsync(stoppingToken);

        // Subscribe to RabbitMQ
        await SubscribeToRabbitMQAsync(stoppingToken);

        // Keep the service running
        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(1000, stoppingToken);
        }
    }

    private async Task SubscribeToRedisAsync(CancellationToken stoppingToken)
    {
        var subscriber = _redisConnection.GetSubscriber();
        
        // Subscribe to general logout channel
        await subscriber.SubscribeAsync(_options.RedisLogoutChannel, (channel, message) =>
        {
            try
            {
                var logoutMessage = JsonSerializer.Deserialize<LogoutRedisMessage>(message);
                HandleLogoutNotification(logoutMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Redis logout message");
            }
        });

        // Subscribe to client-specific channel
        var clientChannel = $"{_options.RedisLogoutChannel}:{_options.ClientId}";
        await subscriber.SubscribeAsync(clientChannel, (channel, message) =>
        {
            try
            {
                var clientLogoutMessage = JsonSerializer.Deserialize<ClientLogoutRedisMessage>(message);
                HandleClientLogoutNotification(clientLogoutMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Redis client logout message");
            }
        });

        _logger.LogInformation("Subscribed to Redis logout notifications");
    }

    private async Task SubscribeToRabbitMQAsync(CancellationToken stoppingToken)
    {
        // Declare exchange and queue
        _rabbitMqChannel.ExchangeDeclare(_options.RabbitMqExchange, ExchangeType.Topic, true, false);
        var queueName = _rabbitMqChannel.QueueDeclare().QueueName;
        _rabbitMqChannel.QueueBind(queueName, _options.RabbitMqExchange, "identityserver.logout.*");

        var consumer = new EventingBasicConsumer(_rabbitMqChannel);
        consumer.Received += (model, ea) =>
        {
            try
            {
                var body = ea.Body.ToArray();
                var message = System.Text.Encoding.UTF8.GetString(body);
                var logoutEvent = JsonSerializer.Deserialize<UserLogoutEvent>(message);
                HandleLogoutNotification(logoutEvent);
                
                _rabbitMqChannel.BasicAck(ea.DeliveryTag, false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing RabbitMQ logout message");
                _rabbitMqChannel.BasicNack(ea.DeliveryTag, false, true);
            }
        };

        _rabbitMqChannel.BasicConsume(queue: queueName,
                                    autoAck: false,
                                    consumer: consumer);

        _logger.LogInformation("Subscribed to RabbitMQ logout notifications");
    }

    private void HandleLogoutNotification(LogoutRedisMessage logoutMessage)
    {
        _logger.LogInformation("Received logout notification for user {UserId}", logoutMessage.UserId);
        
        // Implement your logout logic here
        // For example:
        // - Clear local session data
        // - Redirect user to login page
        // - Update UI state
        // - Clear cached user data
        
        // Example: Clear authentication state
        ClearUserSession(logoutMessage.UserId);
    }

    private void HandleClientLogoutNotification(ClientLogoutRedisMessage logoutMessage)
    {
        _logger.LogInformation("Received client-specific logout notification for user {UserId}", logoutMessage.UserId);
        
        // Handle client-specific logout logic
        HandleLogoutNotification(new LogoutRedisMessage
        {
            UserId = logoutMessage.UserId,
            UserName = logoutMessage.UserName,
            SessionId = logoutMessage.SessionId,
            TenantId = logoutMessage.TenantId,
            SourceApplication = logoutMessage.SourceApplication,
            LogoutType = LogoutType.Manual,
            LogoutTimestamp = logoutMessage.LogoutTimestamp
        });
    }

    private void HandleLogoutNotification(UserLogoutEvent logoutEvent)
    {
        _logger.LogInformation("Received RabbitMQ logout notification for user {UserId}", logoutEvent.UserId);
        
        // Same logic as Redis notification
        ClearUserSession(logoutEvent.UserId);
    }

    private void ClearUserSession(Guid userId)
    {
        // Implement session clearing logic
        // This could involve:
        // - Clearing authentication cookies
        // - Removing user data from memory cache
        // - Updating application state
        // - Triggering UI updates
        
        _logger.LogInformation("Cleared session for user {UserId}", userId);
    }

    public override void Dispose()
    {
        _rabbitMqChannel?.Dispose();
        _rabbitMqConnection?.Dispose();
        base.Dispose();
    }
}

public class LogoutNotificationOptions
{
    public string RedisLogoutChannel { get; set; } = "identityserver:logout";
    public string ClientId { get; set; } = "your-client-id";
    public string RabbitMqHost { get; set; } = "localhost";
    public string RabbitMqUsername { get; set; } = "guest";
    public string RabbitMqPassword { get; set; } = "guest";
    public int RabbitMqPort { get; set; } = 5672;
    public string RabbitMqExchange { get; set; } = "LogoutEvents";
}

public class LogoutRedisMessage
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string? SessionId { get; set; }
    public Guid? TenantId { get; set; }
    public string SourceApplication { get; set; } = string.Empty;
    public LogoutType LogoutType { get; set; }
    public DateTime LogoutTimestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string[]? TargetClients { get; set; }
}

public class ClientLogoutRedisMessage
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string? ClientName { get; set; }
    public string? SessionId { get; set; }
    public Guid? TenantId { get; set; }
    public string SourceApplication { get; set; } = string.Empty;
    public LogoutNotificationType NotificationType { get; set; }
    public string? LogoutUrl { get; set; }
    public string? BackChannelLogoutUrl { get; set; }
    public DateTime LogoutTimestamp { get; set; }
    public string? LogoutToken { get; set; }
}

public enum LogoutType
{
    Manual = 0,
    FrontChannel = 1,
    BackChannel = 2,
    Timeout = 3,
    Administrative = 4
}

public enum LogoutNotificationType
{
    FrontChannel = 0,
    BackChannel = 1,
    Both = 2
}

public class UserLogoutEvent
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string? SessionId { get; set; }
    public Guid? TenantId { get; set; }
    public string SourceApplication { get; set; } = string.Empty;
    public LogoutType LogoutType { get; set; }
    public DateTime LogoutTimestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string[]? TargetClients { get; set; }
}
```

### 3. Register the Service

```csharp
// Program.cs or Startup.cs
builder.Services.AddSingleton<IConnectionMultiplexer>(sp =>
{
    var configuration = sp.GetRequiredService<IConfiguration>();
    return ConnectionMultiplexer.Connect(configuration.GetConnectionString("Redis"));
});

builder.Services.Configure<LogoutNotificationOptions>(builder.Configuration.GetSection("LogoutNotification"));

builder.Services.AddHostedService<LogoutNotificationService>();
```

### 4. Configuration

```json
{
  "ConnectionStrings": {
    "Redis": "localhost:6379"
  },
  "LogoutNotification": {
    "RedisLogoutChannel": "identityserver:logout",
    "ClientId": "your-client-id",
    "RabbitMqHost": "localhost",
    "RabbitMqUsername": "guest",
    "RabbitMqPassword": "guest",
    "RabbitMqPort": 5672,
    "RabbitMqExchange": "LogoutEvents"
  }
}
```

## NextJS Client Example

### 1. Install Dependencies

```bash
npm install redis socket.io-client
```

### 2. Create Logout Notification Service

```typescript
// services/logoutNotificationService.ts
import { createClient } from 'redis';
import { io, Socket } from 'socket.io-client';

export interface LogoutMessage {
  userId: string;
  userName: string;
  sessionId?: string;
  tenantId?: string;
  sourceApplication: string;
  logoutType: number;
  logoutTimestamp: string;
  ipAddress?: string;
  userAgent?: string;
  targetClients?: string[];
}

export interface ClientLogoutMessage {
  userId: string;
  userName: string;
  clientId: string;
  clientName?: string;
  sessionId?: string;
  tenantId?: string;
  sourceApplication: string;
  notificationType: number;
  logoutUrl?: string;
  backChannelLogoutUrl?: string;
  logoutTimestamp: string;
  logoutToken?: string;
}

export class LogoutNotificationService {
  private redisClient: ReturnType<typeof createClient>;
  private socket: Socket | null = null;
  private onLogoutCallback: ((message: LogoutMessage) => void) | null = null;

  constructor(
    private config: {
      redisUrl: string;
      redisChannel: string;
      clientId: string;
      socketUrl?: string;
    }
  ) {
    this.initializeRedis();
    if (config.socketUrl) {
      this.initializeSocket();
    }
  }

  private async initializeRedis() {
    this.redisClient = createClient({
      url: this.config.redisUrl
    });

    await this.redisClient.connect();
    
    // Subscribe to general logout channel
    await this.redisClient.subscribe(this.config.redisChannel, (message) => {
      try {
        const logoutMessage: LogoutMessage = JSON.parse(message);
        this.handleLogoutNotification(logoutMessage);
      } catch (error) {
        console.error('Error processing Redis logout message:', error);
      }
    });

    // Subscribe to client-specific channel
    const clientChannel = `${this.config.redisChannel}:${this.config.clientId}`;
    await this.redisClient.subscribe(clientChannel, (message) => {
      try {
        const clientLogoutMessage: ClientLogoutMessage = JSON.parse(message);
        this.handleClientLogoutNotification(clientLogoutMessage);
      } catch (error) {
        console.error('Error processing Redis client logout message:', error);
      }
    });

    console.log('Subscribed to Redis logout notifications');
  }

  private initializeSocket() {
    if (!this.config.socketUrl) return;

    this.socket = io(this.config.socketUrl);
    
    this.socket.on('connect', () => {
      console.log('Connected to logout notification socket');
    });

    this.socket.on('logout', (message: LogoutMessage) => {
      this.handleLogoutNotification(message);
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from logout notification socket');
    });
  }

  private handleLogoutNotification(message: LogoutMessage) {
    console.log('Received logout notification for user:', message.userId);
    
    // Implement your logout logic here
    // For example:
    // - Clear local storage
    // - Clear session storage
    // - Redirect to login page
    // - Update application state
    
    this.clearUserSession(message.userId);
    
    // Call the callback if provided
    if (this.onLogoutCallback) {
      this.onLogoutCallback(message);
    }
  }

  private handleClientLogoutNotification(message: ClientLogoutMessage) {
    console.log('Received client-specific logout notification for user:', message.userId);
    
    // Convert to general logout message
    const logoutMessage: LogoutMessage = {
      userId: message.userId,
      userName: message.userName,
      sessionId: message.sessionId,
      tenantId: message.tenantId,
      sourceApplication: message.sourceApplication,
      logoutType: 0, // Manual
      logoutTimestamp: message.logoutTimestamp,
      ipAddress: undefined,
      userAgent: undefined,
      targetClients: undefined
    };
    
    this.handleLogoutNotification(logoutMessage);
  }

  private clearUserSession(userId: string) {
    // Clear authentication data
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    sessionStorage.clear();
    
    // Update application state (if using React Context, Redux, etc.)
    // dispatch({ type: 'LOGOUT' });
    
    // Redirect to login page
    window.location.href = '/login';
    
    console.log('Cleared session for user:', userId);
  }

  public onLogout(callback: (message: LogoutMessage) => void) {
    this.onLogoutCallback = callback;
  }

  public async disconnect() {
    if (this.redisClient) {
      await this.redisClient.disconnect();
    }
    
    if (this.socket) {
      this.socket.disconnect();
    }
  }
}

// Usage in your app
export const logoutService = new LogoutNotificationService({
  redisUrl: 'redis://localhost:6379',
  redisChannel: 'identityserver:logout',
  clientId: 'your-nextjs-client-id',
  socketUrl: 'http://localhost:3001' // Optional WebSocket server
});

// In your React component
logoutService.onLogout((message) => {
  // Handle logout in your React app
  console.log('User logged out:', message.userName);
  // Update your app state, show notifications, etc.
});
```

### 3. React Hook Example

```typescript
// hooks/useLogoutNotification.ts
import { useEffect, useCallback } from 'react';
import { logoutService } from '../services/logoutNotificationService';

export const useLogoutNotification = () => {
  const handleLogout = useCallback((message: any) => {
    // Handle logout in your React component
    console.log('Logout notification received:', message);
    
    // You can dispatch to Redux, update context, etc.
    // dispatch(logoutAction());
  }, []);

  useEffect(() => {
    logoutService.onLogout(handleLogout);
    
    return () => {
      // Cleanup if needed
    };
  }, [handleLogout]);

  return { logoutService };
};
```

## PHP Client Example

### 1. Install Dependencies

```bash
composer require predis/predis
composer require php-amqplib/php-amqplib
```

### 2. Create Logout Notification Service

```php
<?php

namespace YourApp\Services;

use Predis\Client;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Exception\AMQPException;

class LogoutNotificationService
{
    private Client $redisClient;
    private AMQPStreamConnection $rabbitMqConnection;
    private $rabbitMqChannel;
    private array $config;
    private $onLogoutCallback;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->initializeRedis();
        $this->initializeRabbitMQ();
    }

    private function initializeRedis(): void
    {
        $this->redisClient = new Client([
            'host' => $this->config['redis']['host'],
            'port' => $this->config['redis']['port'],
            'password' => $this->config['redis']['password'] ?? null,
        ]);

        // Subscribe to general logout channel
        $this->redisClient->subscribe([$this->config['redis']['logout_channel']], function ($redis, $channel, $message) {
            $this->handleLogoutNotification(json_decode($message, true));
        });

        // Subscribe to client-specific channel
        $clientChannel = $this->config['redis']['logout_channel'] . ':' . $this->config['client_id'];
        $this->redisClient->subscribe([$clientChannel], function ($redis, $channel, $message) {
            $this->handleClientLogoutNotification(json_decode($message, true));
        });

        echo "Subscribed to Redis logout notifications\n";
    }

    private function initializeRabbitMQ(): void
    {
        $this->rabbitMqConnection = new AMQPStreamConnection(
            $this->config['rabbitmq']['host'],
            $this->config['rabbitmq']['port'],
            $this->config['rabbitmq']['username'],
            $this->config['rabbitmq']['password']
        );

        $this->rabbitMqChannel = $this->rabbitMqConnection->channel();
        
        // Declare exchange and queue
        $this->rabbitMqChannel->exchange_declare(
            $this->config['rabbitmq']['exchange'],
            'topic',
            false,
            true,
            false
        );

        $queueName = $this->rabbitMqChannel->queue_declare('', false, false, true, false)[0];
        $this->rabbitMqChannel->queue_bind($queueName, $this->config['rabbitmq']['exchange'], 'identityserver.logout.*');

        // Set up consumer
        $this->rabbitMqChannel->basic_consume(
            $queueName,
            '',
            false,
            false,
            false,
            false,
            function (AMQPMessage $message) {
                $this->handleRabbitMQLogoutNotification($message);
            }
        );

        echo "Subscribed to RabbitMQ logout notifications\n";
    }

    private function handleLogoutNotification(array $message): void
    {
        echo "Received logout notification for user: " . $message['userId'] . "\n";
        
        // Implement your logout logic here
        $this->clearUserSession($message['userId']);
        
        // Call the callback if provided
        if ($this->onLogoutCallback) {
            call_user_func($this->onLogoutCallback, $message);
        }
    }

    private function handleClientLogoutNotification(array $message): void
    {
        echo "Received client-specific logout notification for user: " . $message['userId'] . "\n";
        
        // Convert to general logout message
        $logoutMessage = [
            'userId' => $message['userId'],
            'userName' => $message['userName'],
            'sessionId' => $message['sessionId'] ?? null,
            'tenantId' => $message['tenantId'] ?? null,
            'sourceApplication' => $message['sourceApplication'],
            'logoutType' => 0, // Manual
            'logoutTimestamp' => $message['logoutTimestamp'],
        ];
        
        $this->handleLogoutNotification($logoutMessage);
    }

    private function handleRabbitMQLogoutNotification(AMQPMessage $message): void
    {
        try {
            $body = $message->getBody();
            $logoutEvent = json_decode($body, true);
            
            echo "Received RabbitMQ logout notification for user: " . $logoutEvent['userId'] . "\n";
            
            $this->handleLogoutNotification($logoutEvent);
            
            $this->rabbitMqChannel->basic_ack($message->get_delivery_tag());
        } catch (Exception $e) {
            echo "Error processing RabbitMQ logout message: " . $e->getMessage() . "\n";
            $this->rabbitMqChannel->basic_nack($message->get_delivery_tag(), false, true);
        }
    }

    private function clearUserSession(string $userId): void
    {
        // Clear session data
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_destroy();
        }
        
        // Clear cookies
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }
        
        // Clear any other authentication cookies
        setcookie('auth_token', '', time() - 3600, '/');
        setcookie('user_data', '', time() - 3600, '/');
        
        echo "Cleared session for user: " . $userId . "\n";
    }

    public function onLogout(callable $callback): void
    {
        $this->onLogoutCallback = $callback;
    }

    public function startListening(): void
    {
        echo "Starting logout notification listener...\n";
        
        while ($this->rabbitMqChannel->is_consuming()) {
            $this->rabbitMqChannel->wait();
        }
    }

    public function disconnect(): void
    {
        if ($this->rabbitMqChannel) {
            $this->rabbitMqChannel->close();
        }
        
        if ($this->rabbitMqConnection) {
            $this->rabbitMqConnection->close();
        }
        
        if ($this->redisClient) {
            $this->redisClient->disconnect();
        }
    }
}

// Configuration
$config = [
    'redis' => [
        'host' => 'localhost',
        'port' => 6379,
        'password' => null,
        'logout_channel' => 'identityserver:logout'
    ],
    'rabbitmq' => [
        'host' => 'localhost',
        'port' => 5672,
        'username' => 'guest',
        'password' => 'guest',
        'exchange' => 'LogoutEvents'
    ],
    'client_id' => 'your-php-client-id'
];

// Usage
$logoutService = new LogoutNotificationService($config);

$logoutService->onLogout(function ($message) {
    // Handle logout in your PHP application
    echo "User logged out: " . $message['userName'] . "\n";
    
    // You can redirect to login page, clear cache, etc.
    header('Location: /login');
    exit;
});

// Start listening for notifications
$logoutService->startListening();
```

## Configuration Summary

### Identity Server Configuration

```json
{
  "Redis": {
    "IsEnabled": "true",
    "Configuration": "localhost:6379"
  },
  "RabbitMQ": {
    "Connections": {
      "Default": {
        "HostName": "localhost",
        "UserName": "guest",
        "Password": "guest",
        "Port": 5672
      }
    },
    "EventBus": {
      "ClientName": "IdentityServer",
      "ExchangeName": "LogoutEvents"
    }
  },
  "LogoutNotification": {
    "RedisLogoutChannel": "identityserver:logout",
    "EnableRedisNotifications": true,
    "EnableRabbitMQNotifications": true,
    "HttpClientTimeoutSeconds": 30,
    "MaxRetryAttempts": 3,
    "RetryDelaySeconds": 5
  }
}
```

### Client Configuration

Each client should configure:
1. **Redis connection** for real-time notifications
2. **RabbitMQ connection** for reliable delivery
3. **Client ID** for identification
4. **Logout handling logic** specific to the application

## Best Practices

1. **Always handle both Redis and RabbitMQ notifications** for maximum reliability
2. **Implement proper error handling** and retry logic
3. **Use connection pooling** for Redis and RabbitMQ connections
4. **Implement graceful shutdown** to properly close connections
5. **Log all logout events** for audit purposes
6. **Test the notification system** thoroughly in your environment
7. **Monitor the health** of Redis and RabbitMQ connections
8. **Implement circuit breakers** for external service calls
9. **Use secure connections** (TLS/SSL) in production
10. **Implement rate limiting** to prevent abuse

## Security Considerations

1. **Validate all incoming messages** before processing
2. **Use secure connections** for Redis and RabbitMQ
3. **Implement proper authentication** for client applications
4. **Log security events** for monitoring
5. **Use environment-specific configurations**
6. **Implement message signing** for critical notifications
7. **Regular security audits** of the notification system 