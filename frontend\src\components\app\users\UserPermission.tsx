import {
  type IdentityUserUpdateDto,
  type PermissionGrantInfoDto,
  type PermissionGroupDto,
  type UpdatePermissionsDto,
  putApiPermissionManagementPermissions,
} from '@/client'
import { Permission } from '@/components/app/permission/PermissionToggle'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { usePermissions } from '@/lib/hooks/usePermissions'
import { useUserRoles } from '@/lib/hooks/useUserRoles'
import { useToast } from "@/lib/useToast"
import { PermissionProvider, USER_ROLE } from '@/lib/utils'
import { useQueryClient } from '@tanstack/react-query'
import { type FormEvent, useCallback, useEffect, useMemo, useState } from 'react'

type UserPermissionProps = {
  userDto: IdentityUserUpdateDto
  userId: string
  onDismiss: () => void
}

export const UserPermission = ({ userDto, userId, onDismiss }: UserPermissionProps) => {
  const { toast } = useToast()
  const userRoles = useUserRoles({ userId })

  // flag determine to enable/disable all the permissions to a user.
  const [hasAllGranted, setHasAllGranted] = useState(false)
  const { data } = usePermissions(PermissionProvider.U, userId)
  const queryClient = useQueryClient()

  const [permissionGroups, setPermissionGroups] = useState<PermissionGroupDto[]>([])

  useEffect(() => {
    return () => {
      void queryClient.invalidateQueries({ queryKey: [PermissionProvider.U] })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Update the local state with the remote data
  useEffect(() => {
    if (data?.groups && Array.isArray(data.groups)) {
      setPermissionGroups([...data.groups])
    }
  }, [data])

  // check if user have all the permissions are granted already.
  // Only run this when data changes, not when permissionGroups changes internally
  useEffect(() => {
    if (data?.groups && data.groups.length > 0) {
      const hasAllPermissionGranted = data.groups
        .map((g) => g.permissions?.every((p) => p.isGranted))
        .every((e) => e)
      setHasAllGranted(hasAllPermissionGranted)
    }
  }, [data])

  // Apply hasAllGranted to all permissions - only when hasAllGranted changes
  useEffect(() => {
    if (permissionGroups.length > 0) {
      const updatedGroups = permissionGroups.map(group => {
        // Create a new group object to avoid reference issues
        return {
          ...group,
          permissions: group.permissions?.map(permission => ({
            ...permission,
            isGranted: hasAllGranted
          }))
        };
      });

      // Break the circular dependency by using a ref to track updates
      setPermissionGroups(updatedGroups);
    }
  }, [hasAllGranted]); // Remove permissionGroups from dependency array

  const onSubmit = useCallback(
    async (e: FormEvent) => {
      e.preventDefault()
      const payload = permissionGroups
        ?.map((p) =>
          p.permissions!.map((grant) => ({
            name: grant.name,
            isGranted: grant.isGranted,
          }))
        )
        .flat()
      const requestPayload: UpdatePermissionsDto = {
        permissions: payload,
      }
      try {
        await putApiPermissionManagementPermissions({
          query: {
            providerName: PermissionProvider.U,
            providerKey: userId
          },
          body: requestPayload
        })
        toast({
          title: 'Success',
          description: 'Permission Updated Successfully',
          variant: 'default',
        })
        void queryClient.invalidateQueries({
          queryKey: [PermissionProvider.U],
        })
        onCloseEvent()
      } catch (err: unknown) {
        if (err instanceof Error) {
          toast({
            title: 'Failed',
            description: "Permission update wasn't successful.",
            variant: 'destructive',
          })
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [permissionGroups]
  )

  const onCloseEvent = useCallback(() => {
    onDismiss()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const hasAdmin = useMemo(() => {
    if (userRoles?.data?.items) {
      return userRoles.data.items.filter((role) => role.name?.includes(USER_ROLE.ADMIN)).length > 0
    }
    return false
  }, [userRoles])

  const [selectedTab, setSelectedTab] = useState(0)

  // Helper: Group permissions by parent and children (Create, View, Edit, Delete)
  const groupPermissionsByParent = (permissions: PermissionGrantInfoDto[] | null | undefined) => {
    if (!Array.isArray(permissions) || permissions.length === 0) return { parentRows: [], otherPermissions: [] }
    const parentMap: Record<string, { parent: PermissionGrantInfoDto, children: Record<string, PermissionGrantInfoDto> }> = {}
    const otherPermissions: PermissionGrantInfoDto[] = []
    const childActions = ["Create", "View", "Edit", "Delete"]
    permissions.forEach((perm) => {
      // Parent: no dot in name, or ends with parent entity
      const dotCount = (perm.name?.match(/\./g) || []).length
      if (dotCount === 1 && !childActions.some(a => perm.name?.endsWith('.' + a))) {
        // e.g., EkbApp.MasterAgent
        parentMap[perm.name!] = { parent: perm, children: {} }
      }
    })
    // Now, assign children
    permissions.forEach((perm) => {
      const match = perm.name?.match(/^(.*)\.(Create|View|Edit|Delete)$/)
      if (match) {
        const parentKey = match[1]
        const action = match[2]
        if (parentMap[parentKey]) {
          parentMap[parentKey].children[action] = perm
        } else {
          otherPermissions.push(perm)
        }
      } else {
        // If not a parent or child, and not already in parentMap, treat as other
        if (!parentMap[perm.name!]) {
          otherPermissions.push(perm)
        }
      }
    })
    return { parentRows: Object.values(parentMap), otherPermissions }
  }

  return (
    <Dialog open={true} onOpenChange={onCloseEvent}>
      <DialogContent size={'4xl'} className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Permissions - {userDto.userName}</DialogTitle>
        </DialogHeader>
        <form onSubmit={onSubmit} className="flex-1 overflow-y-auto">
          <div className="p-1">
            <Permission
              name="Grant All Permissions"
              isGranted={hasAllGranted}
              id="all_granted"
              disabled={!hasAdmin}
              onUpdate={() => {
                setHasAllGranted((prev) => !prev)
              }}
              className="ml-2 mb-4"
            />
            {/* Tabs for permission groups */}
            <Tabs
              value={selectedTab.toString()}
              onValueChange={v => setSelectedTab(Number(v))}
              orientation='vertical'
              className="flex flex-col justify-stretch lg:flex-row gap-4 text-sm text-muted-foreground w-full p-4 border border-border rounded-lg"
            >
              <div className="lg:w-[200px] lg:shrink-0">
                <TabsList variant="button" className="flex flex-col items-stretch *:justify-start sticky top-0 bg-white z-10">
                  {permissionGroups.map((group, idx) => (
                    <TabsTrigger key={group.name} value={idx.toString()}>{group.displayName}</TabsTrigger>
                  ))}
                </TabsList>
              </div>
              <div className="grow border-s border-border py-0 ps-4 overflow-y-auto">
                {permissionGroups.map((group, idx) => {
                  const { parentRows, otherPermissions } = groupPermissionsByParent(group.permissions ?? [])
                  // Gather all parent and child permissions in this group (table only)
                  const allTablePerms: PermissionGrantInfoDto[] = [
                    ...parentRows.map(r => r.parent),
                    ...parentRows.flatMap(r => Object.values(r.children))
                  ]
                  const allTableChecked = allTablePerms.length > 0 && allTablePerms.every(p => p.isGranted)
                  const handleSelectAllTable = (checked: boolean) => {
                    setPermissionGroups(prev => prev.map((g, gIdx) => {
                      if (gIdx !== idx) return g
                      return {
                        ...g,
                        permissions: g.permissions?.map(p => {
                          // Only update parent and child permissions
                          const isParent = parentRows.some(r => r.parent.name === p.name)
                          const isChild = parentRows.some(r => Object.values(r.children).some(c => c.name === p.name))
                          if (isParent || isChild) {
                            return { ...p, isGranted: checked }
                          }
                          return p
                        })
                      }
                    }))
                  }
                  return (
                    <TabsContent key={group.name} value={idx.toString()} className="overflow-x-auto">
                      {/* Select All for table */}
                      <div className="mb-2 flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={allTableChecked}
                          onChange={e => handleSelectAllTable(e.target.checked)}
                          aria-label="Select all permissions in this table"
                          className="w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"
                        />
                        <span>Select All</span>
                      </div>
                      {/* Table for parent/child permissions */}
                      {parentRows.length > 0 && (
                        <table className="min-w-full border mb-6">
                          <thead>
                            <tr>
                              <th className="px-4 py-2 text-left">Name</th>
                              <th className="px-4 py-2 text-center">Parent</th>
                              <th className="px-4 py-2 text-center">View</th>
                              <th className="px-4 py-2 text-center">Create</th>
                              <th className="px-4 py-2 text-center">Edit</th>
                              <th className="px-4 py-2 text-center">Delete</th>
                            </tr>
                          </thead>
                          <tbody>
                            {parentRows.map(({ parent, children }) => (
                              <tr key={parent.name} className="border-t">
                                <td className="px-4 py-2 font-medium">{parent.displayName ?? parent.name}</td>
                                {/* Parent checkbox */}
                                <td className="px-4 py-2 text-center">
                                  <input
                                    type="checkbox"
                                    checked={parent.isGranted}
                                    onChange={() => {
                                      setPermissionGroups(prev => prev.map((g, gIdx) => {
                                        if (gIdx !== idx) return g
                                        return {
                                          ...g,
                                          permissions: g.permissions?.map(p =>
                                            p.name === parent.name ? { ...p, isGranted: !p.isGranted } : p
                                          )
                                        }
                                      }))
                                    }}
                                    aria-label={String(parent.displayName ?? parent.name)}
                                    className="w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"
                                  />
                                </td>
                                {/* Child checkboxes: View, Create, Edit, Delete */}
                                {['View', 'Create', 'Edit', 'Delete'].map(action => (
                                  <td key={action} className="px-4 py-2 text-center">
                                    {children[action] ? (
                                      <input
                                        type="checkbox"
                                        checked={children[action].isGranted}
                                        onChange={() => {
                                          setPermissionGroups(prev => prev.map((g, gIdx) => {
                                            if (gIdx !== idx) return g
                                            return {
                                              ...g,
                                              permissions: g.permissions?.map(p =>
                                                p.name === children[action].name ? { ...p, isGranted: !p.isGranted } : p
                                              )
                                            }
                                          }))
                                        }}
                                        aria-label={String(children[action].displayName ?? children[action].name)}
                                        className="w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"
                                      />
                                    ) : null}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      )}
                      {/* Other permissions */}
                      {otherPermissions.length > 0 && (
                        <div className="mb-4">
                          <div className="font-semibold mb-2">Other Permissions</div>
                          <div className="grid grid-cols-1 md:grid-cols-1 gap-2">
                            {otherPermissions.map(perm => (
                              <label key={perm.name} className="flex items-center gap-2">
                                <input
                                  type="checkbox"
                                  checked={perm.isGranted}
                                  onChange={() => {
                                    setPermissionGroups(prev => prev.map((g, gIdx) => {
                                      if (gIdx !== idx) return g
                                      return {
                                        ...g,
                                        permissions: g.permissions?.map(p =>
                                          p.name === perm.name ? { ...p, isGranted: !p.isGranted } : p
                                        )
                                      }
                                    }))
                                  }}
                                  aria-label={String(perm.name ?? perm.displayName)}
                                  className="w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400"
                                />
                                <span>{perm.name ?? perm.displayName}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      )}
                    </TabsContent>
                  )
                })}
              </div>
            </Tabs>
          </div>
        </form>
        <DialogFooter className="mt-4 border-t pt-4 bg-white dark:bg-gray-950">
          <Button
            onClick={(e) => {
              e.preventDefault()
              onCloseEvent()
            }}
            variant="ghost"
          >
            Cancel
          </Button>
          <Button onClick={onSubmit}>Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
