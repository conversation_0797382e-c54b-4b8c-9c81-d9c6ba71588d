import{j as M,b as F,r as Ke}from"./vendor-B0b15ZrB.js";import{p as Et,q as pt,R as Ct,C as Tt,r as Ot,s as Rt,t as Lt,a as Nt,O as Mt}from"./radix-BQPyiA8r.js";import{G as le,H as Ut,X as Bt,at as It}from"./app-layout-D_A4XD_6.js";function yr({className:e,...s}){return M.jsx(Et,{"data-slot":"checkbox",className:le("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:M.jsx(pt,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:M.jsx(Ut,{className:"size-3.5"})})})}function gr({className:e,type:s,...t}){return M.jsx("input",{type:s,"data-slot":"input",className:le("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground","flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none","file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium","disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","md:text-sm","border-input dark:bg-input/30","focus-visible:border-primary focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40 focus-visible:ring-[3px]","aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40","hover:border-primary/50 dark:hover:border-primary/30","active:border-primary/70 dark:active:border-primary/50",e),...t})}const Pt=It("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200",{variants:{size:{sm:"sm:max-w-sm",md:"sm:max-w-md",lg:"sm:max-w-lg",xl:"sm:max-w-xl","2xl":"sm:max-w-2xl","3xl":"sm:max-w-3xl","4xl":"sm:max-w-4xl","5xl":"sm:max-w-5xl","6xl":"sm:max-w-6xl","7xl":"sm:max-w-7xl",full:"sm:max-w-full"}},defaultVariants:{size:"lg"}});function hr({...e}){return M.jsx(Ct,{"data-slot":"dialog",...e})}function br({...e}){return M.jsx(Lt,{"data-slot":"dialog-trigger",...e})}function jt({...e}){return M.jsx(Nt,{"data-slot":"dialog-portal",...e})}function qt({className:e,...s}){return M.jsx(Mt,{"data-slot":"dialog-overlay",className:le("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...s})}function vr({className:e,children:s,showCloseButton:t=!0,size:a,...l}){return M.jsxs(jt,{"data-slot":"dialog-portal",children:[M.jsx(qt,{}),M.jsxs(Tt,{"data-slot":"dialog-content",className:le(Pt({size:a}),e),...l,children:[s,t&&M.jsxs(Ot,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[M.jsx(Bt,{}),M.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function mr({className:e,...s}){return M.jsx("div",{"data-slot":"dialog-header",className:le("flex flex-col gap-2 text-center sm:text-left",e),...s})}function xr({className:e,...s}){return M.jsx("div",{"data-slot":"dialog-footer",className:le("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...s})}function _r({className:e,...s}){return M.jsx(Rt,{"data-slot":"dialog-title",className:le("text-lg leading-none font-semibold",e),...s})}var ge=e=>e.type==="checkbox",ae=e=>e instanceof Date,B=e=>e==null;const lt=e=>typeof e=="object";var T=e=>!B(e)&&!Array.isArray(e)&&lt(e)&&!ae(e),nt=e=>T(e)&&e.target?ge(e.target)?e.target.checked:e.target.value:e,zt=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,ot=(e,s)=>e.has(zt(s)),Wt=e=>{const s=e.constructor&&e.constructor.prototype;return T(s)&&s.hasOwnProperty("isPrototypeOf")},Oe=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function N(e){let s;const t=Array.isArray(e),a=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)s=new Date(e);else if(e instanceof Set)s=new Set(e);else if(!(Oe&&(e instanceof Blob||a))&&(t||T(e)))if(s=t?[]:{},!t&&!Wt(e))s=e;else for(const l in e)e.hasOwnProperty(l)&&(s[l]=N(e[l]));else return e;return s}var we=e=>Array.isArray(e)?e.filter(Boolean):[],C=e=>e===void 0,f=(e,s,t)=>{if(!s||!T(e))return t;const a=we(s.split(/[,[\].]+?/)).reduce((l,n)=>B(l)?l:l[n],e);return C(a)||a===e?C(e[s])?t:e[s]:a},q=e=>typeof e=="boolean",Re=e=>/^\w*$/.test(e),ut=e=>we(e.replace(/["|']|\]/g,"").split(/\.|\[/)),A=(e,s,t)=>{let a=-1;const l=Re(s)?[s]:ut(s),n=l.length,c=n-1;for(;++a<n;){const g=l[a];let m=t;if(a!==c){const L=e[g];m=T(L)||Array.isArray(L)?L:isNaN(+l[a+1])?{}:[]}if(g==="__proto__"||g==="constructor"||g==="prototype")return;e[g]=m,e=e[g]}};const xe={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},G={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Z={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Ht=F.createContext(null),Le=()=>F.useContext(Ht);var dt=(e,s,t,a=!0)=>{const l={defaultValues:s._defaultValues};for(const n in e)Object.defineProperty(l,n,{get:()=>{const c=n;return s._proxyFormState[c]!==G.all&&(s._proxyFormState[c]=!a||G.all),t&&(t[c]=!0),e[c]}});return l};const Ne=typeof window<"u"?Ke.useLayoutEffect:Ke.useEffect;function $t(e){const s=Le(),{control:t=s.control,disabled:a,name:l,exact:n}=e||{},[c,g]=F.useState(t._formState),m=F.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return Ne(()=>t._subscribe({name:l,formState:m.current,exact:n,callback:L=>{!a&&g({...t._formState,...L})}}),[l,a,n]),F.useEffect(()=>{m.current.isValid&&t._setValid(!0)},[t]),F.useMemo(()=>dt(c,t,m.current,!1),[c,t])}var J=e=>typeof e=="string",ct=(e,s,t,a,l)=>J(e)?(a&&s.watch.add(e),f(t,e,l)):Array.isArray(e)?e.map(n=>(a&&s.watch.add(n),f(t,n))):(a&&(s.watchAll=!0),t);function Gt(e){const s=Le(),{control:t=s.control,name:a,defaultValue:l,disabled:n,exact:c}=e||{},g=F.useRef(l),[m,L]=F.useState(t._getWatch(a,g.current));return Ne(()=>t._subscribe({name:a,formState:{values:!0},exact:c,callback:_=>!n&&L(ct(a,t._names,_.values||t._formValues,!1,g.current))}),[a,t,n,c]),F.useEffect(()=>t._removeUnmounted()),m}function Kt(e){const s=Le(),{name:t,disabled:a,control:l=s.control,shouldUnregister:n}=e,c=ot(l._names.array,t),g=Gt({control:l,name:t,defaultValue:f(l._formValues,t,f(l._defaultValues,t,e.defaultValue)),exact:!0}),m=$t({control:l,name:t,exact:!0}),L=F.useRef(e),_=F.useRef(l.register(t,{...e.rules,value:g,...q(e.disabled)?{disabled:e.disabled}:{}})),D=F.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!f(m.errors,t)},isDirty:{enumerable:!0,get:()=>!!f(m.dirtyFields,t)},isTouched:{enumerable:!0,get:()=>!!f(m.touchedFields,t)},isValidating:{enumerable:!0,get:()=>!!f(m.validatingFields,t)},error:{enumerable:!0,get:()=>f(m.errors,t)}}),[m,t]),b=F.useCallback(E=>_.current.onChange({target:{value:nt(E),name:t},type:xe.CHANGE}),[t]),ee=F.useCallback(()=>_.current.onBlur({target:{value:f(l._formValues,t),name:t},type:xe.BLUR}),[t,l._formValues]),W=F.useCallback(E=>{const Q=f(l._fields,t);Q&&E&&(Q._f.ref={focus:()=>E.focus&&E.focus(),select:()=>E.select&&E.select(),setCustomValidity:v=>E.setCustomValidity(v),reportValidity:()=>E.reportValidity()})},[l._fields,t]),S=F.useMemo(()=>({name:t,value:g,...q(a)||m.disabled?{disabled:m.disabled||a}:{},onChange:b,onBlur:ee,ref:W}),[t,a,m.disabled,b,ee,W,g]);return F.useEffect(()=>{const E=l._options.shouldUnregister||n;l.register(t,{...L.current.rules,...q(L.current.disabled)?{disabled:L.current.disabled}:{}});const Q=(v,H)=>{const I=f(l._fields,v);I&&I._f&&(I._f.mount=H)};if(Q(t,!0),E){const v=N(f(l._options.defaultValues,t));A(l._defaultValues,t,v),C(f(l._formValues,t))&&A(l._formValues,t,v)}return!c&&l.register(t),()=>{(c?E&&!l._state.action:E)?l.unregister(t):Q(t,!1)}},[t,l,c,n]),F.useEffect(()=>{l._setDisabledField({disabled:a,name:t})},[a,t,l]),F.useMemo(()=>({field:S,formState:m,fieldState:D}),[S,m,D])}const Vr=e=>e.render(Kt(e));var Xt=(e,s,t,a,l)=>s?{...t[e],types:{...t[e]&&t[e].types?t[e].types:{},[a]:l||!0}}:{},fe=e=>Array.isArray(e)?e:[e],Xe=()=>{let e=[];return{get observers(){return e},next:l=>{for(const n of e)n.next&&n.next(l)},subscribe:l=>(e.push(l),{unsubscribe:()=>{e=e.filter(n=>n!==l)}}),unsubscribe:()=>{e=[]}}},Te=e=>B(e)||!lt(e);function se(e,s){if(Te(e)||Te(s))return e===s;if(ae(e)&&ae(s))return e.getTime()===s.getTime();const t=Object.keys(e),a=Object.keys(s);if(t.length!==a.length)return!1;for(const l of t){const n=e[l];if(!a.includes(l))return!1;if(l!=="ref"){const c=s[l];if(ae(n)&&ae(c)||T(n)&&T(c)||Array.isArray(n)&&Array.isArray(c)?!se(n,c):n!==c)return!1}}return!0}var j=e=>T(e)&&!Object.keys(e).length,Me=e=>e.type==="file",K=e=>typeof e=="function",_e=e=>{if(!Oe)return!1;const s=e?e.ownerDocument:0;return e instanceof(s&&s.defaultView?s.defaultView.HTMLElement:HTMLElement)},ft=e=>e.type==="select-multiple",Ue=e=>e.type==="radio",Yt=e=>Ue(e)||ge(e),Ce=e=>_e(e)&&e.isConnected;function Jt(e,s){const t=s.slice(0,-1).length;let a=0;for(;a<t;)e=C(e)?a++:e[s[a++]];return e}function Qt(e){for(const s in e)if(e.hasOwnProperty(s)&&!C(e[s]))return!1;return!0}function R(e,s){const t=Array.isArray(s)?s:Re(s)?[s]:ut(s),a=t.length===1?e:Jt(e,t),l=t.length-1,n=t[l];return a&&delete a[n],l!==0&&(T(a)&&j(a)||Array.isArray(a)&&Qt(a))&&R(e,t.slice(0,-1)),e}var yt=e=>{for(const s in e)if(K(e[s]))return!0;return!1};function Ve(e,s={}){const t=Array.isArray(e);if(T(e)||t)for(const a in e)Array.isArray(e[a])||T(e[a])&&!yt(e[a])?(s[a]=Array.isArray(e[a])?[]:{},Ve(e[a],s[a])):B(e[a])||(s[a]=!0);return s}function gt(e,s,t){const a=Array.isArray(e);if(T(e)||a)for(const l in e)Array.isArray(e[l])||T(e[l])&&!yt(e[l])?C(s)||Te(t[l])?t[l]=Array.isArray(e[l])?Ve(e[l],[]):{...Ve(e[l])}:gt(e[l],B(s)?{}:s[l],t[l]):t[l]=!se(e[l],s[l]);return t}var de=(e,s)=>gt(e,s,Ve(s));const Ye={value:!1,isValid:!1},Je={value:!0,isValid:!0};var ht=e=>{if(Array.isArray(e)){if(e.length>1){const s=e.filter(t=>t&&t.checked&&!t.disabled).map(t=>t.value);return{value:s,isValid:!!s.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!C(e[0].attributes.value)?C(e[0].value)||e[0].value===""?Je:{value:e[0].value,isValid:!0}:Je:Ye}return Ye},bt=(e,{valueAsNumber:s,valueAsDate:t,setValueAs:a})=>C(e)?e:s?e===""?NaN:e&&+e:t&&J(e)?new Date(e):a?a(e):e;const Qe={isValid:!1,value:null};var vt=e=>Array.isArray(e)?e.reduce((s,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:s,Qe):Qe;function Ze(e){const s=e.ref;return Me(s)?s.files:Ue(s)?vt(e.refs).value:ft(s)?[...s.selectedOptions].map(({value:t})=>t):ge(s)?ht(e.refs).value:bt(C(s.value)?e.ref.value:s.value,e)}var Zt=(e,s,t,a)=>{const l={};for(const n of e){const c=f(s,n);c&&A(l,n,c._f)}return{criteriaMode:t,names:[...e],fields:l,shouldUseNativeValidation:a}},Fe=e=>e instanceof RegExp,ce=e=>C(e)?e:Fe(e)?e.source:T(e)?Fe(e.value)?e.value.source:e.value:e,et=e=>({isOnSubmit:!e||e===G.onSubmit,isOnBlur:e===G.onBlur,isOnChange:e===G.onChange,isOnAll:e===G.all,isOnTouch:e===G.onTouched});const tt="AsyncFunction";var er=e=>!!e&&!!e.validate&&!!(K(e.validate)&&e.validate.constructor.name===tt||T(e.validate)&&Object.values(e.validate).find(s=>s.constructor.name===tt)),tr=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),rt=(e,s,t)=>!t&&(s.watchAll||s.watch.has(e)||[...s.watch].some(a=>e.startsWith(a)&&/^\.\w+/.test(e.slice(a.length))));const ye=(e,s,t,a)=>{for(const l of t||Object.keys(e)){const n=f(e,l);if(n){const{_f:c,...g}=n;if(c){if(c.refs&&c.refs[0]&&s(c.refs[0],l)&&!a)return!0;if(c.ref&&s(c.ref,c.name)&&!a)return!0;if(ye(g,s))break}else if(T(g)&&ye(g,s))break}}};function st(e,s,t){const a=f(e,t);if(a||Re(t))return{error:a,name:t};const l=t.split(".");for(;l.length;){const n=l.join("."),c=f(s,n),g=f(e,n);if(c&&!Array.isArray(c)&&t!==n)return{name:t};if(g&&g.type)return{name:n,error:g};if(g&&g.root&&g.root.type)return{name:`${n}.root`,error:g.root};l.pop()}return{name:t}}var rr=(e,s,t,a)=>{t(e);const{name:l,...n}=e;return j(n)||Object.keys(n).length>=Object.keys(s).length||Object.keys(n).find(c=>s[c]===(!a||G.all))},sr=(e,s,t)=>!e||!s||e===s||fe(e).some(a=>a&&(t?a===s:a.startsWith(s)||s.startsWith(a))),ir=(e,s,t,a,l)=>l.isOnAll?!1:!t&&l.isOnTouch?!(s||e):(t?a.isOnBlur:l.isOnBlur)?!e:(t?a.isOnChange:l.isOnChange)?e:!0,ar=(e,s)=>!we(f(e,s)).length&&R(e,s),lr=(e,s,t)=>{const a=fe(f(e,t));return A(a,"root",s[t]),A(e,t,a),e},me=e=>J(e);function it(e,s,t="validate"){if(me(e)||Array.isArray(e)&&e.every(me)||q(e)&&!e)return{type:t,message:me(e)?e:"",ref:s}}var oe=e=>T(e)&&!Fe(e)?e:{value:e,message:""},at=async(e,s,t,a,l,n)=>{const{ref:c,refs:g,required:m,maxLength:L,minLength:_,min:D,max:b,pattern:ee,validate:W,name:S,valueAsNumber:E,mount:Q}=e._f,v=f(t,S);if(!Q||s.has(S))return{};const H=g?g[0]:c,I=x=>{l&&H.reportValidity&&(H.setCustomValidity(q(x)?"":x||""),H.reportValidity())},O={},he=Ue(c),te=ge(c),Ae=he||te,$=(E||Me(c))&&C(c.value)&&C(v)||_e(c)&&c.value===""||v===""||Array.isArray(v)&&!v.length,ie=Xt.bind(null,S,a,O),X=(x,w,p,U=Z.maxLength,P=Z.minLength)=>{const Y=x?w:p;O[S]={type:x?U:P,message:Y,ref:c,...ie(x?U:P,Y)}};if(n?!Array.isArray(v)||!v.length:m&&(!Ae&&($||B(v))||q(v)&&!v||te&&!ht(g).isValid||he&&!vt(g).isValid)){const{value:x,message:w}=me(m)?{value:!!m,message:m}:oe(m);if(x&&(O[S]={type:Z.required,message:w,ref:H,...ie(Z.required,w)},!a))return I(w),O}if(!$&&(!B(D)||!B(b))){let x,w;const p=oe(b),U=oe(D);if(!B(v)&&!isNaN(v)){const P=c.valueAsNumber||v&&+v;B(p.value)||(x=P>p.value),B(U.value)||(w=P<U.value)}else{const P=c.valueAsDate||new Date(v),Y=be=>new Date(new Date().toDateString()+" "+be),ue=c.type=="time",ne=c.type=="week";J(p.value)&&v&&(x=ue?Y(v)>Y(p.value):ne?v>p.value:P>new Date(p.value)),J(U.value)&&v&&(w=ue?Y(v)<Y(U.value):ne?v<U.value:P<new Date(U.value))}if((x||w)&&(X(!!x,p.message,U.message,Z.max,Z.min),!a))return I(O[S].message),O}if((L||_)&&!$&&(J(v)||n&&Array.isArray(v))){const x=oe(L),w=oe(_),p=!B(x.value)&&v.length>+x.value,U=!B(w.value)&&v.length<+w.value;if((p||U)&&(X(p,x.message,w.message),!a))return I(O[S].message),O}if(ee&&!$&&J(v)){const{value:x,message:w}=oe(ee);if(Fe(x)&&!v.match(x)&&(O[S]={type:Z.pattern,message:w,ref:c,...ie(Z.pattern,w)},!a))return I(w),O}if(W){if(K(W)){const x=await W(v,t),w=it(x,H);if(w&&(O[S]={...w,...ie(Z.validate,w.message)},!a))return I(w.message),O}else if(T(W)){let x={};for(const w in W){if(!j(x)&&!a)break;const p=it(await W[w](v,t),H,w);p&&(x={...p,...ie(w,p.message)},I(p.message),a&&(O[S]=x))}if(!j(x)&&(O[S]={ref:H,...x},!a))return O}}return I(!0),O};const nr={mode:G.onSubmit,reValidateMode:G.onChange,shouldFocusError:!0};function or(e={}){let s={...nr,...e},t={submitCount:0,isDirty:!1,isReady:!1,isLoading:K(s.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:s.errors||{},disabled:s.disabled||!1};const a={};let l=T(s.defaultValues)||T(s.values)?N(s.defaultValues||s.values)||{}:{},n=s.shouldUnregister?{}:N(l),c={action:!1,mount:!1,watch:!1},g={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},m,L=0;const _={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let D={..._};const b={array:Xe(),state:Xe()},ee=s.criteriaMode===G.all,W=r=>i=>{clearTimeout(L),L=setTimeout(r,i)},S=async r=>{if(!s.disabled&&(_.isValid||D.isValid||r)){const i=s.resolver?j((await te()).errors):await $(a,!0);i!==t.isValid&&b.state.next({isValid:i})}},E=(r,i)=>{!s.disabled&&(_.isValidating||_.validatingFields||D.isValidating||D.validatingFields)&&((r||Array.from(g.mount)).forEach(o=>{o&&(i?A(t.validatingFields,o,i):R(t.validatingFields,o))}),b.state.next({validatingFields:t.validatingFields,isValidating:!j(t.validatingFields)}))},Q=(r,i=[],o,y,d=!0,u=!0)=>{if(y&&o&&!s.disabled){if(c.action=!0,u&&Array.isArray(f(a,r))){const h=o(f(a,r),y.argA,y.argB);d&&A(a,r,h)}if(u&&Array.isArray(f(t.errors,r))){const h=o(f(t.errors,r),y.argA,y.argB);d&&A(t.errors,r,h),ar(t.errors,r)}if((_.touchedFields||D.touchedFields)&&u&&Array.isArray(f(t.touchedFields,r))){const h=o(f(t.touchedFields,r),y.argA,y.argB);d&&A(t.touchedFields,r,h)}(_.dirtyFields||D.dirtyFields)&&(t.dirtyFields=de(l,n)),b.state.next({name:r,isDirty:X(r,i),dirtyFields:t.dirtyFields,errors:t.errors,isValid:t.isValid})}else A(n,r,i)},v=(r,i)=>{A(t.errors,r,i),b.state.next({errors:t.errors})},H=r=>{t.errors=r,b.state.next({errors:t.errors,isValid:!1})},I=(r,i,o,y)=>{const d=f(a,r);if(d){const u=f(n,r,C(o)?f(l,r):o);C(u)||y&&y.defaultChecked||i?A(n,r,i?u:Ze(d._f)):p(r,u),c.mount&&S()}},O=(r,i,o,y,d)=>{let u=!1,h=!1;const V={name:r};if(!s.disabled){if(!o||y){(_.isDirty||D.isDirty)&&(h=t.isDirty,t.isDirty=V.isDirty=X(),u=h!==V.isDirty);const k=se(f(l,r),i);h=!!f(t.dirtyFields,r),k?R(t.dirtyFields,r):A(t.dirtyFields,r,!0),V.dirtyFields=t.dirtyFields,u=u||(_.dirtyFields||D.dirtyFields)&&h!==!k}if(o){const k=f(t.touchedFields,r);k||(A(t.touchedFields,r,o),V.touchedFields=t.touchedFields,u=u||(_.touchedFields||D.touchedFields)&&k!==o)}u&&d&&b.state.next(V)}return u?V:{}},he=(r,i,o,y)=>{const d=f(t.errors,r),u=(_.isValid||D.isValid)&&q(i)&&t.isValid!==i;if(s.delayError&&o?(m=W(()=>v(r,o)),m(s.delayError)):(clearTimeout(L),m=null,o?A(t.errors,r,o):R(t.errors,r)),(o?!se(d,o):d)||!j(y)||u){const h={...y,...u&&q(i)?{isValid:i}:{},errors:t.errors,name:r};t={...t,...h},b.state.next(h)}},te=async r=>{E(r,!0);const i=await s.resolver(n,s.context,Zt(r||g.mount,a,s.criteriaMode,s.shouldUseNativeValidation));return E(r),i},Ae=async r=>{const{errors:i}=await te(r);if(r)for(const o of r){const y=f(i,o);y?A(t.errors,o,y):R(t.errors,o)}else t.errors=i;return i},$=async(r,i,o={valid:!0})=>{for(const y in r){const d=r[y];if(d){const{_f:u,...h}=d;if(u){const V=g.array.has(u.name),k=d._f&&er(d._f);k&&_.validatingFields&&E([y],!0);const z=await at(d,g.disabled,n,ee,s.shouldUseNativeValidation&&!i,V);if(k&&_.validatingFields&&E([y]),z[u.name]&&(o.valid=!1,i))break;!i&&(f(z,u.name)?V?lr(t.errors,z,u.name):A(t.errors,u.name,z[u.name]):R(t.errors,u.name))}!j(h)&&await $(h,i,o)}}return o.valid},ie=()=>{for(const r of g.unMount){const i=f(a,r);i&&(i._f.refs?i._f.refs.every(o=>!Ce(o)):!Ce(i._f.ref))&&ke(r)}g.unMount=new Set},X=(r,i)=>!s.disabled&&(r&&i&&A(n,r,i),!se(be(),l)),x=(r,i,o)=>ct(r,g,{...c.mount?n:C(i)?l:J(r)?{[r]:i}:i},o,i),w=r=>we(f(c.mount?n:l,r,s.shouldUnregister?f(l,r,[]):[])),p=(r,i,o={})=>{const y=f(a,r);let d=i;if(y){const u=y._f;u&&(!u.disabled&&A(n,r,bt(i,u)),d=_e(u.ref)&&B(i)?"":i,ft(u.ref)?[...u.ref.options].forEach(h=>h.selected=d.includes(h.value)):u.refs?ge(u.ref)?u.refs.forEach(h=>{(!h.defaultChecked||!h.disabled)&&(Array.isArray(d)?h.checked=!!d.find(V=>V===h.value):h.checked=d===h.value||!!d)}):u.refs.forEach(h=>h.checked=h.value===d):Me(u.ref)?u.ref.value="":(u.ref.value=d,u.ref.type||b.state.next({name:r,values:N(n)})))}(o.shouldDirty||o.shouldTouch)&&O(r,d,o.shouldTouch,o.shouldDirty,!0),o.shouldValidate&&ne(r)},U=(r,i,o)=>{for(const y in i){if(!i.hasOwnProperty(y))return;const d=i[y],u=r+"."+y,h=f(a,u);(g.array.has(r)||T(d)||h&&!h._f)&&!ae(d)?U(u,d,o):p(u,d,o)}},P=(r,i,o={})=>{const y=f(a,r),d=g.array.has(r),u=N(i);A(n,r,u),d?(b.array.next({name:r,values:N(n)}),(_.isDirty||_.dirtyFields||D.isDirty||D.dirtyFields)&&o.shouldDirty&&b.state.next({name:r,dirtyFields:de(l,n),isDirty:X(r,u)})):y&&!y._f&&!B(u)?U(r,u,o):p(r,u,o),rt(r,g)&&b.state.next({...t}),b.state.next({name:c.mount?r:void 0,values:N(n)})},Y=async r=>{c.mount=!0;const i=r.target;let o=i.name,y=!0;const d=f(a,o),u=k=>{y=Number.isNaN(k)||ae(k)&&isNaN(k.getTime())||se(k,f(n,o,k))},h=et(s.mode),V=et(s.reValidateMode);if(d){let k,z;const ve=i.type?Ze(d._f):nt(r),re=r.type===xe.BLUR||r.type===xe.FOCUS_OUT,kt=!tr(d._f)&&!s.resolver&&!f(t.errors,o)&&!d._f.deps||ir(re,f(t.touchedFields,o),t.isSubmitted,V,h),Ee=rt(o,g,re);A(n,o,ve),re?(d._f.onBlur&&d._f.onBlur(r),m&&m(0)):d._f.onChange&&d._f.onChange(r);const pe=O(o,ve,re),Dt=!j(pe)||Ee;if(!re&&b.state.next({name:o,type:r.type,values:N(n)}),kt)return(_.isValid||D.isValid)&&(s.mode==="onBlur"?re&&S():re||S()),Dt&&b.state.next({name:o,...Ee?{}:pe});if(!re&&Ee&&b.state.next({...t}),s.resolver){const{errors:$e}=await te([o]);if(u(ve),y){const St=st(t.errors,a,o),Ge=st($e,a,St.name||o);k=Ge.error,o=Ge.name,z=j($e)}}else E([o],!0),k=(await at(d,g.disabled,n,ee,s.shouldUseNativeValidation))[o],E([o]),u(ve),y&&(k?z=!1:(_.isValid||D.isValid)&&(z=await $(a,!0)));y&&(d._f.deps&&ne(d._f.deps),he(o,z,k,pe))}},ue=(r,i)=>{if(f(t.errors,i)&&r.focus)return r.focus(),1},ne=async(r,i={})=>{let o,y;const d=fe(r);if(s.resolver){const u=await Ae(C(r)?r:d);o=j(u),y=r?!d.some(h=>f(u,h)):o}else r?(y=(await Promise.all(d.map(async u=>{const h=f(a,u);return await $(h&&h._f?{[u]:h}:h)}))).every(Boolean),!(!y&&!t.isValid)&&S()):y=o=await $(a);return b.state.next({...!J(r)||(_.isValid||D.isValid)&&o!==t.isValid?{}:{name:r},...s.resolver||!r?{isValid:o}:{},errors:t.errors}),i.shouldFocus&&!y&&ye(a,ue,r?d:g.mount),y},be=r=>{const i={...c.mount?n:l};return C(r)?i:J(r)?f(i,r):r.map(o=>f(i,o))},Be=(r,i)=>({invalid:!!f((i||t).errors,r),isDirty:!!f((i||t).dirtyFields,r),error:f((i||t).errors,r),isValidating:!!f(t.validatingFields,r),isTouched:!!f((i||t).touchedFields,r)}),mt=r=>{r&&fe(r).forEach(i=>R(t.errors,i)),b.state.next({errors:r?t.errors:{}})},Ie=(r,i,o)=>{const y=(f(a,r,{_f:{}})._f||{}).ref,d=f(t.errors,r)||{},{ref:u,message:h,type:V,...k}=d;A(t.errors,r,{...k,...i,ref:y}),b.state.next({name:r,errors:t.errors,isValid:!1}),o&&o.shouldFocus&&y&&y.focus&&y.focus()},xt=(r,i)=>K(r)?b.state.subscribe({next:o=>r(x(void 0,i),o)}):x(r,i,!0),Pe=r=>b.state.subscribe({next:i=>{sr(r.name,i.name,r.exact)&&rr(i,r.formState||_,At,r.reRenderRoot)&&r.callback({values:{...n},...t,...i})}}).unsubscribe,_t=r=>(c.mount=!0,D={...D,...r.formState},Pe({...r,formState:D})),ke=(r,i={})=>{for(const o of r?fe(r):g.mount)g.mount.delete(o),g.array.delete(o),i.keepValue||(R(a,o),R(n,o)),!i.keepError&&R(t.errors,o),!i.keepDirty&&R(t.dirtyFields,o),!i.keepTouched&&R(t.touchedFields,o),!i.keepIsValidating&&R(t.validatingFields,o),!s.shouldUnregister&&!i.keepDefaultValue&&R(l,o);b.state.next({values:N(n)}),b.state.next({...t,...i.keepDirty?{isDirty:X()}:{}}),!i.keepIsValid&&S()},je=({disabled:r,name:i})=>{(q(r)&&c.mount||r||g.disabled.has(i))&&(r?g.disabled.add(i):g.disabled.delete(i))},De=(r,i={})=>{let o=f(a,r);const y=q(i.disabled)||q(s.disabled);return A(a,r,{...o||{},_f:{...o&&o._f?o._f:{ref:{name:r}},name:r,mount:!0,...i}}),g.mount.add(r),o?je({disabled:q(i.disabled)?i.disabled:s.disabled,name:r}):I(r,!0,i.value),{...y?{disabled:i.disabled||s.disabled}:{},...s.progressive?{required:!!i.required,min:ce(i.min),max:ce(i.max),minLength:ce(i.minLength),maxLength:ce(i.maxLength),pattern:ce(i.pattern)}:{},name:r,onChange:Y,onBlur:Y,ref:d=>{if(d){De(r,i),o=f(a,r);const u=C(d.value)&&d.querySelectorAll&&d.querySelectorAll("input,select,textarea")[0]||d,h=Yt(u),V=o._f.refs||[];if(h?V.find(k=>k===u):u===o._f.ref)return;A(a,r,{_f:{...o._f,...h?{refs:[...V.filter(Ce),u,...Array.isArray(f(l,r))?[{}]:[]],ref:{type:u.type,name:r}}:{ref:u}}}),I(r,!1,void 0,u)}else o=f(a,r,{}),o._f&&(o._f.mount=!1),(s.shouldUnregister||i.shouldUnregister)&&!(ot(g.array,r)&&c.action)&&g.unMount.add(r)}}},Se=()=>s.shouldFocusError&&ye(a,ue,g.mount),Vt=r=>{q(r)&&(b.state.next({disabled:r}),ye(a,(i,o)=>{const y=f(a,o);y&&(i.disabled=y._f.disabled||r,Array.isArray(y._f.refs)&&y._f.refs.forEach(d=>{d.disabled=y._f.disabled||r}))},0,!1))},qe=(r,i)=>async o=>{let y;o&&(o.preventDefault&&o.preventDefault(),o.persist&&o.persist());let d=N(n);if(b.state.next({isSubmitting:!0}),s.resolver){const{errors:u,values:h}=await te();t.errors=u,d=h}else await $(a);if(g.disabled.size)for(const u of g.disabled)A(d,u,void 0);if(R(t.errors,"root"),j(t.errors)){b.state.next({errors:{}});try{await r(d,o)}catch(u){y=u}}else i&&await i({...t.errors},o),Se(),setTimeout(Se);if(b.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:j(t.errors)&&!y,submitCount:t.submitCount+1,errors:t.errors}),y)throw y},Ft=(r,i={})=>{f(a,r)&&(C(i.defaultValue)?P(r,N(f(l,r))):(P(r,i.defaultValue),A(l,r,N(i.defaultValue))),i.keepTouched||R(t.touchedFields,r),i.keepDirty||(R(t.dirtyFields,r),t.isDirty=i.defaultValue?X(r,N(f(l,r))):X()),i.keepError||(R(t.errors,r),_.isValid&&S()),b.state.next({...t}))},ze=(r,i={})=>{const o=r?N(r):l,y=N(o),d=j(r),u=d?l:y;if(i.keepDefaultValues||(l=o),!i.keepValues){if(i.keepDirtyValues){const h=new Set([...g.mount,...Object.keys(de(l,n))]);for(const V of Array.from(h))f(t.dirtyFields,V)?A(u,V,f(n,V)):P(V,f(u,V))}else{if(Oe&&C(r))for(const h of g.mount){const V=f(a,h);if(V&&V._f){const k=Array.isArray(V._f.refs)?V._f.refs[0]:V._f.ref;if(_e(k)){const z=k.closest("form");if(z){z.reset();break}}}}for(const h of g.mount)P(h,f(u,h))}n=N(u),b.array.next({values:{...u}}),b.state.next({values:{...u}})}g={mount:i.keepDirtyValues?g.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},c.mount=!_.isValid||!!i.keepIsValid||!!i.keepDirtyValues,c.watch=!!s.shouldUnregister,b.state.next({submitCount:i.keepSubmitCount?t.submitCount:0,isDirty:d?!1:i.keepDirty?t.isDirty:!!(i.keepDefaultValues&&!se(r,l)),isSubmitted:i.keepIsSubmitted?t.isSubmitted:!1,dirtyFields:d?{}:i.keepDirtyValues?i.keepDefaultValues&&n?de(l,n):t.dirtyFields:i.keepDefaultValues&&r?de(l,r):i.keepDirty?t.dirtyFields:{},touchedFields:i.keepTouched?t.touchedFields:{},errors:i.keepErrors?t.errors:{},isSubmitSuccessful:i.keepIsSubmitSuccessful?t.isSubmitSuccessful:!1,isSubmitting:!1})},We=(r,i)=>ze(K(r)?r(n):r,i),wt=(r,i={})=>{const o=f(a,r),y=o&&o._f;if(y){const d=y.refs?y.refs[0]:y.ref;d.focus&&(d.focus(),i.shouldSelect&&K(d.select)&&d.select())}},At=r=>{t={...t,...r}},He={control:{register:De,unregister:ke,getFieldState:Be,handleSubmit:qe,setError:Ie,_subscribe:Pe,_runSchema:te,_focusError:Se,_getWatch:x,_getDirty:X,_setValid:S,_setFieldArray:Q,_setDisabledField:je,_setErrors:H,_getFieldArray:w,_reset:ze,_resetDefaultValues:()=>K(s.defaultValues)&&s.defaultValues().then(r=>{We(r,s.resetOptions),b.state.next({isLoading:!1})}),_removeUnmounted:ie,_disableForm:Vt,_subjects:b,_proxyFormState:_,get _fields(){return a},get _formValues(){return n},get _state(){return c},set _state(r){c=r},get _defaultValues(){return l},get _names(){return g},set _names(r){g=r},get _formState(){return t},get _options(){return s},set _options(r){s={...s,...r}}},subscribe:_t,trigger:ne,register:De,handleSubmit:qe,watch:xt,setValue:P,getValues:be,reset:We,resetField:Ft,clearErrors:mt,unregister:ke,setError:Ie,setFocus:wt,getFieldState:Be};return{...He,formControl:He}}function Fr(e={}){const s=F.useRef(void 0),t=F.useRef(void 0),[a,l]=F.useState({isDirty:!1,isValidating:!1,isLoading:K(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:K(e.defaultValues)?void 0:e.defaultValues});s.current||(s.current={...e.formControl?e.formControl:or(e),formState:a},e.formControl&&e.defaultValues&&!K(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const n=s.current.control;return n._options=e,Ne(()=>{const c=n._subscribe({formState:n._proxyFormState,callback:()=>l({...n._formState}),reRenderRoot:!0});return l(g=>({...g,isReady:!0})),n._formState.isReady=!0,c},[n]),F.useEffect(()=>n._disableForm(e.disabled),[n,e.disabled]),F.useEffect(()=>{e.mode&&(n._options.mode=e.mode),e.reValidateMode&&(n._options.reValidateMode=e.reValidateMode)},[n,e.mode,e.reValidateMode]),F.useEffect(()=>{e.errors&&(n._setErrors(e.errors),n._focusError())},[n,e.errors]),F.useEffect(()=>{e.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})},[n,e.shouldUnregister]),F.useEffect(()=>{if(n._proxyFormState.isDirty){const c=n._getDirty();c!==a.isDirty&&n._subjects.state.next({isDirty:c})}},[n,a.isDirty]),F.useEffect(()=>{e.values&&!se(e.values,t.current)?(n._reset(e.values,n._options.resetOptions),t.current=e.values,l(c=>({...c}))):n._resetDefaultValues()},[n,e.values]),F.useEffect(()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),s.current.formState=dt(a,n),s.current}export{yr as C,hr as D,gr as I,br as a,vr as b,mr as c,_r as d,xr as e,Vr as f,Fr as u};
//# sourceMappingURL=index.esm-DqIqfoOW.js.map
