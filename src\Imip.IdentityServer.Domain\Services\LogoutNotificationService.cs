using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.OpenIddict.Applications;
using Volo.Abp.Uow;
using Imip.IdentityServer.Events;

namespace Imip.IdentityServer.Domain.Services;

/// <summary>
/// Implementation of logout notification service using Redis Pub/Sub and RabbitMQ
/// </summary>
public class LogoutNotificationService : ILogoutNotificationService, ITransientDependency
{
    private readonly ILogger<LogoutNotificationService> _logger;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly IRepository<OpenIddictApplication, Guid> _applicationRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IConnectionMultiplexer _redisConnection;
    private readonly LogoutNotificationOptions _options;
    private readonly RabbitMqHealthService _rabbitMqHealthService;

    public LogoutNotificationService(
        ILogger<LogoutNotificationService> logger,
        IDistributedEventBus distributedEventBus,
        IRepository<OpenIddictApplication, Guid> applicationRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IConnectionMultiplexer redisConnection,
        IOptions<LogoutNotificationOptions> options,
        RabbitMqHealthService rabbitMqHealthService)
    {
        _logger = logger;
        _distributedEventBus = distributedEventBus;
        _applicationRepository = applicationRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _redisConnection = redisConnection;
        _options = options.Value;
        _rabbitMqHealthService = rabbitMqHealthService;
    }

    public async Task NotifyClientApplicationsAsync(UserLogoutEvent logoutEvent)
    {
        _logger.LogInformation(
            "Notifying client applications about logout for user {UserId} ({UserName})",
            logoutEvent.UserId, logoutEvent.UserName);

        try
        {
            // 1. Broadcast via Redis Pub/Sub for real-time notifications
            await BroadcastLogoutViaRedisAsync(logoutEvent);

            // 2. Publish via RabbitMQ for reliable delivery (if enabled)
            if (_options.EnableRabbitMQNotifications)
            {
                await PublishLogoutViaRabbitMQAsync(logoutEvent);
            }
            else
            {
                _logger.LogDebug("RabbitMQ notifications disabled, skipping RabbitMQ publish for user {UserId}", logoutEvent.UserId);
            }

            // 3. Get target applications and send specific notifications
            var applications = await GetTargetApplicationsAsync(logoutEvent);

            foreach (var application in applications)
            {
                // Skip the source application to avoid circular notifications
                if (string.Equals(application.ClientId, logoutEvent.SourceApplication, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                var notificationEvent = new ClientLogoutNotificationEvent(
                    logoutEvent.UserId,
                    logoutEvent.UserName,
                    application.ClientId!,
                    logoutEvent.SourceApplication,
                    LogoutNotificationType.Both,
                    logoutEvent.SessionId,
                    logoutEvent.TenantId,
                    application.DisplayName,
                    GetFrontChannelLogoutUrl(application),
                    GetBackChannelLogoutUrl(application)
                );

                await NotifyClientApplicationAsync(notificationEvent);
            }

            _logger.LogInformation(
                "Successfully notified {ApplicationCount} client applications about logout for user {UserId}",
                applications.Count, logoutEvent.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error notifying client applications about logout for user {UserId}",
                logoutEvent.UserId);
            throw;
        }
    }

    public async Task NotifyClientApplicationAsync(ClientLogoutNotificationEvent notificationEvent)
    {
        _logger.LogDebug(
            "Notifying client {ClientId} about logout for user {UserId}",
            notificationEvent.ClientId, notificationEvent.UserId);

        try
        {
            // Publish via RabbitMQ for reliable delivery (if enabled and healthy)
            if (_options.EnableRabbitMQNotifications)
            {
                await _rabbitMqHealthService.ExecuteIfHealthyAsync(async () =>
                {
                    await _distributedEventBus.PublishAsync(notificationEvent);
                }, $"PublishClientLogoutNotification for client {notificationEvent.ClientId}");
            }
            else
            {
                _logger.LogDebug("RabbitMQ notifications disabled, skipping RabbitMQ publish for client {ClientId}", notificationEvent.ClientId);
            }

            // Also broadcast via Redis for real-time delivery
            await BroadcastClientLogoutViaRedisAsync(notificationEvent);

            _logger.LogDebug(
                "Successfully notified client {ClientId} about logout for user {UserId}",
                notificationEvent.ClientId, notificationEvent.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error notifying client {ClientId} about logout for user {UserId}",
                notificationEvent.ClientId, notificationEvent.UserId);
            throw;
        }
    }

    public async Task BroadcastLogoutViaRedisAsync(UserLogoutEvent logoutEvent)
    {
        try
        {
            var subscriber = _redisConnection.GetSubscriber();
            var channel = _options.RedisLogoutChannel;

            var logoutMessage = new LogoutRedisMessage
            {
                UserId = logoutEvent.UserId,
                UserName = logoutEvent.UserName,
                SessionId = logoutEvent.SessionId,
                TenantId = logoutEvent.TenantId,
                SourceApplication = logoutEvent.SourceApplication,
                LogoutType = logoutEvent.LogoutType,
                LogoutTimestamp = logoutEvent.LogoutTimestamp,
                IpAddress = logoutEvent.IpAddress,
                UserAgent = logoutEvent.UserAgent,
                TargetClients = logoutEvent.TargetClients
            };

            var messageJson = JsonSerializer.Serialize(logoutMessage);
            var publishedCount = await subscriber.PublishAsync(channel, messageJson);

            _logger.LogDebug(
                "Broadcasted logout event via Redis to {SubscriberCount} subscribers for user {UserId}",
                publishedCount, logoutEvent.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error broadcasting logout event via Redis for user {UserId}",
                logoutEvent.UserId);
            // Don't throw - Redis Pub/Sub is for real-time notifications, not critical for logout flow
        }
    }

    public async Task PublishLogoutViaRabbitMQAsync(UserLogoutEvent logoutEvent)
    {
        await _rabbitMqHealthService.ExecuteIfHealthyAsync(async () =>
        {
            // Use ABP's distributed event bus (configured with RabbitMQ)
            await _distributedEventBus.PublishAsync(logoutEvent);

            _logger.LogDebug(
                "Published logout event via RabbitMQ for user {UserId}",
                logoutEvent.UserId);
        }, $"PublishLogoutEvent for user {logoutEvent.UserId}");
    }

    private async Task<List<OpenIddictApplication>> GetTargetApplicationsAsync(UserLogoutEvent logoutEvent)
    {
        using var uow = _unitOfWorkManager.Begin();

        if (logoutEvent.TargetClients?.Any() == true)
        {
            // Get specific target clients
            var applications = await _applicationRepository.GetListAsync(
                a => logoutEvent.TargetClients.Contains(a.ClientId!));
            await uow.CompleteAsync();
            return applications;
        }

        // Get all applications with logout URLs configured
        var allApplications = await _applicationRepository.GetListAsync();

        // Filter to only applications that have logout URLs configured
        var applicationsWithLogoutUrls = allApplications.Where(a =>
            !string.IsNullOrEmpty(a.PostLogoutRedirectUris) ||
            !string.IsNullOrEmpty(a.Properties)).ToList();

        await uow.CompleteAsync();

        _logger.LogInformation(
            "Found {Count} applications with logout URLs configured for user {UserId}. " +
            "Consider implementing session-based filtering to reduce unnecessary notifications.",
            applicationsWithLogoutUrls.Count, logoutEvent.UserId);

        return applicationsWithLogoutUrls;
    }

    private async Task BroadcastClientLogoutViaRedisAsync(ClientLogoutNotificationEvent notificationEvent)
    {
        try
        {
            var subscriber = _redisConnection.GetSubscriber();
            var channel = $"{_options.RedisLogoutChannel}:{notificationEvent.ClientId}";

            var clientLogoutMessage = new ClientLogoutRedisMessage
            {
                UserId = notificationEvent.UserId,
                UserName = notificationEvent.UserName,
                ClientId = notificationEvent.ClientId,
                ClientName = notificationEvent.ClientName,
                SessionId = notificationEvent.SessionId,
                TenantId = notificationEvent.TenantId,
                SourceApplication = notificationEvent.SourceApplication,
                NotificationType = notificationEvent.NotificationType,
                LogoutUrl = notificationEvent.LogoutUrl,
                BackChannelLogoutUrl = notificationEvent.BackChannelLogoutUrl,
                LogoutTimestamp = notificationEvent.LogoutTimestamp,
                LogoutToken = notificationEvent.LogoutToken
            };

            var messageJson = JsonSerializer.Serialize(clientLogoutMessage);
            var publishedCount = await subscriber.PublishAsync(channel, messageJson);

            _logger.LogDebug(
                "Broadcasted client logout notification via Redis to {SubscriberCount} subscribers for client {ClientId}",
                publishedCount, notificationEvent.ClientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error broadcasting client logout notification via Redis for client {ClientId}",
                notificationEvent.ClientId);
            // Don't throw - Redis Pub/Sub is for real-time notifications
        }
    }

    private static string? GetFrontChannelLogoutUrl(OpenIddictApplication application)
    {
        return application.PostLogoutRedirectUris?.Split(' ', StringSplitOptions.RemoveEmptyEntries).FirstOrDefault();
    }

    private static string? GetBackChannelLogoutUrl(OpenIddictApplication application)
    {
        // Extract back-channel logout URL from application properties
        // This would typically be stored in the application's Properties field
        // You may need to implement custom logic based on how you store this information
        return null; // Implement based on your application configuration
    }
}

/// <summary>
/// Redis message for logout events
/// </summary>
public class LogoutRedisMessage
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string? SessionId { get; set; }
    public Guid? TenantId { get; set; }
    public string SourceApplication { get; set; } = string.Empty;
    public LogoutType LogoutType { get; set; }
    public DateTime LogoutTimestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string[]? TargetClients { get; set; }
}

/// <summary>
/// Redis message for client-specific logout notifications
/// </summary>
public class ClientLogoutRedisMessage
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string? ClientName { get; set; }
    public string? SessionId { get; set; }
    public Guid? TenantId { get; set; }
    public string SourceApplication { get; set; } = string.Empty;
    public LogoutNotificationType NotificationType { get; set; }
    public string? LogoutUrl { get; set; }
    public string? BackChannelLogoutUrl { get; set; }
    public DateTime LogoutTimestamp { get; set; }
    public string? LogoutToken { get; set; }
}