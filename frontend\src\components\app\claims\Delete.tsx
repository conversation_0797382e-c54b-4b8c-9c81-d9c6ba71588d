import { deleteApiIdentityClaimTypesById } from '@/client'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useToast } from '@/lib/useToast'
import { handleApiError } from '@/lib/handleApiError'
import { useEffect, useState } from 'react'

type DeleteUserProps = {
  dataId: string
  onDismiss: () => void
}
export const Delete = ({ dataId, onDismiss }: DeleteUserProps) => {
  const { toast } = useToast()
  const [open, setOpen] = useState<boolean>(false)
  const onYesEvent = async () => {
    try {
      await deleteApiIdentityClaimTypesById({
        path: { id: dataId },
      })
      toast({
        title: 'Success',
        description: `Claim Type has been deleted successfully.`,
      })
      onDismiss()
    } catch (err: unknown) {
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  }

  useEffect(() => {
    setOpen(true)
  }, [])

  return (
    <AlertDialog open={open}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete your this claim type.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
