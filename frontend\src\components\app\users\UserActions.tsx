'use client'

import { type IdentityUserUpdateDto } from '@/client'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'
import { RiMoreLine, RiPencilLine, RiShieldKeyholeLine } from '@remixicon/react'

type UserActionProps = {
  userId: string
  userDto: IdentityUserUpdateDto
  onAction: (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void
  variant?: 'dropdown' | 'buttons'
}

export const UserActions = ({ userId, userDto, onAction, variant = 'dropdown' }: UserActionProps) => {
  const { can } = useGrantedPolicies()

  // For dropdown menu style (first image)
  if (variant === 'dropdown') {
    return (
      <div className="flex justify-end">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
            >
              <RiMoreLine className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[160px]">
            {can('AbpIdentity.Users.ManagePermissions') && (
              <DropdownMenuItem
                className="cursor-pointer text-sm"
                onClick={() => onAction(userId, userDto, 'permission')}
              >
                Permission
              </DropdownMenuItem>
            )}
            {can('AbpIdentity.Users.Update') && (
              <DropdownMenuItem
                className="cursor-pointer text-sm"
                onClick={() => onAction(userId, userDto, 'edit')}
              >
                Edit
              </DropdownMenuItem>
            )}
            {can('AbpIdentity.Users.Delete') && (
              <DropdownMenuItem
                className="cursor-pointer text-sm text-red-500"
                onClick={() => onAction(userId, userDto, 'delete')}
              >
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  // For button group style (second image)
  return (
    <div className="flex items-center justify-end gap-1">
      {can('AbpIdentity.Users.ManagePermissions') && (
        <Button
          variant="primary"
          size="sm"
          className="flex items-center gap-1 px-2 py-1"
          onClick={() => onAction(userId, userDto, 'permission')}
        >
          <RiShieldKeyholeLine className="h-4 w-4" />
          <span>Permission</span>
        </Button>
      )}
      {can('AbpIdentity.Users.Update') && (
        <Button
          variant="primary"
          size="sm"
          className="flex items-center gap-1 px-2 py-1"
          onClick={() => onAction(userId, userDto, 'edit')}
        >
          <RiPencilLine className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      )}
    </div>
  )
}
