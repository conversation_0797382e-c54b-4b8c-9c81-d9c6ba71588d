# User Query Service Usage Guide

## Overview

The `UserQueryAppService` provides a flexible way to query users using the `DynamicQueryBuilder` for advanced filtering and sorting capabilities. This service is designed for client applications that need to retrieve user data with complex filtering requirements.

## API Endpoints

### Get Users with Dynamic Filtering
```
GET /api/user-query/users
```

### Get User by ID
```
GET /api/user-query/users/{id}
```

## Features

- **Dynamic Filtering**: Use `FilterGroup` to create complex filter conditions
- **Multiple Sorting**: Support for multiple sort fields with direction
- **Search**: Basic keyword search across username, email, name, and surname
- **Paging**: Built-in pagination support
- **Additional Filters**: Quick filters for active status and lockout status

## Usage Examples

### 1. Basic User Retrieval
```csharp
var input = new GetUsersInput
{
    MaxResultCount = 10,
    SkipCount = 0
};

var result = await userQueryAppService.GetUsersAsync(input);
```

### 2. Search Users by Keyword
```csharp
var input = new GetUsersInput
{
    SearchKeyword = "john",
    MaxResultCount = 20
};

var result = await userQueryAppService.GetUsersAsync(input);
```

### 3. Filter Active Users
```csharp
var input = new GetUsersInput
{
    IsActive = true,
    MaxResultCount = 50
};

var result = await userQueryAppService.GetUsersAsync(input);
```

### 4. Complex Dynamic Filtering
```csharp
var input = new GetUsersInput
{
    FilterGroup = new FilterGroup
    {
        Operator = LogicalOperator.And,
        Conditions = new List<FilterCondition>
        {
            new FilterCondition
            {
                FieldName = "Email",
                Operator = FilterOperator.Contains,
                Value = "@company.com"
            },
            new FilterCondition
            {
                FieldName = "IsActive",
                Operator = FilterOperator.Equals,
                Value = true
            },
            new FilterCondition
            {
                FieldName = "CreationTime",
                Operator = FilterOperator.GreaterThan,
                Value = DateTime.Now.AddDays(-30)
            }
        }
    },
    MaxResultCount = 100
};

var result = await userQueryAppService.GetUsersAsync(input);
```

### 5. Multiple Field Sorting
```csharp
var input = new GetUsersInput
{
    SortFields = new List<SortInfo>
    {
        new SortInfo { Field = "Surname", Desc = false },
        new SortInfo { Field = "Name", Desc = false },
        new SortInfo { Field = "CreationTime", Desc = true }
    },
    MaxResultCount = 25
};

var result = await userQueryAppService.GetUsersAsync(input);
```

### 6. Combined Filtering and Sorting
```csharp
var input = new GetUsersInput
{
    SearchKeyword = "admin",
    IsActive = true,
    FilterGroup = new FilterGroup
    {
        Operator = LogicalOperator.Or,
        Conditions = new List<FilterCondition>
        {
            new FilterCondition
            {
                FieldName = "UserName",
                Operator = FilterOperator.StartsWith,
                Value = "admin"
            },
            new FilterCondition
            {
                FieldName = "Email",
                Operator = FilterOperator.EndsWith,
                Value = "@admin.com"
            }
        }
    },
    SortFields = new List<SortInfo>
    {
        new SortInfo { Field = "UserName", Desc = false }
    },
    MaxResultCount = 50
};

var result = await userQueryAppService.GetUsersAsync(input);
```

## Available Filter Operators

The service supports all operators from the `FilterOperator` enum:

- **String Operations**: `Equals`, `NotEquals`, `Contains`, `StartsWith`, `EndsWith`
- **Comparison**: `GreaterThan`, `GreaterThanOrEqual`, `LessThan`, `LessThanOrEqual`
- **Collections**: `In`, `NotIn`, `Between`, `NotBetween`
- **Null Checks**: `IsNull`, `IsNotNull`
- **Empty Checks**: `IsEmpty`, `IsNotEmpty`
- **Boolean**: `IsTrue`, `IsFalse`
- **String Validation**: `IsNullOrEmpty`, `IsNotNullOrEmpty`, `IsNullOrWhiteSpace`, `IsNotNullOrWhiteSpace`
- **Format Validation**: `IsNumeric`, `IsAlpha`, `IsAlphaNumeric`, `IsEmail`, `IsUrl`, `IsIp`, `IsGuid`
- **And many more...**

## Logical Operators

- `And`: All conditions must be true
- `Or`: At least one condition must be true

## Response Format

The service returns a `PagedResultDto<ExtendedIdentityUserDto>` containing:

```json
{
  "totalCount": 150,
  "items": [
    {
      "id": "guid-here",
      "userName": "john.doe",
      "email": "<EMAIL>",
      "name": "John",
      "surname": "Doe",
      "isActive": true,
      "company": "Company Name",
      "department": "IT",
      "position": "Developer",
      "mustChangePassword": false,
      "activeDirectoryLogin": false,
      "concurrentLoginPreventionMode": 0
    }
  ]
}
```

## Security

- All endpoints require authentication (`[Authorize]` attribute)
- The service respects ABP's permission system
- Consider implementing additional authorization policies based on your requirements

## Performance Considerations

- Use appropriate `MaxResultCount` values to avoid large result sets
- Combine filters to reduce the result set early in the query
- Consider adding database indexes for frequently filtered fields
- The `DynamicQueryBuilder` generates efficient LINQ expressions that are translated to SQL 