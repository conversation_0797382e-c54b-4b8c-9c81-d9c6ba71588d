import{j as u,b as _,r as q}from"./vendor-B0b15ZrB.js";import{G as Se,av as X,aw as Nn,ax as Tn,ay as On,L as jt,B as ce,S as St,k as Nt,l as Tt,m as Ot,I as At,az as An}from"./app-layout-D_A4XD_6.js";import{I as Xt,C as Vr}from"./index.esm-DqIqfoOW.js";import{e as En,f as In,g as Ln,h as Mn,i as Rn}from"./radix-BQPyiA8r.js";import{h as mt,u as Pn,S as Dn,i as zn,j as Zn,k as Vn,l as $n}from"./TableSkeleton-DgDki6RL.js";function Fn({className:t,...e}){return u.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:u.jsx("table",{"data-slot":"table",className:Se("w-full caption-bottom text-sm",t),...e})})}function Bn({className:t,...e}){return u.jsx("thead",{"data-slot":"table-header",className:Se("[&_tr]:border-b",t),...e})}function Hn({className:t,...e}){return u.jsx("tbody",{"data-slot":"table-body",className:Se("[&_tr:last-child]:border-0",t),...e})}function et({className:t,...e}){return u.jsx("tr",{"data-slot":"table-row",className:Se("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...e})}function Un({className:t,...e}){return u.jsx("th",{"data-slot":"table-head",className:Se("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...e})}function ht({className:t,...e}){return u.jsx("td",{"data-slot":"table-cell",className:Se("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...e})}const $r=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"})),Wn=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M13.0001 16.1716L18.3641 10.8076L19.7783 12.2218L12.0001 20L4.22192 12.2218L5.63614 10.8076L11.0001 16.1716V4H13.0001V16.1716Z"})),Fr=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"})),Gn=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M10.8284 12.0007L15.7782 16.9504L14.364 18.3646L8 12.0007L14.364 5.63672L15.7782 7.05093L10.8284 12.0007Z"})),qn=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z"})),Kn=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M13.0001 7.82843V20H11.0001V7.82843L5.63614 13.1924L4.22192 11.7782L12.0001 4L19.7783 11.7782L18.3641 13.1924L13.0001 7.82843Z"})),Yn=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M11.9999 10.8284L7.0502 15.7782L5.63599 14.364L11.9999 8L18.3639 14.364L16.9497 15.7782L11.9999 10.8284Z"})),ao=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M11.9997 10.5865L16.9495 5.63672L18.3637 7.05093L13.4139 12.0007L18.3637 16.9504L16.9495 18.3646L11.9997 13.4149L7.04996 18.3646L5.63574 16.9504L10.5855 12.0007L5.63574 7.05093L7.04996 5.63672L11.9997 10.5865Z"})),Xn=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M3 19H21V21H3V19ZM13 13.1716L19.0711 7.1005L20.4853 8.51472L12 17L3.51472 8.51472L4.92893 7.1005L11 13.1716V2H13V13.1716Z"})),so=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M21 4V6H20L15 13.5V22H9V13.5L4 6H3V4H21ZM6.4037 6L11 12.8944V20H13V12.8944L17.5963 6H6.4037Z"})),io=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M4.5 10.5C3.675 10.5 3 11.175 3 12C3 12.825 3.675 13.5 4.5 13.5C5.325 13.5 6 12.825 6 12C6 11.175 5.325 10.5 4.5 10.5ZM19.5 10.5C18.675 10.5 18 11.175 18 12C18 12.825 18.675 13.5 19.5 13.5C20.325 13.5 21 12.825 21 12C21 11.175 20.325 10.5 19.5 10.5ZM12 10.5C11.175 10.5 10.5 11.175 10.5 12C10.5 12.825 11.175 13.5 12 13.5C12.825 13.5 13.5 12.825 13.5 12C13.5 11.175 12.825 10.5 12 10.5Z"})),oo=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M15.7279 9.57627L14.3137 8.16206L5 17.4758V18.89H6.41421L15.7279 9.57627ZM17.1421 8.16206L18.5563 6.74785L17.1421 5.33363L15.7279 6.74785L17.1421 8.16206ZM7.24264 20.89H3V16.6473L16.435 3.21231C16.8256 2.82179 17.4587 2.82179 17.8492 3.21231L20.6777 6.04074C21.0682 6.43126 21.0682 7.06443 20.6777 7.45495L7.24264 20.89Z"})),Jn=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M5.46257 4.43262C7.21556 2.91688 9.5007 2 12 2C17.5228 2 22 6.47715 22 12C22 14.1361 21.3302 16.1158 20.1892 17.7406L17 12H20C20 7.58172 16.4183 4 12 4C9.84982 4 7.89777 4.84827 6.46023 6.22842L5.46257 4.43262ZM18.5374 19.5674C16.7844 21.0831 14.4993 22 12 22C6.47715 22 2 17.5228 2 12C2 9.86386 2.66979 7.88416 3.8108 6.25944L7 12H4C4 16.4183 7.58172 20 12 20C14.1502 20 16.1022 19.1517 17.5398 17.7716L18.5374 19.5674Z"})),Qn=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M18.031 16.6168L22.3137 20.8995L20.8995 22.3137L16.6168 18.031C15.0769 19.263 13.124 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2C15.968 2 20 6.032 20 11C20 13.124 19.263 15.0769 18.031 16.6168ZM16.0247 15.8748C17.2475 14.6146 18 12.8956 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18C12.8956 18 14.6146 17.2475 15.8748 16.0247L16.0247 15.8748Z"})),lo=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M12 1L20.2169 2.82598C20.6745 2.92766 21 3.33347 21 3.80217V13.7889C21 15.795 19.9974 17.6684 18.3282 18.7812L12 23L5.6718 18.7812C4.00261 17.6684 3 15.795 3 13.7889V3.80217C3 3.33347 3.32553 2.92766 3.78307 2.82598L12 1ZM12 3.04879L5 4.60434V13.7889C5 15.1263 5.6684 16.3752 6.7812 17.1171L12 20.5963L17.2188 17.1171C18.3316 16.3752 19 15.1263 19 13.7889V4.60434L12 3.04879ZM12 7C13.1046 7 14 7.89543 14 9C14 9.73984 13.5983 10.3858 13.0011 10.7318L13 15H11L10.9999 10.7324C10.4022 10.3866 10 9.74025 10 9C10 7.89543 10.8954 7 12 7Z"})),co=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M3 19H21V21H3V19ZM13 5.82843V17H11V5.82843L4.92893 11.8995L3.51472 10.4853L12 2L20.4853 10.4853L19.0711 11.8995L13 5.82843Z"})),ea=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M4.83582 12L11.0429 18.2071L12.4571 16.7929L7.66424 12L12.4571 7.20712L11.0429 5.79291L4.83582 12ZM10.4857 12L16.6928 18.2071L18.107 16.7929L13.3141 12L18.107 7.20712L16.6928 5.79291L10.4857 12Z"})),ta=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M19.1642 12L12.9571 5.79291L11.5429 7.20712L16.3358 12L11.5429 16.7929L12.9571 18.2071L19.1642 12ZM13.5143 12L7.30722 5.79291L5.89301 7.20712L10.6859 12L5.89301 16.7929L7.30722 18.2071L13.5143 12Z"})),ra=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M4.99989 13.9999L4.99976 5L6.99976 4.99997L6.99986 11.9999L17.1717 12L13.222 8.05024L14.6362 6.63603L21.0001 13L14.6362 19.364L13.222 17.9497L17.1717 14L4.99989 13.9999Z"})),na=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M8.5 7C9.32843 7 10 6.32843 10 5.5C10 4.67157 9.32843 4 8.5 4C7.67157 4 7 4.67157 7 5.5C7 6.32843 7.67157 7 8.5 7ZM8.5 13.5C9.32843 13.5 10 12.8284 10 12C10 11.1716 9.32843 10.5 8.5 10.5C7.67157 10.5 7 11.1716 7 12C7 12.8284 7.67157 13.5 8.5 13.5ZM10 18.5C10 19.3284 9.32843 20 8.5 20C7.67157 20 7 19.3284 7 18.5C7 17.6716 7.67157 17 8.5 17C9.32843 17 10 17.6716 10 18.5ZM15.5 7C16.3284 7 17 6.32843 17 5.5C17 4.67157 16.3284 4 15.5 4C14.6716 4 14 4.67157 14 5.5C14 6.32843 14.6716 7 15.5 7ZM17 12C17 12.8284 16.3284 13.5 15.5 13.5C14.6716 13.5 14 12.8284 14 12C14 11.1716 14.6716 10.5 15.5 10.5C16.3284 10.5 17 11.1716 17 12ZM15.5 20C16.3284 20 17 19.3284 17 18.5C17 17.6716 16.3284 17 15.5 17C14.6716 17 14 17.6716 14 18.5C14 19.3284 14.6716 20 15.5 20Z"})),aa=({color:t="currentColor",size:e=24,className:r,...n})=>_.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:e,height:e,fill:t,...n,className:"remixicon "+(r||"")},_.createElement("path",{d:"M5 7C5 6.17157 5.67157 5.5 6.5 5.5C7.32843 5.5 8 6.17157 8 7C8 7.82843 7.32843 8.5 6.5 8.5C5.67157 8.5 5 7.82843 5 7ZM6.5 3.5C4.567 3.5 3 5.067 3 7C3 8.933 4.567 10.5 6.5 10.5C8.433 10.5 10 8.933 10 7C10 5.067 8.433 3.5 6.5 3.5ZM12 8H20V6H12V8ZM16 17C16 16.1716 16.6716 15.5 17.5 15.5C18.3284 15.5 19 16.1716 19 17C19 17.8284 18.3284 18.5 17.5 18.5C16.6716 18.5 16 17.8284 16 17ZM17.5 13.5C15.567 13.5 14 15.067 14 17C14 18.933 15.567 20.5 17.5 20.5C19.433 20.5 21 18.933 21 17C21 15.067 19.433 13.5 17.5 13.5ZM4 16V18H12V16H4Z"}));var Jt=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,re=t=>!t||typeof t!="object"||Object.keys(t).length===0,sa=(t,e)=>JSON.stringify(t)===JSON.stringify(e);function Br(t,e){t.forEach(function(r){Array.isArray(r)?Br(r,e):e.push(r)})}function Hr(t){let e=[];return Br(t,e),e}var Ur=(...t)=>Hr(t).filter(Boolean),Wr=(t,e)=>{let r={},n=Object.keys(t),a=Object.keys(e);for(let s of n)if(a.includes(s)){let i=t[s],o=e[s];Array.isArray(i)||Array.isArray(o)?r[s]=Ur(o,i):typeof i=="object"&&typeof o=="object"?r[s]=Wr(i,o):r[s]=o+" "+i}else r[s]=t[s];for(let s of a)n.includes(s)||(r[s]=e[s]);return r},Qt=t=>!t||typeof t!="string"?t:t.replace(/\s+/g," ").trim();const Wt="-",ia=t=>{const e=la(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:i=>{const o=i.split(Wt);return o[0]===""&&o.length!==1&&o.shift(),Gr(o,e)||oa(i)},getConflictingClassGroupIds:(i,o)=>{const l=r[i]||[];return o&&n[i]?[...l,...n[i]]:l}}},Gr=(t,e)=>{if(t.length===0)return e.classGroupId;const r=t[0],n=e.nextPart.get(r),a=n?Gr(t.slice(1),n):void 0;if(a)return a;if(e.validators.length===0)return;const s=t.join(Wt);return e.validators.find(({validator:i})=>i(s))?.classGroupId},er=/^\[(.+)\]$/,oa=t=>{if(er.test(t)){const e=er.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},la=t=>{const{theme:e,classGroups:r}=t,n={nextPart:new Map,validators:[]};for(const a in r)Et(r[a],n,a,e);return n},Et=(t,e,r,n)=>{t.forEach(a=>{if(typeof a=="string"){const s=a===""?e:tr(e,a);s.classGroupId=r;return}if(typeof a=="function"){if(ca(a)){Et(a(n),e,r,n);return}e.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([s,i])=>{Et(i,tr(e,s),r,n)})})},tr=(t,e)=>{let r=t;return e.split(Wt).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},ca=t=>t.isThemeGetter,da=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,r=new Map,n=new Map;const a=(s,i)=>{r.set(s,i),e++,e>t&&(e=0,n=r,r=new Map)};return{get(s){let i=r.get(s);if(i!==void 0)return i;if((i=n.get(s))!==void 0)return a(s,i),i},set(s,i){r.has(s)?r.set(s,i):a(s,i)}}},It="!",Lt=":",ua=Lt.length,fa=t=>{const{prefix:e,experimentalParseClassName:r}=t;let n=a=>{const s=[];let i=0,o=0,l=0,d;for(let m=0;m<a.length;m++){let S=a[m];if(i===0&&o===0){if(S===Lt){s.push(a.slice(l,m)),l=m+ua;continue}if(S==="/"){d=m;continue}}S==="["?i++:S==="]"?i--:S==="("?o++:S===")"&&o--}const c=s.length===0?a:a.substring(l),f=pa(c),y=f!==c,g=d&&d>l?d-l:void 0;return{modifiers:s,hasImportantModifier:y,baseClassName:f,maybePostfixModifierPosition:g}};if(e){const a=e+Lt,s=n;n=i=>i.startsWith(a)?s(i.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const a=n;n=s=>r({className:s,parseClassName:a})}return n},pa=t=>t.endsWith(It)?t.substring(0,t.length-1):t.startsWith(It)?t.substring(1):t,ma=t=>{const e=Object.fromEntries(t.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const a=[];let s=[];return n.forEach(i=>{i[0]==="["||e[i]?(a.push(...s.sort(),i),s=[]):s.push(i)}),a.push(...s.sort()),a}},ha=t=>({cache:da(t.cacheSize),parseClassName:fa(t),sortModifiers:ma(t),...ia(t)}),ga=/\s+/,va=(t,e)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:a,sortModifiers:s}=e,i=[],o=t.trim().split(ga);let l="";for(let d=o.length-1;d>=0;d-=1){const c=o[d],{isExternal:f,modifiers:y,hasImportantModifier:g,baseClassName:m,maybePostfixModifierPosition:S}=r(c);if(f){l=c+(l.length>0?" "+l:l);continue}let I=!!S,D=n(I?m.substring(0,S):m);if(!D){if(!I){l=c+(l.length>0?" "+l:l);continue}if(D=n(m),!D){l=c+(l.length>0?" "+l:l);continue}I=!1}const E=s(y).join(":"),F=g?E+It:E,P=F+D;if(i.includes(P))continue;i.push(P);const H=a(D,I);for(let h=0;h<H.length;++h){const U=H[h];i.push(F+U)}l=c+(l.length>0?" "+l:l)}return l};function ya(){let t=0,e,r,n="";for(;t<arguments.length;)(e=arguments[t++])&&(r=qr(e))&&(n&&(n+=" "),n+=r);return n}const qr=t=>{if(typeof t=="string")return t;let e,r="";for(let n=0;n<t.length;n++)t[n]&&(e=qr(t[n]))&&(r&&(r+=" "),r+=e);return r};function Mt(t,...e){let r,n,a,s=i;function i(l){const d=e.reduce((c,f)=>f(c),t());return r=ha(d),n=r.cache.get,a=r.cache.set,s=o,o(l)}function o(l){const d=n(l);if(d)return d;const c=va(l,r);return a(l,c),c}return function(){return s(ya.apply(null,arguments))}}const ee=t=>{const e=r=>r[t]||[];return e.isThemeGetter=!0,e},Kr=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Yr=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ba=/^\d+\/\d+$/,xa=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,wa=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,_a=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ka=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Ca=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ae=t=>ba.test(t),V=t=>!!t&&!Number.isNaN(Number(t)),we=t=>!!t&&Number.isInteger(Number(t)),rr=t=>t.endsWith("%")&&V(t.slice(0,-1)),pe=t=>xa.test(t),ja=()=>!0,Sa=t=>wa.test(t)&&!_a.test(t),Gt=()=>!1,Na=t=>ka.test(t),Ta=t=>Ca.test(t),Oa=t=>!C(t)&&!j(t),Aa=t=>ze(t,Qr,Gt),C=t=>Kr.test(t),_e=t=>ze(t,en,Sa),gt=t=>ze(t,$a,V),Ea=t=>ze(t,Xr,Gt),Ia=t=>ze(t,Jr,Ta),La=t=>ze(t,Gt,Na),j=t=>Yr.test(t),tt=t=>Ze(t,en),Ma=t=>Ze(t,Fa),Ra=t=>Ze(t,Xr),Pa=t=>Ze(t,Qr),Da=t=>Ze(t,Jr),za=t=>Ze(t,Ba,!0),ze=(t,e,r)=>{const n=Kr.exec(t);return n?n[1]?e(n[1]):r(n[2]):!1},Ze=(t,e,r=!1)=>{const n=Yr.exec(t);return n?n[1]?e(n[1]):r:!1},Xr=t=>t==="position",Za=new Set(["image","url"]),Jr=t=>Za.has(t),Va=new Set(["length","size","percentage"]),Qr=t=>Va.has(t),en=t=>t==="length",$a=t=>t==="number",Fa=t=>t==="family-name",Ba=t=>t==="shadow",Rt=()=>{const t=ee("color"),e=ee("font"),r=ee("text"),n=ee("font-weight"),a=ee("tracking"),s=ee("leading"),i=ee("breakpoint"),o=ee("container"),l=ee("spacing"),d=ee("radius"),c=ee("shadow"),f=ee("inset-shadow"),y=ee("drop-shadow"),g=ee("blur"),m=ee("perspective"),S=ee("aspect"),I=ee("ease"),D=ee("animate"),E=()=>["auto","avoid","all","avoid-page","page","left","right","column"],F=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],P=()=>["auto","hidden","clip","visible","scroll"],H=()=>["auto","contain","none"],h=()=>[j,C,l],U=()=>[Ae,"full","auto",...h()],W=()=>[we,"none","subgrid",j,C],Y=()=>["auto",{span:["full",we,j,C]},j,C],K=()=>[we,"auto",j,C],te=()=>["auto","min","max","fr",j,C],A=()=>["start","end","center","between","around","evenly","stretch","baseline"],k=()=>["start","end","center","stretch"],x=()=>["auto",...h()],L=()=>[Ae,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...h()],v=()=>[t,j,C],w=()=>[rr,_e],T=()=>["","none","full",d,j,C],z=()=>["",V,tt,_e],G=()=>["solid","dashed","dotted","double"],se=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Q=()=>["","none",g,j,C],xe=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",j,C],ue=()=>["none",V,j,C],fe=()=>["none",V,j,C],Te=()=>[V,j,C],Oe=()=>[Ae,"full",...h()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[pe],breakpoint:[pe],color:[ja],container:[pe],"drop-shadow":[pe],ease:["in","out","in-out"],font:[Oa],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[pe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[pe],shadow:[pe],spacing:["px",V],text:[pe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ae,C,j,S]}],container:["container"],columns:[{columns:[V,C,j,o]}],"break-after":[{"break-after":E()}],"break-before":[{"break-before":E()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...F(),C,j]}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:H()}],"overscroll-x":[{"overscroll-x":H()}],"overscroll-y":[{"overscroll-y":H()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:U()}],"inset-x":[{"inset-x":U()}],"inset-y":[{"inset-y":U()}],start:[{start:U()}],end:[{end:U()}],top:[{top:U()}],right:[{right:U()}],bottom:[{bottom:U()}],left:[{left:U()}],visibility:["visible","invisible","collapse"],z:[{z:[we,"auto",j,C]}],basis:[{basis:[Ae,"full","auto",o,...h()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[V,Ae,"auto","initial","none",C]}],grow:[{grow:["",V,j,C]}],shrink:[{shrink:["",V,j,C]}],order:[{order:[we,"first","last","none",j,C]}],"grid-cols":[{"grid-cols":W()}],"col-start-end":[{col:Y()}],"col-start":[{"col-start":K()}],"col-end":[{"col-end":K()}],"grid-rows":[{"grid-rows":W()}],"row-start-end":[{row:Y()}],"row-start":[{"row-start":K()}],"row-end":[{"row-end":K()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":te()}],"auto-rows":[{"auto-rows":te()}],gap:[{gap:h()}],"gap-x":[{"gap-x":h()}],"gap-y":[{"gap-y":h()}],"justify-content":[{justify:[...A(),"normal"]}],"justify-items":[{"justify-items":[...k(),"normal"]}],"justify-self":[{"justify-self":["auto",...k()]}],"align-content":[{content:["normal",...A()]}],"align-items":[{items:[...k(),"baseline"]}],"align-self":[{self:["auto",...k(),"baseline"]}],"place-content":[{"place-content":A()}],"place-items":[{"place-items":[...k(),"baseline"]}],"place-self":[{"place-self":["auto",...k()]}],p:[{p:h()}],px:[{px:h()}],py:[{py:h()}],ps:[{ps:h()}],pe:[{pe:h()}],pt:[{pt:h()}],pr:[{pr:h()}],pb:[{pb:h()}],pl:[{pl:h()}],m:[{m:x()}],mx:[{mx:x()}],my:[{my:x()}],ms:[{ms:x()}],me:[{me:x()}],mt:[{mt:x()}],mr:[{mr:x()}],mb:[{mb:x()}],ml:[{ml:x()}],"space-x":[{"space-x":h()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":h()}],"space-y-reverse":["space-y-reverse"],size:[{size:L()}],w:[{w:[o,"screen",...L()]}],"min-w":[{"min-w":[o,"screen","none",...L()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[i]},...L()]}],h:[{h:["screen",...L()]}],"min-h":[{"min-h":["screen","none",...L()]}],"max-h":[{"max-h":["screen",...L()]}],"font-size":[{text:["base",r,tt,_e]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,j,gt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",rr,C]}],"font-family":[{font:[Ma,C,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,j,C]}],"line-clamp":[{"line-clamp":[V,"none",j,gt]}],leading:[{leading:[s,...h()]}],"list-image":[{"list-image":["none",j,C]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",j,C]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:v()}],"text-color":[{text:v()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...G(),"wavy"]}],"text-decoration-thickness":[{decoration:[V,"from-font","auto",j,_e]}],"text-decoration-color":[{decoration:v()}],"underline-offset":[{"underline-offset":[V,"auto",j,C]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:h()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",j,C]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",j,C]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...F(),Ra,Ea]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",Pa,Aa]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},we,j,C],radial:["",j,C],conic:[we,j,C]},Da,Ia]}],"bg-color":[{bg:v()}],"gradient-from-pos":[{from:w()}],"gradient-via-pos":[{via:w()}],"gradient-to-pos":[{to:w()}],"gradient-from":[{from:v()}],"gradient-via":[{via:v()}],"gradient-to":[{to:v()}],rounded:[{rounded:T()}],"rounded-s":[{"rounded-s":T()}],"rounded-e":[{"rounded-e":T()}],"rounded-t":[{"rounded-t":T()}],"rounded-r":[{"rounded-r":T()}],"rounded-b":[{"rounded-b":T()}],"rounded-l":[{"rounded-l":T()}],"rounded-ss":[{"rounded-ss":T()}],"rounded-se":[{"rounded-se":T()}],"rounded-ee":[{"rounded-ee":T()}],"rounded-es":[{"rounded-es":T()}],"rounded-tl":[{"rounded-tl":T()}],"rounded-tr":[{"rounded-tr":T()}],"rounded-br":[{"rounded-br":T()}],"rounded-bl":[{"rounded-bl":T()}],"border-w":[{border:z()}],"border-w-x":[{"border-x":z()}],"border-w-y":[{"border-y":z()}],"border-w-s":[{"border-s":z()}],"border-w-e":[{"border-e":z()}],"border-w-t":[{"border-t":z()}],"border-w-r":[{"border-r":z()}],"border-w-b":[{"border-b":z()}],"border-w-l":[{"border-l":z()}],"divide-x":[{"divide-x":z()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":z()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...G(),"hidden","none"]}],"divide-style":[{divide:[...G(),"hidden","none"]}],"border-color":[{border:v()}],"border-color-x":[{"border-x":v()}],"border-color-y":[{"border-y":v()}],"border-color-s":[{"border-s":v()}],"border-color-e":[{"border-e":v()}],"border-color-t":[{"border-t":v()}],"border-color-r":[{"border-r":v()}],"border-color-b":[{"border-b":v()}],"border-color-l":[{"border-l":v()}],"divide-color":[{divide:v()}],"outline-style":[{outline:[...G(),"none","hidden"]}],"outline-offset":[{"outline-offset":[V,j,C]}],"outline-w":[{outline:["",V,tt,_e]}],"outline-color":[{outline:[t]}],shadow:[{shadow:["","none",c,za,La]}],"shadow-color":[{shadow:v()}],"inset-shadow":[{"inset-shadow":["none",j,C,f]}],"inset-shadow-color":[{"inset-shadow":v()}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:v()}],"ring-offset-w":[{"ring-offset":[V,_e]}],"ring-offset-color":[{"ring-offset":v()}],"inset-ring-w":[{"inset-ring":z()}],"inset-ring-color":[{"inset-ring":v()}],opacity:[{opacity:[V,j,C]}],"mix-blend":[{"mix-blend":[...se(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":se()}],filter:[{filter:["","none",j,C]}],blur:[{blur:Q()}],brightness:[{brightness:[V,j,C]}],contrast:[{contrast:[V,j,C]}],"drop-shadow":[{"drop-shadow":["","none",y,j,C]}],grayscale:[{grayscale:["",V,j,C]}],"hue-rotate":[{"hue-rotate":[V,j,C]}],invert:[{invert:["",V,j,C]}],saturate:[{saturate:[V,j,C]}],sepia:[{sepia:["",V,j,C]}],"backdrop-filter":[{"backdrop-filter":["","none",j,C]}],"backdrop-blur":[{"backdrop-blur":Q()}],"backdrop-brightness":[{"backdrop-brightness":[V,j,C]}],"backdrop-contrast":[{"backdrop-contrast":[V,j,C]}],"backdrop-grayscale":[{"backdrop-grayscale":["",V,j,C]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[V,j,C]}],"backdrop-invert":[{"backdrop-invert":["",V,j,C]}],"backdrop-opacity":[{"backdrop-opacity":[V,j,C]}],"backdrop-saturate":[{"backdrop-saturate":[V,j,C]}],"backdrop-sepia":[{"backdrop-sepia":["",V,j,C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":h()}],"border-spacing-x":[{"border-spacing-x":h()}],"border-spacing-y":[{"border-spacing-y":h()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",j,C]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[V,"initial",j,C]}],ease:[{ease:["linear","initial",I,j,C]}],delay:[{delay:[V,j,C]}],animate:[{animate:["none",D,j,C]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,j,C]}],"perspective-origin":[{"perspective-origin":xe()}],rotate:[{rotate:ue()}],"rotate-x":[{"rotate-x":ue()}],"rotate-y":[{"rotate-y":ue()}],"rotate-z":[{"rotate-z":ue()}],scale:[{scale:fe()}],"scale-x":[{"scale-x":fe()}],"scale-y":[{"scale-y":fe()}],"scale-z":[{"scale-z":fe()}],"scale-3d":["scale-3d"],skew:[{skew:Te()}],"skew-x":[{"skew-x":Te()}],"skew-y":[{"skew-y":Te()}],transform:[{transform:[j,C,"","none","gpu","cpu"]}],"transform-origin":[{origin:xe()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Oe()}],"translate-x":[{"translate-x":Oe()}],"translate-y":[{"translate-y":Oe()}],"translate-z":[{"translate-z":Oe()}],"translate-none":["translate-none"],accent:[{accent:v()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:v()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",j,C]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":h()}],"scroll-mx":[{"scroll-mx":h()}],"scroll-my":[{"scroll-my":h()}],"scroll-ms":[{"scroll-ms":h()}],"scroll-me":[{"scroll-me":h()}],"scroll-mt":[{"scroll-mt":h()}],"scroll-mr":[{"scroll-mr":h()}],"scroll-mb":[{"scroll-mb":h()}],"scroll-ml":[{"scroll-ml":h()}],"scroll-p":[{"scroll-p":h()}],"scroll-px":[{"scroll-px":h()}],"scroll-py":[{"scroll-py":h()}],"scroll-ps":[{"scroll-ps":h()}],"scroll-pe":[{"scroll-pe":h()}],"scroll-pt":[{"scroll-pt":h()}],"scroll-pr":[{"scroll-pr":h()}],"scroll-pb":[{"scroll-pb":h()}],"scroll-pl":[{"scroll-pl":h()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",j,C]}],fill:[{fill:["none",...v()]}],"stroke-w":[{stroke:[V,tt,_e,gt]}],stroke:[{stroke:["none",...v()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},Ha=(t,{cacheSize:e,prefix:r,experimentalParseClassName:n,extend:a={},override:s={}})=>(Fe(t,"cacheSize",e),Fe(t,"prefix",r),Fe(t,"experimentalParseClassName",n),rt(t.theme,s.theme),rt(t.classGroups,s.classGroups),rt(t.conflictingClassGroups,s.conflictingClassGroups),rt(t.conflictingClassGroupModifiers,s.conflictingClassGroupModifiers),Fe(t,"orderSensitiveModifiers",s.orderSensitiveModifiers),nt(t.theme,a.theme),nt(t.classGroups,a.classGroups),nt(t.conflictingClassGroups,a.conflictingClassGroups),nt(t.conflictingClassGroupModifiers,a.conflictingClassGroupModifiers),tn(t,a,"orderSensitiveModifiers"),t),Fe=(t,e,r)=>{r!==void 0&&(t[e]=r)},rt=(t,e)=>{if(e)for(const r in e)Fe(t,r,e[r])},nt=(t,e)=>{if(e)for(const r in e)tn(t,e,r)},tn=(t,e,r)=>{const n=e[r];n!==void 0&&(t[r]=t[r]?t[r].concat(n):n)},Ua=(t,...e)=>typeof t=="function"?Mt(Rt,t,...e):Mt(()=>Ha(Rt(),t),...e),Wa=Mt(Rt);var Ga={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},rn=t=>t||void 0,Ge=(...t)=>rn(Hr(t).filter(Boolean).join(" ")),vt=null,le={},Pt=!1,Ve=(...t)=>e=>e.twMerge?((!vt||Pt)&&(Pt=!1,vt=re(le)?Wa:Ua({...le,extend:{theme:le.theme,classGroups:le.classGroups,conflictingClassGroupModifiers:le.conflictingClassGroupModifiers,conflictingClassGroups:le.conflictingClassGroups,...le.extend}})),rn(vt(Ge(t)))):Ge(t),nr=(t,e)=>{for(let r in e)t.hasOwnProperty(r)?t[r]=Ge(t[r],e[r]):t[r]=e[r];return t},qa=(t,e)=>{let{extend:r=null,slots:n={},variants:a={},compoundVariants:s=[],compoundSlots:i=[],defaultVariants:o={}}=t,l={...Ga,...e},d=r!=null&&r.base?Ge(r.base,t?.base):t?.base,c=r!=null&&r.variants&&!re(r.variants)?Wr(a,r.variants):a,f=r!=null&&r.defaultVariants&&!re(r.defaultVariants)?{...r.defaultVariants,...o}:o;!re(l.twMergeConfig)&&!sa(l.twMergeConfig,le)&&(Pt=!0,le=l.twMergeConfig);let y=re(r?.slots),g=re(n)?{}:{base:Ge(t?.base,y&&r?.base),...n},m=y?g:nr({...r?.slots},re(g)?{base:t?.base}:g),S=re(r?.compoundVariants)?s:Ur(r?.compoundVariants,s),I=E=>{if(re(c)&&re(n)&&y)return Ve(d,E?.class,E?.className)(l);if(S&&!Array.isArray(S))throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof S}`);if(i&&!Array.isArray(i))throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof i}`);let F=(A,k,x=[],L)=>{let v=x;if(typeof k=="string")v=v.concat(Qt(k).split(" ").map(w=>`${A}:${w}`));else if(Array.isArray(k))v=v.concat(k.reduce((w,T)=>w.concat(`${A}:${T}`),[]));else if(typeof k=="object"&&typeof L=="string"){for(let w in k)if(k.hasOwnProperty(w)&&w===L){let T=k[w];if(T&&typeof T=="string"){let z=Qt(T);v[L]?v[L]=v[L].concat(z.split(" ").map(G=>`${A}:${G}`)):v[L]=z.split(" ").map(G=>`${A}:${G}`)}else Array.isArray(T)&&T.length>0&&(v[L]=T.reduce((z,G)=>z.concat(`${A}:${G}`),[]))}}return v},P=(A,k=c,x=null,L=null)=>{var v;let w=k[A];if(!w||re(w))return null;let T=(v=L?.[A])!=null?v:E?.[A];if(T===null)return null;let z=Jt(T),G=Array.isArray(l.responsiveVariants)&&l.responsiveVariants.length>0||l.responsiveVariants===!0,se=f?.[A],Q=[];if(typeof z=="object"&&G)for(let[fe,Te]of Object.entries(z)){let Oe=w[Te];if(fe==="initial"){se=Te;continue}Array.isArray(l.responsiveVariants)&&!l.responsiveVariants.includes(fe)||(Q=F(fe,Oe,Q,x))}let xe=z!=null&&typeof z!="object"?z:Jt(se),ue=w[xe||"false"];return typeof Q=="object"&&typeof x=="string"&&Q[x]?nr(Q,ue):Q.length>0?(Q.push(ue),x==="base"?Q.join(" "):Q):ue},H=()=>c?Object.keys(c).map(A=>P(A,c)):null,h=(A,k)=>{if(!c||typeof c!="object")return null;let x=new Array;for(let L in c){let v=P(L,c,A,k),w=A==="base"&&typeof v=="string"?v:v&&v[A];w&&(x[x.length]=w)}return x},U={};for(let A in E)E[A]!==void 0&&(U[A]=E[A]);let W=(A,k)=>{var x;let L=typeof E?.[A]=="object"?{[A]:(x=E[A])==null?void 0:x.initial}:{};return{...f,...U,...L,...k}},Y=(A=[],k)=>{let x=[];for(let{class:L,className:v,...w}of A){let T=!0;for(let[z,G]of Object.entries(w)){let se=W(z,k)[z];if(Array.isArray(G)){if(!G.includes(se)){T=!1;break}}else{let Q=xe=>xe==null||xe===!1;if(Q(G)&&Q(se))continue;if(se!==G){T=!1;break}}}T&&(L&&x.push(L),v&&x.push(v))}return x},K=A=>{let k=Y(S,A);if(!Array.isArray(k))return k;let x={};for(let L of k)if(typeof L=="string"&&(x.base=Ve(x.base,L)(l)),typeof L=="object")for(let[v,w]of Object.entries(L))x[v]=Ve(x[v],w)(l);return x},te=A=>{if(i.length<1)return null;let k={};for(let{slots:x=[],class:L,className:v,...w}of i){if(!re(w)){let T=!0;for(let z of Object.keys(w)){let G=W(z,A)[z];if(G===void 0||(Array.isArray(w[z])?!w[z].includes(G):w[z]!==G)){T=!1;break}}if(!T)continue}for(let T of x)k[T]=k[T]||[],k[T].push([L,v])}return k};if(!re(n)||!y){let A={};if(typeof m=="object"&&!re(m))for(let k of Object.keys(m))A[k]=x=>{var L,v;return Ve(m[k],h(k,x),((L=K(x))!=null?L:[])[k],((v=te(x))!=null?v:[])[k],x?.class,x?.className)(l)};return A}return Ve(d,H(),Y(S),E?.class,E?.className)(l)},D=()=>{if(!(!c||typeof c!="object"))return Object.keys(c)};return I.variantKeys=D(),I.extend=r,I.base=d,I.slots=m,I.variants=c,I.defaultVariants=f,I.compoundSlots=i,I.compoundVariants=S,I};const Ka=qa({base:["relative block w-full appearance-none rounded-md border px-2.5 py-1.5 outline-hidden transition sm:text-sm","border-transparent dark:border-gray-800","text-gray-900 dark:text-gray-50","placeholder-gray-400 dark:placeholder-gray-500","bg-gray-100 dark:bg-gray-950","disabled:border-gray-300 disabled:bg-gray-100 disabled:text-gray-400","dark:disabled:border-gray-700 dark:disabled:bg-gray-800 dark:disabled:text-gray-500",Nn,"[&::--webkit-search-cancel-button]:hidden [&::-webkit-search-cancel-button]:hidden [&::-webkit-search-decoration]:hidden"],variants:{hasError:{true:Tn},enableStepper:{true:"[appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"}}}),nn=q.forwardRef(({className:t,inputClassName:e,hasError:r,enableStepper:n,type:a="search",...s},i)=>u.jsxs("div",{className:X("relative w-full",t),children:[u.jsx("input",{ref:i,type:a,className:X(Ka({hasError:r,enableStepper:n}),"pl-8",e),...s}),u.jsx("div",{className:X("pointer-events-none absolute bottom-0 left-2 flex h-full items-center justify-center","text-gray-400 dark:text-gray-600"),children:u.jsx(Qn,{className:"size-[1.125rem] shrink-0","aria-hidden":"true"})})]}));nn.displayName="Searchbar";var B;(function(t){t.assertEqual=a=>{};function e(a){}t.assertIs=e;function r(a){throw new Error}t.assertNever=r,t.arrayToEnum=a=>{const s={};for(const i of a)s[i]=i;return s},t.getValidEnumValues=a=>{const s=t.objectKeys(a).filter(o=>typeof a[a[o]]!="number"),i={};for(const o of s)i[o]=a[o];return t.objectValues(i)},t.objectValues=a=>t.objectKeys(a).map(function(s){return a[s]}),t.objectKeys=typeof Object.keys=="function"?a=>Object.keys(a):a=>{const s=[];for(const i in a)Object.prototype.hasOwnProperty.call(a,i)&&s.push(i);return s},t.find=(a,s)=>{for(const i of a)if(s(i))return i},t.isInteger=typeof Number.isInteger=="function"?a=>Number.isInteger(a):a=>typeof a=="number"&&Number.isFinite(a)&&Math.floor(a)===a;function n(a,s=" | "){return a.map(i=>typeof i=="string"?`'${i}'`:i).join(s)}t.joinValues=n,t.jsonStringifyReplacer=(a,s)=>typeof s=="bigint"?s.toString():s})(B||(B={}));var ar;(function(t){t.mergeShapes=(e,r)=>({...e,...r})})(ar||(ar={}));const N=B.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),he=t=>{switch(typeof t){case"undefined":return N.undefined;case"string":return N.string;case"number":return Number.isNaN(t)?N.nan:N.number;case"boolean":return N.boolean;case"function":return N.function;case"bigint":return N.bigint;case"symbol":return N.symbol;case"object":return Array.isArray(t)?N.array:t===null?N.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?N.promise:typeof Map<"u"&&t instanceof Map?N.map:typeof Set<"u"&&t instanceof Set?N.set:typeof Date<"u"&&t instanceof Date?N.date:N.object;default:return N.unknown}},p=B.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class de extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const r=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,r):this.__proto__=r,this.name="ZodError",this.issues=e}format(e){const r=e||function(s){return s.message},n={_errors:[]},a=s=>{for(const i of s.issues)if(i.code==="invalid_union")i.unionErrors.map(a);else if(i.code==="invalid_return_type")a(i.returnTypeError);else if(i.code==="invalid_arguments")a(i.argumentsError);else if(i.path.length===0)n._errors.push(r(i));else{let o=n,l=0;for(;l<i.path.length;){const d=i.path[l];l===i.path.length-1?(o[d]=o[d]||{_errors:[]},o[d]._errors.push(r(i))):o[d]=o[d]||{_errors:[]},o=o[d],l++}}};return a(this),n}static assert(e){if(!(e instanceof de))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,B.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=r=>r.message){const r={},n=[];for(const a of this.issues)a.path.length>0?(r[a.path[0]]=r[a.path[0]]||[],r[a.path[0]].push(e(a))):n.push(e(a));return{formErrors:n,fieldErrors:r}}get formErrors(){return this.flatten()}}de.create=t=>new de(t);const Dt=(t,e)=>{let r;switch(t.code){case p.invalid_type:t.received===N.undefined?r="Required":r=`Expected ${t.expected}, received ${t.received}`;break;case p.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(t.expected,B.jsonStringifyReplacer)}`;break;case p.unrecognized_keys:r=`Unrecognized key(s) in object: ${B.joinValues(t.keys,", ")}`;break;case p.invalid_union:r="Invalid input";break;case p.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${B.joinValues(t.options)}`;break;case p.invalid_enum_value:r=`Invalid enum value. Expected ${B.joinValues(t.options)}, received '${t.received}'`;break;case p.invalid_arguments:r="Invalid function arguments";break;case p.invalid_return_type:r="Invalid function return type";break;case p.invalid_date:r="Invalid date";break;case p.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(r=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(r=`${r} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?r=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?r=`Invalid input: must end with "${t.validation.endsWith}"`:B.assertNever(t.validation):t.validation!=="regex"?r=`Invalid ${t.validation}`:r="Invalid";break;case p.too_small:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:r="Invalid input";break;case p.too_big:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?r=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:r="Invalid input";break;case p.custom:r="Invalid input";break;case p.invalid_intersection_types:r="Intersection results could not be merged";break;case p.not_multiple_of:r=`Number must be a multiple of ${t.multipleOf}`;break;case p.not_finite:r="Number must be finite";break;default:r=e.defaultError,B.assertNever(t)}return{message:r}};let Ya=Dt;function Xa(){return Ya}const Ja=t=>{const{data:e,path:r,errorMaps:n,issueData:a}=t,s=[...r,...a.path||[]],i={...a,path:s};if(a.message!==void 0)return{...a,path:s,message:a.message};let o="";const l=n.filter(d=>!!d).slice().reverse();for(const d of l)o=d(i,{data:e,defaultError:o}).message;return{...a,path:s,message:o}};function b(t,e){const r=Xa(),n=Ja({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,r,r===Dt?void 0:Dt].filter(a=>!!a)});t.common.issues.push(n)}class ne{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,r){const n=[];for(const a of r){if(a.status==="aborted")return M;a.status==="dirty"&&e.dirty(),n.push(a.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,r){const n=[];for(const a of r){const s=await a.key,i=await a.value;n.push({key:s,value:i})}return ne.mergeObjectSync(e,n)}static mergeObjectSync(e,r){const n={};for(const a of r){const{key:s,value:i}=a;if(s.status==="aborted"||i.status==="aborted")return M;s.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),s.value!=="__proto__"&&(typeof i.value<"u"||a.alwaysSet)&&(n[s.value]=i.value)}return{status:e.value,value:n}}}const M=Object.freeze({status:"aborted"}),Be=t=>({status:"dirty",value:t}),ae=t=>({status:"valid",value:t}),sr=t=>t.status==="aborted",ir=t=>t.status==="dirty",Ie=t=>t.status==="valid",ot=t=>typeof Promise<"u"&&t instanceof Promise;var O;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e?.message})(O||(O={}));class ye{constructor(e,r,n,a){this._cachedPath=[],this.parent=e,this.data=r,this._path=n,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const or=(t,e)=>{if(Ie(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const r=new de(t.common.issues);return this._error=r,this._error}}};function Z(t){if(!t)return{};const{errorMap:e,invalid_type_error:r,required_error:n,description:a}=t;if(e&&(r||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(i,o)=>{const{message:l}=t;return i.code==="invalid_enum_value"?{message:l??o.defaultError}:typeof o.data>"u"?{message:l??n??o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:l??r??o.defaultError}},description:a}}class ${get description(){return this._def.description}_getType(e){return he(e.data)}_getOrReturnCtx(e,r){return r||{common:e.parent.common,data:e.data,parsedType:he(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new ne,ctx:{common:e.parent.common,data:e.data,parsedType:he(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const r=this._parse(e);if(ot(r))throw new Error("Synchronous parse encountered promise.");return r}_parseAsync(e){const r=this._parse(e);return Promise.resolve(r)}parse(e,r){const n=this.safeParse(e,r);if(n.success)return n.data;throw n.error}safeParse(e,r){const n={common:{issues:[],async:r?.async??!1,contextualErrorMap:r?.errorMap},path:r?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:he(e)},a=this._parseSync({data:e,path:n.path,parent:n});return or(n,a)}"~validate"(e){const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:he(e)};if(!this["~standard"].async)try{const n=this._parseSync({data:e,path:[],parent:r});return Ie(n)?{value:n.value}:{issues:r.common.issues}}catch(n){n?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then(n=>Ie(n)?{value:n.value}:{issues:r.common.issues})}async parseAsync(e,r){const n=await this.safeParseAsync(e,r);if(n.success)return n.data;throw n.error}async safeParseAsync(e,r){const n={common:{issues:[],contextualErrorMap:r?.errorMap,async:!0},path:r?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:he(e)},a=this._parse({data:e,path:n.path,parent:n}),s=await(ot(a)?a:Promise.resolve(a));return or(n,s)}refine(e,r){const n=a=>typeof r=="string"||typeof r>"u"?{message:r}:typeof r=="function"?r(a):r;return this._refinement((a,s)=>{const i=e(a),o=()=>s.addIssue({code:p.custom,...n(a)});return typeof Promise<"u"&&i instanceof Promise?i.then(l=>l?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,r){return this._refinement((n,a)=>e(n)?!0:(a.addIssue(typeof r=="function"?r(n,a):r),!1))}_refinement(e){return new Re({schema:this,typeName:R.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:r=>this["~validate"](r)}}optional(){return ve.create(this,this._def)}nullable(){return Pe.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return oe.create(this)}promise(){return ut.create(this,this._def)}or(e){return ct.create([this,e],this._def)}and(e){return dt.create(this,e,this._def)}transform(e){return new Re({...Z(this._def),schema:this,typeName:R.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const r=typeof e=="function"?e:()=>e;return new Zt({...Z(this._def),innerType:this,defaultValue:r,typeName:R.ZodDefault})}brand(){return new ws({typeName:R.ZodBranded,type:this,...Z(this._def)})}catch(e){const r=typeof e=="function"?e:()=>e;return new Vt({...Z(this._def),innerType:this,catchValue:r,typeName:R.ZodCatch})}describe(e){const r=this.constructor;return new r({...this._def,description:e})}pipe(e){return qt.create(this,e)}readonly(){return $t.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Qa=/^c[^\s-]{8,}$/i,es=/^[0-9a-z]+$/,ts=/^[0-9A-HJKMNP-TV-Z]{26}$/i,rs=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,ns=/^[a-z0-9_-]{21}$/i,as=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,ss=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,is=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,os="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let yt;const ls=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,cs=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ds=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,us=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,fs=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ps=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,an="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",ms=new RegExp(`^${an}$`);function sn(t){let e="[0-5]\\d";t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`);const r=t.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${r}`}function hs(t){return new RegExp(`^${sn(t)}$`)}function gs(t){let e=`${an}T${sn(t)}`;const r=[];return r.push(t.local?"Z?":"Z"),t.offset&&r.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${r.join("|")})`,new RegExp(`^${e}$`)}function vs(t,e){return!!((e==="v4"||!e)&&ls.test(t)||(e==="v6"||!e)&&ds.test(t))}function ys(t,e){if(!as.test(t))return!1;try{const[r]=t.split("."),n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),a=JSON.parse(atob(n));return!(typeof a!="object"||a===null||"typ"in a&&a?.typ!=="JWT"||!a.alg||e&&a.alg!==e)}catch{return!1}}function bs(t,e){return!!((e==="v4"||!e)&&cs.test(t)||(e==="v6"||!e)&&us.test(t))}class ge extends ${_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==N.string){const s=this._getOrReturnCtx(e);return b(s,{code:p.invalid_type,expected:N.string,received:s.parsedType}),M}const n=new ne;let a;for(const s of this._def.checks)if(s.kind==="min")e.data.length<s.value&&(a=this._getOrReturnCtx(e,a),b(a,{code:p.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="max")e.data.length>s.value&&(a=this._getOrReturnCtx(e,a),b(a,{code:p.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="length"){const i=e.data.length>s.value,o=e.data.length<s.value;(i||o)&&(a=this._getOrReturnCtx(e,a),i?b(a,{code:p.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):o&&b(a,{code:p.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),n.dirty())}else if(s.kind==="email")is.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"email",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="emoji")yt||(yt=new RegExp(os,"u")),yt.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"emoji",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="uuid")rs.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"uuid",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="nanoid")ns.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"nanoid",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid")Qa.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"cuid",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid2")es.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"cuid2",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="ulid")ts.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"ulid",code:p.invalid_string,message:s.message}),n.dirty());else if(s.kind==="url")try{new URL(e.data)}catch{a=this._getOrReturnCtx(e,a),b(a,{validation:"url",code:p.invalid_string,message:s.message}),n.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"regex",code:p.invalid_string,message:s.message}),n.dirty())):s.kind==="trim"?e.data=e.data.trim():s.kind==="includes"?e.data.includes(s.value,s.position)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),n.dirty()):s.kind==="toLowerCase"?e.data=e.data.toLowerCase():s.kind==="toUpperCase"?e.data=e.data.toUpperCase():s.kind==="startsWith"?e.data.startsWith(s.value)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:{startsWith:s.value},message:s.message}),n.dirty()):s.kind==="endsWith"?e.data.endsWith(s.value)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:{endsWith:s.value},message:s.message}),n.dirty()):s.kind==="datetime"?gs(s).test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:"datetime",message:s.message}),n.dirty()):s.kind==="date"?ms.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:"date",message:s.message}),n.dirty()):s.kind==="time"?hs(s).test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{code:p.invalid_string,validation:"time",message:s.message}),n.dirty()):s.kind==="duration"?ss.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"duration",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="ip"?vs(e.data,s.version)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"ip",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="jwt"?ys(e.data,s.alg)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"jwt",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="cidr"?bs(e.data,s.version)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"cidr",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="base64"?fs.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"base64",code:p.invalid_string,message:s.message}),n.dirty()):s.kind==="base64url"?ps.test(e.data)||(a=this._getOrReturnCtx(e,a),b(a,{validation:"base64url",code:p.invalid_string,message:s.message}),n.dirty()):B.assertNever(s);return{status:n.value,value:e.data}}_regex(e,r,n){return this.refinement(a=>e.test(a),{validation:r,code:p.invalid_string,...O.errToObj(n)})}_addCheck(e){return new ge({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...O.errToObj(e)})}url(e){return this._addCheck({kind:"url",...O.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...O.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...O.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...O.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...O.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...O.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...O.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...O.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...O.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...O.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...O.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...O.errToObj(e)})}datetime(e){return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof e?.precision>"u"?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...O.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof e?.precision>"u"?null:e?.precision,...O.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...O.errToObj(e)})}regex(e,r){return this._addCheck({kind:"regex",regex:e,...O.errToObj(r)})}includes(e,r){return this._addCheck({kind:"includes",value:e,position:r?.position,...O.errToObj(r?.message)})}startsWith(e,r){return this._addCheck({kind:"startsWith",value:e,...O.errToObj(r)})}endsWith(e,r){return this._addCheck({kind:"endsWith",value:e,...O.errToObj(r)})}min(e,r){return this._addCheck({kind:"min",value:e,...O.errToObj(r)})}max(e,r){return this._addCheck({kind:"max",value:e,...O.errToObj(r)})}length(e,r){return this._addCheck({kind:"length",value:e,...O.errToObj(r)})}nonempty(e){return this.min(1,O.errToObj(e))}trim(){return new ge({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ge({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ge({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxLength(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}ge.create=t=>new ge({checks:[],typeName:R.ZodString,coerce:t?.coerce??!1,...Z(t)});function xs(t,e){const r=(t.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,a=r>n?r:n,s=Number.parseInt(t.toFixed(a).replace(".","")),i=Number.parseInt(e.toFixed(a).replace(".",""));return s%i/10**a}class Le extends ${constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==N.number){const s=this._getOrReturnCtx(e);return b(s,{code:p.invalid_type,expected:N.number,received:s.parsedType}),M}let n;const a=new ne;for(const s of this._def.checks)s.kind==="int"?B.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{code:p.invalid_type,expected:"integer",received:"float",message:s.message}),a.dirty()):s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="multipleOf"?xs(e.data,s.value)!==0&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):s.kind==="finite"?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{code:p.not_finite,message:s.message}),a.dirty()):B.assertNever(s);return{status:a.value,value:e.data}}gte(e,r){return this.setLimit("min",e,!0,O.toString(r))}gt(e,r){return this.setLimit("min",e,!1,O.toString(r))}lte(e,r){return this.setLimit("max",e,!0,O.toString(r))}lt(e,r){return this.setLimit("max",e,!1,O.toString(r))}setLimit(e,r,n,a){return new Le({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:O.toString(a)}]})}_addCheck(e){return new Le({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:O.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:O.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:O.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:O.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:O.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:O.toString(r)})}finite(e){return this._addCheck({kind:"finite",message:O.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:O.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:O.toString(e)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&B.isInteger(e.value))}get isFinite(){let e=null,r=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(r===null||n.value>r)&&(r=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(r)&&Number.isFinite(e)}}Le.create=t=>new Le({checks:[],typeName:R.ZodNumber,coerce:t?.coerce||!1,...Z(t)});class qe extends ${constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==N.bigint)return this._getInvalidInput(e);let n;const a=new ne;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="multipleOf"?e.data%s.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),b(n,{code:p.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):B.assertNever(s);return{status:a.value,value:e.data}}_getInvalidInput(e){const r=this._getOrReturnCtx(e);return b(r,{code:p.invalid_type,expected:N.bigint,received:r.parsedType}),M}gte(e,r){return this.setLimit("min",e,!0,O.toString(r))}gt(e,r){return this.setLimit("min",e,!1,O.toString(r))}lte(e,r){return this.setLimit("max",e,!0,O.toString(r))}lt(e,r){return this.setLimit("max",e,!1,O.toString(r))}setLimit(e,r,n,a){return new qe({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:O.toString(a)}]})}_addCheck(e){return new qe({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:O.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:O.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:O.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:O.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:O.toString(r)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}qe.create=t=>new qe({checks:[],typeName:R.ZodBigInt,coerce:t?.coerce??!1,...Z(t)});class lr extends ${_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==N.boolean){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:N.boolean,received:n.parsedType}),M}return ae(e.data)}}lr.create=t=>new lr({typeName:R.ZodBoolean,coerce:t?.coerce||!1,...Z(t)});class lt extends ${_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==N.date){const s=this._getOrReturnCtx(e);return b(s,{code:p.invalid_type,expected:N.date,received:s.parsedType}),M}if(Number.isNaN(e.data.getTime())){const s=this._getOrReturnCtx(e);return b(s,{code:p.invalid_date}),M}const n=new ne;let a;for(const s of this._def.checks)s.kind==="min"?e.data.getTime()<s.value&&(a=this._getOrReturnCtx(e,a),b(a,{code:p.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),n.dirty()):s.kind==="max"?e.data.getTime()>s.value&&(a=this._getOrReturnCtx(e,a),b(a,{code:p.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),n.dirty()):B.assertNever(s);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new lt({...this._def,checks:[...this._def.checks,e]})}min(e,r){return this._addCheck({kind:"min",value:e.getTime(),message:O.toString(r)})}max(e,r){return this._addCheck({kind:"max",value:e.getTime(),message:O.toString(r)})}get minDate(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e!=null?new Date(e):null}}lt.create=t=>new lt({checks:[],coerce:t?.coerce||!1,typeName:R.ZodDate,...Z(t)});class cr extends ${_parse(e){if(this._getType(e)!==N.symbol){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:N.symbol,received:n.parsedType}),M}return ae(e.data)}}cr.create=t=>new cr({typeName:R.ZodSymbol,...Z(t)});class dr extends ${_parse(e){if(this._getType(e)!==N.undefined){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:N.undefined,received:n.parsedType}),M}return ae(e.data)}}dr.create=t=>new dr({typeName:R.ZodUndefined,...Z(t)});class ur extends ${_parse(e){if(this._getType(e)!==N.null){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:N.null,received:n.parsedType}),M}return ae(e.data)}}ur.create=t=>new ur({typeName:R.ZodNull,...Z(t)});class fr extends ${constructor(){super(...arguments),this._any=!0}_parse(e){return ae(e.data)}}fr.create=t=>new fr({typeName:R.ZodAny,...Z(t)});class pr extends ${constructor(){super(...arguments),this._unknown=!0}_parse(e){return ae(e.data)}}pr.create=t=>new pr({typeName:R.ZodUnknown,...Z(t)});class be extends ${_parse(e){const r=this._getOrReturnCtx(e);return b(r,{code:p.invalid_type,expected:N.never,received:r.parsedType}),M}}be.create=t=>new be({typeName:R.ZodNever,...Z(t)});class mr extends ${_parse(e){if(this._getType(e)!==N.undefined){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:N.void,received:n.parsedType}),M}return ae(e.data)}}mr.create=t=>new mr({typeName:R.ZodVoid,...Z(t)});class oe extends ${_parse(e){const{ctx:r,status:n}=this._processInputParams(e),a=this._def;if(r.parsedType!==N.array)return b(r,{code:p.invalid_type,expected:N.array,received:r.parsedType}),M;if(a.exactLength!==null){const i=r.data.length>a.exactLength.value,o=r.data.length<a.exactLength.value;(i||o)&&(b(r,{code:i?p.too_big:p.too_small,minimum:o?a.exactLength.value:void 0,maximum:i?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),n.dirty())}if(a.minLength!==null&&r.data.length<a.minLength.value&&(b(r,{code:p.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),n.dirty()),a.maxLength!==null&&r.data.length>a.maxLength.value&&(b(r,{code:p.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),n.dirty()),r.common.async)return Promise.all([...r.data].map((i,o)=>a.type._parseAsync(new ye(r,i,r.path,o)))).then(i=>ne.mergeArray(n,i));const s=[...r.data].map((i,o)=>a.type._parseSync(new ye(r,i,r.path,o)));return ne.mergeArray(n,s)}get element(){return this._def.type}min(e,r){return new oe({...this._def,minLength:{value:e,message:O.toString(r)}})}max(e,r){return new oe({...this._def,maxLength:{value:e,message:O.toString(r)}})}length(e,r){return new oe({...this._def,exactLength:{value:e,message:O.toString(r)}})}nonempty(e){return this.min(1,e)}}oe.create=(t,e)=>new oe({type:t,minLength:null,maxLength:null,exactLength:null,typeName:R.ZodArray,...Z(e)});function Ee(t){if(t instanceof J){const e={};for(const r in t.shape){const n=t.shape[r];e[r]=ve.create(Ee(n))}return new J({...t._def,shape:()=>e})}else return t instanceof oe?new oe({...t._def,type:Ee(t.element)}):t instanceof ve?ve.create(Ee(t.unwrap())):t instanceof Pe?Pe.create(Ee(t.unwrap())):t instanceof je?je.create(t.items.map(e=>Ee(e))):t}class J extends ${constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),r=B.objectKeys(e);return this._cached={shape:e,keys:r},this._cached}_parse(e){if(this._getType(e)!==N.object){const d=this._getOrReturnCtx(e);return b(d,{code:p.invalid_type,expected:N.object,received:d.parsedType}),M}const{status:n,ctx:a}=this._processInputParams(e),{shape:s,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof be&&this._def.unknownKeys==="strip"))for(const d in a.data)i.includes(d)||o.push(d);const l=[];for(const d of i){const c=s[d],f=a.data[d];l.push({key:{status:"valid",value:d},value:c._parse(new ye(a,f,a.path,d)),alwaysSet:d in a.data})}if(this._def.catchall instanceof be){const d=this._def.unknownKeys;if(d==="passthrough")for(const c of o)l.push({key:{status:"valid",value:c},value:{status:"valid",value:a.data[c]}});else if(d==="strict")o.length>0&&(b(a,{code:p.unrecognized_keys,keys:o}),n.dirty());else if(d!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const d=this._def.catchall;for(const c of o){const f=a.data[c];l.push({key:{status:"valid",value:c},value:d._parse(new ye(a,f,a.path,c)),alwaysSet:c in a.data})}}return a.common.async?Promise.resolve().then(async()=>{const d=[];for(const c of l){const f=await c.key,y=await c.value;d.push({key:f,value:y,alwaysSet:c.alwaysSet})}return d}).then(d=>ne.mergeObjectSync(n,d)):ne.mergeObjectSync(n,l)}get shape(){return this._def.shape()}strict(e){return O.errToObj,new J({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(r,n)=>{const a=this._def.errorMap?.(r,n).message??n.defaultError;return r.code==="unrecognized_keys"?{message:O.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new J({...this._def,unknownKeys:"strip"})}passthrough(){return new J({...this._def,unknownKeys:"passthrough"})}extend(e){return new J({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new J({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:R.ZodObject})}setKey(e,r){return this.augment({[e]:r})}catchall(e){return new J({...this._def,catchall:e})}pick(e){const r={};for(const n of B.objectKeys(e))e[n]&&this.shape[n]&&(r[n]=this.shape[n]);return new J({...this._def,shape:()=>r})}omit(e){const r={};for(const n of B.objectKeys(this.shape))e[n]||(r[n]=this.shape[n]);return new J({...this._def,shape:()=>r})}deepPartial(){return Ee(this)}partial(e){const r={};for(const n of B.objectKeys(this.shape)){const a=this.shape[n];e&&!e[n]?r[n]=a:r[n]=a.optional()}return new J({...this._def,shape:()=>r})}required(e){const r={};for(const n of B.objectKeys(this.shape))if(e&&!e[n])r[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof ve;)s=s._def.innerType;r[n]=s}return new J({...this._def,shape:()=>r})}keyof(){return on(B.objectKeys(this.shape))}}J.create=(t,e)=>new J({shape:()=>t,unknownKeys:"strip",catchall:be.create(),typeName:R.ZodObject,...Z(e)});J.strictCreate=(t,e)=>new J({shape:()=>t,unknownKeys:"strict",catchall:be.create(),typeName:R.ZodObject,...Z(e)});J.lazycreate=(t,e)=>new J({shape:t,unknownKeys:"strip",catchall:be.create(),typeName:R.ZodObject,...Z(e)});class ct extends ${_parse(e){const{ctx:r}=this._processInputParams(e),n=this._def.options;function a(s){for(const o of s)if(o.result.status==="valid")return o.result;for(const o of s)if(o.result.status==="dirty")return r.common.issues.push(...o.ctx.common.issues),o.result;const i=s.map(o=>new de(o.ctx.common.issues));return b(r,{code:p.invalid_union,unionErrors:i}),M}if(r.common.async)return Promise.all(n.map(async s=>{const i={...r,common:{...r.common,issues:[]},parent:null};return{result:await s._parseAsync({data:r.data,path:r.path,parent:i}),ctx:i}})).then(a);{let s;const i=[];for(const l of n){const d={...r,common:{...r.common,issues:[]},parent:null},c=l._parseSync({data:r.data,path:r.path,parent:d});if(c.status==="valid")return c;c.status==="dirty"&&!s&&(s={result:c,ctx:d}),d.common.issues.length&&i.push(d.common.issues)}if(s)return r.common.issues.push(...s.ctx.common.issues),s.result;const o=i.map(l=>new de(l));return b(r,{code:p.invalid_union,unionErrors:o}),M}}get options(){return this._def.options}}ct.create=(t,e)=>new ct({options:t,typeName:R.ZodUnion,...Z(e)});function zt(t,e){const r=he(t),n=he(e);if(t===e)return{valid:!0,data:t};if(r===N.object&&n===N.object){const a=B.objectKeys(e),s=B.objectKeys(t).filter(o=>a.indexOf(o)!==-1),i={...t,...e};for(const o of s){const l=zt(t[o],e[o]);if(!l.valid)return{valid:!1};i[o]=l.data}return{valid:!0,data:i}}else if(r===N.array&&n===N.array){if(t.length!==e.length)return{valid:!1};const a=[];for(let s=0;s<t.length;s++){const i=t[s],o=e[s],l=zt(i,o);if(!l.valid)return{valid:!1};a.push(l.data)}return{valid:!0,data:a}}else return r===N.date&&n===N.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class dt extends ${_parse(e){const{status:r,ctx:n}=this._processInputParams(e),a=(s,i)=>{if(sr(s)||sr(i))return M;const o=zt(s.value,i.value);return o.valid?((ir(s)||ir(i))&&r.dirty(),{status:r.value,value:o.data}):(b(n,{code:p.invalid_intersection_types}),M)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([s,i])=>a(s,i)):a(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}dt.create=(t,e,r)=>new dt({left:t,right:e,typeName:R.ZodIntersection,...Z(r)});class je extends ${_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==N.array)return b(n,{code:p.invalid_type,expected:N.array,received:n.parsedType}),M;if(n.data.length<this._def.items.length)return b(n,{code:p.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),M;!this._def.rest&&n.data.length>this._def.items.length&&(b(n,{code:p.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),r.dirty());const s=[...n.data].map((i,o)=>{const l=this._def.items[o]||this._def.rest;return l?l._parse(new ye(n,i,n.path,o)):null}).filter(i=>!!i);return n.common.async?Promise.all(s).then(i=>ne.mergeArray(r,i)):ne.mergeArray(r,s)}get items(){return this._def.items}rest(e){return new je({...this._def,rest:e})}}je.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new je({items:t,typeName:R.ZodTuple,rest:null,...Z(e)})};class hr extends ${get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==N.map)return b(n,{code:p.invalid_type,expected:N.map,received:n.parsedType}),M;const a=this._def.keyType,s=this._def.valueType,i=[...n.data.entries()].map(([o,l],d)=>({key:a._parse(new ye(n,o,n.path,[d,"key"])),value:s._parse(new ye(n,l,n.path,[d,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const l of i){const d=await l.key,c=await l.value;if(d.status==="aborted"||c.status==="aborted")return M;(d.status==="dirty"||c.status==="dirty")&&r.dirty(),o.set(d.value,c.value)}return{status:r.value,value:o}})}else{const o=new Map;for(const l of i){const d=l.key,c=l.value;if(d.status==="aborted"||c.status==="aborted")return M;(d.status==="dirty"||c.status==="dirty")&&r.dirty(),o.set(d.value,c.value)}return{status:r.value,value:o}}}}hr.create=(t,e,r)=>new hr({valueType:e,keyType:t,typeName:R.ZodMap,...Z(r)});class Ke extends ${_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==N.set)return b(n,{code:p.invalid_type,expected:N.set,received:n.parsedType}),M;const a=this._def;a.minSize!==null&&n.data.size<a.minSize.value&&(b(n,{code:p.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),r.dirty()),a.maxSize!==null&&n.data.size>a.maxSize.value&&(b(n,{code:p.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),r.dirty());const s=this._def.valueType;function i(l){const d=new Set;for(const c of l){if(c.status==="aborted")return M;c.status==="dirty"&&r.dirty(),d.add(c.value)}return{status:r.value,value:d}}const o=[...n.data.values()].map((l,d)=>s._parse(new ye(n,l,n.path,d)));return n.common.async?Promise.all(o).then(l=>i(l)):i(o)}min(e,r){return new Ke({...this._def,minSize:{value:e,message:O.toString(r)}})}max(e,r){return new Ke({...this._def,maxSize:{value:e,message:O.toString(r)}})}size(e,r){return this.min(e,r).max(e,r)}nonempty(e){return this.min(1,e)}}Ke.create=(t,e)=>new Ke({valueType:t,minSize:null,maxSize:null,typeName:R.ZodSet,...Z(e)});class gr extends ${get schema(){return this._def.getter()}_parse(e){const{ctx:r}=this._processInputParams(e);return this._def.getter()._parse({data:r.data,path:r.path,parent:r})}}gr.create=(t,e)=>new gr({getter:t,typeName:R.ZodLazy,...Z(e)});class vr extends ${_parse(e){if(e.data!==this._def.value){const r=this._getOrReturnCtx(e);return b(r,{received:r.data,code:p.invalid_literal,expected:this._def.value}),M}return{status:"valid",value:e.data}}get value(){return this._def.value}}vr.create=(t,e)=>new vr({value:t,typeName:R.ZodLiteral,...Z(e)});function on(t,e){return new Me({values:t,typeName:R.ZodEnum,...Z(e)})}class Me extends ${_parse(e){if(typeof e.data!="string"){const r=this._getOrReturnCtx(e),n=this._def.values;return b(r,{expected:B.joinValues(n),received:r.parsedType,code:p.invalid_type}),M}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const r=this._getOrReturnCtx(e),n=this._def.values;return b(r,{received:r.data,code:p.invalid_enum_value,options:n}),M}return ae(e.data)}get options(){return this._def.values}get enum(){const e={};for(const r of this._def.values)e[r]=r;return e}get Values(){const e={};for(const r of this._def.values)e[r]=r;return e}get Enum(){const e={};for(const r of this._def.values)e[r]=r;return e}extract(e,r=this._def){return Me.create(e,{...this._def,...r})}exclude(e,r=this._def){return Me.create(this.options.filter(n=>!e.includes(n)),{...this._def,...r})}}Me.create=on;class yr extends ${_parse(e){const r=B.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==N.string&&n.parsedType!==N.number){const a=B.objectValues(r);return b(n,{expected:B.joinValues(a),received:n.parsedType,code:p.invalid_type}),M}if(this._cache||(this._cache=new Set(B.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const a=B.objectValues(r);return b(n,{received:n.data,code:p.invalid_enum_value,options:a}),M}return ae(e.data)}get enum(){return this._def.values}}yr.create=(t,e)=>new yr({values:t,typeName:R.ZodNativeEnum,...Z(e)});class ut extends ${unwrap(){return this._def.type}_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==N.promise&&r.common.async===!1)return b(r,{code:p.invalid_type,expected:N.promise,received:r.parsedType}),M;const n=r.parsedType===N.promise?r.data:Promise.resolve(r.data);return ae(n.then(a=>this._def.type.parseAsync(a,{path:r.path,errorMap:r.common.contextualErrorMap})))}}ut.create=(t,e)=>new ut({type:t,typeName:R.ZodPromise,...Z(e)});class Re extends ${innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===R.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:r,ctx:n}=this._processInputParams(e),a=this._def.effect||null,s={addIssue:i=>{b(n,i),i.fatal?r.abort():r.dirty()},get path(){return n.path}};if(s.addIssue=s.addIssue.bind(s),a.type==="preprocess"){const i=a.transform(n.data,s);if(n.common.async)return Promise.resolve(i).then(async o=>{if(r.value==="aborted")return M;const l=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return l.status==="aborted"?M:l.status==="dirty"||r.value==="dirty"?Be(l.value):l});{if(r.value==="aborted")return M;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?M:o.status==="dirty"||r.value==="dirty"?Be(o.value):o}}if(a.type==="refinement"){const i=o=>{const l=a.refinement(o,s);if(n.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?M:(o.status==="dirty"&&r.dirty(),i(o.value),{status:r.value,value:o.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?M:(o.status==="dirty"&&r.dirty(),i(o.value).then(()=>({status:r.value,value:o.value}))))}if(a.type==="transform")if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Ie(i))return M;const o=a.transform(i.value,s);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:r.value,value:o}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>Ie(i)?Promise.resolve(a.transform(i.value,s)).then(o=>({status:r.value,value:o})):M);B.assertNever(a)}}Re.create=(t,e,r)=>new Re({schema:t,typeName:R.ZodEffects,effect:e,...Z(r)});Re.createWithPreprocess=(t,e,r)=>new Re({schema:e,effect:{type:"preprocess",transform:t},typeName:R.ZodEffects,...Z(r)});class ve extends ${_parse(e){return this._getType(e)===N.undefined?ae(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ve.create=(t,e)=>new ve({innerType:t,typeName:R.ZodOptional,...Z(e)});class Pe extends ${_parse(e){return this._getType(e)===N.null?ae(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Pe.create=(t,e)=>new Pe({innerType:t,typeName:R.ZodNullable,...Z(e)});class Zt extends ${_parse(e){const{ctx:r}=this._processInputParams(e);let n=r.data;return r.parsedType===N.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:r.path,parent:r})}removeDefault(){return this._def.innerType}}Zt.create=(t,e)=>new Zt({innerType:t,typeName:R.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...Z(e)});class Vt extends ${_parse(e){const{ctx:r}=this._processInputParams(e),n={...r,common:{...r.common,issues:[]}},a=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return ot(a)?a.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new de(n.common.issues)},input:n.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new de(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Vt.create=(t,e)=>new Vt({innerType:t,typeName:R.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...Z(e)});class br extends ${_parse(e){if(this._getType(e)!==N.nan){const n=this._getOrReturnCtx(e);return b(n,{code:p.invalid_type,expected:N.nan,received:n.parsedType}),M}return{status:"valid",value:e.data}}}br.create=t=>new br({typeName:R.ZodNaN,...Z(t)});class ws extends ${_parse(e){const{ctx:r}=this._processInputParams(e),n=r.data;return this._def.type._parse({data:n,path:r.path,parent:r})}unwrap(){return this._def.type}}class qt extends ${_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?M:s.status==="dirty"?(r.dirty(),Be(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{const a=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return a.status==="aborted"?M:a.status==="dirty"?(r.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:n.path,parent:n})}}static create(e,r){return new qt({in:e,out:r,typeName:R.ZodPipeline})}}class $t extends ${_parse(e){const r=this._def.innerType._parse(e),n=a=>(Ie(a)&&(a.value=Object.freeze(a.value)),a);return ot(r)?r.then(a=>n(a)):n(r)}unwrap(){return this._def.innerType}}$t.create=(t,e)=>new $t({innerType:t,typeName:R.ZodReadonly,...Z(e)});var R;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(R||(R={}));const ie=ge.create,_s=Le.create;be.create;oe.create;const ks=J.create;ct.create;dt.create;je.create;Me.create;ut.create;ve.create;Pe.create;ks({transaction_id:ie(),transaction_date:ie(),expense_status:ie(),payment_status:ie(),merchant:ie(),category:ie(),amount:_s(),currency:ie(),lastEdited:ie(),continent:ie(),country:ie()});const Cs=[{value:"live",label:"Live",variant:"success"},{value:"inactive",label:"Inactive",variant:"neutral"},{value:"archived",label:"Archived",variant:"warning"}],js=[{value:"US-West 1",label:"US-West 1"},{value:"US-West 2",label:"US-West 2"},{value:"US-East 1",label:"US-East 1"},{value:"US-East 2",label:"US-East 2"},{value:"EU-West 1",label:"EU-West 1"},{value:"EU-North 1",label:"EU-North 1"},{value:"EU-Central 1",label:"EU-Central 1"}],Ss=[{value:"is-equal-to",label:"is equal to"},{value:"is-between",label:"is between"},{value:"is-greater-than",label:"is greater than"},{value:"is-less-than",label:"is less than"}];function Ns(t,e,r,n){var a=this,s=q.useRef(null),i=q.useRef(0),o=q.useRef(0),l=q.useRef(null),d=q.useRef([]),c=q.useRef(),f=q.useRef(),y=q.useRef(t),g=q.useRef(!0);y.current=t;var m=typeof window<"u",S=!e&&e!==0&&m;if(typeof t!="function")throw new TypeError("Expected a function");e=+e||0;var I=!!(r=r||{}).leading,D=!("trailing"in r)||!!r.trailing,E="maxWait"in r,F="debounceOnServer"in r&&!!r.debounceOnServer,P=E?Math.max(+r.maxWait||0,e):null;q.useEffect(function(){return g.current=!0,function(){g.current=!1}},[]);var H=q.useMemo(function(){var h=function(k){var x=d.current,L=c.current;return d.current=c.current=null,i.current=k,o.current=o.current||k,f.current=y.current.apply(L,x)},U=function(k,x){S&&cancelAnimationFrame(l.current),l.current=S?requestAnimationFrame(k):setTimeout(k,x)},W=function(k){if(!g.current)return!1;var x=k-s.current;return!s.current||x>=e||x<0||E&&k-i.current>=P},Y=function(k){return l.current=null,D&&d.current?h(k):(d.current=c.current=null,f.current)},K=function k(){var x=Date.now();if(I&&o.current===i.current&&te(),W(x))return Y(x);if(g.current){var L=e-(x-s.current),v=E?Math.min(L,P-(x-i.current)):L;U(k,v)}},te=function(){},A=function(){if(m||F){var k=Date.now(),x=W(k);if(d.current=[].slice.call(arguments),c.current=a,s.current=k,x){if(!l.current&&g.current)return i.current=s.current,U(K,e),I?h(s.current):f.current;if(E)return U(K,e),h(s.current)}return l.current||U(K,e),f.current}};return A.cancel=function(){l.current&&(S?cancelAnimationFrame(l.current):clearTimeout(l.current)),i.current=0,d.current=s.current=c.current=l.current=null},A.isPending=function(){return!!l.current},A.flush=function(){return l.current?Y(Date.now()):f.current},A},[I,E,e,P,D,S,m,F,n]);return H}function ln({...t}){return u.jsx(En,{"data-slot":"popover",...t})}function cn({...t}){return u.jsx(In,{"data-slot":"popover-trigger",...t})}function dn({className:t,align:e="center",sideOffset:r=4,...n}){return u.jsx(Ln,{children:u.jsx(Mn,{"data-slot":"popover-content",align:e,sideOffset:r,className:Se("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...n})})}const Ts=({columnFilterLabels:t,className:e})=>t?t.length<3?u.jsx("span",{className:X("truncate",e),children:t.map((r,n)=>u.jsxs("span",{className:X("font-semibold text-indigo-600 dark:text-indigo-400"),children:[r,n<t.length-1&&", "]},r))}):u.jsx(u.Fragment,{children:u.jsxs("span",{className:X("font-semibold text-indigo-600 dark:text-indigo-400",e),children:[t[0]," and ",t.length-1," more"]})}):null;function bt({column:t,title:e,options:r,type:n="select",formatter:a=s=>String(s)}){const s=t?.getFilterValue(),[i,o]=_.useState(s),l=_.useMemo(()=>{if(i){if(Array.isArray(i))return i.map(c=>a(c));if(typeof i=="string")return[a(i)];if(typeof i=="object"&&"condition"in i){const c=r?.find(f=>f.value===i.condition)?.label;return c?!i.value?.[0]&&!i.value?.[1]?[`${c}`]:i.value?.[1]?[`${c} ${a(i.value?.[0])} and ${a(i.value?.[1])}`]:[`${c} ${a(i.value?.[0])}`]:void 0}}},[i,r,a]),d=()=>{switch(n){case"select":return u.jsxs(St,{value:i,onValueChange:c=>{o(c)},children:[u.jsx(Nt,{className:"mt-2 sm:py-1",children:u.jsx(Tt,{placeholder:"Select"})}),u.jsx(Ot,{children:r?.map(c=>u.jsx(At,{value:c.value,children:c.label},c.value))})]});case"checkbox":return u.jsx("div",{className:"mt-2 space-y-2 overflow-y-auto sm:max-h-36",children:r?.map(c=>u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(Vr,{id:c.value,checked:i?.includes(c.value),onCheckedChange:f=>{o(y=>f?y?[...y,c.value]:[c.value]:y.filter(g=>g!==c.value))}}),u.jsx(jt,{htmlFor:c.value,className:"text-base sm:text-sm",children:c.label})]},c.label))});case"number":{const c=i?.condition==="is-between";return u.jsxs("div",{className:"space-y-2",children:[u.jsxs(St,{value:i?.condition,onValueChange:f=>{o(y=>({condition:f,value:[f!==""?y?.value?.[0]:"",""]}))},children:[u.jsx(Nt,{className:"mt-2 sm:py-1",children:u.jsx(Tt,{placeholder:"Select condition"})}),u.jsx(Ot,{children:r?.map(f=>u.jsx(At,{value:f.value,children:f.label},f.value))})]}),u.jsxs("div",{className:"flex w-full items-center gap-2",children:[u.jsx(ra,{className:"size-4 shrink-0 text-gray-500","aria-hidden":"true"}),u.jsx(Xt,{disabled:!i?.condition,type:"number",placeholder:"$0",className:"sm:[&>input]:py-1",value:i?.value?.[0],onChange:f=>{o(y=>({condition:y?.condition,value:[f.target.value,c?y?.value?.[1]:""]}))}}),i?.condition==="is-between"&&u.jsxs(u.Fragment,{children:[u.jsx("span",{className:"text-xs font-medium text-gray-500",children:"and"}),u.jsx(Xt,{disabled:!i?.condition,type:"number",placeholder:"$0",className:"sm:[&>input]:py-1",value:i?.value?.[1],onChange:f=>{o(y=>({condition:y?.condition,value:[y?.value?.[0],f.target.value]}))}})]})]})]})}}};return _.useEffect(()=>{o(s)},[s]),u.jsxs(ln,{children:[u.jsx(cn,{asChild:!0,children:u.jsxs("button",{type:"button",className:X("flex w-full items-center gap-x-1.5 whitespace-nowrap rounded-md border border-gray-300 px-2 py-1.5 font-medium text-gray-600 hover:bg-gray-50 sm:w-fit sm:text-xs dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-900",i&&(typeof i=="object"&&"condition"in i&&i.condition!==""||typeof i=="string"&&i!==""||Array.isArray(i)&&i.length>0)?"":"border-dashed",On),children:[u.jsx("span",{"aria-hidden":"true",onClick:c=>{i&&(c.stopPropagation(),t?.setFilterValue(""),o(""))},children:u.jsx($r,{className:X("-ml-px size-5 shrink-0 transition sm:size-4",i&&"rotate-45 hover:text-red-500"),"aria-hidden":"true"})}),l&&l.length>0?u.jsx("span",{children:e}):u.jsx("span",{className:"w-full text-left sm:w-fit",children:e}),l&&l.length>0&&u.jsx("span",{className:"h-4 w-px bg-gray-300 dark:bg-gray-700","aria-hidden":"true"}),u.jsx(Ts,{columnFilterLabels:l,className:"w-full text-left sm:w-fit"}),u.jsx(Fr,{className:"size-5 shrink-0 text-gray-500 sm:size-4","aria-hidden":"true"})]})}),u.jsx(dn,{align:"start",sideOffset:7,className:"min-w-[calc(var(--radix-popover-trigger-width))] max-w-[calc(var(--radix-popover-trigger-width))] sm:min-w-56 sm:max-w-56",onInteractOutside:()=>{(!s||typeof s=="string"&&s===""||Array.isArray(s)&&s.length===0||typeof s=="object"&&"condition"in s&&s.condition==="")&&(t?.setFilterValue(""),o(""))},children:u.jsx("form",{onSubmit:c=>{c.preventDefault(),t?.setFilterValue(i)},children:u.jsxs("div",{className:"space-y-2",children:[u.jsxs("div",{children:[u.jsxs(jt,{className:"text-base font-medium sm:text-sm",children:["Filter by ",e]}),d()]}),u.jsx("div",{className:"w-full",children:u.jsx(ce,{type:"submit",className:"w-full sm:py-1",children:"Apply"})}),l&&l.length>0&&u.jsx(ce,{variant:"secondary",className:"w-full sm:py-1",type:"button",onClick:()=>{t?.setFilterValue(""),o(n==="checkbox"?[]:n==="number"?{condition:"",value:["",""]}:"")},children:"Reset"})]})})})]})}var Os="Invariant failed";function st(t,e){if(!t)throw new Error(Os)}var As={large:700};function Es(t){t.animate([{backgroundColor:"var(--ds-background-selected, #E9F2FF)"},{}],{duration:As.large,easing:"cubic-bezier(0.25, 0.1, 0.25, 1.0)",iterations:1})}function Ye(t){"@babel/helpers - typeof";return Ye=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ye(t)}function Is(t,e){if(Ye(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(Ye(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ls(t){var e=Is(t,"string");return Ye(e)=="symbol"?e:e+""}function Je(t,e,r){return(e=Ls(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function xr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function wr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?xr(Object(r),!0).forEach(function(n){Je(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}var Ms={top:function(e,r){return Math.abs(r.y-e.top)},right:function(e,r){return Math.abs(e.right-r.x)},bottom:function(e,r){return Math.abs(e.bottom-r.y)},left:function(e,r){return Math.abs(r.x-e.left)}},un=Symbol("closestEdge");function Rs(t,e){var r,n,a=e.element,s=e.input,i=e.allowedEdges,o={x:s.clientX,y:s.clientY},l=a.getBoundingClientRect(),d=i.map(function(f){return{edge:f,value:Ms[f](l,o)}}),c=(r=(n=d.sort(function(f,y){return f.value-y.value})[0])===null||n===void 0?void 0:n.edge)!==null&&r!==void 0?r:null;return wr(wr({},t),{},Je({},un,c))}function fn(t){var e;return(e=t[un])!==null&&e!==void 0?e:null}function Ps(t){var e=t.startIndex,r=t.closestEdgeOfTarget,n=t.indexOfTarget,a=t.axis;if(e===-1||n===-1||e===n)return e;if(r==null)return n;var s=r==="bottom"||a==="horizontal",i=e<n;return i?s?n:n-1:s?n+1:n}var Ds=1e3,He=null,ke="1px",zs={width:ke,height:ke,padding:"0",position:"absolute",border:"0",clip:"rect(".concat(ke,", ").concat(ke,", ").concat(ke,", ").concat(ke,")"),overflow:"hidden",whiteSpace:"nowrap",marginTop:"-".concat(ke),pointerEvents:"none"};function Zs(){var t=document.createElement("div");return t.setAttribute("role","status"),Object.assign(t.style,zs),document.body.append(t),t}function _r(){return He===null&&(He=Zs()),He}var Ue=null;function pn(){Ue!==null&&clearTimeout(Ue),Ue=null}function Vs(t){_r(),pn(),Ue=setTimeout(function(){Ue=null;var e=_r();e.textContent=t},Ds)}function $s(){var t;pn(),(t=He)===null||t===void 0||t.remove(),He=null}const Fs=5;function Bs(t){if(!t.length)return;if(t.length===1&&t[0]&&!t[0].includes(" "))return t[0];const e={};for(const n of t){if(!n)continue;const a=n.split(" ");for(const s of a){const i=s.startsWith("_")?s.slice(0,Fs):s;e[i]=s}}let r="";for(const n in e)r+=e[n]+" ";if(r)return r.trimEnd()}var mn={default:"var(--ds-border-selected, #0C66E4)",warning:"var(--ds-border-warning, #E56910)"},Hs="var(--ds-border-width-outline, 2px)",Us={top:"horizontal",bottom:"horizontal",left:"vertical",right:"vertical"},Ws={root:"_1e0c1ule _kqswstnw _1pbykb7n _lcxvglyw _bfhkys7w _rfx31ssb _3l8810ly _kzdanqa1 _15m6ys7w _cfu11ld9 _1kt9b3bt _1cs8stnw _13y0usvi _1mp4vjfa _kfgtvjfa"},Gs={horizontal:"_4t3i10ly _1e02fghn _rjxpidpf _z5wtuj5p",vertical:"_1bsb10ly _154ifghn _94n5idpf _1aukuj5p"},qs={top:"_154ihv0e _1auk70hn",right:"_1xi2hv0e _ooun70hn",bottom:"_94n5hv0e _19wo70hn",left:"_1ltvhv0e _qnec70hn"},Ks={terminal:function(e){var r=e.indent;return"calc(var(--terminal-radius) + ".concat(r,")")},"terminal-no-bleed":function(e){var r=e.indent;return"calc(var(--terminal-diameter) + ".concat(r,")")},"no-terminal":function(e){var r=e.indent;return r}};function Ys(t){var e=t.edge,r=t.gap,n=r===void 0?"0px":r,a=t.indent,s=a===void 0?"0px":a,i=t.strokeColor,o=i===void 0?mn.default:i,l=t.strokeWidth,d=l===void 0?Hs:l,c=t.type,f=c===void 0?"terminal":c,y=Us[e];return q.createElement("div",{style:{"--stroke-color":o,"--stroke-width":d,"--main-axis-offset":"calc(-0.5 * (".concat(n," + var(--stroke-width)))"),"--line-main-axis-start":Ks[f]({indent:s}),"--terminal-display":f==="no-terminal"?"none":"block","--terminal-diameter":"calc(var(--stroke-width) * 4)","--terminal-radius":"calc(var(--terminal-diameter) / 2)","--terminal-main-axis-start":"calc(-1 * var(--terminal-diameter))","--terminal-cross-axis-offset":"calc(calc(var(--stroke-width) - var(--terminal-diameter)) / 2)"},className:Bs([Ws.root,Gs[y],qs[e]])})}function Xs(t){var e=t.appearance,r=e===void 0?"default":e,n=t.edge,a=t.gap,s=t.indent,i=t.type;return _.createElement(Ys,{edge:n,gap:a,strokeColor:mn[r],type:i,indent:s})}function pt(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return function(){e.forEach(function(a){return a()})}}function Js(t){if(Array.isArray(t))return t}function Qs(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,a,s,i,o=[],l=!0,d=!1;try{if(s=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=s.call(r)).done)&&(o.push(n.value),o.length!==e);l=!0);}catch(c){d=!0,a=c}finally{try{if(!l&&r.return!=null&&(i=r.return(),Object(i)!==i))return}finally{if(d)throw a}}return o}}function Ft(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hn(t,e){if(t){if(typeof t=="string")return Ft(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ft(t,e):void 0}}function ei(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Kt(t,e){return Js(t)||Qs(t,e)||hn(t,e)||ei()}var xt={},$e={},kr;function gn(){if(kr)return $e;kr=1,Object.defineProperty($e,"__esModule",{value:!0}),$e.bind=void 0;function t(e,r){var n=r.type,a=r.listener,s=r.options;return e.addEventListener(n,a,s),function(){e.removeEventListener(n,a,s)}}return $e.bind=t,$e}var Ce={},Cr;function ti(){if(Cr)return Ce;Cr=1;var t=Ce&&Ce.__assign||function(){return t=Object.assign||function(s){for(var i,o=1,l=arguments.length;o<l;o++){i=arguments[o];for(var d in i)Object.prototype.hasOwnProperty.call(i,d)&&(s[d]=i[d])}return s},t.apply(this,arguments)};Object.defineProperty(Ce,"__esModule",{value:!0}),Ce.bindAll=void 0;var e=gn();function r(s){if(!(typeof s>"u"))return typeof s=="boolean"?{capture:s}:s}function n(s,i){if(i==null)return s;var o=t(t({},s),{options:t(t({},r(i)),r(s.options))});return o}function a(s,i,o){var l=i.map(function(d){var c=n(d,o);return(0,e.bind)(s,c)});return function(){l.forEach(function(c){return c()})}}return Ce.bindAll=a,Ce}var jr;function ri(){return jr||(jr=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.bindAll=t.bind=void 0;var e=gn();Object.defineProperty(t,"bind",{enumerable:!0,get:function(){return e.bind}});var r=ti();Object.defineProperty(t,"bindAll",{enumerable:!0,get:function(){return r.bindAll}})}(xt)),xt}var De=ri(),vn="data-pdnd-honey-pot";function yn(t){return t instanceof Element&&t.hasAttribute(vn)}function bn(t){var e=document.elementsFromPoint(t.x,t.y),r=Kt(e,2),n=r[0],a=r[1];return n?yn(n)?a??null:n:null}var xn=2147483647;function Sr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Nr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Sr(Object(r),!0).forEach(function(n){Je(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Sr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}var Xe=2,Tr=Xe/2;function ni(t){return{x:Math.floor(t.x),y:Math.floor(t.y)}}function ai(t){return{x:t.x-Tr,y:t.y-Tr}}function si(t){return{x:Math.max(t.x,0),y:Math.max(t.y,0)}}function ii(t){return{x:Math.min(t.x,window.innerWidth-Xe),y:Math.min(t.y,window.innerHeight-Xe)}}function Or(t){var e=t.client,r=ii(si(ai(ni(e))));return DOMRect.fromRect({x:r.x,y:r.y,width:Xe,height:Xe})}function Ar(t){var e=t.clientRect;return{left:"".concat(e.left,"px"),top:"".concat(e.top,"px"),width:"".concat(e.width,"px"),height:"".concat(e.height,"px")}}function oi(t){var e=t.client,r=t.clientRect;return e.x>=r.x&&e.x<=r.x+r.width&&e.y>=r.y&&e.y<=r.y+r.height}function li(t){var e=t.initial,r=document.createElement("div");r.setAttribute(vn,"true");var n=Or({client:e});Object.assign(r.style,Nr(Nr({backgroundColor:"transparent",position:"fixed",padding:0,margin:0,boxSizing:"border-box"},Ar({clientRect:n})),{},{pointerEvents:"auto",zIndex:xn})),document.body.appendChild(r);var a=De.bind(window,{type:"pointermove",listener:function(i){var o={x:i.clientX,y:i.clientY};n=Or({client:o}),Object.assign(r.style,Ar({clientRect:n}))},options:{capture:!0}});return function(i){var o=i.current;if(a(),oi({client:o,clientRect:n})){r.remove();return}function l(){d(),r.remove()}var d=De.bindAll(window,[{type:"pointerdown",listener:l},{type:"pointermove",listener:l},{type:"focusin",listener:l},{type:"focusout",listener:l},{type:"dragstart",listener:l},{type:"dragenter",listener:l},{type:"dragover",listener:l}],{capture:!0})}}function ci(){var t=null;function e(){return t=null,De.bind(window,{type:"pointermove",listener:function(a){t={x:a.clientX,y:a.clientY}},options:{capture:!0}})}function r(){var n=null;return function(s){var i=s.eventName,o=s.payload;if(i==="onDragStart"){var l=o.location.initial.input,d=t??{x:l.clientX,y:l.clientY};n=li({initial:d})}if(i==="onDrop"){var c,f=o.location.current.input;(c=n)===null||c===void 0||c({current:{x:f.clientX,y:f.clientY}}),n=null,t=null}}}return{bindEvents:e,getOnPostDispatch:r}}function di(t){if(Array.isArray(t))return Ft(t)}function ui(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function fi(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wn(t){return di(t)||ui(t)||hn(t)||fi()}function Ne(t){var e=null;return function(){if(!e){for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];var i=t.apply(this,a);e={result:i}}return e.result}}var pi=Ne(function(){return navigator.userAgent.includes("Firefox")}),Qe=Ne(function(){var e=navigator,r=e.userAgent;return r.includes("AppleWebKit")&&!r.includes("Chrome")}),Bt={isLeavingWindow:Symbol("leaving"),isEnteringWindow:Symbol("entering")};function mi(t){var e=t.dragLeave;return Qe()?e.hasOwnProperty(Bt.isLeavingWindow):!1}(function(){if(typeof window>"u"||!Qe())return;function e(){return{enterCount:0,isOverWindow:!1}}var r=e();function n(){r=e()}De.bindAll(window,[{type:"dragstart",listener:function(){r.enterCount=0,r.isOverWindow=!0}},{type:"drop",listener:n},{type:"dragend",listener:n},{type:"dragenter",listener:function(s){!r.isOverWindow&&r.enterCount===0&&(s[Bt.isEnteringWindow]=!0),r.isOverWindow=!0,r.enterCount++}},{type:"dragleave",listener:function(s){r.enterCount--,r.isOverWindow&&r.enterCount===0&&(s[Bt.isLeavingWindow]=!0,r.isOverWindow=!1)}}],{capture:!0})})();function hi(t){return"nodeName"in t}function gi(t){return hi(t)&&t.ownerDocument!==document}function vi(t){var e=t.dragLeave,r=e.type,n=e.relatedTarget;return r!=="dragleave"?!1:Qe()?mi({dragLeave:e}):n==null?!0:pi()?gi(n):n instanceof HTMLIFrameElement}function yi(t){var e=t.onDragEnd;return[{type:"pointermove",listener:function(){var r=0;return function(){if(r<20){r++;return}e()}}()},{type:"pointerdown",listener:e}]}function We(t){return{altKey:t.altKey,button:t.button,buttons:t.buttons,ctrlKey:t.ctrlKey,metaKey:t.metaKey,shiftKey:t.shiftKey,clientX:t.clientX,clientY:t.clientY,pageX:t.pageX,pageY:t.pageY}}var bi=function(e){var r=[],n=null,a=function(){for(var i=arguments.length,o=new Array(i),l=0;l<i;l++)o[l]=arguments[l];r=o,!n&&(n=requestAnimationFrame(function(){n=null,e.apply(void 0,r)}))};return a.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},a},wt=bi(function(t){return t()}),at=function(){var t=null;function e(n){var a=requestAnimationFrame(function(){t=null,n()});t={frameId:a,fn:n}}function r(){t&&(cancelAnimationFrame(t.frameId),t.fn(),t=null)}return{schedule:e,flush:r}}();function xi(t){var e=t.source,r=t.initial,n=t.dispatchEvent,a={dropTargets:[]};function s(o){n(o),a={dropTargets:o.payload.location.current.dropTargets}}var i={start:function(l){var d=l.nativeSetDragImage,c={current:r,previous:a,initial:r};s({eventName:"onGenerateDragPreview",payload:{source:e,location:c,nativeSetDragImage:d}}),at.schedule(function(){s({eventName:"onDragStart",payload:{source:e,location:c}})})},dragUpdate:function(l){var d=l.current;at.flush(),wt.cancel(),s({eventName:"onDropTargetChange",payload:{source:e,location:{initial:r,previous:a,current:d}}})},drag:function(l){var d=l.current;wt(function(){at.flush();var c={initial:r,previous:a,current:d};s({eventName:"onDrag",payload:{source:e,location:c}})})},drop:function(l){var d=l.current,c=l.updatedSourcePayload;at.flush(),wt.cancel(),s({eventName:"onDrop",payload:{source:c??e,location:{current:d,previous:a,initial:r}}})}};return i}var Ht={isActive:!1};function _n(){return!Ht.isActive}function wi(t){return t.dataTransfer?t.dataTransfer.setDragImage.bind(t.dataTransfer):null}function _i(t){var e=t.current,r=t.next;if(e.length!==r.length)return!0;for(var n=0;n<e.length;n++)if(e[n].element!==r[n].element)return!0;return!1}function ki(t){var e=t.event,r=t.dragType,n=t.getDropTargetsOver,a=t.dispatchEvent;if(!_n())return;var s=Ci({event:e,dragType:r,getDropTargetsOver:n});Ht.isActive=!0;var i={current:s};_t({event:e,current:s.dropTargets});var o=xi({source:r.payload,dispatchEvent:a,initial:s});function l(g){var m=_i({current:i.current.dropTargets,next:g.dropTargets});i.current=g,m&&o.dragUpdate({current:i.current})}function d(g){var m=We(g),S=yn(g.target)?bn({x:m.clientX,y:m.clientY}):g.target,I=n({target:S,input:m,source:r.payload,current:i.current.dropTargets});I.length&&(g.preventDefault(),_t({event:g,current:I})),l({dropTargets:I,input:m})}function c(){i.current.dropTargets.length&&l({dropTargets:[],input:i.current.input}),o.drop({current:i.current,updatedSourcePayload:null}),f()}function f(){Ht.isActive=!1,y()}var y=De.bindAll(window,[{type:"dragover",listener:function(m){d(m),o.drag({current:i.current})}},{type:"dragenter",listener:d},{type:"dragleave",listener:function(m){vi({dragLeave:m})&&(l({input:i.current.input,dropTargets:[]}),r.startedFrom==="external"&&c())}},{type:"drop",listener:function(m){if(i.current={dropTargets:i.current.dropTargets,input:We(m)},!i.current.dropTargets.length){c();return}m.preventDefault(),_t({event:m,current:i.current.dropTargets}),o.drop({current:i.current,updatedSourcePayload:r.type==="external"?r.getDropPayload(m):null}),f()}},{type:"dragend",listener:function(m){i.current={dropTargets:i.current.dropTargets,input:We(m)},c()}}].concat(wn(yi({onDragEnd:c}))),{capture:!0});o.start({nativeSetDragImage:wi(e)})}function _t(t){var e,r=t.event,n=t.current,a=(e=n[0])===null||e===void 0?void 0:e.dropEffect;a!=null&&r.dataTransfer&&(r.dataTransfer.dropEffect=a)}function Ci(t){var e=t.event,r=t.dragType,n=t.getDropTargetsOver,a=We(e);if(r.startedFrom==="external")return{input:a,dropTargets:[]};var s=n({input:a,source:r.payload,target:e.target,current:[]});return{input:a,dropTargets:s}}var Er={canStart:_n,start:ki},Ut=new Map;function ji(t){var e=t.typeKey,r=t.mount,n=Ut.get(e);if(n)return n.usageCount++,n;var a={typeKey:e,unmount:r(),usageCount:1};return Ut.set(e,a),a}function Si(t){var e=ji(t);return function(){e.usageCount--,!(e.usageCount>0)&&(e.unmount(),Ut.delete(t.typeKey))}}function kn(t,e){var r=e.attribute,n=e.value;return t.setAttribute(r,n),function(){return t.removeAttribute(r)}}function Ir(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function me(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ir(Object(r),!0).forEach(function(n){Je(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ir(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function kt(t,e){var r=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=Ni(t))||e){r&&(t=r);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(d){throw d},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var s,i=!0,o=!1;return{s:function(){r=r.call(t)},n:function(){var d=r.next();return i=d.done,d},e:function(d){o=!0,s=d},f:function(){try{i||r.return==null||r.return()}finally{if(o)throw s}}}}function Ni(t,e){if(t){if(typeof t=="string")return Lr(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Lr(t,e):void 0}}function Lr(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Ct(t){return t.slice(0).reverse()}function Ti(t){var e=t.typeKey,r=t.defaultDropEffect,n=new WeakMap,a="data-drop-target-for-".concat(e),s="[".concat(a,"]");function i(g){return n.set(g.element,g),function(){return n.delete(g.element)}}function o(g){var m=pt(kn(g.element,{attribute:a,value:"true"}),i(g));return Ne(m)}function l(g){var m,S,I,D,E=g.source,F=g.target,P=g.input,H=g.result,h=H===void 0?[]:H;if(F==null)return h;if(!(F instanceof Element))return F instanceof Node?l({source:E,target:F.parentElement,input:P,result:h}):h;var U=F.closest(s);if(U==null)return h;var W=n.get(U);if(W==null)return h;var Y={input:P,source:E,element:W.element};if(W.canDrop&&!W.canDrop(Y))return l({source:E,target:W.element.parentElement,input:P,result:h});var K=(m=(S=W.getData)===null||S===void 0?void 0:S.call(W,Y))!==null&&m!==void 0?m:{},te=(I=(D=W.getDropEffect)===null||D===void 0?void 0:D.call(W,Y))!==null&&I!==void 0?I:r,A={data:K,element:W.element,dropEffect:te,isActiveDueToStickiness:!1};return l({source:E,target:W.element.parentElement,input:P,result:[].concat(wn(h),[A])})}function d(g){var m=g.eventName,S=g.payload,I=kt(S.location.current.dropTargets),D;try{for(I.s();!(D=I.n()).done;){var E,F=D.value,P=n.get(F.element),H=me(me({},S),{},{self:F});P==null||(E=P[m])===null||E===void 0||E.call(P,H)}}catch(h){I.e(h)}finally{I.f()}}var c={onGenerateDragPreview:d,onDrag:d,onDragStart:d,onDrop:d,onDropTargetChange:function(m){var S=m.payload,I=new Set(S.location.current.dropTargets.map(function(w){return w.element})),D=new Set,E=kt(S.location.previous.dropTargets),F;try{for(E.s();!(F=E.n()).done;){var P,H=F.value;D.add(H.element);var h=n.get(H.element),U=I.has(H.element),W=me(me({},S),{},{self:H});if(h==null||(P=h.onDropTargetChange)===null||P===void 0||P.call(h,W),!U){var Y;h==null||(Y=h.onDragLeave)===null||Y===void 0||Y.call(h,W)}}}catch(w){E.e(w)}finally{E.f()}var K=kt(S.location.current.dropTargets),te;try{for(K.s();!(te=K.n()).done;){var A,k,x=te.value;if(!D.has(x.element)){var L=me(me({},S),{},{self:x}),v=n.get(x.element);v==null||(A=v.onDropTargetChange)===null||A===void 0||A.call(v,L),v==null||(k=v.onDragEnter)===null||k===void 0||k.call(v,L)}}}catch(w){K.e(w)}finally{K.f()}}};function f(g){c[g.eventName](g)}function y(g){var m=g.source,S=g.target,I=g.input,D=g.current,E=l({source:m,target:S,input:I});if(E.length>=D.length)return E;for(var F=Ct(D),P=Ct(E),H=[],h=0;h<F.length;h++){var U,W=F[h],Y=P[h];if(Y!=null){H.push(Y);continue}var K=H[h-1],te=F[h-1];if(K?.element!==te?.element)break;var A=n.get(W.element);if(!A)break;var k={input:I,source:m,element:A.element};if(A.canDrop&&!A.canDrop(k)||!((U=A.getIsSticky)!==null&&U!==void 0&&U.call(A,k)))break;H.push(me(me({},W),{},{isActiveDueToStickiness:!0}))}return Ct(H)}return{dropTargetForConsumers:o,getIsOver:y,dispatchEvent:f}}function Oi(t,e){var r=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=Ai(t))||e){r&&(t=r);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(d){throw d},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var s,i=!0,o=!1;return{s:function(){r=r.call(t)},n:function(){var d=r.next();return i=d.done,d},e:function(d){o=!0,s=d},f:function(){try{i||r.return==null||r.return()}finally{if(o)throw s}}}}function Ai(t,e){if(t){if(typeof t=="string")return Mr(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Mr(t,e):void 0}}function Mr(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Rr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Ei(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Rr(Object(r),!0).forEach(function(n){Je(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Rr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Ii(){var t=new Set,e=null;function r(s){e&&(!s.canMonitor||s.canMonitor(e.canMonitorArgs))&&e.active.add(s)}function n(s){var i=Ei({},s);t.add(i),r(i);function o(){t.delete(i),e&&e.active.delete(i)}return Ne(o)}function a(s){var i=s.eventName,o=s.payload;if(i==="onGenerateDragPreview"){e={canMonitorArgs:{initial:o.location.initial,source:o.source},active:new Set};var l=Oi(t),d;try{for(l.s();!(d=l.n()).done;){var c=d.value;r(c)}}catch(I){l.e(I)}finally{l.f()}}if(e){for(var f=Array.from(e.active),y=0,g=f;y<g.length;y++){var m=g[y];if(e.active.has(m)){var S;(S=m[i])===null||S===void 0||S.call(m,o)}}i==="onDrop"&&(e.active.clear(),e=null)}}return{dispatchEvent:a,monitorForConsumers:n}}function Li(t){var e=t.typeKey,r=t.mount,n=t.dispatchEventToSource,a=t.onPostDispatch,s=t.defaultDropEffect,i=Ii(),o=Ti({typeKey:e,defaultDropEffect:s});function l(f){n?.(f),o.dispatchEvent(f),i.dispatchEvent(f),a?.(f)}function d(f){var y=f.event,g=f.dragType;Er.start({event:y,dragType:g,getDropTargetsOver:o.getIsOver,dispatchEvent:l})}function c(){function f(){var y={canStart:Er.canStart,start:d};return r(y)}return Si({typeKey:e,mount:f})}return{registerUsage:c,dropTarget:o.dropTargetForConsumers,monitor:i.monitorForConsumers}}var Mi=Ne(function(){return navigator.userAgent.toLocaleLowerCase().includes("android")}),Ri="pdnd:android-fallback",Pr="text/plain",Pi="text/uri-list",Di="application/vnd.pdnd",ft=new WeakMap;function zi(t){return ft.set(t.element,t),function(){ft.delete(t.element)}}var Dr=ci(),Yt=Li({typeKey:"element",defaultDropEffect:"move",mount:function(e){return pt(Dr.bindEvents(),De.bind(document,{type:"dragstart",listener:function(n){var a,s,i,o,l,d;if(e.canStart(n)&&!n.defaultPrevented&&n.dataTransfer){var c=n.target;if(!(c instanceof HTMLElement))return null;var f=ft.get(c);if(!f)return null;var y=We(n),g={element:f.element,dragHandle:(a=f.dragHandle)!==null&&a!==void 0?a:null,input:y};if(f.canDrag&&!f.canDrag(g))return n.preventDefault(),null;if(f.dragHandle){var m=bn({x:y.clientX,y:y.clientY});if(!f.dragHandle.contains(m))return n.preventDefault(),null}var S=(s=(i=f.getInitialDataForExternal)===null||i===void 0?void 0:i.call(f,g))!==null&&s!==void 0?s:null;if(S)for(var I=0,D=Object.entries(S);I<D.length;I++){var E=Kt(D[I],2),F=E[0],P=E[1];n.dataTransfer.setData(F,P??"")}Mi()&&!n.dataTransfer.types.includes(Pr)&&!n.dataTransfer.types.includes(Pi)&&n.dataTransfer.setData(Pr,Ri),n.dataTransfer.setData(Di,"");var H={element:f.element,dragHandle:(o=f.dragHandle)!==null&&o!==void 0?o:null,data:(l=(d=f.getInitialData)===null||d===void 0?void 0:d.call(f,g))!==null&&l!==void 0?l:{}},h={type:"element",payload:H,startedFrom:"internal"};e.start({event:n,dragType:h})}}}))},dispatchEventToSource:function(e){var r,n,a=e.eventName,s=e.payload;(r=ft.get(s.source.element))===null||r===void 0||(n=r[a])===null||n===void 0||n.call(r,s)},onPostDispatch:Dr.getOnPostDispatch()}),Zi=Yt.dropTarget,Cn=Yt.monitor;function Vi(t){var e=pt(Yt.registerUsage(),zi(t),kn(t.element,{attribute:"draggable",value:"true"}));return Ne(e)}var $i=Ne(function(){return Qe()&&"ontouchend"in document});function Fi(t){return function(e){var r=e.container;$i()||Object.assign(r.style,{borderInlineStart:"".concat(t.x," solid transparent"),borderTop:"".concat(t.y," solid transparent")});var n=window.getComputedStyle(r);if(n.direction==="rtl"){var a=r.getBoundingClientRect();return{x:a.width,y:0}}return{x:0,y:0}}}function Bi(){return{x:0,y:0}}function Hi(t){var e=t.render,r=t.nativeSetDragImage,n=t.getOffset,a=n===void 0?Bi:n,s=document.createElement("div");Object.assign(s.style,{position:"fixed",top:0,left:0,zIndex:xn,pointerEvents:"none"}),document.body.append(s);var i=e({container:s});queueMicrotask(function(){var d=a({container:s});if(Qe()){var c=s.getBoundingClientRect();if(c.width===0)return;s.style.left="-".concat(c.width-1e-4,"px")}r?.(s,d.x,d.y)});function o(){l(),i?.(),document.body.removeChild(s)}var l=Cn({onDragStart:o,onDrop:o})}function Ui(t){var e=t.list,r=t.startIndex,n=t.finishIndex;if(r===-1||n===-1)return Array.from(e);var a=Array.from(e),s=a.splice(r,1),i=Kt(s,1),o=i[0];return a.splice(n,0,o),a}const jn=_.createContext(null);function Wi(){const t=_.useContext(jn);return st(t!==null),t}const Sn=Symbol("item");function Gi({item:t,index:e,instanceId:r}){return{[Sn]:!0,item:t,index:e,instanceId:r}}function it(t){return t[Sn]===!0}const zr={type:"idle"},Zr={type:"dragging"};function qi({item:t,index:e,column:r}){const{registerItem:n,instanceId:a}=Wi(),s=_.useRef(null),[i,o]=_.useState(null),l=_.useRef(null),[d,c]=_.useState(zr);return _.useEffect(()=>{const f=s.current,y=l.current;st(f),st(y);const g=Gi({item:t,index:e,instanceId:a});return pt(n({itemId:t.id,element:f}),Vi({element:y,getInitialData:()=>g,onGenerateDragPreview({nativeSetDragImage:m}){Hi({nativeSetDragImage:m,getOffset:Fi({x:"10px",y:"10px"}),render({container:S}){return c({type:"preview",container:S}),()=>c(Zr)}})},onDragStart(){c(Zr)},onDrop(){c(zr)}}),Zi({element:f,canDrop({source:m}){return it(m.data)&&m.data.instanceId===a},getData({input:m}){return Rs(g,{element:f,input:m,allowedEdges:["top","bottom"]})},onDrag({self:m,source:S}){if(S.element===f){o(null);return}const D=fn(m.data),E=S.data.index;st(typeof E=="number");const F=e===E-1,P=e===E+1;if(F&&D==="bottom"||P&&D==="top"){o(null);return}o(D)},onDragLeave(){o(null)},onDrop(){o(null)}}))},[a,t,e,n]),u.jsxs(_.Fragment,{children:[u.jsxs("div",{ref:s,className:"relative border-b border-transparent",children:[u.jsxs("div",{className:X("relative flex items-center justify-between gap-2",d.type==="dragging"&&"opacity-50"),children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(Vr,{checked:r?.getIsVisible(),onCheckedChange:()=>r?.toggleVisibility()}),u.jsx("span",{children:t.label})]}),u.jsx(ce,{"aria-hidden":"true",tabIndex:-1,variant:"ghost",className:"-mr-1 px-0 py-1",ref:l,"aria-label":`Reorder ${t.label}`,children:u.jsx(na,{className:"size-5 text-gray-400 dark:text-gray-600"})})]}),i&&u.jsx(Xs,{edge:i,gap:"1px"})]}),d.type==="preview"&&Rn.createPortal(u.jsx("div",{children:t.label}),d.container)]})}function Ki(){const t=new Map;function e({itemId:n,element:a}){return t.set(n,a),function(){t.delete(n)}}function r(n){return t.get(n)??null}return{register:e,getElement:r}}function Yi({table:t}){const e=t.getAllColumns().map(c=>({id:c.id,label:c.columnDef.meta?.displayName??c.id})),[{items:r,lastCardMoved:n},a]=_.useState({items:e,lastCardMoved:null}),[s]=_.useState(Ki),[i]=_.useState(()=>Symbol("instance-id"));_.useEffect(()=>{t.setColumnOrder(r.map(c=>c.id))},[r]);const o=_.useCallback(({startIndex:c,indexOfTarget:f,closestEdgeOfTarget:y})=>{const g=Ps({startIndex:c,closestEdgeOfTarget:y,indexOfTarget:f,axis:"vertical"});g!==c&&a(m=>{const S=m.items[c];return S?{items:Ui({list:m.items,startIndex:c,finishIndex:g}),lastCardMoved:{item:S,previousIndex:c,currentIndex:g,numberOfItems:m.items.length}}:m})},[]);_.useEffect(()=>Cn({canMonitor({source:c}){return it(c.data)&&c.data.instanceId===i},onDrop({location:c,source:f}){const y=c.current.dropTargets[0];if(!y)return;const g=f.data,m=y.data;if(!it(g)||!it(m))return;const S=r.findIndex(D=>D.id===m.item.id);if(S<0)return;const I=fn(m);o({startIndex:g.index,indexOfTarget:S,closestEdgeOfTarget:I})}}),[i,r,o]),_.useEffect(()=>{if(n===null)return;const{item:c,previousIndex:f,currentIndex:y,numberOfItems:g}=n,m=s.getElement(c.id);m&&Es(m),Vs(`You've moved ${c.label} from position ${f+1} to position ${y+1} of ${g}.`)},[n,s]),_.useEffect(()=>function(){$s()},[]);const l=_.useCallback(()=>r.length,[r.length]),d=_.useMemo(()=>({registerItem:s.register,reorderItem:o,instanceId:i,getListLength:l}),[s.register,o,i,l]);return u.jsx("div",{children:u.jsx("div",{className:"flex justify-center",children:u.jsxs(ln,{children:[u.jsx(cn,{asChild:!0,children:u.jsxs(ce,{variant:"secondary",className:X("ml-auto hidden gap-x-2 px-2 py-1.5 text-sm sm:text-xs lg:flex"),children:[u.jsx(aa,{className:"size-4","aria-hidden":"true"}),"View"]})}),u.jsxs(dn,{align:"end",sideOffset:7,className:"z-50 w-fit space-y-2",children:[u.jsx(jt,{className:"font-medium",children:"Display properties"}),u.jsx(jn.Provider,{value:d,children:u.jsx("div",{className:"flex flex-col",children:r.map((c,f)=>{const y=t.getColumn(c.id);return y?u.jsx("div",{className:X(!y.getCanHide()&&"hidden"),children:u.jsx(qi,{column:y,item:c,index:f})},y.id):null})})})]})]})})})}function Xi({table:t}){const e=t.getState().columnFilters.length>0,[r,n]=q.useState(""),a=Ns(i=>{t.getColumn("owner")?.setFilterValue(i)},300),s=i=>{const o=i.target.value;n(o),a(o)};return u.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2 sm:gap-x-6",children:[u.jsxs("div",{className:"flex w-full flex-col gap-2 sm:w-fit sm:flex-row sm:items-center",children:[t.getColumn("status")?.getIsVisible()&&u.jsx(bt,{column:t.getColumn("status"),title:"Status",options:Cs,type:"select"}),t.getColumn("region")?.getIsVisible()&&u.jsx(bt,{column:t.getColumn("region"),title:"Region",options:js,type:"checkbox"}),t.getColumn("costs")?.getIsVisible()&&u.jsx(bt,{column:t.getColumn("costs"),title:"Costs",type:"number",options:Ss,formatter:i=>An.currency({number:Number(i)})}),t.getColumn("owner")?.getIsVisible()&&u.jsx(nn,{type:"search",placeholder:"Search by owner...",value:r,onChange:s,className:"w-full sm:max-w-[250px] sm:[&>input]:h-[30px]"}),e&&u.jsx(ce,{variant:"ghost",onClick:()=>t.resetColumnFilters(),className:"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500",children:"Clear filters"})]}),u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsxs(ce,{variant:"secondary",className:"hidden gap-x-2 px-2 py-1.5 text-sm sm:text-xs lg:flex",children:[u.jsx(Xn,{className:"size-4 shrink-0","aria-hidden":"true"}),"Export"]}),u.jsx(Yi,{table:t})]})]})}function Ji({table:t,pageSize:e,totalCount:r,onPageSizeChange:n}){const a=[{icon:ea,onClick:()=>t.setPageIndex(0),disabled:!t.getCanPreviousPage(),srText:"First page",mobileView:"hidden sm:block"},{icon:Gn,onClick:()=>t.previousPage(),disabled:!t.getCanPreviousPage(),srText:"Previous page",mobileView:""},{icon:qn,onClick:()=>t.nextPage(),disabled:!t.getCanNextPage(),srText:"Next page",mobileView:""},{icon:ta,onClick:()=>t.setPageIndex(t.getPageCount()-1),disabled:!t.getCanNextPage(),srText:"Last page",mobileView:"hidden sm:block"}],s=r??t.getFilteredRowModel().rows.length,o=t.getState().pagination.pageIndex*e+1,l=Math.min(s,o+e-1),d=[10,25,50,100,"All"],c=f=>{const y=f==="All"?s:parseInt(f,10);t.setPageSize(y),n&&n(y)};return u.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx("span",{className:"text-sm text-gray-500",children:"Items per page:"}),u.jsxs(St,{value:e.toString(),onValueChange:c,children:[u.jsx(Nt,{className:"h-8 w-[80px]",children:u.jsx(Tt,{placeholder:e.toString()})}),u.jsx(Ot,{children:d.map(f=>u.jsx(At,{value:f.toString(),children:f},f))})]})]}),u.jsxs("div",{className:"flex items-center justify-between w-full sm:w-auto",children:[u.jsxs("div",{className:"text-sm tabular-nums text-gray-500",children:[t.getFilteredSelectedRowModel().rows.length," of ",s," row(s) selected."]}),u.jsxs("div",{className:"flex items-center gap-x-6 lg:gap-x-8",children:[u.jsxs("p",{className:"hidden text-sm tabular-nums text-gray-500 sm:block",children:["Showing"," ",u.jsxs("span",{className:"font-medium text-gray-900 dark:text-gray-50",children:[o,"-",l]})," ","of"," ",u.jsx("span",{className:"font-medium text-gray-900 dark:text-gray-50",children:s})]}),u.jsx("div",{className:"flex items-center gap-x-1.5",children:a.map((f,y)=>u.jsxs(ce,{variant:"secondary",className:X(f.mobileView,"p-1.5"),onClick:()=>{f.onClick(),t.resetRowSelection()},disabled:f.disabled,children:[u.jsx("span",{className:"sr-only",children:f.srText}),u.jsx(f.icon,{className:"size-4 shrink-0","aria-hidden":"true"})]},y))})]})]})]})}function uo({columns:t,data:e,totalCount:r,isLoading:n,manualPagination:a=!1,manualSorting:s=!1,pageSize:i=20,onPaginationChange:o,onSortingChange:l,sortingState:d,onSearch:c,searchValue:f="",customFilterbar:y,hideDefaultFilterbar:g=!1,onRefresh:m,title:S,actionButton:I,enableRowSelection:D=!0}){const[E,F]=q.useState({}),[P,H]=q.useState({pageIndex:0,pageSize:i}),[h,U]=q.useState(d??[]),[W,Y]=q.useState(!1);q.useEffect(()=>{d&&U(d)},[d]);const K=q.useCallback(w=>{const T=typeof w=="function"?w(P):w;H(T),o&&o(T)},[o,P]),te=q.useCallback(w=>{const T=typeof w=="function"?w(h):w;U(T),l&&l(T)},[l,h]),A=q.useCallback(w=>{const T={pageIndex:0,pageSize:w};H(T),o&&o(T)},[o]),k=q.useCallback(()=>{m&&!n&&!W&&(Y(!0),m(),setTimeout(()=>{Y(!1)},1e3))},[m,n,W]),x=q.useMemo(()=>D?t:t.filter(w=>w.id!=="select"),[t,D]),L=q.useMemo(()=>x.map(w=>w.enableSorting===!1?w:{...w,header:T=>{const z=T.column,G=z.getIsSorted(),se=typeof w.header=="string"?w.header:w.header?mt(w.header,T):null,Q=u.jsxs(u.Fragment,{children:[se,G&&u.jsx("span",{className:"inline-flex items-center",children:G==="asc"?u.jsx(Kn,{className:"w-3.5 h-3.5"}):u.jsx(Wn,{className:"w-3.5 h-3.5"})})]});return w.enableSorting!==!1?u.jsx("button",{type:"button",onClick:()=>z.toggleSorting(),className:X("inline-flex items-center gap-1 hover:text-primary",G?"text-primary":""),children:Q}):Q}}),[x]),v=Pn({data:e,columns:L,state:{rowSelection:E,pagination:P,sorting:h},pageCount:r?Math.ceil(r/P.pageSize):-1,enableRowSelection:D,enableSorting:!0,manualSorting:s,getFilteredRowModel:$n(),getPaginationRowModel:Vn(),getSortedRowModel:Zn(),onRowSelectionChange:F,onSortingChange:te,getCoreRowModel:zn(),onPaginationChange:K,manualPagination:a});return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"space-y-3",children:[S&&u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsx("h2",{className:"text-xl font-semibold",children:S}),I?.content]}),u.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2",children:[u.jsx("div",{className:"flex-1 w-full",children:y?u.jsx(y,{table:v,onSearch:c,searchValue:f}):u.jsxs(u.Fragment,{children:[c&&u.jsx(Dn,{onUpdate:c,value:f}),!g&&u.jsx(Xi,{table:v})]})}),u.jsxs("div",{className:"flex items-center gap-2",children:[!S&&I&&u.jsxs(ce,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-3 py-2",onClick:I.onClick,children:[I.content??u.jsx($r,{className:"h-4 w-4"}),u.jsx("span",{children:I.label})]}),m&&u.jsxs(ce,{variant:"secondary",size:"sm",className:"flex items-center gap-1",onClick:k,disabled:n??W,children:[u.jsx(Jn,{className:X("h-4 w-4",W&&"animate-spin")}),u.jsx("span",{className:"hidden sm:inline",children:"Refresh"})]})]})]}),u.jsx("div",{className:"relative overflow-hidden overflow-x-auto",children:u.jsxs(Fn,{children:[u.jsx(Bn,{children:v.getHeaderGroups().map(w=>u.jsx(et,{className:"border-y border-gray-200 dark:border-gray-800",children:w.headers.map(T=>u.jsx(Un,{className:X("whitespace-nowrap py-1 text-sm sm:text-xs",T.column.columnDef.meta?.className),children:T.isPlaceholder?null:mt(T.column.columnDef.header,T.getContext())},T.id))},w.id))}),u.jsx(Hn,{children:n?u.jsx(et,{children:u.jsx(ht,{colSpan:x.length,className:"h-24 text-center",children:"Loading..."})}):v.getRowModel().rows?.length?v.getRowModel().rows.map(w=>u.jsx(et,{onClick:()=>D&&w.toggleSelected(!w.getIsSelected()),className:X("group",D?"select-none hover:bg-gray-50 dark:hover:bg-gray-900":""),children:w.getVisibleCells().map((T,z)=>u.jsxs(ht,{className:X(w.getIsSelected()?"bg-gray-50 dark:bg-gray-900":"","relative whitespace-nowrap py-1 text-gray-600 first:w-10 dark:text-gray-400",T.column.columnDef.meta?.className),children:[z===0&&w.getIsSelected()&&u.jsx("div",{className:"absolute inset-y-0 left-0 w-0.5 bg-indigo-600 dark:bg-indigo-500"}),mt(T.column.columnDef.cell,T.getContext())]},T.id))},w.id)):u.jsx(et,{children:u.jsx(ht,{colSpan:x.length,className:"h-24 text-center",children:"No results."})})})]})}),u.jsx(Ji,{table:v,pageSize:P.pageSize,totalCount:r,onPageSizeChange:A})]})})}function fo({column:t,title:e,className:r}){return t.getCanSort()?u.jsxs("div",{onClick:t.getToggleSortingHandler(),className:X(t.columnDef.enableSorting===!0?"-mx-2 inline-flex cursor-pointer select-none items-center gap-2 rounded-md px-2 py-1 hover:bg-gray-50 dark:hover:bg-gray-900":""),children:[u.jsx("span",{children:e}),t.getCanSort()?u.jsxs("div",{className:"-space-y-2",children:[u.jsx(Yn,{className:X("size-3.5 text-gray-900 dark:text-gray-50",t.getIsSorted()==="desc"?"opacity-30":""),"aria-hidden":"true"}),u.jsx(Fr,{className:X("size-3.5 text-gray-900 dark:text-gray-50",t.getIsSorted()==="asc"?"opacity-30":""),"aria-hidden":"true"})]}):null]}):u.jsx("div",{className:X(r),children:e})}export{fo as D,ln as P,co as R,Yi as V,oo as _,lo as a,uo as b,cn as c,Jn as d,dn as e,ao as f,io as l,$r as p,so as t,Xn as w};
//# sourceMappingURL=DataTableColumnHeader-CSMG3Uqi.js.map
