import { deleteApiOpeniddictScopesById, type OpenIddictScopeDto } from '@/client'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useToast } from "@/lib/useToast"
import { useEffect, useState } from 'react'

type DeleteUserProps = {
  data: { dataId: string; dataEdit: OpenIddictScopeDto }
  onDismiss: () => void
}
export const Delete = ({ data: { dataId, dataEdit }, onDismiss }: DeleteUserProps) => {
  const { toast } = useToast()
  const [open, setOpen] = useState<boolean>(false)
  const onYesEvent = async () => {
    try {
      await deleteApiOpeniddictScopesById({
        path: { id: dataId },
      })
      toast({
        title: 'Success',
        description: `User "${dataEdit.name}" has been deleted successfully.`,
      })
      onDismiss()
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast({
          title: 'Failed',
          description: `There was a problem when deleting the user "${dataEdit.name}". Kindly try again.`,
          variant: 'destructive',
        })
      }
    }
  }

  useEffect(() => {
    setOpen(true)
  }, [])

  return (
    <AlertDialog open={open}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete your this user &quot;
            {dataEdit.name}&quot;
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
