{"version": 3, "file": "resource-IoXJ-ltL.js", "sources": ["../../../../../frontend/src/components/app/clients/resources/Delete.tsx", "../../../../../frontend/src/lib/hooks/useOpeniddictResourcesWithFilters.ts", "../../../../../frontend/src/components/app/clients/resources/Add.tsx", "../../../../../frontend/src/components/app/clients/resources/Actions.tsx", "../../../../../frontend/src/components/app/clients/resources/filterFunctions.ts", "../../../../../frontend/src/components/app/clients/resources/Columns.tsx", "../../../../../frontend/src/components/app/clients/resources/Edit.tsx", "../../../../../frontend/src/components/app/clients/resources/List.tsx", "../../../../../frontend/src/pages/client/resource.tsx"], "sourcesContent": ["import { deleteApiOpeniddictResourcesById } from '@/client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { handleApiError } from '@/lib/handleApiError'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { useEffect, useState } from 'react'\r\n\r\ntype DeleteUserProps = {\r\n  user: { userId: string; username: string }\r\n  onDismiss: () => void\r\n}\r\nexport const Delete = ({ user: { userId, username }, onDismiss }: DeleteUserProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiOpeniddictResourcesById({\r\n        path: { id: userId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `User \"${username}\" has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      // Use the global helper to handle API errors\r\n      const error = handleApiError(err);\r\n      toast({\r\n        title: error.title,\r\n        description: error.description,\r\n        variant: 'error',\r\n      })\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your this user &quot;\r\n            {username}&quot;\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "import {\r\n  type PagedResultDtoOfOpenIddictApplicationDto,\r\n  postApiOpeniddictResourcesList,\r\n} from '@/client'\r\nimport { extractApiError } from '@/lib/query-utils'\r\nimport { generateExtendedQueryParameters } from '@/lib/query-utils-extended'\r\nimport { toast } from '@/lib/useToast'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\nimport type { FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\n\r\nexport const useOpeniddictResourcesWithFilters = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filterConditions: FilterCondition[] = [],\r\n  sorting?: string\r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetOpeniddictResources, pageIndex, pageSize, JSON.stringify(filterConditions), sorting],\r\n    queryFn: async () => {\r\n      try {\r\n        // Generate query parameters using the extended utility function\r\n        const body = generateExtendedQueryParameters({\r\n          pageIndex,\r\n          pageSize,\r\n          sorting,\r\n          filterConditions,\r\n        })\r\n\r\n        const response = await postApiOpeniddictResourcesList({\r\n          body,\r\n        })\r\n\r\n        // Ensure we return a valid data structure even if the API response is unexpected\r\n        return response.data?.data as PagedResultDtoOfOpenIddictApplicationDto\r\n      } catch (error) {\r\n        // Use the error extraction utility\r\n        const { title, description } = extractApiError(error, 'Error loading resources')\r\n\r\n        // Show toast notification\r\n        toast({\r\n          title,\r\n          description,\r\n          variant: 'destructive',\r\n        })\r\n\r\n        // Return empty data to prevent UI crashes\r\n        return { items: [], totalCount: 0 }\r\n      }\r\n    },\r\n    retry: false, // Don't retry on error\r\n  })\r\n}\r\n", "'use client'\r\nimport { type CreateUpdateOpenIddictResourceDto, postApiOpeniddictResources, type RemoteServiceErrorResponse } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  <PERSON><PERSON>Footer,\r\n  <PERSON><PERSON>Header,\r\n  <PERSON>alogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Textarea } from '@/components/ui/textarea'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { RiAddLine } from '@remixicon/react'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\n\r\nexport type AddClientProps = {\r\n  children?: React.ReactNode\r\n}\r\n\r\nexport const Add = ({ children }: AddClientProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, reset } = useForm<CreateUpdateOpenIddictResourceDto>()\r\n\r\n  const resetForm = () => {\r\n    reset({\r\n      name: '',\r\n      displayName: '',\r\n      description: ''\r\n    })\r\n  }\r\n\r\n  const createDataMutation = useMutation({\r\n    mutationFn: async (dataMutation: CreateUpdateOpenIddictResourceDto) =>\r\n      postApiOpeniddictResources({\r\n        body: dataMutation\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Resource Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictResources] })\r\n      resetForm()\r\n      setOpen(false)\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateOpenIddictResourceDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: CreateUpdateOpenIddictResourceDto = {\r\n      ...formData,\r\n    }\r\n\r\n    // Explicitly mark the promise as handled\r\n    void createDataMutation.mutate(userData)\r\n  }\r\n\r\n  const handleOpenChange = (newOpen: boolean) => {\r\n    if (newOpen) {\r\n      resetForm()\r\n    }\r\n    setOpen(newOpen)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={handleOpenChange}>\r\n        <DialogTrigger asChild>{children}</DialogTrigger>\r\n        <section className=\"flex items-center justify-between pb-5\">\r\n          {can('AbpIdentity.Users.Create') && (\r\n            <Button size='sm' className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\" onClick={() => handleOpenChange(true)}>\r\n              <RiAddLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"hidden truncate sm:inline\">Create New Resources</span>\r\n            </Button>\r\n          )}\r\n        </section>\r\n        <DialogContent size=\"xl\">\r\n          <DialogHeader>\r\n            <DialogTitle>Create a New Resource</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The unique identifier for this resource. Used in requests\"\r\n                >\r\n                  <Input required {...register('name')} placeholder=\"Name\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Display Name\"\r\n                  description=\"The display name for this resource\"\r\n                >\r\n                  <Input required {...register('displayName')} placeholder=\"Display Name\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Description\"\r\n                  description=\"The description for this resource\"\r\n                >\r\n                  <Textarea required {...register('description')} placeholder=\"Description\" />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createDataMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createDataMutation.isPending}>\r\n                {createDataMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type OpenIddictResourceDto } from '@/client'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { RiMoreLine, RiPencilLine, RiShieldKeyholeLine } from '@remixicon/react'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\n\r\ntype UserActionProps = {\r\n  dataId: string\r\n  dataEdit: OpenIddictResourceDto\r\n  onAction: (dataId: string, dataEdit: OpenIddictResourceDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n  variant?: 'dropdown' | 'buttons'\r\n}\r\n\r\nexport const Actions = ({ dataId, dataEdit, onAction, variant = 'dropdown' }: UserActionProps) => {\r\n  const { can } = useGrantedPolicies()\r\n\r\n  // For dropdown menu style (first image)\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <div className=\"flex justify-end\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 w-8 p-0\"\r\n            >\r\n              <RiMoreLine className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Open menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"w-[160px]\">\r\n            {can('IdentityServer.OpenIddictResources.Edit') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(dataId, dataEdit, 'edit')}\r\n              >\r\n                Edit\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('IdentityServer.OpenIddictResources.Delete') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm text-red-500\"\r\n                onClick={() => onAction(dataId, dataEdit, 'delete')}\r\n              >\r\n                Delete\r\n              </DropdownMenuItem>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // For button group style (second image)\r\n  return (\r\n    <div className=\"flex items-center justify-end gap-1\">\r\n      {can('AbpIdentity.Users.ManagePermissions') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(dataId, dataEdit, 'permission')}\r\n        >\r\n          <RiShieldKeyholeLine className=\"h-4 w-4\" />\r\n          <span>Permission</span>\r\n        </Button>\r\n      )}\r\n      {can('AbpIdentity.Users.Update') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(dataId, dataEdit, 'edit')}\r\n        >\r\n          <RiPencilLine className=\"h-4 w-4\" />\r\n          <span>Edit</span>\r\n        </Button>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n", "import type { FilterOperator } from \"@/client\"\r\nimport type { Row } from \"@tanstack/react-table\"\r\n\r\n// Define the filter value type\r\nexport interface FilterValue {\r\n  operator: FilterOperator;\r\n  value: string | null;\r\n}\r\n\r\n// Custom filter function for handling our filter operators\r\nexport function customFilterFunction<TData>(\r\n  row: Row<TData>,\r\n  columnId: string,\r\n  filterValue: unknown\r\n): boolean {\r\n  // Handle case when filterValue is not in the expected format\r\n  if (!filterValue || typeof filterValue !== 'object' || !('operator' in filterValue)) {\r\n    return true\r\n  }\r\n\r\n  // Type guard to ensure filterValue is of type FilterValue\r\n  const isFilterValue = (value: unknown): value is FilterValue => {\r\n    return (\r\n      typeof value === 'object' &&\r\n      value !== null &&\r\n      'operator' in value &&\r\n      'value' in value\r\n    );\r\n  };\r\n\r\n  // If not a valid FilterValue, return true (show all rows)\r\n  if (!isFilterValue(filterValue)) {\r\n    return true;\r\n  }\r\n\r\n  // Get the value from the row\r\n  const value = row.getValue(columnId)\r\n\r\n  // Extract operator and filter value\r\n  const { operator, value: filterVal } = filterValue\r\n\r\n  // Handle null or undefined values\r\n  if (value === undefined || value === null) {\r\n    if (operator === \"IsNull\") return true\r\n    if (operator === \"IsNotNull\") return false\r\n    if (operator === \"IsEmpty\") return true\r\n    if (operator === \"IsNotEmpty\") return false\r\n    return false\r\n  }\r\n\r\n  // Convert value to string for string operations\r\n  let stringValue = \"\";\r\n  if (typeof value === 'object' && value !== null) {\r\n    // Safely stringify objects with proper handling\r\n    try {\r\n      stringValue = JSON.stringify(value).toLowerCase();\r\n    } catch {\r\n      // If JSON stringify fails, use a more descriptive representation\r\n      stringValue = `[${Object.prototype.toString.call(value)}]`;\r\n    }\r\n  } else if (typeof value === 'number' || typeof value === 'boolean' || typeof value === 'string') {\r\n    // For primitive values, use standard string conversion\r\n    stringValue = String(value).toLowerCase();\r\n  } else {\r\n    // For other types, use a safe representation\r\n    stringValue = `[${typeof value}]`;\r\n  }\r\n\r\n  const stringFilterVal = filterVal ? String(filterVal).toLowerCase() : \"\"\r\n\r\n  // Handle different operators\r\n  switch (operator) {\r\n    case \"Equals\":\r\n      return stringValue === stringFilterVal\r\n    case \"NotEquals\":\r\n      return stringValue !== stringFilterVal\r\n    case \"Contains\":\r\n      return stringValue.includes(stringFilterVal)\r\n    case \"StartsWith\":\r\n      return stringValue.startsWith(stringFilterVal)\r\n    case \"EndsWith\":\r\n      return stringValue.endsWith(stringFilterVal)\r\n    case \"GreaterThan\":\r\n      return Number(value) > Number(filterVal)\r\n    case \"GreaterThanOrEqual\":\r\n      return Number(value) >= Number(filterVal)\r\n    case \"LessThan\":\r\n      return Number(value) < Number(filterVal)\r\n    case \"LessThanOrEqual\":\r\n      return Number(value) <= Number(filterVal)\r\n    case \"IsEmpty\":\r\n      return stringValue === \"\"\r\n    case \"IsNotEmpty\":\r\n      return stringValue !== \"\"\r\n    case \"IsNull\":\r\n      return value === null || value === undefined\r\n    case \"IsNotNull\":\r\n      return value !== null && value !== undefined\r\n    default:\r\n      return true\r\n  }\r\n}\r\n", "'use client'\r\n\r\nimport { type OpenIddictResourceDto } from '@/client'\r\nimport { type ColumnDef } from '@tanstack/react-table'\r\nimport { Actions } from './Actions'\r\nimport { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'\r\nimport { customFilterFunction } from './filterFunctions'\r\n\r\n// Type for the callback function to handle user actions\r\ntype UserActionCallback = (dataId: string, dataEdit: OpenIddictResourceDto, dialogType: 'edit' | 'permission' | 'delete') => void\r\n\r\n// Function to create user columns with the action callback\r\nexport const getColumns = (\r\n  handleUserAction: UserActionCallback\r\n): ColumnDef<OpenIddictResourceDto>[] => [\r\n    {\r\n      accessorKey: 'name',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Resource Name\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Name\",\r\n      },\r\n      filterFn: customFilterFunction,\r\n    },\r\n    {\r\n      accessorKey: 'displayName',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Display Name\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Display Name\",\r\n      },\r\n      filterFn: customFilterFunction,\r\n    },\r\n    {\r\n      accessorKey: 'description',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Description\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Description\",\r\n      },\r\n      filterFn: customFilterFunction,\r\n    },\r\n    {\r\n      id: 'actions',\r\n      header: 'Actions',\r\n      enableHiding: true,\r\n      cell: (info) => (\r\n        <Actions\r\n          dataId={info.row.original.id!}\r\n          dataEdit={info.row.original}\r\n          onAction={handleUserAction}\r\n          variant=\"dropdown\" // Use \"dropdown\" for the first image style or \"buttons\" for the second image style\r\n        />\r\n      ),\r\n      meta: {\r\n        className: \"text-right\",\r\n        displayName: \"Actions\",\r\n      },\r\n    },\r\n  ]\r\n", "'use client'\r\nimport { type CreateUpdateOpenIddictResourceDto, type OpenIddictResourceDto, putApiOpeniddictResourcesById, type RemoteServiceErrorResponse } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Textarea } from '@/components/ui/textarea'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\n\r\ntype EditDataProps = {\r\n  dataEdit: OpenIddictResourceDto\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const Edit = ({ dataEdit, dataId, onDismiss }: EditDataProps) => {\r\n  const [open, setOpen] = useState(true)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, reset } = useForm<CreateUpdateOpenIddictResourceDto>()\r\n\r\n  // useEffect(() => {\r\n  //   if (dataEdit) {\r\n  //     console.log(\"dataEdit2\", dataEdit)\r\n  //     reset({\r\n  //       name: dataEdit.name || '',\r\n  //       displayName: dataEdit.displayName || '',\r\n  //       description: dataEdit.description || '',\r\n  //     })\r\n  //   }\r\n  // }, [dataEdit, reset])\r\n\r\n  const resetForm = () => {\r\n    reset({\r\n      name: '',\r\n      displayName: '',\r\n      description: ''\r\n    })\r\n  }\r\n\r\n  const createDataMutation = useMutation({\r\n    mutationFn: async (dataMutation: CreateUpdateOpenIddictResourceDto) =>\r\n      putApiOpeniddictResourcesById({\r\n        path: { id: dataId || '' },\r\n        body: dataMutation\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Resource Updated Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictResources] })\r\n      resetForm()\r\n      setOpen(false)\r\n      onDismiss()\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: CreateUpdateOpenIddictResourceDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: CreateUpdateOpenIddictResourceDto = {\r\n      ...formData,\r\n\r\n    }\r\n\r\n    createDataMutation.mutate(userData)\r\n  }\r\n\r\n  const handleOpenChange = (newOpen: boolean) => {\r\n    if (!newOpen) {\r\n      onDismiss()\r\n    }\r\n    setOpen(newOpen)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={handleOpenChange}>\r\n        <DialogContent size=\"xl\">\r\n          <DialogHeader>\r\n            <DialogTitle>Edit Resource</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The unique identifier for this resource. Used in requests\"\r\n                >\r\n                  <Input required {...register('name')} defaultValue={dataEdit.name ?? ''} placeholder=\"Name\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Display Name\"\r\n                  description=\"The display name for this resource\"\r\n                >\r\n                  <Input required {...register('displayName')} defaultValue={dataEdit.displayName ?? ''} placeholder=\"Display Name\" />\r\n                </FormField>\r\n                <FormField\r\n                  label=\"Description\"\r\n                  description=\"The description for this resource\"\r\n                >\r\n                  <Textarea required {...register('description')} defaultValue={dataEdit.description ?? ''} placeholder=\"Description\" />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                  onDismiss()\r\n                }}\r\n                disabled={createDataMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createDataMutation.isPending}>\r\n                {createDataMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "'use client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useState } from 'react'\r\n\r\nimport { type OpenIddictResourceDto } from '@/client'\r\nimport { TableSkeleton } from '@/components/ui/TableSkeleton'\r\nimport { type PaginationState } from '@tanstack/react-table'\r\n\r\nimport { useToast } from '@/lib/useToast'\r\nimport { Delete } from './Delete'\r\n\r\nimport { DataTable } from '@/components/data-table/DataTable'\r\nimport { NotionFilter } from '@/components/data-table/NotionFilter'\r\nimport { useOpeniddictResourcesWithFilters } from '@/lib/hooks/useOpeniddictResourcesWithFilters'\r\nimport { type FilterCondition } from '@/lib/interfaces/IFilterCondition'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Add } from './Add'\r\nimport { getColumns } from './Columns'\r\nimport { Edit } from './Edit'\r\n\r\nexport const CLientResourceList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  const [searchStr, setSearchStr] = useState<string>('')\r\n  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])\r\n  const [userActionDialog, setUserActionDialog] = useState<{\r\n    dataId: string\r\n    dataEdit: OpenIddictResourceDto\r\n    dialogType?: 'edit' | 'permission' | 'delete'\r\n  } | null>(null)\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n\r\n  const { isLoading, data } = useOpeniddictResourcesWithFilters(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    filterConditions\r\n  )\r\n\r\n  // Handler for user actions (edit, permission, delete)\r\n  const handleUserAction = (dataId: string, dataEdit: OpenIddictResourceDto, dialogType: 'edit' | 'permission' | 'delete') => {\r\n    setUserActionDialog({\r\n      dataId,\r\n      dataEdit,\r\n      dialogType,\r\n    })\r\n  }\r\n\r\n  // Get columns with the action handler\r\n  const columns = getColumns(handleUserAction)\r\n\r\n  const handleSearch = (value: string) => {\r\n    // Always update the search string for UI consistency\r\n    setSearchStr(value)\r\n\r\n    // Create a search filter condition if there's a search value\r\n    // First, remove any existing name filter\r\n    const existingFilters = filterConditions.filter(fc => fc.fieldName !== 'name')\r\n    const newFilterConditions = [...existingFilters]\r\n\r\n    // Only add the search filter if there's a value\r\n    if (value) {\r\n      newFilterConditions.push({\r\n        fieldName: 'name',\r\n        operator: 'Contains',\r\n        value: value\r\n      })\r\n    }\r\n\r\n    // Only update state if filters have changed\r\n    const currentFiltersStr = JSON.stringify(filterConditions)\r\n    const newFiltersStr = JSON.stringify(newFilterConditions)\r\n\r\n    if (currentFiltersStr !== newFiltersStr) {\r\n      setFilterConditions(newFilterConditions)\r\n      setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search\r\n    }\r\n  }\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination)\r\n  }\r\n\r\n  // Handler for refreshing the data\r\n  const handleRefresh = () => {\r\n    // Invalidate the query to fetch fresh data\r\n    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictResources] })\r\n\r\n    // Show toast notification after a short delay to match the animation\r\n    setTimeout(() => {\r\n      toast({\r\n        title: \"Data refreshed\",\r\n        description: \"The client list has been refreshed.\",\r\n        variant: \"success\",\r\n      })\r\n    }, 800)\r\n  }\r\n\r\n  if (isLoading) return (\r\n    <TableSkeleton\r\n      rowCount={pagination.pageSize}\r\n      columnCount={4}\r\n      hasTitle={true}\r\n      hasSearch={true}\r\n      hasFilters={true}\r\n      hasPagination={true}\r\n      hasActions={true}\r\n    />\r\n  )\r\n\r\n  // Ensure we have valid data to render\r\n  const items = data?.items ?? [];\r\n  const totalCount = data?.totalCount ?? 0;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4\">\r\n        <DataTable\r\n          title=\"Resources Management\"\r\n          columns={columns}\r\n          data={items}\r\n          totalCount={totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={handlePaginationChange}\r\n          onSearch={handleSearch}\r\n          searchValue={searchStr}\r\n          customFilterbar={(props) => (\r\n            <NotionFilter\r\n              {...props}\r\n              activeFilters={filterConditions}\r\n              onServerFilter={(conditions) => {\r\n                // Only update if the conditions have actually changed\r\n                const currentStr = JSON.stringify(filterConditions);\r\n                const newStr = JSON.stringify(conditions);\r\n\r\n                if (currentStr !== newStr) {\r\n                  setFilterConditions(conditions)\r\n                  setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on filter change\r\n                }\r\n              }}\r\n            />\r\n          )}\r\n          hideDefaultFilterbar={true}\r\n          onRefresh={handleRefresh}\r\n          enableRowSelection={false}\r\n          actionButton={{\r\n            // label: \"Create New User\",\r\n            onClick: () => { /* Required but not used */ },\r\n            content: <Add />\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {userActionDialog && userActionDialog.dialogType === 'edit' && (\r\n        <Edit\r\n          dataId={userActionDialog.dataId}\r\n          dataEdit={userActionDialog.dataEdit}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictResources] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'permission' && (\r\n        <Edit\r\n          dataId={userActionDialog.dataId}\r\n          dataEdit={userActionDialog.dataEdit}\r\n          onDismiss={() => setUserActionDialog(null)}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'delete' && (\r\n        <Delete\r\n          user={{\r\n            username: userActionDialog.dataEdit.name!,\r\n            userId: userActionDialog.dataId,\r\n          }}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictApplications] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { CLientResourceList } from '@/components/app/clients/resources/List';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Client Resources\" />\r\n\r\n      <CLientResourceList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["Delete", "userId", "username", "on<PERSON><PERSON><PERSON>", "toast", "useToast", "open", "<PERSON><PERSON><PERSON>", "useState", "onYesEvent", "deleteApiOpeniddictResourcesById", "err", "error", "handleApiError", "useEffect", "jsx", "AlertDialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "useOpeniddictResourcesWithFilters", "pageIndex", "pageSize", "filterConditions", "sorting", "useQuery", "QueryNames", "body", "generateExtendedQueryParameters", "postApiOpeniddictResourcesList", "title", "description", "extractApiError", "Add", "children", "can", "useGrantedPolicies", "queryClient", "useQueryClient", "handleSubmit", "register", "reset", "useForm", "resetForm", "createDataMutation", "useMutation", "dataMutation", "postApiOpeniddictResources", "onSubmit", "formData", "userData", "handleOpenChange", "newOpen", "Toaster", "Dialog", "DialogTrigger", "<PERSON><PERSON>", "RiAddLine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "FormSection", "FormField", "Input", "Textarea", "<PERSON><PERSON><PERSON><PERSON>er", "e", "Actions", "dataId", "dataEdit", "onAction", "variant", "DropdownMenu", "DropdownMenuTrigger", "RiMoreLine", "DropdownMenuContent", "DropdownMenuItem", "RiShieldKeyholeLine", "RiPencilLine", "customFilterFunction", "row", "columnId", "filterValue", "value", "operator", "filterVal", "stringValue", "stringFilterVal", "getColumns", "handleUserAction", "column", "DataTableColumnHeader", "info", "Edit", "putApiOpeniddictResourcesById", "CLientResourceList", "searchStr", "setSearchStr", "setFilterConditions", "userActionDialog", "setUserActionDialog", "pagination", "setPagination", "isLoading", "data", "columns", "dialogType", "handleSearch", "newFilterConditions", "fc", "currentFiltersStr", "newFiltersStr", "prev", "handlePaginationChange", "newPagination", "handleRefresh", "TableSkeleton", "items", "totalCount", "Fragment", "DataTable", "props", "NotionFilter", "conditions", "currentStr", "newStr", "OverViewLayout", "AppLayout", "Head"], "mappings": "q2BAmBa,MAAAA,GAAS,CAAC,CAAE,KAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAA,EAAY,UAAAC,KAAiC,CAC9E,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EACzCC,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,EAAiC,CACrC,KAAM,CAAE,GAAIT,CAAO,CAAA,CACpB,EACKG,EAAA,CACJ,MAAO,UACP,YAAa,SAASF,CAAQ,kCAAA,CAC/B,EACSC,EAAA,QACHQ,EAAc,CAEf,MAAAC,EAAQC,GAAeF,CAAG,EAC1BP,EAAA,CACJ,MAAOQ,EAAM,MACb,YAAaA,EAAM,YACnB,QAAS,OAAA,CACV,CAAA,CAEL,EAEAE,OAAAA,EAAAA,UAAU,IAAM,CACdP,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFQ,EAAA,IAAAC,GAAA,CAAY,KAAAV,EACX,SAAAW,OAACC,GACC,CAAA,SAAA,CAAAD,OAACE,GACC,CAAA,SAAA,CAAAJ,EAAAA,IAACK,IAAiB,SAAwB,0BAAA,CAAA,SACzCC,GAAuB,CAAA,SAAA,CAAA,8EAErBnB,EAAS,GAAA,CACZ,CAAA,CAAA,EACF,SACCoB,GACC,CAAA,SAAA,CAACP,EAAA,IAAAQ,GAAA,CAAkB,QAASpB,EAAW,SAAM,SAAA,EAC5CY,EAAA,IAAAS,GAAA,CAAkB,QAASf,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,ECrDagB,GAAoC,CAC/CC,EACAC,EACAC,EAAsC,CAAA,EACtCC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,uBAAwBL,EAAWC,EAAU,KAAK,UAAUC,CAAgB,EAAGC,CAAO,EAC5G,QAAS,SAAY,CACf,GAAA,CAEF,MAAMG,EAAOC,GAAgC,CAC3C,UAAAP,EACA,SAAAC,EACA,QAAAE,EACA,iBAAAD,CAAA,CACD,EAOD,OALiB,MAAMM,EAA+B,CACpD,KAAAF,CAAA,CACD,GAGe,MAAM,WACfpB,EAAO,CAEd,KAAM,CAAE,MAAAuB,EAAO,YAAAC,CAAA,EAAgBC,GAAgBzB,EAAO,yBAAyB,EAGzE,OAAAR,EAAA,CACJ,MAAA+B,EACA,YAAAC,EACA,QAAS,aAAA,CACV,EAGM,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CAEtC,EACA,MAAO,EAAA,CACR,ECxBUE,GAAM,CAAC,CAAE,SAAAC,KAA+B,CAC7C,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAC7B,CAACnC,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBqC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,MAAAC,CAAA,EAAUC,EAA2C,EAE/EC,EAAY,IAAM,CAChBF,EAAA,CACJ,KAAM,GACN,YAAa,GACb,YAAa,EAAA,CACd,CACH,EAEMG,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBC,EAA2B,CACzB,KAAMD,CAAA,CACP,EACH,UAAW,IAAM,CACT/C,EAAA,CACJ,MAAO,UACP,YAAa,gCACb,QAAS,SAAA,CACV,EACIsC,EAAY,kBAAkB,CAAE,SAAU,CAACX,EAAW,sBAAsB,EAAG,EAC1EiB,EAAA,EACVzC,EAAQ,EAAK,CACf,EACA,QAAUI,GAAoC,CACtCP,EAAA,CACJ,MAAOO,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEK0C,EAAYC,GAAgD,CAEhE,MAAMC,EAA8C,CAClD,GAAGD,CACL,EAGKL,EAAmB,OAAOM,CAAQ,CACzC,EAEMC,EAAoBC,GAAqB,CACzCA,GACQT,EAAA,EAEZzC,EAAQkD,CAAO,CACjB,EAEA,cACG,UACC,CAAA,SAAA,CAAA1C,EAAA,IAAC2C,EAAQ,EAAA,EACRzC,EAAA,KAAA0C,EAAA,CAAO,KAAArD,EAAY,aAAckD,EAChC,SAAA,CAACzC,EAAAA,IAAA6C,GAAA,CAAc,QAAO,GAAE,SAAArB,CAAS,CAAA,QAChC,UAAQ,CAAA,UAAU,yCAChB,SAAAC,EAAI,0BAA0B,GAC7BvB,OAAC4C,EAAO,CAAA,KAAK,KAAK,UAAU,kCAAkC,QAAS,IAAML,EAAiB,EAAI,EAChG,SAAA,CAAAzC,EAAA,IAAC+C,GAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/D/C,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAoB,sBAAA,CAAA,CAAA,CAAA,CAClE,CAEJ,CAAA,EACAE,EAAAA,KAAC8C,EAAc,CAAA,KAAK,KAClB,SAAA,CAAAhD,MAACiD,EACC,CAAA,SAAAjD,EAAA,IAACkD,EAAY,CAAA,SAAA,uBAAqB,CAAA,EACpC,SACC,OAAK,CAAA,SAAUrB,EAAaS,CAAQ,EAAG,UAAU,OAChD,SAAA,CAAAtC,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAACiD,EACC,CAAA,SAAA,CAAAnD,EAAA,IAACoD,EAAA,CACC,MAAM,OACN,YAAY,4DAEZ,SAAApD,EAAA,IAACqD,GAAM,SAAQ,GAAE,GAAGvB,EAAS,MAAM,EAAG,YAAY,MAAO,CAAA,CAAA,CAC3D,EACA9B,EAAA,IAACoD,EAAA,CACC,MAAM,eACN,YAAY,qCAEZ,SAAApD,EAAA,IAACqD,GAAM,SAAQ,GAAE,GAAGvB,EAAS,aAAa,EAAG,YAAY,cAAe,CAAA,CAAA,CAC1E,EACA9B,EAAA,IAACoD,EAAA,CACC,MAAM,cACN,YAAY,oCAEZ,SAAApD,EAAA,IAACsD,GAAS,SAAQ,GAAE,GAAGxB,EAAS,aAAa,EAAG,YAAY,aAAc,CAAA,CAAA,CAAA,CAC5E,CAAA,CACF,CACF,CAAA,EACA5B,EAAAA,KAACqD,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAvD,EAAA,IAAC8C,EAAA,CACC,QAAQ,QACR,QAAUU,GAAM,CACdA,EAAE,eAAe,EACjBhE,EAAQ,EAAK,CACf,EACA,SAAU0C,EAAmB,UAC9B,SAAA,QAAA,CAED,EACAlC,EAAAA,IAAC8C,EAAO,CAAA,KAAK,SAAS,SAAUZ,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,EC3HauB,GAAU,CAAC,CAAE,OAAAC,EAAQ,SAAAC,EAAU,SAAAC,EAAU,QAAAC,EAAU,cAAkC,CAC1F,KAAA,CAAE,IAAApC,CAAI,EAAIC,EAAmB,EAGnC,OAAImC,IAAY,WAEX7D,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,gBAAC8D,EACC,CAAA,SAAA,CAAC9D,EAAAA,IAAA+D,GAAA,CAAoB,QAAO,GAC1B,SAAA7D,EAAA,KAAC4C,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cAEV,SAAA,CAAC9C,EAAAA,IAAAgE,GAAA,CAAW,UAAU,SAAU,CAAA,EAC/BhE,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEvC,EACCE,EAAA,KAAA+D,GAAA,CAAoB,MAAM,MAAM,UAAU,YACxC,SAAA,CAAAxC,EAAI,yCAAyC,GAC5CzB,EAAA,IAACkE,EAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAASF,EAAQC,EAAU,MAAM,EACjD,SAAA,MAAA,CAED,EAEDlC,EAAI,2CAA2C,GAC9CzB,EAAA,IAACkE,EAAA,CACC,UAAU,sCACV,QAAS,IAAMN,EAASF,EAAQC,EAAU,QAAQ,EACnD,SAAA,QAAA,CAAA,CAED,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAMFzD,EAAA,KAAC,MAAI,CAAA,UAAU,sCACZ,SAAA,CAAAuB,EAAI,qCAAqC,GACxCvB,EAAA,KAAC4C,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMc,EAASF,EAAQC,EAAU,YAAY,EAEtD,SAAA,CAAC3D,EAAAA,IAAAmE,GAAA,CAAoB,UAAU,SAAU,CAAA,EACzCnE,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,EAEDyB,EAAI,0BAA0B,GAC7BvB,EAAA,KAAC4C,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMc,EAASF,EAAQC,EAAU,MAAM,EAEhD,SAAA,CAAC3D,EAAAA,IAAAoE,GAAA,CAAa,UAAU,SAAU,CAAA,EAClCpE,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,CAAA,CAAA,CACZ,EAEJ,CAEJ,EC9EgB,SAAAqE,EACdC,EACAC,EACAC,EACS,CAiBL,GAfA,CAACA,GAAe,OAAOA,GAAgB,UAAY,EAAE,aAAcA,IAenE,EAVmBC,GAEnB,OAAOA,GAAU,UACjBA,IAAU,MACV,aAAcA,GACd,UAAWA,GAKID,CAAW,EACrB,MAAA,GAIH,MAAAC,EAAQH,EAAI,SAASC,CAAQ,EAG7B,CAAE,SAAAG,EAAU,MAAOC,CAAc,EAAAH,EAGnC,GAAuBC,GAAU,KAC/B,OAAAC,IAAa,SAAiB,GAC9BA,IAAa,YAAoB,GACjCA,IAAa,UAMnB,IAAIE,EAAc,GAClB,GAAI,OAAOH,GAAU,UAAYA,IAAU,KAErC,GAAA,CACFG,EAAc,KAAK,UAAUH,CAAK,EAAE,YAAY,CAAA,MAC1C,CAENG,EAAc,IAAI,OAAO,UAAU,SAAS,KAAKH,CAAK,CAAC,GAAA,MAEhD,OAAOA,GAAU,UAAY,OAAOA,GAAU,WAAa,OAAOA,GAAU,SAEvEG,EAAA,OAAOH,CAAK,EAAE,YAAY,EAG1BG,EAAA,IAAI,OAAOH,CAAK,IAGhC,MAAMI,EAAkBF,EAAY,OAAOA,CAAS,EAAE,cAAgB,GAGtE,OAAQD,EAAU,CAChB,IAAK,SACH,OAAOE,IAAgBC,EACzB,IAAK,YACH,OAAOD,IAAgBC,EACzB,IAAK,WACI,OAAAD,EAAY,SAASC,CAAe,EAC7C,IAAK,aACI,OAAAD,EAAY,WAAWC,CAAe,EAC/C,IAAK,WACI,OAAAD,EAAY,SAASC,CAAe,EAC7C,IAAK,cACH,OAAO,OAAOJ,CAAK,EAAI,OAAOE,CAAS,EACzC,IAAK,qBACH,OAAO,OAAOF,CAAK,GAAK,OAAOE,CAAS,EAC1C,IAAK,WACH,OAAO,OAAOF,CAAK,EAAI,OAAOE,CAAS,EACzC,IAAK,kBACH,OAAO,OAAOF,CAAK,GAAK,OAAOE,CAAS,EAC1C,IAAK,UACH,OAAOC,IAAgB,GACzB,IAAK,aACH,OAAOA,IAAgB,GACzB,IAAK,SACI,OAAAH,GAAU,KACnB,IAAK,YACI,OAAAA,GAAU,KACnB,QACS,MAAA,EAAA,CAEb,CCzFa,MAAAK,GACXC,GACuC,CACrC,CACE,YAAa,OACb,OAAQ,CAAC,CAAE,OAAAC,CAAA,IACRhF,EAAA,IAAAiF,EAAA,CAAsB,OAAAD,EAAgB,MAAM,gBAAgB,EAE/D,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,MACf,EACA,SAAUb,CACZ,EACA,CACE,YAAa,cACb,OAAQ,CAAC,CAAE,OAAAW,CAAA,IACRhF,EAAA,IAAAiF,EAAA,CAAsB,OAAAD,EAAgB,MAAM,eAAe,EAE9D,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,cACf,EACA,SAAUb,CACZ,EACA,CACE,YAAa,cACb,OAAQ,CAAC,CAAE,OAAAW,CAAA,IACRhF,EAAA,IAAAiF,EAAA,CAAsB,OAAAD,EAAgB,MAAM,cAAc,EAE7D,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,aACf,EACA,SAAUb,CACZ,EACA,CACE,GAAI,UACJ,OAAQ,UACR,aAAc,GACd,KAAOa,GACLlF,EAAA,IAACyD,GAAA,CACC,OAAQyB,EAAK,IAAI,SAAS,GAC1B,SAAUA,EAAK,IAAI,SACnB,SAAUH,EACV,QAAQ,UAAA,CACV,EAEF,KAAM,CACJ,UAAW,aACX,YAAa,SAAA,CACf,CAEJ,EChDWI,EAAO,CAAC,CAAE,SAAAxB,EAAU,OAAAD,EAAQ,UAAAtE,KAA+B,CACtE,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAI,EAC/B,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBqC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,MAAAC,CAAA,EAAUC,EAA2C,EAa/EC,EAAY,IAAM,CAChBF,EAAA,CACJ,KAAM,GACN,YAAa,GACb,YAAa,EAAA,CACd,CACH,EAEMG,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBgD,GAA8B,CAC5B,KAAM,CAAE,GAAI1B,GAAU,EAAG,EACzB,KAAMtB,CAAA,CACP,EACH,UAAW,IAAM,CACT/C,EAAA,CACJ,MAAO,UACP,YAAa,gCACb,QAAS,SAAA,CACV,EACIsC,EAAY,kBAAkB,CAAE,SAAU,CAACX,EAAW,sBAAsB,EAAG,EAC1EiB,EAAA,EACVzC,EAAQ,EAAK,EACHJ,EAAA,CACZ,EACA,QAAUQ,GAAoC,CACtCP,EAAA,CACJ,MAAOO,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEK0C,EAAYC,GAAgD,CAEhE,MAAMC,EAA8C,CAClD,GAAGD,CAEL,EAEAL,EAAmB,OAAOM,CAAQ,CACpC,EAEMC,EAAoBC,GAAqB,CACxCA,GACOtD,EAAA,EAEZI,EAAQkD,CAAO,CACjB,EAEA,cACG,UACC,CAAA,SAAA,CAAA1C,EAAA,IAAC2C,EAAQ,EAAA,EACT3C,EAAAA,IAAC4C,GAAO,KAAArD,EAAY,aAAckD,EAChC,SAACvC,EAAAA,KAAA8C,EAAA,CAAc,KAAK,KAClB,SAAA,CAAAhD,MAACiD,EACC,CAAA,SAAAjD,EAAA,IAACkD,EAAY,CAAA,SAAA,eAAa,CAAA,EAC5B,SACC,OAAK,CAAA,SAAUrB,EAAaS,CAAQ,EAAG,UAAU,OAChD,SAAA,CAAAtC,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAACiD,EACC,CAAA,SAAA,CAAAnD,EAAA,IAACoD,EAAA,CACC,MAAM,OACN,YAAY,4DAEZ,SAACpD,EAAA,IAAAqD,EAAA,CAAM,SAAQ,GAAE,GAAGvB,EAAS,MAAM,EAAG,aAAc6B,EAAS,MAAQ,GAAI,YAAY,MAAO,CAAA,CAAA,CAC9F,EACA3D,EAAA,IAACoD,EAAA,CACC,MAAM,eACN,YAAY,qCAEZ,SAACpD,EAAA,IAAAqD,EAAA,CAAM,SAAQ,GAAE,GAAGvB,EAAS,aAAa,EAAG,aAAc6B,EAAS,aAAe,GAAI,YAAY,cAAe,CAAA,CAAA,CACpH,EACA3D,EAAA,IAACoD,EAAA,CACC,MAAM,cACN,YAAY,oCAEZ,SAACpD,EAAA,IAAAsD,EAAA,CAAS,SAAQ,GAAE,GAAGxB,EAAS,aAAa,EAAG,aAAc6B,EAAS,aAAe,GAAI,YAAY,aAAc,CAAA,CAAA,CAAA,CACtH,CAAA,CACF,CACF,CAAA,EACAzD,EAAAA,KAACqD,EAAa,CAAA,UAAU,OACtB,SAAA,CAAAvD,EAAA,IAAC8C,EAAA,CACC,QAAQ,QACR,QAAUU,GAAM,CACdA,EAAE,eAAe,EACjBhE,EAAQ,EAAK,EACHJ,EAAA,CACZ,EACA,SAAU8C,EAAmB,UAC9B,SAAA,QAAA,CAED,EACAlC,EAAAA,IAAC8C,EAAO,CAAA,KAAK,SAAS,SAAUZ,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,EC9HamD,GAAqB,IAAM,CAChC,KAAA,CAAE,MAAAhG,CAAM,EAAIC,EAAS,EACrBqC,EAAcC,EAAe,EAE7B,CAAC0D,EAAWC,CAAY,EAAI9F,EAAAA,SAAiB,EAAE,EAC/C,CAACoB,EAAkB2E,CAAmB,EAAI/F,EAAAA,SAA4B,CAAA,CAAE,EACxE,CAACgG,EAAkBC,CAAmB,EAAIjG,EAAAA,SAItC,IAAI,EAER,CAACkG,EAAYC,CAAa,EAAInG,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAEK,CAAE,UAAAoG,EAAW,KAAAC,CAAA,EAASpF,GAC1BiF,EAAW,UACXA,EAAW,SACX9E,CACF,EAYMkF,EAAUjB,GATS,CAACpB,EAAgBC,EAAiCqC,IAAiD,CACtGN,EAAA,CAClB,OAAAhC,EACA,SAAAC,EACA,WAAAqC,CAAA,CACD,CACH,CAG2C,EAErCC,EAAgBxB,GAAkB,CAEtCc,EAAad,CAAK,EAKZ,MAAAyB,EAAsB,CAAC,GADLrF,EAAiB,OAAasF,GAAAA,EAAG,YAAc,MAAM,CAC9B,EAG3C1B,GACFyB,EAAoB,KAAK,CACvB,UAAW,OACX,SAAU,WACV,MAAAzB,CAAA,CACD,EAIG,MAAA2B,EAAoB,KAAK,UAAUvF,CAAgB,EACnDwF,EAAgB,KAAK,UAAUH,CAAmB,EAEpDE,IAAsBC,IACxBb,EAAoBU,CAAmB,EACvCN,MAAuB,CAAE,GAAGU,EAAM,UAAW,GAAI,EAErD,EAEMC,EAA0BC,GAAmC,CACjEZ,EAAcY,CAAa,CAC7B,EAGMC,EAAgB,IAAM,CAErB9E,EAAY,kBAAkB,CAAE,SAAU,CAACX,EAAW,sBAAsB,EAAG,EAGpF,WAAW,IAAM,CACT3B,EAAA,CACJ,MAAO,iBACP,YAAa,sCACb,QAAS,SAAA,CACV,GACA,GAAG,CACR,EAEA,GAAIwG,EACF,OAAA7F,EAAA,IAAC0G,GAAA,CACC,SAAUf,EAAW,SACrB,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CACd,EAII,MAAAgB,EAAQb,GAAM,OAAS,CAAC,EACxBc,EAAad,GAAM,YAAc,EAEvC,OAEI5F,EAAA,KAAA2G,WAAA,CAAA,SAAA,CAAC7G,EAAAA,IAAA,MAAA,CAAI,UAAU,+EACb,SAAAA,EAAA,IAAC8G,GAAA,CACC,MAAM,uBACN,QAAAf,EACA,KAAMY,EACN,WAAAC,EACA,UAAAf,EACA,iBAAkB,GAClB,SAAUF,EAAW,SACrB,mBAAoBY,EACpB,SAAUN,EACV,YAAaX,EACb,gBAAkByB,GAChB/G,EAAA,IAACgH,GAAA,CACE,GAAGD,EACJ,cAAelG,EACf,eAAiBoG,GAAe,CAExB,MAAAC,EAAa,KAAK,UAAUrG,CAAgB,EAC5CsG,EAAS,KAAK,UAAUF,CAAU,EAEpCC,IAAeC,IACjB3B,EAAoByB,CAAU,EAC9BrB,MAAuB,CAAE,GAAGU,EAAM,UAAW,GAAI,EACnD,CACF,CACF,EAEF,qBAAsB,GACtB,UAAWG,EACX,mBAAoB,GACpB,aAAc,CAEZ,QAAS,IAAM,CAA8B,EAC7C,cAAUlF,GAAI,CAAA,CAAA,CAAA,CAChB,CAAA,EAEJ,EAECkE,GAAoBA,EAAiB,aAAe,QACnDzF,EAAA,IAACmF,EAAA,CACC,OAAQM,EAAiB,OACzB,SAAUA,EAAiB,SAC3B,UAAW,IAAM,CACV9D,EAAY,kBAAkB,CAAE,SAAU,CAACX,EAAW,sBAAsB,EAAG,EACpF0E,EAAoB,IAAI,CAAA,CAC1B,CACF,EAEDD,GAAoBA,EAAiB,aAAe,cACnDzF,EAAA,IAACmF,EAAA,CACC,OAAQM,EAAiB,OACzB,SAAUA,EAAiB,SAC3B,UAAW,IAAMC,EAAoB,IAAI,CAAA,CAC3C,EAEDD,GAAoBA,EAAiB,aAAe,UACnDzF,EAAA,IAACf,GAAA,CACC,KAAM,CACJ,SAAUwG,EAAiB,SAAS,KACpC,OAAQA,EAAiB,MAC3B,EACA,UAAW,IAAM,CACV9D,EAAY,kBAAkB,CAAE,SAAU,CAACX,EAAW,yBAAyB,EAAG,EACvF0E,EAAoB,IAAI,CAAA,CAC1B,CAAA,CACF,EAEJ,CAEJ,ECzLA,SAAwB0B,IAAiB,CACvC,cACGC,GACC,CAAA,SAAA,CAACrH,EAAAA,IAAAsH,GAAA,CAAK,MAAM,kBAAmB,CAAA,QAE9BjC,GAAmB,CAAA,CAAA,CAAA,EACtB,CAEJ"}