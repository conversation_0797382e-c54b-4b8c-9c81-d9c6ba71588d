"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ViewOptions } from "@/components/data-table/DataTableViewOptions"
import { Search } from "@/components/ui/search"
import { type FilterOperator } from "@/client"
import { type Table } from "@tanstack/react-table"
import { RiAddLine, RiCloseLine, RiFilterLine } from "@remixicon/react"
import { useEffect, useState } from "react"

interface FilterProps<TData> {
  table: Table<TData>
  onSearch?: (value: string) => void
  searchValue?: string
}

type FilterCondition = {
  id: string
  columnId: string
  operator: FilterOperator
  value: string
}

// Define the type for the filter value object
type FilterValue = {
  operator: FilterOperator
  value: string | null
}

// Map of user-friendly operator names to actual FilterOperator values
const operatorOptions: { label: string; value: FilterOperator }[] = [
  { label: "Equals", value: "Equals" },
  { label: "Not Equals", value: "NotEquals" },
  { label: "Contains", value: "Contains" },
  { label: "Starts With", value: "StartsWith" },
  { label: "Ends With", value: "EndsWith" },
  { label: "Greater Than", value: "GreaterThan" },
  { label: "Greater Than or Equal", value: "GreaterThanOrEqual" },
  { label: "Less Than", value: "LessThan" },
  { label: "Less Than or Equal", value: "LessThanOrEqual" },
  { label: "Is Empty", value: "IsEmpty" },
  { label: "Is Not Empty", value: "IsNotEmpty" },
  { label: "Is Null", value: "IsNull" },
  { label: "Is Not Null", value: "IsNotNull" },
]

export function Filter<TData>({
  table,
  onSearch,
  searchValue = "",
}: FilterProps<TData>) {
  // Initialize filter conditions from existing table filters
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])

  // Initialize filter conditions from existing table filters
  useEffect(() => {
    const existingFilters = table.getState().columnFilters
    if (existingFilters.length > 0) {
      const initialConditions = existingFilters.map(filter => ({
        id: generateId(),
        columnId: filter.id,
        operator: (filter.value as FilterValue)?.operator ?? "Contains",
        value: (filter.value as FilterValue)?.value ?? ""
      }))
      setFilterConditions(initialConditions)
    }
  }, [table])
  const [isOpen, setIsOpen] = useState(false)

  // Generate a unique ID for new filter conditions
  function generateId() {
    return `filter-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
  }

  // Get all visible columns that can be filtered
  const filterableColumns = table
    .getAllColumns()
    .filter(
      (column) =>
        // Include all columns except special ones and ensure they have a filterFn
        column.id !== "select" &&
        column.id !== "actions" &&
        column.id !== "drag" &&
        column.getCanFilter()
    )

  // Force enable filtering on all columns
  useEffect(() => {
    // This is a workaround to ensure all columns can be filtered
  }, [filterableColumns])

  // Log when filter conditions change
  useEffect(() => {
    console.log('Filter conditions changed:', filterConditions);
  }, [filterConditions])

  const isFiltered = table.getState().columnFilters.length > 0

  // We don't want to automatically clear filter conditions when table filters change
  // This would prevent us from adding new filter conditions

  // Add a new empty filter condition
  const addFilterCondition = () => {
    console.log('Add filter button clicked');

    // Create a simple condition with a hardcoded column
    const newCondition: FilterCondition = {
      id: generateId(),
      columnId: 'name',
      operator: "Contains" as FilterOperator,
      value: "",
    }

    console.log('Adding new condition:', newCondition);

    // Create a new array with the new condition
    const updatedConditions = [...filterConditions, newCondition];
    console.log('Updated conditions:', updatedConditions);

    // Set the state with the new array
    setFilterConditions(updatedConditions);

    // Make sure the popover stays open
    setIsOpen(true);
  }

  // Remove a filter condition by ID
  const removeFilterCondition = (id: string) => {
    const updatedConditions = filterConditions.filter(condition => condition.id !== id)
    setFilterConditions(updatedConditions)

    // If we're removing a condition that was applied to the table, reset that column's filter
    const removedCondition = filterConditions.find(condition => condition.id === id)
    if (removedCondition) {
      const column = table.getColumn(removedCondition.columnId)
      if (column) column.setFilterValue(undefined)
    }
  }

  // Update a specific filter condition
  const updateFilterCondition = (id: string, updates: Partial<FilterCondition>) => {
    const updatedConditions = filterConditions.map(condition => {
      if (condition.id === id) {
        return { ...condition, ...updates }
      }
      return condition
    })
    setFilterConditions(updatedConditions)
  }

  // Apply all filter conditions to the table
  const applyFilters = () => {
    // First, clear any existing filters
    table.resetColumnFilters()

    // Then apply each filter condition
    filterConditions.forEach(condition => {
      const column = table.getColumn(condition.columnId)

      if (column) {
        // For operators that don't need a value
        const noValueOperators = ["IsEmpty", "IsNotEmpty", "IsNull", "IsNotNull"]

        if (noValueOperators.includes(condition.operator) || condition.value) {
          try {
            // Create the filter value object
            const filterObj: FilterValue = {
              operator: condition.operator,
              value: condition.value ?? null
            }

            // Set the filter value directly
            column.setFilterValue(filterObj)

            // Force a table update
            setTimeout(() => {
              table.getFilteredRowModel()
            }, 0)
          } catch (error) {
            console.error('Error setting filter:', error)
          }
        }
      }
    })

    setIsOpen(false)
  }

  // Clear all filters
  const clearFilters = () => {
    setFilterConditions([])
    table.resetColumnFilters()
  }

  // Get a user-friendly column name
  const getColumnDisplayName = (columnId: string) => {
    const column = table.getColumn(columnId)
    return column?.columnDef?.header?.toString() ?? columnId
  }

  return (
    <div className="flex flex-wrap items-center justify-between gap-2">
      {onSearch && (
        <div className="w-full sm:w-auto sm:max-w-[250px]">
          <Search onUpdate={onSearch} value={searchValue} />
        </div>
      )}

      <div className="flex items-center gap-2 ml-auto">
        <Popover open={isOpen} onOpenChange={setIsOpen} defaultOpen={true}>
          <PopoverTrigger asChild>
            <Button
              variant={isFiltered ? "primary" : "ghost"}
              size="sm"
              className="h-8 gap-1"
            >
              <RiFilterLine className="h-3.5 w-3.5" />
              <span>Filter</span>
              {isFiltered && (
                <span className="ml-1 rounded-full bg-primary/20 px-1 text-xs font-medium">
                  {table.getState().columnFilters.length}
                </span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[320px] p-3" align="end">
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium leading-none">Filter</h4>
                <p className="text-sm text-muted-foreground">
                  Filter records by specific conditions
                </p>
                <Button
                  onClick={() => {
                    const newCondition = {
                      id: generateId(),
                      columnId: 'name',
                      operator: "Contains" as FilterOperator,
                      value: "",
                    };
                    console.log('Adding test condition:', newCondition);
                    const updatedConditions = [...filterConditions, newCondition];
                    console.log('Updated conditions:', updatedConditions);
                    setFilterConditions(updatedConditions);
                  }}
                >
                  Test Add Filter
                </Button>
              </div>

              <div>
                <pre className="text-xs">
                  {JSON.stringify(filterConditions, null, 2)}
                </pre>
              </div>
              {filterConditions.length > 0 ? (
                <div className="space-y-2" key={`filter-conditions-${filterConditions.length}`}>
                  {filterConditions.map((condition) => (
                    <div key={condition.id} className="flex flex-col gap-2 rounded-md border p-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs font-medium">Column</Label>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => removeFilterCondition(condition.id)}
                        >
                          <RiCloseLine className="h-3.5 w-3.5" />
                        </Button>
                      </div>
                      <Select
                        value={condition.columnId}
                        onValueChange={(value) => updateFilterCondition(condition.id, { columnId: value })}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select column" />
                        </SelectTrigger>
                        <SelectContent>
                          {filterableColumns.map((column) => (
                            <SelectItem key={column.id} value={column.id}>
                              {getColumnDisplayName(column.id)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Label className="text-xs font-medium">Operator</Label>
                      <Select
                        value={condition.operator}
                        onValueChange={(value) => updateFilterCondition(condition.id, { operator: value as FilterOperator })}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select operator" />
                        </SelectTrigger>
                        <SelectContent>
                          {operatorOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Label className="text-xs font-medium">Value</Label>
                      <Input
                        className="h-8"
                        placeholder="Enter value"
                        value={condition.value}
                        onChange={(e) => updateFilterCondition(condition.id, { value: e.target.value })}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex h-20 items-center justify-center rounded-md border border-dashed">
                  <p className="text-sm text-muted-foreground">No filters added</p>
                </div>
              )}

              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 gap-1"
                  onClick={addFilterCondition}
                >
                  <RiAddLine className="h-3.5 w-3.5" />
                  <span>Add filter</span>
                </Button>
                {filterConditions.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8"
                    onClick={clearFilters}
                  >
                    Clear all
                  </Button>
                )}
              </div>

              <Button
                className="w-full"
                onClick={applyFilters}
                disabled={filterConditions.length === 0}
              >
                Apply filters
              </Button>
            </div>
          </PopoverContent>
        </Popover>

        {isFiltered && (
          <Button
            variant="ghost"
            onClick={clearFilters}
            className="border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500"
          >
            Clear filters
          </Button>
        )}
        <ViewOptions table={table} />
      </div>
    </div>
  )
}
