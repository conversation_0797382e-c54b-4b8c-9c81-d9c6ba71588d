import{r as p,j as e,u as K,f as E,h as V}from"./vendor-B0b15ZrB.js";import{u as A,y as _,Q as S,z as W,t as X,C as Z,a as k,T as z,B as N,E as ee,D as se,c as te,e as ie,f as P,F as ae,A as re}from"./app-layout-D_A4XD_6.js";import{$ as ne}from"./App-De6zOdMU.js";import{A as oe,a as le,b as ce,c as de,d as ue,e as pe,f as me,g as he,T as xe}from"./TableSkeleton-DgDki6RL.js";import{p as ge,l as ye,_ as fe,D as v,b as je}from"./DataTableColumnHeader-CSMG3Uqi.js";import{g as Se,e as Ne}from"./query-utils-extended-wVOeERM5.js";import{N as be}from"./NotionFilter-B-J2qhpm.js";import{u as H,D as L,a as De,b as M,c as Q,d as U,I as w,e as G}from"./index.esm-DqIqfoOW.js";import{F as B,a as g}from"./FormField-POW7SsfI.js";import{M as J,c as F}from"./filterFunctions-EQ3oUkRA.js";import{T as $}from"./textarea-DPuaXqY_.js";import{B as Ce}from"./badge-B7pYtUY6.js";import"./radix-BQPyiA8r.js";import"./card-Iy60I049.js";import"./scroll-area-Dw-Gl1P6.js";const ve=({data:{dataId:a,dataEdit:s},onDismiss:n})=>{const{toast:r}=A(),[i,c]=p.useState(!1),l=async()=>{try{await _({path:{id:a}}),r({title:"Success",description:`User "${s.name}" has been deleted successfully.`}),n()}catch(d){d instanceof Error&&r({title:"Failed",description:`There was a problem when deleting the user "${s.name}". Kindly try again.`,variant:"destructive"})}};return p.useEffect(()=>{c(!0)},[]),e.jsx(oe,{open:i,children:e.jsxs(le,{children:[e.jsxs(ce,{children:[e.jsx(de,{children:"Are you absolutely sure?"}),e.jsxs(ue,{children:['This action cannot be undone. This will permanently delete your this user "',s.name,'"']})]}),e.jsxs(pe,{children:[e.jsx(me,{onClick:n,children:"Cancel"}),e.jsx(he,{onClick:l,children:"Yes"})]})]})})},Fe=(a,s,n=[],r)=>K({queryKey:[S.GetOpeniddictScopes,a,s,JSON.stringify(n),r],queryFn:async()=>{try{const i=Se({pageIndex:a,pageSize:s,sorting:r,filterConditions:n});return(await W({body:i})).data?.data}catch(i){const{title:c,description:l}=Ne(i,"Error loading scopes");return X({title:c,description:l,variant:"destructive"}),{items:[],totalCount:0}}},retry:!1}),Y=()=>K({queryKey:[S.GetOpeniddictResourcesAvailableResources],queryFn:async()=>(await Z()).data?.data}),we=({children:a})=>{const{can:s}=k(),[n,r]=p.useState(!1),{toast:i}=A(),c=E(),{handleSubmit:l,register:d,setValue:u}=H(),[m,b]=p.useState([]),{data:y,isLoading:C}=Y(),h=V({mutationFn:async o=>ee({body:o}),onSuccess:()=>{i({title:"Success",description:"Client Created Successfully",variant:"success"}),c.invalidateQueries({queryKey:[S.GetOpeniddictScopes]}),r(!1)},onError:o=>{i({title:o?.error?.message,description:o?.error?.details,variant:"destructive"})}}),D=o=>{const x={...o,resources:m};h.mutate(x)};return e.jsxs("section",{children:[e.jsx(z,{}),e.jsxs(L,{open:n,onOpenChange:r,children:[e.jsx(De,{asChild:!0,children:a}),e.jsx("section",{className:"flex items-center justify-between pb-5",children:s("AbpIdentity.Users.Create")&&e.jsxs(N,{size:"sm",className:"w-full sm:py-1 sm:mt-0 sm:w-fit",onClick:()=>r(!0),children:[e.jsx(ge,{className:"-ml-1 size-4 shrink-0","aria-hidden":"true"}),e.jsx("span",{className:"hidden truncate sm:inline",children:"Create Scope"})]})}),e.jsxs(M,{size:"xl",children:[e.jsx(Q,{children:e.jsx(U,{children:"Create a New Scope"})}),e.jsxs("form",{onSubmit:l(D),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(B,{children:[e.jsx(g,{label:"Name",description:"The unique identifier for this resource. Used in requests",children:e.jsx(w,{required:!0,...d("name"),placeholder:"Name"})}),e.jsx(g,{label:"Display Name",description:"The display name for this resource",children:e.jsx(w,{required:!0,...d("displayName"),placeholder:"Display Name"})}),e.jsx(g,{label:"Description",description:"The description for this resource",children:e.jsx($,{required:!0,...d("description"),placeholder:"Description"})}),e.jsx(g,{label:"Resources",description:"The resources of the scope",children:e.jsx(J,{options:y?.map(o=>({value:o.value??"",label:o.label??""}))??[],value:m,onChange:o=>{b(o),u("resources",o)},placeholder:C?"Loading resources...":"Select resources",disabled:C,maxHeight:300})})]})}),e.jsxs(G,{className:"mt-5",children:[e.jsx(N,{variant:"ghost",onClick:o=>{o.preventDefault(),r(!1)},disabled:h.isPending,children:"Cancel"}),e.jsx(N,{type:"submit",disabled:h.isPending,children:h.isPending?"Saving...":"Save"})]})]})]})]})]})},Ae=({dataId:a,dataEdit:s,onAction:n,variant:r="dropdown"})=>{const{can:i}=k();return r==="dropdown"?e.jsx("div",{className:"flex justify-end",children:e.jsxs(se,{children:[e.jsx(te,{asChild:!0,children:e.jsxs(N,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[e.jsx(ye,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(ie,{align:"end",className:"w-[160px]",children:[i("IdentityServer.OpenIddictScopes.Edit")&&e.jsx(P,{className:"cursor-pointer text-sm",onClick:()=>n(a,s,"edit"),children:"Edit"}),i("IdentityServer.OpenIddictScopes.Delete")&&e.jsx(P,{className:"cursor-pointer text-sm text-red-500",onClick:()=>n(a,s,"delete"),children:"Delete"})]})]})}):e.jsx("div",{className:"flex items-center justify-end gap-1",children:i("IdentityServer.OpenIddictScopes.Edit")&&e.jsxs(N,{variant:"primary",size:"sm",className:"flex items-center gap-1 px-2 py-1",onClick:()=>n(a,s,"edit"),children:[e.jsx(fe,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})})},Oe=a=>[{accessorKey:"name",header:({column:s})=>e.jsx(v,{column:s,title:"Name"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Name"},filterFn:F},{accessorKey:"displayName",header:({column:s})=>e.jsx(v,{column:s,title:"Display Name"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Display Name"},filterFn:F},{accessorKey:"description",header:({column:s})=>e.jsx(v,{column:s,title:"Description"}),enableSorting:!0,enableHiding:!0,cell:s=>s.getValue(),meta:{className:"text-left",displayName:"Description"},filterFn:F},{accessorKey:"resources",header:({column:s})=>e.jsx(v,{column:s,title:"Resources"}),enableSorting:!0,enableHiding:!0,filterFn:F,cell:s=>{const n=s.getValue();return e.jsx("div",{className:"flex flex-wrap gap-1",children:n?.map(r=>e.jsx(Ce,{variant:"secondary",children:r},r))})},meta:{className:"text-left",displayName:"Resources"}},{id:"actions",header:({column:s})=>e.jsx(v,{column:s,title:"Actions"}),enableHiding:!0,cell:s=>e.jsx(Ae,{dataId:s.row.original.id,dataEdit:s.row.original,onAction:a,variant:"dropdown"}),meta:{className:"text-right",displayName:"Actions"}}],Te=({dataEdit:a,dataId:s,onDismiss:n})=>{const[r,i]=p.useState(!0),{toast:c}=A(),l=E(),{handleSubmit:d,register:u,setValue:m,reset:b}=H(),[y,C]=p.useState(a.resources??[]),{data:h,isLoading:D}=Y(),o=()=>{b({name:"",displayName:"",description:"",resources:[]})},x=V({mutationFn:async t=>ae({path:{id:s},body:t}),onSuccess:()=>{c({title:"Success",description:"Scope Updated Successfully",variant:"success"}),l.invalidateQueries({queryKey:[S.GetOpeniddictScopes]}),o(),i(!1),n()},onError:t=>{c({title:t?.error?.message,description:t?.error?.details,variant:"destructive"})}}),O=t=>{const f={...t,resources:y};x.mutate(f)},T=t=>{t||n(),i(t)};return e.jsxs("section",{children:[e.jsx(z,{}),e.jsx(L,{open:r,onOpenChange:T,children:e.jsxs(M,{size:"xl",children:[e.jsx(Q,{children:e.jsx(U,{children:"Edit Scope"})}),e.jsxs("form",{onSubmit:d(O),className:"mt-2",children:[e.jsx("section",{className:"flex w-full flex-col space-y-2",children:e.jsxs(B,{children:[e.jsx(g,{label:"Name",description:"The unique identifier for this resource. Used in requests",children:e.jsx(w,{required:!0,...u("name"),placeholder:"Name",defaultValue:a.name??""})}),e.jsx(g,{label:"Display Name",description:"The display name for this resource",children:e.jsx(w,{required:!0,...u("displayName"),placeholder:"Display Name",defaultValue:a.displayName??""})}),e.jsx(g,{label:"Description",description:"The description for this resource",children:e.jsx($,{required:!0,...u("description"),placeholder:"Description",defaultValue:a.description??""})}),e.jsx(g,{label:"Resources",description:"The resources of the scope",children:e.jsx(J,{options:h?.map(t=>({value:t.value??"",label:t.label??""}))??[],value:y,onChange:t=>{C(t),m("resources",t)},placeholder:D?"Loading resources...":"Select resources",disabled:D,maxHeight:300})})]})}),e.jsxs(G,{className:"mt-5",children:[e.jsx(N,{variant:"ghost",onClick:t=>{t.preventDefault(),i(!1)},disabled:x.isPending,children:"Cancel"}),e.jsx(N,{type:"submit",disabled:x.isPending,children:x.isPending?"Updating...":"Update"})]})]})]})})]})},qe=()=>{const{toast:a}=A(),s=E(),[n,r]=p.useState(""),[i,c]=p.useState([]),[l,d]=p.useState(),[u,m]=p.useState({pageIndex:0,pageSize:10}),{isLoading:b,data:y}=Fe(u.pageIndex,u.pageSize,i),h=Oe((t,f,j)=>{d({dataId:t,dataEdit:f,dialogType:j})}),D=t=>{r(t);const j=[...i.filter(R=>R.fieldName!=="name")];t&&j.push({fieldName:"name",operator:"Contains",value:t});const q=JSON.stringify(i),I=JSON.stringify(j);q!==I&&(c(j),m(R=>({...R,pageIndex:0})))},o=t=>{m(t)},x=()=>{s.invalidateQueries({queryKey:[S.GetOpeniddictScopes]}),setTimeout(()=>{a({title:"Data refreshed",description:"The client list has been refreshed.",variant:"success"})},800)};if(b)return e.jsx(xe,{rowCount:u.pageSize,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0});const O=y?.items??[],T=y?.totalCount??0;return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4",children:e.jsx(je,{title:"Scopes Management",columns:h,data:O,totalCount:T,isLoading:b,manualPagination:!0,pageSize:u.pageSize,onPaginationChange:o,onSearch:D,searchValue:n,customFilterbar:t=>e.jsx(be,{...t,activeFilters:i,onServerFilter:f=>{const j=JSON.stringify(i),q=JSON.stringify(f);j!==q&&(c(f),m(I=>({...I,pageIndex:0})))}}),hideDefaultFilterbar:!0,onRefresh:x,enableRowSelection:!1,actionButton:{onClick:()=>{},content:e.jsx(we,{})}})}),l&&l.dialogType==="edit"&&e.jsx(Te,{dataId:l.dataId,dataEdit:l.dataEdit,onDismiss:()=>{s.invalidateQueries({queryKey:[S.GetOpeniddictScopes]}),d(null)}}),l&&l.dialogType==="delete"&&e.jsx(ve,{data:{dataId:l.dataId,dataEdit:l.dataEdit},onDismiss:()=>{s.invalidateQueries({queryKey:[S.GetOpeniddictScopes]}),d(null)}})]})};function Je(){return e.jsxs(re,{children:[e.jsx(ne,{title:"Client Scopes"}),e.jsx(qe,{})]})}export{Je as default};
//# sourceMappingURL=scope-BmbzEuNQ.js.map
