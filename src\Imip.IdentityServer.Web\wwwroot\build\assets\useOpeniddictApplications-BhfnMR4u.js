import{Q as n,W as c,t as p}from"./app-layout-D_A4XD_6.js";import{g as u,e as d}from"./query-utils-extended-wVOeERM5.js";import{u as y}from"./vendor-B0b15ZrB.js";const f=(r,e,s=[],a)=>y({queryKey:[n.GetOpeniddictApplications,r,e,JSON.stringify(s),a],queryFn:async()=>{try{const t=u({pageIndex:r,pageSize:e,sorting:a,filterConditions:s});return(await c({body:t})).data?.data}catch(t){const{title:i,description:o}=d(t,"Error loading clients");return p({title:i,description:o,variant:"destructive"}),{items:[],totalCount:0}}},retry:!1});export{f as u};
//# sourceMappingURL=useOpeniddictApplications-BhfnMR4u.js.map
