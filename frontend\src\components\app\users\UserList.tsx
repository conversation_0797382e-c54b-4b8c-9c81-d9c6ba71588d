'use client'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useUsers } from '@/lib/hooks/useUsers'
import { useState } from 'react'

import { type IdentityUserUpdateDto } from '@/client'
import Error from '@/components/ui/Error'
import { type PaginationState, type SortingState } from '@tanstack/react-table'

import { AddUser } from '@/components/app/users/AddUser'
import { UploadUsersCsv } from '@/components/app/users/UploadUsersCsv'
import { useToast } from "@/lib/useToast"
import { DeleteUser } from './DeleteUser'
import { UserEdit } from './UserEdit'
import { UserPermission } from './UserPermission'

import { DataTable } from '@/components/data-table/DataTable'
import { TableSkeleton } from '@/components/ui/TableSkeleton'
import { useQueryClient } from '@tanstack/react-query'
import { getUserColumns } from './columns'
import { UserFilterbar } from './UserFilterbar'

export const UserList = () => {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const [searchStr, setSearchStr] = useState<string>('')
  const [userActionDialog, setUserActionDialog] = useState<{
    userId: string
    userDto: IdentityUserUpdateDto
    dialogType?: 'edit' | 'permission' | 'delete'
  } | null>()

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })

  // Initialize sorting state
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'name', desc: false }
  ])

  // Convert SortingState to API sorting string
  const getSortingString = (sortState: SortingState): string => {
    if (!sortState.length) return 'name asc';

    const sort = sortState[0] ?? { id: 'name', desc: false };
    return `${sort.id} ${sort.desc ? 'desc' : 'asc'}`;
  }

  const { isLoading, data, isError } = useUsers(
    pagination.pageIndex,
    pagination.pageSize,
    searchStr,
    getSortingString(sorting)
  )

  // Handler for user actions (edit, permission, delete)
  const handleUserAction = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => {
    setUserActionDialog({
      userId,
      userDto,
      dialogType,
    });
  }

  // Get columns with the action handler
  const columns = getUserColumns(handleUserAction)

  const handleSearch = (value: string) => {
    setSearchStr(value)
    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search
  }

  const handlePaginationChange = (newPagination: PaginationState) => {
    setPagination(newPagination)
  }

  // Handler for sorting change
  const handleSortingChange = (newSorting: SortingState) => {
    setSorting(newSorting)
    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change
  }

  // Handler for refreshing the data
  const handleRefresh = () => {
    // Invalidate the query to fetch fresh data
    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })

    // Show toast notification after a short delay to match the animation
    setTimeout(() => {
      toast({
        title: "Data refreshed",
        description: "The user list has been refreshed.",
        variant: "default",
      })
    }, 800)
  }

  if (isLoading) return (
    <TableSkeleton
      rowCount={pagination.pageSize}
      columnCount={4}
      hasTitle={true}
      hasSearch={true}
      hasFilters={true}
      hasPagination={true}
      hasActions={true}
    />
  )
  if (isError) return <Error />

  return (
    <>
      {userActionDialog && userActionDialog.dialogType === 'edit' && (
        <UserEdit
          userId={userActionDialog.userId}
          userDto={userActionDialog.userDto}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })
            setUserActionDialog(null)
          }}
        />
      )}
      {userActionDialog && userActionDialog.dialogType === 'permission' && (
        <UserPermission
          userId={userActionDialog.userId}
          userDto={userActionDialog.userDto}
          onDismiss={() => setUserActionDialog(null)}
        />
      )}
      {userActionDialog && userActionDialog.dialogType === 'delete' && (
        <DeleteUser
          user={{
            username: userActionDialog.userDto.userName,
            userId: userActionDialog.userId,
          }}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })
            setUserActionDialog(null)
          }}
        />
      )}

      <div className="space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4">
        <DataTable
          title="Users Management"
          columns={columns}
          data={data?.items ?? []}
          totalCount={data?.totalCount}
          isLoading={isLoading}
          manualPagination={true}
          manualSorting={true}
          pageSize={pagination.pageSize}
          onPaginationChange={handlePaginationChange}
          onSortingChange={handleSortingChange}
          sortingState={sorting}
          onSearch={handleSearch}
          searchValue={searchStr}
          customFilterbar={UserFilterbar}
          hideDefaultFilterbar={true}
          onRefresh={handleRefresh}
          enableRowSelection={false}
          actionButton={{
            // label: "Create New User",
            // No action needed - this is handled by the custom cell renderer
            onClick: () => { /* Required but not used */ },
            content: (
              <div className="flex gap-2">
                <AddUser />
                <UploadUsersCsv />
              </div>
            )
          }}
        />
      </div>
    </>
  )
}
