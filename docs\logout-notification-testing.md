# Testing the Logout Notification System

This guide provides instructions for testing the logout notification system to ensure it works correctly across different client platforms.

## Prerequisites

1. **Redis Server** running on `localhost:6379`
2. **RabbitMQ Server** running on `localhost:5672`
3. **Identity Server** running and configured
4. **Test clients** (optional) for different platforms

## Testing Steps

### 1. Verify Infrastructure

#### Check Redis Connection
```bash
# Test Redis connection
redis-cli ping
# Should return: PONG

# Test Redis Pub/Sub
redis-cli subscribe identityserver:logout
# Keep this running to see logout messages
```

#### Check RabbitMQ Connection
```bash
# Test RabbitMQ connection (if you have rabbitmqctl)
rabbitmqctl status

# Or use the RabbitMQ Management UI
# Open http://localhost:15672 (default credentials: guest/guest)
```

### 2. Test Identity Server Logout

#### Manual Logout Test
1. **Login to Identity Server** with a test user
2. **Call the logout endpoint**:
   ```bash
   curl -X GET "https://localhost:44309/api/my-account/logout" \
     -H "Cookie: your-session-cookie" \
     -k
   ```
3. **Check Redis** for the logout message:
   ```bash
   # In another terminal
   redis-cli subscribe identityserver:logout
   ```
4. **Check RabbitMQ** for the message in the management UI

#### Admin Logout Test
1. **Call the admin logout endpoint**:
   ```bash
   curl -X POST "https://localhost:44309/api/my-account/logout/{userId}" \
     -H "Authorization: Bearer your-admin-token" \
     -k
   ```
2. **Verify** the logout notification is sent

### 3. Test Client Integration

#### .NET Client Test
Create a simple console application to test the logout notifications:

```csharp
// TestClient.cs
using System;
using System.Text.Json;
using StackExchange.Redis;

class Program
{
    static async Task Main(string[] args)
    {
        var redis = ConnectionMultiplexer.Connect("localhost:6379");
        var subscriber = redis.GetSubscriber();
        
        Console.WriteLine("Listening for logout notifications...");
        
        await subscriber.SubscribeAsync("identityserver:logout", (channel, message) =>
        {
            Console.WriteLine($"Received logout message: {message}");
            
            try
            {
                var logoutMessage = JsonSerializer.Deserialize<LogoutMessage>(message);
                Console.WriteLine($"User {logoutMessage.UserName} logged out at {logoutMessage.LogoutTimestamp}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing message: {ex.Message}");
            }
        });
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}

public class LogoutMessage
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string? SessionId { get; set; }
    public Guid? TenantId { get; set; }
    public string SourceApplication { get; set; } = string.Empty;
    public int LogoutType { get; set; }
    public DateTime LogoutTimestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string[]? TargetClients { get; set; }
}
```

#### Node.js Client Test
Create a simple Node.js script to test Redis Pub/Sub:

```javascript
// test-client.js
const redis = require('redis');

async function testLogoutNotifications() {
    const client = redis.createClient({
        url: 'redis://localhost:6379'
    });

    await client.connect();
    
    console.log('Listening for logout notifications...');
    
    await client.subscribe('identityserver:logout', (message) => {
        console.log('Received logout message:', message);
        
        try {
            const logoutMessage = JSON.parse(message);
            console.log(`User ${logoutMessage.userName} logged out at ${logoutMessage.logoutTimestamp}`);
        } catch (error) {
            console.error('Error parsing message:', error.message);
        }
    });
    
    // Keep the script running
    process.on('SIGINT', async () => {
        await client.disconnect();
        process.exit(0);
    });
}

testLogoutNotifications().catch(console.error);
```

#### PHP Client Test
Create a simple PHP script to test Redis Pub/Sub:

```php
<?php
// test-client.php
require_once 'vendor/autoload.php';

use Predis\Client;

$redis = new Client([
    'host' => 'localhost',
    'port' => 6379
]);

echo "Listening for logout notifications...\n";

$redis->subscribe(['identityserver:logout'], function ($redis, $channel, $message) {
    echo "Received logout message: $message\n";
    
    try {
        $logoutMessage = json_decode($message, true);
        echo "User {$logoutMessage['userName']} logged out at {$logoutMessage['logoutTimestamp']}\n";
    } catch (Exception $e) {
        echo "Error parsing message: " . $e->getMessage() . "\n";
    }
});
```

### 4. Test Scenarios

#### Scenario 1: Single User Logout
1. **Start test clients** (Redis subscribers)
2. **Login** with a test user
3. **Logout** from Identity Server
4. **Verify** all test clients receive the logout notification
5. **Check** the notification contains correct user information

#### Scenario 2: Admin Logout User
1. **Start test clients**
2. **Login** with multiple test users
3. **Use admin endpoint** to logout a specific user
4. **Verify** the logout notification is sent
5. **Check** that only the target user's session is affected

#### Scenario 3: Multiple Client Applications
1. **Start multiple test clients** with different client IDs
2. **Login** with a test user
3. **Logout** from Identity Server
4. **Verify** all clients receive the notification
5. **Check** client-specific channels work correctly

#### Scenario 4: Network Issues
1. **Start test clients**
2. **Disconnect Redis** temporarily
3. **Perform logout**
4. **Reconnect Redis**
5. **Verify** RabbitMQ still delivers the message
6. **Check** Redis Pub/Sub resumes working

### 5. Monitoring and Debugging

#### Enable Detailed Logging
Add these settings to your `appsettings.Development.json`:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Imip.IdentityServer.Domain.Services": "Debug",
      "Imip.IdentityServer.Domain.Events": "Debug"
    }
  }
}
```

#### Check Application Logs
Monitor the Identity Server logs for:
- Logout event processing
- Redis connection status
- RabbitMQ message publishing
- Client notification attempts

#### Monitor Redis
```bash
# Monitor Redis commands
redis-cli monitor

# Check Redis info
redis-cli info

# Check Redis memory usage
redis-cli info memory
```

#### Monitor RabbitMQ
1. **Open RabbitMQ Management UI** at `http://localhost:15672`
2. **Check Queues** for message counts
3. **Check Exchanges** for message routing
4. **Monitor Connections** and channels

### 6. Performance Testing

#### Load Testing
1. **Create multiple test users**
2. **Simulate concurrent logouts**
3. **Monitor** Redis and RabbitMQ performance
4. **Check** message delivery latency
5. **Verify** no message loss

#### Stress Testing
1. **Generate high volume** of logout events
2. **Monitor** system resources
3. **Check** for memory leaks
4. **Verify** graceful degradation

### 7. Security Testing

#### Message Validation
1. **Send malformed messages** to Redis/RabbitMQ
2. **Verify** the system handles them gracefully
3. **Check** for proper error logging
4. **Ensure** no security vulnerabilities

#### Authentication Testing
1. **Test** with invalid credentials
2. **Verify** proper error handling
3. **Check** for secure connection usage
4. **Test** with different user roles

### 8. Troubleshooting

#### Common Issues

**Redis Connection Issues**
```bash
# Check Redis status
redis-cli ping

# Check Redis configuration
redis-cli config get bind
redis-cli config get port
```

**RabbitMQ Connection Issues**
```bash
# Check RabbitMQ status
rabbitmqctl status

# Check RabbitMQ logs
tail -f /var/log/rabbitmq/<EMAIL>
```

**Message Not Received**
1. **Check** Redis/RabbitMQ connections
2. **Verify** channel names match
3. **Check** message format
4. **Review** application logs

#### Debug Commands

**Redis Debug**
```bash
# Check Redis channels
redis-cli pubsub channels

# Check Redis subscribers
redis-cli pubsub numsub identityserver:logout
```

**RabbitMQ Debug**
```bash
# Check RabbitMQ queues
rabbitmqctl list_queues

# Check RabbitMQ exchanges
rabbitmqctl list_exchanges
```

### 9. Production Readiness Checklist

- [ ] **Infrastructure** properly configured
- [ ] **Security** measures implemented
- [ ] **Monitoring** and alerting set up
- [ ] **Backup** and recovery procedures
- [ ] **Performance** requirements met
- [ ] **Error handling** tested
- [ ] **Documentation** complete
- [ ] **Client integration** tested
- [ ] **Load testing** completed
- [ ] **Security testing** passed

## Conclusion

This testing guide ensures your logout notification system is robust, reliable, and ready for production use. Regular testing should be performed to maintain system health and catch issues early. 