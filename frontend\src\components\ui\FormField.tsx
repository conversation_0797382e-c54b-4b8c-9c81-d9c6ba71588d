'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Divider } from '@/components/ui/divider'

interface FormFieldProps {
  label: string
  description?: string
  className?: string
  labelWidth?: string
  children: React.ReactNode
}

export function FormField({
  label,
  description,
  className,
  labelWidth = '200px',
  children
}: FormFieldProps) {
  return (
    <div className={cn("grid items-start gap-4", className)}
      style={{ gridTemplateColumns: `${labelWidth} 1fr` }}>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
        {description && (
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {description}
          </p>
        )}
      </div>
      <div>
        {children}
      </div>
    </div>
  )
}

export function FormSection({ children, className }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={cn("space-y-4", className)}>
      {children}
      <Divider />
    </div>
  )
}
