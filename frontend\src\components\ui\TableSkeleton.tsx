import { Skeleton } from '@/components/ui/skeleton'
import { Card } from '@/components/ui/card'

interface TableSkeletonProps {
  rowCount?: number
  columnCount?: number
  hasTitle?: boolean
  hasSearch?: boolean
  hasFilters?: boolean
  hasPagination?: boolean
  hasActions?: boolean
}

export function TableSkeleton({
  rowCount = 10,
  columnCount = 4,
  hasTitle = true,
  hasSearch = true,
  hasFilters = true,
  hasPagination = true,
  hasActions = true,
}: TableSkeletonProps) {
  // Create arrays for rows and columns
  const rows = Array.from({ length: rowCount }, (_, i) => i)
  const columns = Array.from({ length: columnCount }, (_, i) => i)
  
  return (
    <Card className="space-y-4 py-4">
      {/* Table header skeleton */}
      {hasTitle && (
        <div className="flex items-center justify-between mb-6 px-4">
          <Skeleton className="h-8 w-48" /> {/* Title */}
          {hasActions && (
            <div className="flex space-x-2">
              <Skeleton className="h-9 w-24" /> {/* Button */}
              <Skeleton className="h-9 w-24" /> {/* Button */}
            </div>
          )}
        </div>
      )}
      
      {/* Search and filter bar skeleton */}
      {(hasSearch || hasFilters) && (
        <div className="flex items-center justify-between mb-4 px-4">
          {hasSearch && <Skeleton className="h-10 w-64" />} {/* Search bar */}
          {hasFilters && <Skeleton className="h-10 w-32" />} {/* Filter button */}
        </div>
      )}
      
      {/* Table header row */}
      <div className="flex w-full border-b pb-2 px-4">
        <Skeleton className="h-6 w-8 mr-4" /> {/* Checkbox */}
        {columns.map((col) => (
          <Skeleton 
            key={`header-${col}`} 
            className={`h-6 ${col === columns.length - 1 ? 'w-1/6' : 'w-1/4 mr-4'}`} 
          />
        ))}
      </div>
      
      {/* Table rows */}
      {rows.map((row) => (
        <div key={`row-${row}`} className="flex w-full py-3 border-b px-4">
          <Skeleton className="h-5 w-5 mr-4" /> {/* Checkbox */}
          {columns.map((col) => (
            <Skeleton 
              key={`cell-${row}-${col}`} 
              className={`h-5 ${col === columns.length - 1 ? 'w-1/6' : 'w-1/4 mr-4'}`} 
            />
          ))}
        </div>
      ))}
      
      {/* Pagination skeleton */}
      {hasPagination && (
        <div className="flex items-center justify-between pt-4 px-4">
          <Skeleton className="h-5 w-32" /> {/* Page info */}
          <div className="flex space-x-1">
            <Skeleton className="h-8 w-8" /> {/* Pagination button */}
            <Skeleton className="h-8 w-8" /> {/* Pagination button */}
            <Skeleton className="h-8 w-8" /> {/* Pagination button */}
            <Skeleton className="h-8 w-8" /> {/* Pagination button */}
          </div>
        </div>
      )}
    </Card>
  )
}
