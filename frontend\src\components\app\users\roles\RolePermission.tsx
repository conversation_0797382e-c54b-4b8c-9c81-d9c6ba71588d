import { type FormEvent, useCallback, useEffect, useMemo, useState, useRef } from 'react'

import {
  type IdentityRoleDto,
  type PermissionGroupDto,
  type UpdatePermissionsDto,
  putApiPermissionManagementPermissions,
} from '@/client'
import { usePermissions } from '@/lib/hooks/usePermissions'
import { PermissionProvider, USER_ROLE } from '@/lib/utils'
import { useQueryClient } from '@tanstack/react-query'
import { TogglePermission } from '@/components/app/permission/TogglePermission'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useToast } from "@/lib/useToast"
import { type Management, Permission } from '@/components/app/permission/PermissionToggle'

type RolePermissionProps = {
  roleDto: IdentityRoleDto
  onDismiss: () => void
}

export const RolePermission = ({ roleDto, onDismiss }: RolePermissionProps) => {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()

  // flag determine to enable/disable all the permissions to a role.
  const [hasAllGranted, setHasAllGranted] = useState(false)
  const { data } = usePermissions(PermissionProvider.R, roleDto.name ?? undefined)
  const queryClient = useQueryClient()

  const [permissionGroups, setPermissionGroups] = useState<PermissionGroupDto[]>([])

  useEffect(() => {
    setOpen(true)
    return () => {
      void queryClient.invalidateQueries({ queryKey: [PermissionProvider.R] })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Update the local state with the remote data
  useEffect(() => {
    if (data?.groups) {
      setPermissionGroups([...data?.groups])
    }
  }, [data])

  // Check if all permissions are granted already - only run when data changes
  useEffect(() => {
    if (data?.groups && data.groups.length > 0) {
      const hasAllPermissionGranted = data.groups
        .map((g) => g.permissions?.every((p) => p.isGranted))
        .every((e) => e)
      setHasAllGranted(hasAllPermissionGranted)
    }
  }, [data])

  // Apply hasAllGranted to all permissions - only when hasAllGranted changes
  useEffect(() => {
    if (permissionGroups.length > 0) {
      // Create a new array with updated permissions to avoid direct mutation
      const updatedGroups = permissionGroups.map(group => ({
        ...group,
        permissions: group.permissions?.map(permission => ({
          ...permission,
          isGranted: hasAllGranted
        })) ?? null
      }));

      // Update state with the new array
      setPermissionGroups(updatedGroups);
    }
  }, [hasAllGranted]); // Remove permissionGroups from dependency array

  const onCloseEvent = useCallback(() => {
    setOpen(false)
    onDismiss()
  }, [onDismiss])

  const onSubmit = useCallback(
    async (e: FormEvent) => {
      e.preventDefault()

      if (!permissionGroups || permissionGroups.length === 0) return;

      // Create a stable copy of the permissions to prevent reference issues
      const payload = permissionGroups
        .map((p) =>
          (p.permissions ?? []).map((grant) => ({
            name: grant.name ?? null,
            isGranted: grant.isGranted ?? false,
          }))
        )
        .flat()

      const requestPayload: UpdatePermissionsDto = {
        permissions: payload,
      }

      try {
        await putApiPermissionManagementPermissions({
          query: {
            providerKey: roleDto.name ?? '',
            providerName: PermissionProvider.R
          },
          body: requestPayload,
        })

        toast({
          title: 'Success',
          description: 'Permission Updated Successfully',
          variant: 'default',
        })

        void queryClient.invalidateQueries({
          queryKey: [PermissionProvider.R],
        })

        onCloseEvent()
      } catch (err: unknown) {
        if (err instanceof Error) {
          toast({
            title: 'Failed',
            description: "Permission update wasn't successful.",
            variant: 'destructive',
          })
        }
      }
    },
    [permissionGroups, roleDto.name, toast, queryClient, onCloseEvent]
  )

  const hasAdmin = useMemo(() => roleDto.name?.includes(USER_ROLE.ADMIN) ?? false, [roleDto])

  // Use stable keys to prevent remounting issues
  const dialogKey = useRef(`dialog-${Math.random().toString(36).substring(2, 9)}`).current;

  const formatDisplayName = (str: string): Management => {
    const parts = (str || '').split(' ');
    return (parts[0] ?? '').toLowerCase() as Management;
  }

  // Group permissions into pairs for the grid layout
  const groupedPermissions = useMemo(() => {
    const result = [];
    for (let i = 0; i < permissionGroups.length; i += 2) {
      result.push(permissionGroups.slice(i, i + 2));
    }
    return result;
  }, [permissionGroups]);

  return (
    <Dialog key={dialogKey} open={open} onOpenChange={onCloseEvent}>
      <DialogContent className="max-h-[90vh] overflow-hidden flex flex-col" style={{ maxWidth: "900px", width: "90vw" }}>
        <DialogHeader>
          <DialogTitle>Permissions - {roleDto.name}</DialogTitle>
        </DialogHeader>
        <form onSubmit={onSubmit} className="flex-1 overflow-y-auto">
          <div className="p-1">
            <Permission
              name="Grant All Permissions"
              isGranted={hasAllGranted}
              id="all_granted"
              disabled={!hasAdmin}
              onUpdate={() => {
                setHasAllGranted((prev) => !prev)
              }}
              className="ml-2 mb-4"
            />

            <div className="space-y-6">
              {groupedPermissions.map((row, rowIndex) => (
                <div key={rowIndex} className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {row.map((group, colIndex) => (
                    <div key={`${rowIndex}-${colIndex}`} className="border rounded-lg p-4">
                      <h3 className="text-lg font-medium mb-2">{group.displayName}</h3>
                      <div className="border-t pt-3">
                        <TogglePermission
                          key={`group-${group.displayName}-${rowIndex}-${colIndex}`}
                          permissions={group.permissions ?? []}
                          type={formatDisplayName(group.displayName ?? '')}
                          disabled={hasAllGranted && hasAdmin}
                          hideSelectAll={hasAllGranted}
                          hideSave
                        />
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </form>
        <DialogFooter className="mt-4 border-t pt-4 bg-white dark:bg-gray-950">
          <Button
            onClick={(e) => {
              e.preventDefault()
              onCloseEvent()
            }}
            variant="ghost"
          >
            Cancel
          </Button>
          <Button onClick={onSubmit}>Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
