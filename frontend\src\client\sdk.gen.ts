// This file is auto-generated by @hey-api/openapi-ts

import { type Options as ClientOptions, type TDataShape, type Client, formDataBodySerializer } from '@hey-api/client-fetch';
import type { GetApiAbpApiDefinitionData, GetApiAbpApiDefinitionResponses, GetApiAbpApiDefinitionErrors, GetApiAbpApplicationConfigurationData, GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, GetApiAbpApplicationLocalizationData, GetApiAbpApplicationLocalizationResponses, GetApiAbpApplicationLocalizationErrors, GetApiAbpMultiTenancyTenantsByNameByNameData, GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, GetApiAbpMultiTenancyTenantsByIdByIdData, GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, GetApiMyAccountLogoutData, GetApiMyAccountLogoutResponses, PostApiMyAccountLoginData, PostApiMyAccountLoginResponses, PostApiMyAccountCheckPasswordData, PostApiMyAccountCheckPasswordResponses, PostApiMyAccountLogoutByUserIdData, PostApiMyAccountLogoutByUserIdResponses, PostApiMyAccountLogoutAllSessionsData, PostApiMyAccountLogoutAllSessionsResponses, PostApiAccountRegisterData, PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, PostApiAccountSendPasswordResetCodeData, PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, PostApiAccountVerifyPasswordResetTokenData, PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, PostApiAccountResetPasswordData, PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, GetApiActiveDirectorySettingsData, GetApiActiveDirectorySettingsResponses, GetApiActiveDirectorySettingsErrors, PutApiActiveDirectorySettingsData, PutApiActiveDirectorySettingsResponses, PutApiActiveDirectorySettingsErrors, PostApiActiveDirectorySettingsTestConnectionData, PostApiActiveDirectorySettingsTestConnectionResponses, PostApiActiveDirectorySettingsTestConnectionErrors, GetApiIdpActiveDirectorySettingsData, GetApiIdpActiveDirectorySettingsResponses, GetApiIdpActiveDirectorySettingsErrors, PutApiIdpActiveDirectorySettingsData, PutApiIdpActiveDirectorySettingsResponses, PutApiIdpActiveDirectorySettingsErrors, PostApiIdpActiveDirectorySettingsTestConnectionData, PostApiIdpActiveDirectorySettingsTestConnectionResponses, PostApiIdpActiveDirectorySettingsTestConnectionErrors, GetAdminClaimsData, GetAdminClaimsResponses, GetAdminUsersData, GetAdminUsersResponses, GetAdminUsersRolesData, GetAdminUsersRolesResponses, GetAdminClientsData, GetAdminClientsResponses, GetAdminClientsResourcesData, GetAdminClientsResourcesResponses, GetAdminClientsScopesData, GetAdminClientsScopesResponses, GetAdminTenantsData, GetAdminTenantsResponses, GetAdminSettingsData, GetAdminSettingsResponses, PostApiAuditLogsListData, PostApiAuditLogsListResponses, PostApiAuditLogsListErrors, GetApiAuditLogsByIdData, GetApiAuditLogsByIdResponses, GetApiAuditLogsByIdErrors, PostApiIdentityClaimTypesListData, PostApiIdentityClaimTypesListResponses, PostApiIdentityClaimTypesListErrors, DeleteApiIdentityClaimTypesByIdData, DeleteApiIdentityClaimTypesByIdResponses, DeleteApiIdentityClaimTypesByIdErrors, GetApiIdentityClaimTypesByIdData, GetApiIdentityClaimTypesByIdResponses, GetApiIdentityClaimTypesByIdErrors, PutApiIdentityClaimTypesByIdData, PutApiIdentityClaimTypesByIdResponses, PutApiIdentityClaimTypesByIdErrors, PostApiIdentityClaimTypesData, PostApiIdentityClaimTypesResponses, PostApiIdentityClaimTypesErrors, GetApiDashboardUsersCountData, GetApiDashboardUsersCountResponses, GetApiDashboardClientsCountData, GetApiDashboardClientsCountResponses, GetApiDashboardLoginsRecentData, GetApiDashboardLoginsRecentResponses, GetApiDashboardLoginsFailedCountData, GetApiDashboardLoginsFailedCountResponses, GetApiDashboardRolesDistributionData, GetApiDashboardRolesDistributionResponses, GetApiDashboardLoginsFailedRecentData, GetApiDashboardLoginsFailedRecentResponses, GetApiDashboardUsersGrowthData, GetApiDashboardUsersGrowthResponses, GetApiDashboardClientsTopLoginsData, GetApiDashboardClientsTopLoginsResponses, GetApiDashboardSecurityOverviewData, GetApiDashboardSecurityOverviewResponses, GetApiDashboardUsersActivityData, GetApiDashboardUsersActivityResponses, GetApiDashboardClientsPerformanceData, GetApiDashboardClientsPerformanceResponses, GetApiDashboardSecurityThreatsData, GetApiDashboardSecurityThreatsResponses, GetApiDashboardSystemHealthData, GetApiDashboardSystemHealthResponses, GetApiDashboardAnalyticsHourlyActivityData, GetApiDashboardAnalyticsHourlyActivityResponses, PostApiAccountDynamicClaimsRefreshData, PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, GetApiSettingManagementEmailingData, GetApiSettingManagementEmailingResponses, GetApiSettingManagementEmailingErrors, PostApiSettingManagementEmailingData, PostApiSettingManagementEmailingResponses, PostApiSettingManagementEmailingErrors, PostApiSettingManagementEmailingSendTestEmailData, PostApiSettingManagementEmailingSendTestEmailResponses, PostApiSettingManagementEmailingSendTestEmailErrors, GetApiChangeLogData, GetApiChangeLogResponses, GetApiChangeLogEntityData, GetApiChangeLogEntityResponses, GetApiChangeLogEntityLatestData, GetApiChangeLogEntityLatestResponses, GetApiChangeLogEntityTypeData, GetApiChangeLogEntityTypeResponses, GetApiChangeLogTimeRangeData, GetApiChangeLogTimeRangeResponses, GetApiChangeLogPropertyHistoryData, GetApiChangeLogPropertyHistoryResponses, GetApiChangeLogEntityChangesData, GetApiChangeLogEntityChangesResponses, DeleteApiFeatureManagementFeaturesData, DeleteApiFeatureManagementFeaturesResponses, DeleteApiFeatureManagementFeaturesErrors, GetApiFeatureManagementFeaturesData, GetApiFeatureManagementFeaturesResponses, GetApiFeatureManagementFeaturesErrors, PutApiFeatureManagementFeaturesData, PutApiFeatureManagementFeaturesResponses, PutApiFeatureManagementFeaturesErrors, GetApiHealthKubernetesData, GetApiHealthKubernetesResponses, GetData, GetResponses, GetAdminData, GetAdminResponses, PostApiIdentityClaimsListData, PostApiIdentityClaimsListResponses, PostApiIdentityClaimsListErrors, DeleteApiIdentityClaimsByIdData, DeleteApiIdentityClaimsByIdResponses, DeleteApiIdentityClaimsByIdErrors, GetApiIdentityClaimsByIdData, GetApiIdentityClaimsByIdResponses, GetApiIdentityClaimsByIdErrors, PutApiIdentityClaimsByIdData, PutApiIdentityClaimsByIdResponses, PutApiIdentityClaimsByIdErrors, PostApiIdentityClaimsData, PostApiIdentityClaimsResponses, PostApiIdentityClaimsErrors, PostApiImportCsvData, PostApiImportCsvResponses, PostApiImportCsvErrors, GetApiImportTemplatesData, GetApiImportTemplatesResponses, GetApiImportTemplatesErrors, PostApiAccountLoginData, PostApiAccountLoginResponses, PostApiAccountLoginErrors, GetApiAccountLogoutData, GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, PostApiAccountCheckPasswordData, PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, PostApiOpeniddictApplicationsListData, PostApiOpeniddictApplicationsListResponses, PostApiOpeniddictApplicationsListErrors, DeleteApiOpeniddictApplicationsByIdData, DeleteApiOpeniddictApplicationsByIdResponses, DeleteApiOpeniddictApplicationsByIdErrors, GetApiOpeniddictApplicationsByIdData, GetApiOpeniddictApplicationsByIdResponses, GetApiOpeniddictApplicationsByIdErrors, PutApiOpeniddictApplicationsByIdData, PutApiOpeniddictApplicationsByIdResponses, PutApiOpeniddictApplicationsByIdErrors, PostApiOpeniddictApplicationsData, PostApiOpeniddictApplicationsResponses, PostApiOpeniddictApplicationsErrors, GetApiOpeniddictApplicationsPermissionsData, GetApiOpeniddictApplicationsPermissionsResponses, GetApiOpeniddictApplicationsPermissionsErrors, GetApiOpeniddictApplicationsByIdScopesData, GetApiOpeniddictApplicationsByIdScopesResponses, GetApiOpeniddictApplicationsByIdScopesErrors, PutApiOpeniddictApplicationsByIdScopesData, PutApiOpeniddictApplicationsByIdScopesResponses, PutApiOpeniddictApplicationsByIdScopesErrors, GetApiOpeniddictApplicationsAvailableScopesData, GetApiOpeniddictApplicationsAvailableScopesResponses, GetApiOpeniddictApplicationsAvailableScopesErrors, GetApiOpeniddictApplicationsClientTypesData, GetApiOpeniddictApplicationsClientTypesResponses, GetApiOpeniddictApplicationsClientTypesErrors, GetApiOpeniddictApplicationsClientTypesDetailsData, GetApiOpeniddictApplicationsClientTypesDetailsResponses, GetApiOpeniddictApplicationsClientTypesDetailsErrors, GetApiOpeniddictAuthorizationsListData, GetApiOpeniddictAuthorizationsListResponses, GetApiOpeniddictAuthorizationsListErrors, PostApiOpeniddictAuthorizationsListData, PostApiOpeniddictAuthorizationsListResponses, PostApiOpeniddictAuthorizationsListErrors, GetApiOpeniddictAuthorizationsByIdData, GetApiOpeniddictAuthorizationsByIdResponses, GetApiOpeniddictAuthorizationsByIdErrors, GetApiOpeniddictAuthorizationsByStringIdByIdData, GetApiOpeniddictAuthorizationsByStringIdByIdResponses, GetApiOpeniddictAuthorizationsByStringIdByIdErrors, PostApiOpeniddictAuthorizationsRevokeData, PostApiOpeniddictAuthorizationsRevokeResponses, PostApiOpeniddictAuthorizationsRevokeErrors, PostApiOpeniddictAuthorizationsPruneData, PostApiOpeniddictAuthorizationsPruneResponses, PostApiOpeniddictAuthorizationsPruneErrors, GetApiOpeniddictClientPropertiesByApplicationIdData, GetApiOpeniddictClientPropertiesByApplicationIdResponses, GetApiOpeniddictClientPropertiesByApplicationIdErrors, PostApiOpeniddictClientPropertiesByApplicationIdData, PostApiOpeniddictClientPropertiesByApplicationIdResponses, PostApiOpeniddictClientPropertiesByApplicationIdErrors, DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyData, DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyResponses, DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyErrors, GetApiOpeniddictClientPropertiesByApplicationIdByKeyData, GetApiOpeniddictClientPropertiesByApplicationIdByKeyResponses, GetApiOpeniddictClientPropertiesByApplicationIdByKeyErrors, PutApiOpeniddictClientPropertiesByApplicationIdByKeyData, PutApiOpeniddictClientPropertiesByApplicationIdByKeyResponses, PutApiOpeniddictClientPropertiesByApplicationIdByKeyErrors, GetApiOpeniddictClientSecretsByApplicationIdData, GetApiOpeniddictClientSecretsByApplicationIdResponses, GetApiOpeniddictClientSecretsByApplicationIdErrors, PostApiOpeniddictClientSecretsByApplicationIdData, PostApiOpeniddictClientSecretsByApplicationIdResponses, PostApiOpeniddictClientSecretsByApplicationIdErrors, DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdData, DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdResponses, DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdErrors, PostApiOpeniddictClientSecretsHashData, PostApiOpeniddictClientSecretsHashResponses, PostApiOpeniddictClientSecretsHashErrors, GetApiOpeniddictClientSecretsGenerateData, GetApiOpeniddictClientSecretsGenerateResponses, GetApiOpeniddictClientSecretsGenerateErrors, GetApiOpeniddictGrantTypesData, GetApiOpeniddictGrantTypesErrors, GetApiOpeniddictGrantTypesApplicationsByApplicationIdData, GetApiOpeniddictGrantTypesApplicationsByApplicationIdErrors, PutApiOpeniddictGrantTypesApplicationsByApplicationIdData, PutApiOpeniddictGrantTypesApplicationsByApplicationIdErrors, GetApiOpeniddictGrantTypesValidateCombinationData, GetApiOpeniddictGrantTypesValidateCombinationErrors, GetApiOpeniddictGrantTypesRecommendedSettingsData, GetApiOpeniddictGrantTypesRecommendedSettingsErrors, GetApiOpeniddictRequirementsData, GetApiOpeniddictRequirementsResponses, GetApiOpeniddictRequirementsErrors, PostApiOpeniddictResourcesListData, PostApiOpeniddictResourcesListResponses, PostApiOpeniddictResourcesListErrors, DeleteApiOpeniddictResourcesByIdData, DeleteApiOpeniddictResourcesByIdResponses, DeleteApiOpeniddictResourcesByIdErrors, GetApiOpeniddictResourcesByIdData, GetApiOpeniddictResourcesByIdResponses, GetApiOpeniddictResourcesByIdErrors, PutApiOpeniddictResourcesByIdData, PutApiOpeniddictResourcesByIdResponses, PutApiOpeniddictResourcesByIdErrors, PostApiOpeniddictResourcesData, PostApiOpeniddictResourcesResponses, PostApiOpeniddictResourcesErrors, GetApiOpeniddictResourcesAvailableResourcesData, GetApiOpeniddictResourcesAvailableResourcesResponses, GetApiOpeniddictResourcesAvailableResourcesErrors, PostApiOpeniddictScopesListData, PostApiOpeniddictScopesListResponses, PostApiOpeniddictScopesListErrors, DeleteApiOpeniddictScopesByIdData, DeleteApiOpeniddictScopesByIdResponses, DeleteApiOpeniddictScopesByIdErrors, GetApiOpeniddictScopesByIdData, GetApiOpeniddictScopesByIdResponses, GetApiOpeniddictScopesByIdErrors, PutApiOpeniddictScopesByIdData, PutApiOpeniddictScopesByIdResponses, PutApiOpeniddictScopesByIdErrors, PostApiOpeniddictScopesData, PostApiOpeniddictScopesResponses, PostApiOpeniddictScopesErrors, GetApiOpeniddictScopesAvailableResourcesData, GetApiOpeniddictScopesAvailableResourcesResponses, GetApiOpeniddictScopesAvailableResourcesErrors, GetApiOpeniddictScopesByIdResourcesData, GetApiOpeniddictScopesByIdResourcesResponses, GetApiOpeniddictScopesByIdResourcesErrors, PutApiOpeniddictScopesByIdResourcesData, PutApiOpeniddictScopesByIdResourcesResponses, PutApiOpeniddictScopesByIdResourcesErrors, GetApiOpeniddictTokensData, GetApiOpeniddictTokensResponses, GetApiOpeniddictTokensErrors, GetApiOpeniddictTokensByIdData, GetApiOpeniddictTokensByIdResponses, GetApiOpeniddictTokensByIdErrors, PostApiOpeniddictTokensRevokeData, PostApiOpeniddictTokensRevokeResponses, PostApiOpeniddictTokensRevokeErrors, PostApiOpeniddictTokensPruneData, PostApiOpeniddictTokensPruneResponses, PostApiOpeniddictTokensPruneErrors, GetApiPermissionCheckData, GetApiPermissionCheckResponses, GetApiPermissionCheckMultipleData, GetApiPermissionCheckMultipleResponses, GetApiPermissionCheckUserData, GetApiPermissionCheckUserResponses, PostApiPermissionCheckInvalidateCacheData, PostApiPermissionCheckInvalidateCacheResponses, GetApiPermissionManagementPermissionsData, GetApiPermissionManagementPermissionsResponses, GetApiPermissionManagementPermissionsErrors, PutApiPermissionManagementPermissionsData, PutApiPermissionManagementPermissionsResponses, PutApiPermissionManagementPermissionsErrors, GetApiAccountMyProfileData, GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, PutApiAccountMyProfileData, PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, PostApiAccountMyProfileChangePasswordData, PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, GetApiIdentityRolesAllData, GetApiIdentityRolesAllResponses, GetApiIdentityRolesAllErrors, GetApiIdentityRolesData, GetApiIdentityRolesResponses, GetApiIdentityRolesErrors, PostApiIdentityRolesData, PostApiIdentityRolesResponses, PostApiIdentityRolesErrors, DeleteApiIdentityRolesByIdData, DeleteApiIdentityRolesByIdResponses, DeleteApiIdentityRolesByIdErrors, GetApiIdentityRolesByIdData, GetApiIdentityRolesByIdResponses, GetApiIdentityRolesByIdErrors, PutApiIdentityRolesByIdData, PutApiIdentityRolesByIdResponses, PutApiIdentityRolesByIdErrors, GetApiRoleApplicationsByUserByUserIdData, GetApiRoleApplicationsByUserByUserIdResponses, GetApiRoleApplicationsByRoleByRoleIdData, GetApiRoleApplicationsByRoleByRoleIdResponses, PostApiRoleApplicationsUpdateData, PostApiRoleApplicationsUpdateResponses, GetApiIdpRoleApplicationRoleApplicationsByUserByUserIdData, GetApiIdpRoleApplicationRoleApplicationsByUserByUserIdResponses, GetApiIdpRoleApplicationRoleApplicationsByUserByUserIdErrors, GetApiIdpRoleApplicationRoleApplicationsByRoleByRoleIdData, GetApiIdpRoleApplicationRoleApplicationsByRoleByRoleIdResponses, GetApiIdpRoleApplicationRoleApplicationsByRoleByRoleIdErrors, PutApiIdpRoleApplicationRoleApplicationsData, PutApiIdpRoleApplicationRoleApplicationsResponses, PutApiIdpRoleApplicationRoleApplicationsErrors, GetApiIdentityRolesByRoleIdClaimsData, GetApiIdentityRolesByRoleIdClaimsResponses, GetApiIdentityRolesByRoleIdClaimsErrors, PostApiIdentityRolesByRoleIdClaimsData, PostApiIdentityRolesByRoleIdClaimsResponses, PostApiIdentityRolesByRoleIdClaimsErrors, DeleteApiIdentityRolesByRoleIdClaimsByIdData, DeleteApiIdentityRolesByRoleIdClaimsByIdResponses, DeleteApiIdentityRolesByRoleIdClaimsByIdErrors, GetApiIdentityRolesByRoleIdClaimsByIdData, GetApiIdentityRolesByRoleIdClaimsByIdResponses, GetApiIdentityRolesByRoleIdClaimsByIdErrors, PutApiIdentityRolesByRoleIdClaimsByIdData, PutApiIdentityRolesByRoleIdClaimsByIdResponses, PutApiIdentityRolesByRoleIdClaimsByIdErrors, PostApiSecurityLogsListData, PostApiSecurityLogsListResponses, PostApiSecurityLogsListErrors, GetApiSecurityLogsByIdData, GetApiSecurityLogsByIdResponses, GetApiSecurityLogsByIdErrors, PostApiSecurityLogsMyLogsData, PostApiSecurityLogsMyLogsResponses, PostApiSecurityLogsMyLogsErrors, DeleteApiMultiTenancyTenantsByIdData, DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsByIdData, GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, PutApiMultiTenancyTenantsByIdData, PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsData, GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, PostApiMultiTenancyTenantsData, PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiSettingManagementTimezoneData, GetApiSettingManagementTimezoneResponses, GetApiSettingManagementTimezoneErrors, PostApiSettingManagementTimezoneData, PostApiSettingManagementTimezoneResponses, PostApiSettingManagementTimezoneErrors, GetApiSettingManagementTimezoneTimezonesData, GetApiSettingManagementTimezoneTimezonesResponses, GetApiSettingManagementTimezoneTimezonesErrors, DeleteApiIdentityUsersByIdData, DeleteApiIdentityUsersByIdResponses, DeleteApiIdentityUsersByIdErrors, GetApiIdentityUsersByIdData, GetApiIdentityUsersByIdResponses, GetApiIdentityUsersByIdErrors, PutApiIdentityUsersByIdData, PutApiIdentityUsersByIdResponses, PutApiIdentityUsersByIdErrors, GetApiIdentityUsersData, GetApiIdentityUsersResponses, GetApiIdentityUsersErrors, PostApiIdentityUsersData, PostApiIdentityUsersResponses, PostApiIdentityUsersErrors, GetApiIdentityUsersByIdRolesData, GetApiIdentityUsersByIdRolesResponses, GetApiIdentityUsersByIdRolesErrors, PutApiIdentityUsersByIdRolesData, PutApiIdentityUsersByIdRolesResponses, PutApiIdentityUsersByIdRolesErrors, GetApiIdentityUsersAssignableRolesData, GetApiIdentityUsersAssignableRolesResponses, GetApiIdentityUsersAssignableRolesErrors, GetApiIdentityUsersByUsernameByUserNameData, GetApiIdentityUsersByUsernameByUserNameResponses, GetApiIdentityUsersByUsernameByUserNameErrors, GetApiIdentityUsersByEmailByEmailData, GetApiIdentityUsersByEmailByEmailResponses, GetApiIdentityUsersByEmailByEmailErrors, GetApiIdentityUsersByUserIdClaimsData, GetApiIdentityUsersByUserIdClaimsResponses, GetApiIdentityUsersByUserIdClaimsErrors, PostApiIdentityUsersByUserIdClaimsData, PostApiIdentityUsersByUserIdClaimsResponses, PostApiIdentityUsersByUserIdClaimsErrors, DeleteApiIdentityUsersByUserIdClaimsByIdData, DeleteApiIdentityUsersByUserIdClaimsByIdResponses, DeleteApiIdentityUsersByUserIdClaimsByIdErrors, GetApiIdentityUsersByUserIdClaimsByIdData, GetApiIdentityUsersByUserIdClaimsByIdResponses, GetApiIdentityUsersByUserIdClaimsByIdErrors, PutApiIdentityUsersByUserIdClaimsByIdData, PutApiIdentityUsersByUserIdClaimsByIdResponses, PutApiIdentityUsersByUserIdClaimsByIdErrors, GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdData, GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdResponses, GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdErrors, PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeData, PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeResponses, PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeErrors, GetApiIdentityUserDetailsByIdData, GetApiIdentityUserDetailsByIdResponses, GetApiIdentityUserDetailsByIdErrors, GetApiIdentityUsersLookupByIdData, GetApiIdentityUsersLookupByIdResponses, GetApiIdentityUsersLookupByIdErrors, GetApiIdentityUsersLookupByUsernameByUserNameData, GetApiIdentityUsersLookupByUsernameByUserNameResponses, GetApiIdentityUsersLookupByUsernameByUserNameErrors, GetApiIdentityUsersLookupSearchData, GetApiIdentityUsersLookupSearchResponses, GetApiIdentityUsersLookupSearchErrors, GetApiIdentityUsersLookupCountData, GetApiIdentityUsersLookupCountResponses, GetApiIdentityUsersLookupCountErrors, PostApiIdentityUsersByIdUnlockData, PostApiIdentityUsersByIdUnlockErrors, PostApiUserQueryUsersData, PostApiUserQueryUsersResponses, PostApiUserQueryUsersErrors, PostApiUserQueryUsersByIdData, PostApiUserQueryUsersByIdResponses, PostApiUserQueryUsersByIdErrors, GetApiIdpWindowsAuthCurrentWindowsUserData, GetApiIdpWindowsAuthCurrentWindowsUserResponses, GetApiIdpWindowsAuthCurrentWindowsUserErrors, GetApiIdpWindowsAuthWindowsUserDetailsData, GetApiIdpWindowsAuthWindowsUserDetailsResponses, GetApiIdpWindowsAuthWindowsUserDetailsErrors, PostApiIdpWindowsAuthIsServiceAvailableData, PostApiIdpWindowsAuthIsServiceAvailableResponses, PostApiIdpWindowsAuthIsServiceAvailableErrors } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export const getApiAbpApiDefinition = <ThrowOnError extends boolean = false>(options?: Options<GetApiAbpApiDefinitionData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAbpApiDefinitionResponses, GetApiAbpApiDefinitionErrors, ThrowOnError>({
        url: '/api/abp/api-definition',
        ...options
    });
};

export const getApiAbpApplicationConfiguration = <ThrowOnError extends boolean = false>(options?: Options<GetApiAbpApplicationConfigurationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, ThrowOnError>({
        url: '/api/abp/application-configuration',
        ...options
    });
};

export const getApiAbpApplicationLocalization = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpApplicationLocalizationData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpApplicationLocalizationResponses, GetApiAbpApplicationLocalizationErrors, ThrowOnError>({
        url: '/api/abp/application-localization',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByNameByName = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByNameByNameData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-name/{name}',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByIdById = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByIdByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-id/{id}',
        ...options
    });
};

export const getApiMyAccountLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiMyAccountLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMyAccountLogoutResponses, unknown, ThrowOnError>({
        url: '/api/my-account/logout',
        ...options
    });
};

export const postApiMyAccountLogin = <ThrowOnError extends boolean = false>(options: Options<PostApiMyAccountLoginData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiMyAccountLoginResponses, unknown, ThrowOnError>({
        url: '/api/my-account/login',
        ...options
    });
};

export const postApiMyAccountCheckPassword = <ThrowOnError extends boolean = false>(options: Options<PostApiMyAccountCheckPasswordData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiMyAccountCheckPasswordResponses, unknown, ThrowOnError>({
        url: '/api/my-account/check-password',
        ...options
    });
};

export const postApiMyAccountLogoutByUserId = <ThrowOnError extends boolean = false>(options: Options<PostApiMyAccountLogoutByUserIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiMyAccountLogoutByUserIdResponses, unknown, ThrowOnError>({
        url: '/api/my-account/logout/{userId}',
        ...options
    });
};

export const postApiMyAccountLogoutAllSessions = <ThrowOnError extends boolean = false>(options?: Options<PostApiMyAccountLogoutAllSessionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMyAccountLogoutAllSessionsResponses, unknown, ThrowOnError>({
        url: '/api/my-account/logout-all-sessions',
        ...options
    });
};

export const postApiAccountRegister = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountRegisterData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, ThrowOnError>({
        url: '/api/account/register',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountSendPasswordResetCode = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountSendPasswordResetCodeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, ThrowOnError>({
        url: '/api/account/send-password-reset-code',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountVerifyPasswordResetToken = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountVerifyPasswordResetTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, ThrowOnError>({
        url: '/api/account/verify-password-reset-token',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountResetPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, ThrowOnError>({
        url: '/api/account/reset-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiActiveDirectorySettings = <ThrowOnError extends boolean = false>(options?: Options<GetApiActiveDirectorySettingsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiActiveDirectorySettingsResponses, GetApiActiveDirectorySettingsErrors, ThrowOnError>({
        url: '/api/active-directory-settings',
        ...options
    });
};

export const putApiActiveDirectorySettings = <ThrowOnError extends boolean = false>(options?: Options<PutApiActiveDirectorySettingsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiActiveDirectorySettingsResponses, PutApiActiveDirectorySettingsErrors, ThrowOnError>({
        url: '/api/active-directory-settings',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiActiveDirectorySettingsTestConnection = <ThrowOnError extends boolean = false>(options?: Options<PostApiActiveDirectorySettingsTestConnectionData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiActiveDirectorySettingsTestConnectionResponses, PostApiActiveDirectorySettingsTestConnectionErrors, ThrowOnError>({
        url: '/api/active-directory-settings/test-connection',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdpActiveDirectorySettings = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdpActiveDirectorySettingsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdpActiveDirectorySettingsResponses, GetApiIdpActiveDirectorySettingsErrors, ThrowOnError>({
        url: '/api/idp/active-directory-settings',
        ...options
    });
};

export const putApiIdpActiveDirectorySettings = <ThrowOnError extends boolean = false>(options?: Options<PutApiIdpActiveDirectorySettingsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiIdpActiveDirectorySettingsResponses, PutApiIdpActiveDirectorySettingsErrors, ThrowOnError>({
        url: '/api/idp/active-directory-settings',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiIdpActiveDirectorySettingsTestConnection = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdpActiveDirectorySettingsTestConnectionData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdpActiveDirectorySettingsTestConnectionResponses, PostApiIdpActiveDirectorySettingsTestConnectionErrors, ThrowOnError>({
        url: '/api/idp/active-directory-settings/test-connection',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getAdminClaims = <ThrowOnError extends boolean = false>(options?: Options<GetAdminClaimsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminClaimsResponses, unknown, ThrowOnError>({
        url: '/admin/claims',
        ...options
    });
};

export const getAdminUsers = <ThrowOnError extends boolean = false>(options?: Options<GetAdminUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminUsersResponses, unknown, ThrowOnError>({
        url: '/admin/users',
        ...options
    });
};

export const getAdminUsersRoles = <ThrowOnError extends boolean = false>(options?: Options<GetAdminUsersRolesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminUsersRolesResponses, unknown, ThrowOnError>({
        url: '/admin/users/roles',
        ...options
    });
};

export const getAdminClients = <ThrowOnError extends boolean = false>(options?: Options<GetAdminClientsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminClientsResponses, unknown, ThrowOnError>({
        url: '/admin/clients',
        ...options
    });
};

export const getAdminClientsResources = <ThrowOnError extends boolean = false>(options?: Options<GetAdminClientsResourcesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminClientsResourcesResponses, unknown, ThrowOnError>({
        url: '/admin/clients/resources',
        ...options
    });
};

export const getAdminClientsScopes = <ThrowOnError extends boolean = false>(options?: Options<GetAdminClientsScopesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminClientsScopesResponses, unknown, ThrowOnError>({
        url: '/admin/clients/scopes',
        ...options
    });
};

export const getAdminTenants = <ThrowOnError extends boolean = false>(options?: Options<GetAdminTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminTenantsResponses, unknown, ThrowOnError>({
        url: '/admin/tenants',
        ...options
    });
};

export const getAdminSettings = <ThrowOnError extends boolean = false>(options?: Options<GetAdminSettingsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminSettingsResponses, unknown, ThrowOnError>({
        url: '/admin/settings',
        ...options
    });
};

export const postApiAuditLogsList = <ThrowOnError extends boolean = false>(options?: Options<PostApiAuditLogsListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAuditLogsListResponses, PostApiAuditLogsListErrors, ThrowOnError>({
        url: '/api/audit-logs/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAuditLogsById = <ThrowOnError extends boolean = false>(options: Options<GetApiAuditLogsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAuditLogsByIdResponses, GetApiAuditLogsByIdErrors, ThrowOnError>({
        url: '/api/audit-logs/{id}',
        ...options
    });
};

export const postApiIdentityClaimTypesList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdentityClaimTypesListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdentityClaimTypesListResponses, PostApiIdentityClaimTypesListErrors, ThrowOnError>({
        url: '/api/identity/claim-types/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdentityClaimTypesById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdentityClaimTypesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdentityClaimTypesByIdResponses, DeleteApiIdentityClaimTypesByIdErrors, ThrowOnError>({
        url: '/api/identity/claim-types/{id}',
        ...options
    });
};

export const getApiIdentityClaimTypesById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityClaimTypesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityClaimTypesByIdResponses, GetApiIdentityClaimTypesByIdErrors, ThrowOnError>({
        url: '/api/identity/claim-types/{id}',
        ...options
    });
};

export const putApiIdentityClaimTypesById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityClaimTypesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityClaimTypesByIdResponses, PutApiIdentityClaimTypesByIdErrors, ThrowOnError>({
        url: '/api/identity/claim-types/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdentityClaimTypes = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdentityClaimTypesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdentityClaimTypesResponses, PostApiIdentityClaimTypesErrors, ThrowOnError>({
        url: '/api/identity/claim-types',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiDashboardUsersCount = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardUsersCountData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardUsersCountResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/users/count',
        ...options
    });
};

export const getApiDashboardClientsCount = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardClientsCountData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardClientsCountResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/clients/count',
        ...options
    });
};

export const getApiDashboardLoginsRecent = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardLoginsRecentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardLoginsRecentResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/logins/recent',
        ...options
    });
};

export const getApiDashboardLoginsFailedCount = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardLoginsFailedCountData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardLoginsFailedCountResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/logins/failed/count',
        ...options
    });
};

export const getApiDashboardRolesDistribution = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardRolesDistributionData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardRolesDistributionResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/roles/distribution',
        ...options
    });
};

export const getApiDashboardLoginsFailedRecent = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardLoginsFailedRecentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardLoginsFailedRecentResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/logins/failed/recent',
        ...options
    });
};

export const getApiDashboardUsersGrowth = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardUsersGrowthData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardUsersGrowthResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/users/growth',
        ...options
    });
};

export const getApiDashboardClientsTopLogins = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardClientsTopLoginsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardClientsTopLoginsResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/clients/top-logins',
        ...options
    });
};

export const getApiDashboardSecurityOverview = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardSecurityOverviewData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardSecurityOverviewResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/security/overview',
        ...options
    });
};

export const getApiDashboardUsersActivity = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardUsersActivityData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardUsersActivityResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/users/activity',
        ...options
    });
};

export const getApiDashboardClientsPerformance = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardClientsPerformanceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardClientsPerformanceResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/clients/performance',
        ...options
    });
};

export const getApiDashboardSecurityThreats = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardSecurityThreatsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardSecurityThreatsResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/security/threats',
        ...options
    });
};

export const getApiDashboardSystemHealth = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardSystemHealthData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardSystemHealthResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/system/health',
        ...options
    });
};

export const getApiDashboardAnalyticsHourlyActivity = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardAnalyticsHourlyActivityData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardAnalyticsHourlyActivityResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/analytics/hourly-activity',
        ...options
    });
};

export const postApiAccountDynamicClaimsRefresh = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountDynamicClaimsRefreshData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, ThrowOnError>({
        url: '/api/account/dynamic-claims/refresh',
        ...options
    });
};

export const getApiSettingManagementEmailing = <ThrowOnError extends boolean = false>(options?: Options<GetApiSettingManagementEmailingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiSettingManagementEmailingResponses, GetApiSettingManagementEmailingErrors, ThrowOnError>({
        url: '/api/setting-management/emailing',
        ...options
    });
};

export const postApiSettingManagementEmailing = <ThrowOnError extends boolean = false>(options?: Options<PostApiSettingManagementEmailingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSettingManagementEmailingResponses, PostApiSettingManagementEmailingErrors, ThrowOnError>({
        url: '/api/setting-management/emailing',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiSettingManagementEmailingSendTestEmail = <ThrowOnError extends boolean = false>(options?: Options<PostApiSettingManagementEmailingSendTestEmailData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSettingManagementEmailingSendTestEmailResponses, PostApiSettingManagementEmailingSendTestEmailErrors, ThrowOnError>({
        url: '/api/setting-management/emailing/send-test-email',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiChangeLog = <ThrowOnError extends boolean = false>(options?: Options<GetApiChangeLogData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiChangeLogResponses, unknown, ThrowOnError>({
        url: '/api/change-log',
        ...options
    });
};

export const getApiChangeLogEntity = <ThrowOnError extends boolean = false>(options?: Options<GetApiChangeLogEntityData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiChangeLogEntityResponses, unknown, ThrowOnError>({
        url: '/api/change-log/entity',
        ...options
    });
};

export const getApiChangeLogEntityLatest = <ThrowOnError extends boolean = false>(options?: Options<GetApiChangeLogEntityLatestData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiChangeLogEntityLatestResponses, unknown, ThrowOnError>({
        url: '/api/change-log/entity/latest',
        ...options
    });
};

export const getApiChangeLogEntityType = <ThrowOnError extends boolean = false>(options?: Options<GetApiChangeLogEntityTypeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiChangeLogEntityTypeResponses, unknown, ThrowOnError>({
        url: '/api/change-log/entity-type',
        ...options
    });
};

export const getApiChangeLogTimeRange = <ThrowOnError extends boolean = false>(options?: Options<GetApiChangeLogTimeRangeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiChangeLogTimeRangeResponses, unknown, ThrowOnError>({
        url: '/api/change-log/time-range',
        ...options
    });
};

export const getApiChangeLogPropertyHistory = <ThrowOnError extends boolean = false>(options?: Options<GetApiChangeLogPropertyHistoryData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiChangeLogPropertyHistoryResponses, unknown, ThrowOnError>({
        url: '/api/change-log/property-history',
        ...options
    });
};

export const getApiChangeLogEntityChanges = <ThrowOnError extends boolean = false>(options?: Options<GetApiChangeLogEntityChangesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiChangeLogEntityChangesResponses, unknown, ThrowOnError>({
        url: '/api/change-log/entity-changes',
        ...options
    });
};

export const deleteApiFeatureManagementFeatures = <ThrowOnError extends boolean = false>(options?: Options<DeleteApiFeatureManagementFeaturesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<DeleteApiFeatureManagementFeaturesResponses, DeleteApiFeatureManagementFeaturesErrors, ThrowOnError>({
        url: '/api/feature-management/features',
        ...options
    });
};

export const getApiFeatureManagementFeatures = <ThrowOnError extends boolean = false>(options?: Options<GetApiFeatureManagementFeaturesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiFeatureManagementFeaturesResponses, GetApiFeatureManagementFeaturesErrors, ThrowOnError>({
        url: '/api/feature-management/features',
        ...options
    });
};

export const putApiFeatureManagementFeatures = <ThrowOnError extends boolean = false>(options?: Options<PutApiFeatureManagementFeaturesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiFeatureManagementFeaturesResponses, PutApiFeatureManagementFeaturesErrors, ThrowOnError>({
        url: '/api/feature-management/features',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiHealthKubernetes = <ThrowOnError extends boolean = false>(options?: Options<GetApiHealthKubernetesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiHealthKubernetesResponses, unknown, ThrowOnError>({
        url: '/api/health/kubernetes',
        ...options
    });
};

export const get = <ThrowOnError extends boolean = false>(options?: Options<GetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetResponses, unknown, ThrowOnError>({
        url: '/',
        ...options
    });
};

export const getAdmin = <ThrowOnError extends boolean = false>(options?: Options<GetAdminData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminResponses, unknown, ThrowOnError>({
        url: '/admin',
        ...options
    });
};

export const postApiIdentityClaimsList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdentityClaimsListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdentityClaimsListResponses, PostApiIdentityClaimsListErrors, ThrowOnError>({
        url: '/api/identity/claims/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdentityClaimsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdentityClaimsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdentityClaimsByIdResponses, DeleteApiIdentityClaimsByIdErrors, ThrowOnError>({
        url: '/api/identity/claims/{id}',
        ...options
    });
};

export const getApiIdentityClaimsById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityClaimsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityClaimsByIdResponses, GetApiIdentityClaimsByIdErrors, ThrowOnError>({
        url: '/api/identity/claims/{id}',
        ...options
    });
};

export const putApiIdentityClaimsById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityClaimsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityClaimsByIdResponses, PutApiIdentityClaimsByIdErrors, ThrowOnError>({
        url: '/api/identity/claims/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdentityClaims = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdentityClaimsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdentityClaimsResponses, PostApiIdentityClaimsErrors, ThrowOnError>({
        url: '/api/identity/claims',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiImportCsv = <ThrowOnError extends boolean = false>(options?: Options<PostApiImportCsvData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiImportCsvResponses, PostApiImportCsvErrors, ThrowOnError>({
        ...formDataBodySerializer,
        url: '/api/import/csv',
        ...options,
        headers: {
            'Content-Type': null,
            ...options?.headers
        }
    });
};

export const getApiImportTemplates = <ThrowOnError extends boolean = false>(options?: Options<GetApiImportTemplatesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiImportTemplatesResponses, GetApiImportTemplatesErrors, ThrowOnError>({
        url: '/api/import/templates',
        ...options
    });
};

export const postApiAccountLogin = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountLoginResponses, PostApiAccountLoginErrors, ThrowOnError>({
        url: '/api/account/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAccountLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, ThrowOnError>({
        url: '/api/account/logout',
        ...options
    });
};

export const postApiAccountCheckPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountCheckPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, ThrowOnError>({
        url: '/api/account/check-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiOpeniddictApplicationsList = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictApplicationsListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictApplicationsListResponses, PostApiOpeniddictApplicationsListErrors, ThrowOnError>({
        url: '/api/openiddict/applications/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiOpeniddictApplicationsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiOpeniddictApplicationsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiOpeniddictApplicationsByIdResponses, DeleteApiOpeniddictApplicationsByIdErrors, ThrowOnError>({
        url: '/api/openiddict/applications/{id}',
        ...options
    });
};

export const getApiOpeniddictApplicationsById = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictApplicationsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictApplicationsByIdResponses, GetApiOpeniddictApplicationsByIdErrors, ThrowOnError>({
        url: '/api/openiddict/applications/{id}',
        ...options
    });
};

export const putApiOpeniddictApplicationsById = <ThrowOnError extends boolean = false>(options: Options<PutApiOpeniddictApplicationsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiOpeniddictApplicationsByIdResponses, PutApiOpeniddictApplicationsByIdErrors, ThrowOnError>({
        url: '/api/openiddict/applications/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiOpeniddictApplications = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictApplicationsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictApplicationsResponses, PostApiOpeniddictApplicationsErrors, ThrowOnError>({
        url: '/api/openiddict/applications',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiOpeniddictApplicationsPermissions = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictApplicationsPermissionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictApplicationsPermissionsResponses, GetApiOpeniddictApplicationsPermissionsErrors, ThrowOnError>({
        url: '/api/openiddict/applications/permissions',
        ...options
    });
};

export const getApiOpeniddictApplicationsByIdScopes = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictApplicationsByIdScopesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictApplicationsByIdScopesResponses, GetApiOpeniddictApplicationsByIdScopesErrors, ThrowOnError>({
        url: '/api/openiddict/applications/{id}/scopes',
        ...options
    });
};

export const putApiOpeniddictApplicationsByIdScopes = <ThrowOnError extends boolean = false>(options: Options<PutApiOpeniddictApplicationsByIdScopesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiOpeniddictApplicationsByIdScopesResponses, PutApiOpeniddictApplicationsByIdScopesErrors, ThrowOnError>({
        url: '/api/openiddict/applications/{id}/scopes',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiOpeniddictApplicationsAvailableScopes = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictApplicationsAvailableScopesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictApplicationsAvailableScopesResponses, GetApiOpeniddictApplicationsAvailableScopesErrors, ThrowOnError>({
        url: '/api/openiddict/applications/available-scopes',
        ...options
    });
};

export const getApiOpeniddictApplicationsClientTypes = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictApplicationsClientTypesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictApplicationsClientTypesResponses, GetApiOpeniddictApplicationsClientTypesErrors, ThrowOnError>({
        url: '/api/openiddict/applications/client-types',
        ...options
    });
};

export const getApiOpeniddictApplicationsClientTypesDetails = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictApplicationsClientTypesDetailsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictApplicationsClientTypesDetailsResponses, GetApiOpeniddictApplicationsClientTypesDetailsErrors, ThrowOnError>({
        url: '/api/openiddict/applications/client-types/details',
        ...options
    });
};

export const getApiOpeniddictAuthorizationsList = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictAuthorizationsListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictAuthorizationsListResponses, GetApiOpeniddictAuthorizationsListErrors, ThrowOnError>({
        url: '/api/openiddict/authorizations/list',
        ...options
    });
};

export const postApiOpeniddictAuthorizationsList = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictAuthorizationsListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictAuthorizationsListResponses, PostApiOpeniddictAuthorizationsListErrors, ThrowOnError>({
        url: '/api/openiddict/authorizations/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiOpeniddictAuthorizationsById = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictAuthorizationsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictAuthorizationsByIdResponses, GetApiOpeniddictAuthorizationsByIdErrors, ThrowOnError>({
        url: '/api/openiddict/authorizations/{id}',
        ...options
    });
};

export const getApiOpeniddictAuthorizationsByStringIdById = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictAuthorizationsByStringIdByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictAuthorizationsByStringIdByIdResponses, GetApiOpeniddictAuthorizationsByStringIdByIdErrors, ThrowOnError>({
        url: '/api/openiddict/authorizations/by-string-id/{id}',
        ...options
    });
};

export const postApiOpeniddictAuthorizationsRevoke = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictAuthorizationsRevokeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictAuthorizationsRevokeResponses, PostApiOpeniddictAuthorizationsRevokeErrors, ThrowOnError>({
        url: '/api/openiddict/authorizations/revoke',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiOpeniddictAuthorizationsPrune = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictAuthorizationsPruneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictAuthorizationsPruneResponses, PostApiOpeniddictAuthorizationsPruneErrors, ThrowOnError>({
        url: '/api/openiddict/authorizations/prune',
        ...options
    });
};

export const getApiOpeniddictClientPropertiesByApplicationId = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictClientPropertiesByApplicationIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictClientPropertiesByApplicationIdResponses, GetApiOpeniddictClientPropertiesByApplicationIdErrors, ThrowOnError>({
        url: '/api/openiddict/client-properties/{applicationId}',
        ...options
    });
};

export const postApiOpeniddictClientPropertiesByApplicationId = <ThrowOnError extends boolean = false>(options: Options<PostApiOpeniddictClientPropertiesByApplicationIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiOpeniddictClientPropertiesByApplicationIdResponses, PostApiOpeniddictClientPropertiesByApplicationIdErrors, ThrowOnError>({
        url: '/api/openiddict/client-properties/{applicationId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const deleteApiOpeniddictClientPropertiesByApplicationIdByKey = <ThrowOnError extends boolean = false>(options: Options<DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyResponses, DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyErrors, ThrowOnError>({
        url: '/api/openiddict/client-properties/{applicationId}/{key}',
        ...options
    });
};

export const getApiOpeniddictClientPropertiesByApplicationIdByKey = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictClientPropertiesByApplicationIdByKeyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictClientPropertiesByApplicationIdByKeyResponses, GetApiOpeniddictClientPropertiesByApplicationIdByKeyErrors, ThrowOnError>({
        url: '/api/openiddict/client-properties/{applicationId}/{key}',
        ...options
    });
};

export const putApiOpeniddictClientPropertiesByApplicationIdByKey = <ThrowOnError extends boolean = false>(options: Options<PutApiOpeniddictClientPropertiesByApplicationIdByKeyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiOpeniddictClientPropertiesByApplicationIdByKeyResponses, PutApiOpeniddictClientPropertiesByApplicationIdByKeyErrors, ThrowOnError>({
        url: '/api/openiddict/client-properties/{applicationId}/{key}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiOpeniddictClientSecretsByApplicationId = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictClientSecretsByApplicationIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictClientSecretsByApplicationIdResponses, GetApiOpeniddictClientSecretsByApplicationIdErrors, ThrowOnError>({
        url: '/api/openiddict/client-secrets/{applicationId}',
        ...options
    });
};

export const postApiOpeniddictClientSecretsByApplicationId = <ThrowOnError extends boolean = false>(options: Options<PostApiOpeniddictClientSecretsByApplicationIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiOpeniddictClientSecretsByApplicationIdResponses, PostApiOpeniddictClientSecretsByApplicationIdErrors, ThrowOnError>({
        url: '/api/openiddict/client-secrets/{applicationId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const deleteApiOpeniddictClientSecretsByApplicationIdBySecretId = <ThrowOnError extends boolean = false>(options: Options<DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdResponses, DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdErrors, ThrowOnError>({
        url: '/api/openiddict/client-secrets/{applicationId}/{secretId}',
        ...options
    });
};

export const postApiOpeniddictClientSecretsHash = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictClientSecretsHashData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictClientSecretsHashResponses, PostApiOpeniddictClientSecretsHashErrors, ThrowOnError>({
        url: '/api/openiddict/client-secrets/hash',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiOpeniddictClientSecretsGenerate = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictClientSecretsGenerateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictClientSecretsGenerateResponses, GetApiOpeniddictClientSecretsGenerateErrors, ThrowOnError>({
        url: '/api/openiddict/client-secrets/generate',
        ...options
    });
};

export const getApiOpeniddictGrantTypes = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictGrantTypesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, GetApiOpeniddictGrantTypesErrors, ThrowOnError>({
        url: '/api/openiddict/grant-types',
        ...options
    });
};

export const getApiOpeniddictGrantTypesApplicationsByApplicationId = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictGrantTypesApplicationsByApplicationIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, GetApiOpeniddictGrantTypesApplicationsByApplicationIdErrors, ThrowOnError>({
        url: '/api/openiddict/grant-types/applications/{applicationId}',
        ...options
    });
};

export const putApiOpeniddictGrantTypesApplicationsByApplicationId = <ThrowOnError extends boolean = false>(options: Options<PutApiOpeniddictGrantTypesApplicationsByApplicationIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<unknown, PutApiOpeniddictGrantTypesApplicationsByApplicationIdErrors, ThrowOnError>({
        url: '/api/openiddict/grant-types/applications/{applicationId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiOpeniddictGrantTypesValidateCombination = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictGrantTypesValidateCombinationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, GetApiOpeniddictGrantTypesValidateCombinationErrors, ThrowOnError>({
        url: '/api/openiddict/grant-types/validate-combination',
        ...options
    });
};

export const getApiOpeniddictGrantTypesRecommendedSettings = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictGrantTypesRecommendedSettingsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, GetApiOpeniddictGrantTypesRecommendedSettingsErrors, ThrowOnError>({
        url: '/api/openiddict/grant-types/recommended-settings',
        ...options
    });
};

export const getApiOpeniddictRequirements = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictRequirementsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictRequirementsResponses, GetApiOpeniddictRequirementsErrors, ThrowOnError>({
        url: '/api/openiddict/requirements',
        ...options
    });
};

export const postApiOpeniddictResourcesList = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictResourcesListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictResourcesListResponses, PostApiOpeniddictResourcesListErrors, ThrowOnError>({
        url: '/api/openiddict/resources/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiOpeniddictResourcesById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiOpeniddictResourcesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiOpeniddictResourcesByIdResponses, DeleteApiOpeniddictResourcesByIdErrors, ThrowOnError>({
        url: '/api/openiddict/resources/{id}',
        ...options
    });
};

export const getApiOpeniddictResourcesById = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictResourcesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictResourcesByIdResponses, GetApiOpeniddictResourcesByIdErrors, ThrowOnError>({
        url: '/api/openiddict/resources/{id}',
        ...options
    });
};

export const putApiOpeniddictResourcesById = <ThrowOnError extends boolean = false>(options: Options<PutApiOpeniddictResourcesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiOpeniddictResourcesByIdResponses, PutApiOpeniddictResourcesByIdErrors, ThrowOnError>({
        url: '/api/openiddict/resources/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiOpeniddictResources = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictResourcesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictResourcesResponses, PostApiOpeniddictResourcesErrors, ThrowOnError>({
        url: '/api/openiddict/resources',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiOpeniddictResourcesAvailableResources = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictResourcesAvailableResourcesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictResourcesAvailableResourcesResponses, GetApiOpeniddictResourcesAvailableResourcesErrors, ThrowOnError>({
        url: '/api/openiddict/resources/available-resources',
        ...options
    });
};

export const postApiOpeniddictScopesList = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictScopesListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictScopesListResponses, PostApiOpeniddictScopesListErrors, ThrowOnError>({
        url: '/api/openiddict/scopes/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiOpeniddictScopesById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiOpeniddictScopesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiOpeniddictScopesByIdResponses, DeleteApiOpeniddictScopesByIdErrors, ThrowOnError>({
        url: '/api/openiddict/scopes/{id}',
        ...options
    });
};

export const getApiOpeniddictScopesById = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictScopesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictScopesByIdResponses, GetApiOpeniddictScopesByIdErrors, ThrowOnError>({
        url: '/api/openiddict/scopes/{id}',
        ...options
    });
};

export const putApiOpeniddictScopesById = <ThrowOnError extends boolean = false>(options: Options<PutApiOpeniddictScopesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiOpeniddictScopesByIdResponses, PutApiOpeniddictScopesByIdErrors, ThrowOnError>({
        url: '/api/openiddict/scopes/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiOpeniddictScopes = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictScopesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictScopesResponses, PostApiOpeniddictScopesErrors, ThrowOnError>({
        url: '/api/openiddict/scopes',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiOpeniddictScopesAvailableResources = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictScopesAvailableResourcesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictScopesAvailableResourcesResponses, GetApiOpeniddictScopesAvailableResourcesErrors, ThrowOnError>({
        url: '/api/openiddict/scopes/available-resources',
        ...options
    });
};

export const getApiOpeniddictScopesByIdResources = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictScopesByIdResourcesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictScopesByIdResourcesResponses, GetApiOpeniddictScopesByIdResourcesErrors, ThrowOnError>({
        url: '/api/openiddict/scopes/{id}/resources',
        ...options
    });
};

export const putApiOpeniddictScopesByIdResources = <ThrowOnError extends boolean = false>(options: Options<PutApiOpeniddictScopesByIdResourcesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiOpeniddictScopesByIdResourcesResponses, PutApiOpeniddictScopesByIdResourcesErrors, ThrowOnError>({
        url: '/api/openiddict/scopes/{id}/resources',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiOpeniddictTokens = <ThrowOnError extends boolean = false>(options?: Options<GetApiOpeniddictTokensData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOpeniddictTokensResponses, GetApiOpeniddictTokensErrors, ThrowOnError>({
        url: '/api/openiddict/tokens',
        ...options
    });
};

export const getApiOpeniddictTokensById = <ThrowOnError extends boolean = false>(options: Options<GetApiOpeniddictTokensByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOpeniddictTokensByIdResponses, GetApiOpeniddictTokensByIdErrors, ThrowOnError>({
        url: '/api/openiddict/tokens/{id}',
        ...options
    });
};

export const postApiOpeniddictTokensRevoke = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictTokensRevokeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictTokensRevokeResponses, PostApiOpeniddictTokensRevokeErrors, ThrowOnError>({
        url: '/api/openiddict/tokens/revoke',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiOpeniddictTokensPrune = <ThrowOnError extends boolean = false>(options?: Options<PostApiOpeniddictTokensPruneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOpeniddictTokensPruneResponses, PostApiOpeniddictTokensPruneErrors, ThrowOnError>({
        url: '/api/openiddict/tokens/prune',
        ...options
    });
};

export const getApiPermissionCheck = <ThrowOnError extends boolean = false>(options?: Options<GetApiPermissionCheckData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPermissionCheckResponses, unknown, ThrowOnError>({
        url: '/api/permission-check',
        ...options
    });
};

export const getApiPermissionCheckMultiple = <ThrowOnError extends boolean = false>(options?: Options<GetApiPermissionCheckMultipleData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPermissionCheckMultipleResponses, unknown, ThrowOnError>({
        url: '/api/permission-check/multiple',
        ...options
    });
};

export const getApiPermissionCheckUser = <ThrowOnError extends boolean = false>(options?: Options<GetApiPermissionCheckUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPermissionCheckUserResponses, unknown, ThrowOnError>({
        url: '/api/permission-check/user',
        ...options
    });
};

export const postApiPermissionCheckInvalidateCache = <ThrowOnError extends boolean = false>(options?: Options<PostApiPermissionCheckInvalidateCacheData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPermissionCheckInvalidateCacheResponses, unknown, ThrowOnError>({
        url: '/api/permission-check/invalidate-cache',
        ...options
    });
};

export const getApiPermissionManagementPermissions = <ThrowOnError extends boolean = false>(options?: Options<GetApiPermissionManagementPermissionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPermissionManagementPermissionsResponses, GetApiPermissionManagementPermissionsErrors, ThrowOnError>({
        url: '/api/permission-management/permissions',
        ...options
    });
};

export const putApiPermissionManagementPermissions = <ThrowOnError extends boolean = false>(options?: Options<PutApiPermissionManagementPermissionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiPermissionManagementPermissionsResponses, PutApiPermissionManagementPermissionsErrors, ThrowOnError>({
        url: '/api/permission-management/permissions',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options
    });
};

export const putApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<PutApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountMyProfileChangePassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountMyProfileChangePasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, ThrowOnError>({
        url: '/api/account/my-profile/change-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdentityRolesAll = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityRolesAllData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityRolesAllResponses, GetApiIdentityRolesAllErrors, ThrowOnError>({
        url: '/api/identity/roles/all',
        ...options
    });
};

export const getApiIdentityRoles = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityRolesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityRolesResponses, GetApiIdentityRolesErrors, ThrowOnError>({
        url: '/api/identity/roles',
        ...options
    });
};

export const postApiIdentityRoles = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdentityRolesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdentityRolesResponses, PostApiIdentityRolesErrors, ThrowOnError>({
        url: '/api/identity/roles',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdentityRolesById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdentityRolesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdentityRolesByIdResponses, DeleteApiIdentityRolesByIdErrors, ThrowOnError>({
        url: '/api/identity/roles/{id}',
        ...options
    });
};

export const getApiIdentityRolesById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityRolesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityRolesByIdResponses, GetApiIdentityRolesByIdErrors, ThrowOnError>({
        url: '/api/identity/roles/{id}',
        ...options
    });
};

export const putApiIdentityRolesById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityRolesByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityRolesByIdResponses, PutApiIdentityRolesByIdErrors, ThrowOnError>({
        url: '/api/identity/roles/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiRoleApplicationsByUserByUserId = <ThrowOnError extends boolean = false>(options: Options<GetApiRoleApplicationsByUserByUserIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiRoleApplicationsByUserByUserIdResponses, unknown, ThrowOnError>({
        url: '/api/role-applications/by-user/{userId}',
        ...options
    });
};

export const getApiRoleApplicationsByRoleByRoleId = <ThrowOnError extends boolean = false>(options: Options<GetApiRoleApplicationsByRoleByRoleIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiRoleApplicationsByRoleByRoleIdResponses, unknown, ThrowOnError>({
        url: '/api/role-applications/by-role/{roleId}',
        ...options
    });
};

export const postApiRoleApplicationsUpdate = <ThrowOnError extends boolean = false>(options?: Options<PostApiRoleApplicationsUpdateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiRoleApplicationsUpdateResponses, unknown, ThrowOnError>({
        url: '/api/role-applications/update',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdpRoleApplicationRoleApplicationsByUserByUserId = <ThrowOnError extends boolean = false>(options: Options<GetApiIdpRoleApplicationRoleApplicationsByUserByUserIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdpRoleApplicationRoleApplicationsByUserByUserIdResponses, GetApiIdpRoleApplicationRoleApplicationsByUserByUserIdErrors, ThrowOnError>({
        url: '/api/idp/role-application/role-applications-by-user/{userId}',
        ...options
    });
};

export const getApiIdpRoleApplicationRoleApplicationsByRoleByRoleId = <ThrowOnError extends boolean = false>(options: Options<GetApiIdpRoleApplicationRoleApplicationsByRoleByRoleIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdpRoleApplicationRoleApplicationsByRoleByRoleIdResponses, GetApiIdpRoleApplicationRoleApplicationsByRoleByRoleIdErrors, ThrowOnError>({
        url: '/api/idp/role-application/role-applications-by-role/{roleId}',
        ...options
    });
};

export const putApiIdpRoleApplicationRoleApplications = <ThrowOnError extends boolean = false>(options?: Options<PutApiIdpRoleApplicationRoleApplicationsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiIdpRoleApplicationRoleApplicationsResponses, PutApiIdpRoleApplicationRoleApplicationsErrors, ThrowOnError>({
        url: '/api/idp/role-application/role-applications',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdentityRolesByRoleIdClaims = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityRolesByRoleIdClaimsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityRolesByRoleIdClaimsResponses, GetApiIdentityRolesByRoleIdClaimsErrors, ThrowOnError>({
        url: '/api/identity/roles/{roleId}/claims',
        ...options
    });
};

export const postApiIdentityRolesByRoleIdClaims = <ThrowOnError extends boolean = false>(options: Options<PostApiIdentityRolesByRoleIdClaimsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiIdentityRolesByRoleIdClaimsResponses, PostApiIdentityRolesByRoleIdClaimsErrors, ThrowOnError>({
        url: '/api/identity/roles/{roleId}/claims',
        ...options
    });
};

export const deleteApiIdentityRolesByRoleIdClaimsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdentityRolesByRoleIdClaimsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdentityRolesByRoleIdClaimsByIdResponses, DeleteApiIdentityRolesByRoleIdClaimsByIdErrors, ThrowOnError>({
        url: '/api/identity/roles/{roleId}/claims/{id}',
        ...options
    });
};

export const getApiIdentityRolesByRoleIdClaimsById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityRolesByRoleIdClaimsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityRolesByRoleIdClaimsByIdResponses, GetApiIdentityRolesByRoleIdClaimsByIdErrors, ThrowOnError>({
        url: '/api/identity/roles/{roleId}/claims/{id}',
        ...options
    });
};

export const putApiIdentityRolesByRoleIdClaimsById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityRolesByRoleIdClaimsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityRolesByRoleIdClaimsByIdResponses, PutApiIdentityRolesByRoleIdClaimsByIdErrors, ThrowOnError>({
        url: '/api/identity/roles/{roleId}/claims/{id}',
        ...options
    });
};

export const postApiSecurityLogsList = <ThrowOnError extends boolean = false>(options?: Options<PostApiSecurityLogsListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSecurityLogsListResponses, PostApiSecurityLogsListErrors, ThrowOnError>({
        url: '/api/security-logs/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiSecurityLogsById = <ThrowOnError extends boolean = false>(options: Options<GetApiSecurityLogsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiSecurityLogsByIdResponses, GetApiSecurityLogsByIdErrors, ThrowOnError>({
        url: '/api/security-logs/{id}',
        ...options
    });
};

export const postApiSecurityLogsMyLogs = <ThrowOnError extends boolean = false>(options?: Options<PostApiSecurityLogsMyLogsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSecurityLogsMyLogsResponses, PostApiSecurityLogsMyLogsErrors, ThrowOnError>({
        url: '/api/security-logs/my-logs',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const getApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const putApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<GetApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options
    });
};

export const postApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<PostApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const putApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiSettingManagementTimezone = <ThrowOnError extends boolean = false>(options?: Options<GetApiSettingManagementTimezoneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiSettingManagementTimezoneResponses, GetApiSettingManagementTimezoneErrors, ThrowOnError>({
        url: '/api/setting-management/timezone',
        ...options
    });
};

export const postApiSettingManagementTimezone = <ThrowOnError extends boolean = false>(options?: Options<PostApiSettingManagementTimezoneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSettingManagementTimezoneResponses, PostApiSettingManagementTimezoneErrors, ThrowOnError>({
        url: '/api/setting-management/timezone',
        ...options
    });
};

export const getApiSettingManagementTimezoneTimezones = <ThrowOnError extends boolean = false>(options?: Options<GetApiSettingManagementTimezoneTimezonesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiSettingManagementTimezoneTimezonesResponses, GetApiSettingManagementTimezoneTimezonesErrors, ThrowOnError>({
        url: '/api/setting-management/timezone/timezones',
        ...options
    });
};

export const deleteApiIdentityUsersById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdentityUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdentityUsersByIdResponses, DeleteApiIdentityUsersByIdErrors, ThrowOnError>({
        url: '/api/identity/users/{id}',
        ...options
    });
};

export const getApiIdentityUsersById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByIdResponses, GetApiIdentityUsersByIdErrors, ThrowOnError>({
        url: '/api/identity/users/{id}',
        ...options
    });
};

export const putApiIdentityUsersById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityUsersByIdResponses, PutApiIdentityUsersByIdErrors, ThrowOnError>({
        url: '/api/identity/users/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiIdentityUsers = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityUsersResponses, GetApiIdentityUsersErrors, ThrowOnError>({
        url: '/api/identity/users',
        ...options
    });
};

export const postApiIdentityUsers = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdentityUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdentityUsersResponses, PostApiIdentityUsersErrors, ThrowOnError>({
        url: '/api/identity/users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdentityUsersByIdRoles = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByIdRolesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByIdRolesResponses, GetApiIdentityUsersByIdRolesErrors, ThrowOnError>({
        url: '/api/identity/users/{id}/roles',
        ...options
    });
};

export const putApiIdentityUsersByIdRoles = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityUsersByIdRolesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityUsersByIdRolesResponses, PutApiIdentityUsersByIdRolesErrors, ThrowOnError>({
        url: '/api/identity/users/{id}/roles',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiIdentityUsersAssignableRoles = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityUsersAssignableRolesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityUsersAssignableRolesResponses, GetApiIdentityUsersAssignableRolesErrors, ThrowOnError>({
        url: '/api/identity/users/assignable-roles',
        ...options
    });
};

export const getApiIdentityUsersByUsernameByUserName = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByUsernameByUserNameData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByUsernameByUserNameResponses, GetApiIdentityUsersByUsernameByUserNameErrors, ThrowOnError>({
        url: '/api/identity/users/by-username/{userName}',
        ...options
    });
};

export const getApiIdentityUsersByEmailByEmail = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByEmailByEmailData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByEmailByEmailResponses, GetApiIdentityUsersByEmailByEmailErrors, ThrowOnError>({
        url: '/api/identity/users/by-email/{email}',
        ...options
    });
};

export const getApiIdentityUsersByUserIdClaims = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByUserIdClaimsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByUserIdClaimsResponses, GetApiIdentityUsersByUserIdClaimsErrors, ThrowOnError>({
        url: '/api/identity/users/{userId}/claims',
        ...options
    });
};

export const postApiIdentityUsersByUserIdClaims = <ThrowOnError extends boolean = false>(options: Options<PostApiIdentityUsersByUserIdClaimsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiIdentityUsersByUserIdClaimsResponses, PostApiIdentityUsersByUserIdClaimsErrors, ThrowOnError>({
        url: '/api/identity/users/{userId}/claims',
        ...options
    });
};

export const deleteApiIdentityUsersByUserIdClaimsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdentityUsersByUserIdClaimsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdentityUsersByUserIdClaimsByIdResponses, DeleteApiIdentityUsersByUserIdClaimsByIdErrors, ThrowOnError>({
        url: '/api/identity/users/{userId}/claims/{id}',
        ...options
    });
};

export const getApiIdentityUsersByUserIdClaimsById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersByUserIdClaimsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersByUserIdClaimsByIdResponses, GetApiIdentityUsersByUserIdClaimsByIdErrors, ThrowOnError>({
        url: '/api/identity/users/{userId}/claims/{id}',
        ...options
    });
};

export const putApiIdentityUsersByUserIdClaimsById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdentityUsersByUserIdClaimsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdentityUsersByUserIdClaimsByIdResponses, PutApiIdentityUsersByUserIdClaimsByIdErrors, ThrowOnError>({
        url: '/api/identity/users/{userId}/claims/{id}',
        ...options
    });
};

export const getApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserId = <ThrowOnError extends boolean = false>(options: Options<GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdResponses, GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdErrors, ThrowOnError>({
        url: '/api/idp/user-concurrent-login/concurrent-login-prevention-mode/{userId}',
        ...options
    });
};

export const postApiIdpUserConcurrentLoginSetConcurrentLoginPreventionMode = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeResponses, PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeErrors, ThrowOnError>({
        url: '/api/idp/user-concurrent-login/set-concurrent-login-prevention-mode',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdentityUserDetailsById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUserDetailsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUserDetailsByIdResponses, GetApiIdentityUserDetailsByIdErrors, ThrowOnError>({
        url: '/api/identity/user-details/{id}',
        ...options
    });
};

export const getApiIdentityUsersLookupById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersLookupByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersLookupByIdResponses, GetApiIdentityUsersLookupByIdErrors, ThrowOnError>({
        url: '/api/identity/users/lookup/{id}',
        ...options
    });
};

export const getApiIdentityUsersLookupByUsernameByUserName = <ThrowOnError extends boolean = false>(options: Options<GetApiIdentityUsersLookupByUsernameByUserNameData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdentityUsersLookupByUsernameByUserNameResponses, GetApiIdentityUsersLookupByUsernameByUserNameErrors, ThrowOnError>({
        url: '/api/identity/users/lookup/by-username/{userName}',
        ...options
    });
};

export const getApiIdentityUsersLookupSearch = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityUsersLookupSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityUsersLookupSearchResponses, GetApiIdentityUsersLookupSearchErrors, ThrowOnError>({
        url: '/api/identity/users/lookup/search',
        ...options
    });
};

export const getApiIdentityUsersLookupCount = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdentityUsersLookupCountData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdentityUsersLookupCountResponses, GetApiIdentityUsersLookupCountErrors, ThrowOnError>({
        url: '/api/identity/users/lookup/count',
        ...options
    });
};

export const postApiIdentityUsersByIdUnlock = <ThrowOnError extends boolean = false>(options: Options<PostApiIdentityUsersByIdUnlockData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, PostApiIdentityUsersByIdUnlockErrors, ThrowOnError>({
        url: '/api/identity/users/{id}/unlock',
        ...options
    });
};

export const postApiUserQueryUsers = <ThrowOnError extends boolean = false>(options?: Options<PostApiUserQueryUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUserQueryUsersResponses, PostApiUserQueryUsersErrors, ThrowOnError>({
        url: '/api/user-query/users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiUserQueryUsersById = <ThrowOnError extends boolean = false>(options: Options<PostApiUserQueryUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiUserQueryUsersByIdResponses, PostApiUserQueryUsersByIdErrors, ThrowOnError>({
        url: '/api/user-query/users/{id}',
        ...options
    });
};

export const getApiIdpWindowsAuthCurrentWindowsUser = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdpWindowsAuthCurrentWindowsUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdpWindowsAuthCurrentWindowsUserResponses, GetApiIdpWindowsAuthCurrentWindowsUserErrors, ThrowOnError>({
        url: '/api/idp/windows-auth/current-windows-user',
        ...options
    });
};

export const getApiIdpWindowsAuthWindowsUserDetails = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdpWindowsAuthWindowsUserDetailsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdpWindowsAuthWindowsUserDetailsResponses, GetApiIdpWindowsAuthWindowsUserDetailsErrors, ThrowOnError>({
        url: '/api/idp/windows-auth/windows-user-details',
        ...options
    });
};

export const postApiIdpWindowsAuthIsServiceAvailable = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdpWindowsAuthIsServiceAvailableData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdpWindowsAuthIsServiceAvailableResponses, PostApiIdpWindowsAuthIsServiceAvailableErrors, ThrowOnError>({
        url: '/api/idp/windows-auth/is-service-available',
        ...options
    });
};