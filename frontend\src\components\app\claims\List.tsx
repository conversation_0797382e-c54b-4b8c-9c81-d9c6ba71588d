'use client'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useState } from 'react'

import { type IdentityClaimTypeDto } from '@/client'
import { type PaginationState, type SortingState } from '@tanstack/react-table'

import { useToast } from '@/lib/useToast'
import { Delete } from './Delete'

import { DataTable } from '@/components/data-table/DataTable'
import { NotionFilter } from '@/components/data-table/NotionFilter'
import { TableSkeleton } from '@/components/ui/TableSkeleton'
import { useIdentityClaimTypes } from '@/lib/hooks/useIdentityClaimTypes'
import { type FilterCondition } from '@/lib/interfaces/IFilterCondition'
import { useQueryClient } from '@tanstack/react-query'
import { Add } from './Add'
import { getColumns } from './Columns'
import { Edit } from './Edit'

export const ClaimList = () => {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const [searchStr, setSearchStr] = useState<string>('')
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])
  const [userActionDialog, setUserActionDialog] = useState<{
    dataId: string
    dataEdit: IdentityClaimTypeDto
    dialogType?: 'edit' | 'permission' | 'delete'
  } | null>()

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })

  // Initialize sorting state
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'name', desc: false }
  ])

  const { isLoading, data } = useIdentityClaimTypes(
    pagination.pageIndex,
    pagination.pageSize,
    filterConditions
  )

  // Handler for user actions (edit, permission, delete)
  const handleUserAction = (dataId: string, dataEdit: IdentityClaimTypeDto, dialogType: 'edit' | 'permission' | 'delete') => {
    setUserActionDialog({
      dataId,
      dataEdit,
      dialogType,
    })
  }

  // Get columns with the action handler
  const columns = getColumns(handleUserAction)

  const handleSearch = (value: string) => {
    // Always update the search string for UI consistency
    setSearchStr(value)

    // Create a search filter condition if there's a search value
    // First, remove any existing name filter
    const existingFilters = filterConditions.filter(fc => fc.fieldName !== 'name')
    const newFilterConditions = [...existingFilters]

    // Only add the search filter if there's a value
    if (value) {
      newFilterConditions.push({
        fieldName: 'name',
        operator: 'Contains',
        value: value
      })
    }

    // Only update state if filters have changed
    const currentFiltersStr = JSON.stringify(filterConditions)
    const newFiltersStr = JSON.stringify(newFilterConditions)

    if (currentFiltersStr !== newFiltersStr) {
      setFilterConditions(newFilterConditions)
      setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search
    }
  }

  const handlePaginationChange = (newPagination: PaginationState) => {
    setPagination(newPagination)
  }

  // Handler for sorting change
  const handleSortingChange = (newSorting: SortingState) => {
    setSorting(newSorting)
    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change
  }

  // Handler for refreshing the data
  const handleRefresh = () => {
    // Invalidate the query to fetch fresh data
    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })
    // Show toast notification after a short delay to match the animation
    setTimeout(() => {
      toast({
        title: "Data refreshed",
        description: "The claims list has been refreshed.",
        variant: "success",
      })
    }, 100)
  }

  if (isLoading) return (
    <TableSkeleton
      rowCount={pagination.pageSize}
      columnCount={4}
      hasTitle={true}
      hasSearch={true}
      hasFilters={true}
      hasPagination={true}
      hasActions={true}
    />
  )

  // Ensure we have valid data to render
  const items = data?.items ?? [];
  const totalCount = data?.totalCount ?? 0;

  return (
    <>
      <div className="space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4">
        <DataTable
          title="Claims Types"
          columns={columns}
          data={items}
          totalCount={totalCount}
          isLoading={isLoading}
          manualPagination={true}
          manualSorting={true}
          pageSize={pagination.pageSize}
          onPaginationChange={handlePaginationChange}
          onSortingChange={handleSortingChange}
          sortingState={sorting}
          onSearch={handleSearch}
          searchValue={searchStr}
          customFilterbar={(props) => (
            <NotionFilter
              {...props}
              activeFilters={filterConditions}
              onServerFilter={(conditions) => {
                // Only update if the conditions have actually changed
                const currentStr = JSON.stringify(filterConditions);
                const newStr = JSON.stringify(conditions);

                if (currentStr !== newStr) {
                  setFilterConditions(conditions)
                  setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on filter change
                }
              }}
            />
          )}
          hideDefaultFilterbar={true}
          onRefresh={handleRefresh}
          enableRowSelection={false}
          actionButton={{
            // label: "Create New User",
            // We need to provide an onClick handler even though we're using a custom component
            onClick: () => { /* Required but not used */ },
            content: <Add />
          }}
        />
      </div>

      {userActionDialog && userActionDialog.dialogType === 'edit' && (
        <Edit
          dataId={userActionDialog.dataId}
          dataEdit={userActionDialog.dataEdit}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })
            setUserActionDialog(null)
          }}
        />
      )}
      {userActionDialog && userActionDialog.dialogType === 'delete' && (
        <Delete
          dataId={userActionDialog.dataId}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaimTypes] })
            setUserActionDialog(null)
          }}
        />
      )}
    </>
  )
}
