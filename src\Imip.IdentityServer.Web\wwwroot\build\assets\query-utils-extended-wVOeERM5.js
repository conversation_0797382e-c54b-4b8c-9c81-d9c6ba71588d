import"./App-De6zOdMU.js";function g(s,t="Error loading data",o="An unexpected error occurred"){let r=t,n=o;try{const e=s;e?.details?.error&&(r=e.details.error.message??t,n=e.details.error.details??o)}catch{}return{title:r,description:n}}function l(s){const{pageIndex:t,pageSize:o,sorting:r,filterConditions:n=[]}=s,e=r??"",c={operator:"And",conditions:n},a=[];if(r)try{JSON.parse(r).forEach(i=>{a.push({field:i.id,desc:i.desc})})}catch{}return{sorting:e,page:t+1,sort:a,filterGroup:c,maxResultCount:o}}export{g as e,l as g};
//# sourceMappingURL=query-utils-extended-wVOeERM5.js.map
