'use client'

import { type OpenIddictScopeDto } from '@/client'
import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'
import { RiMoreLine, RiPencilLine } from '@remixicon/react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

type DataEditActionProps = {
  dataId: string
  dataEdit: OpenIddictScopeDto
  onAction: (dataId: string, dataEdit: OpenIddictScopeDto, dialogType: 'edit' | 'permission' | 'delete') => void
  variant?: 'dropdown' | 'buttons'
}

export const Actions = ({ dataId, dataEdit, onAction, variant = 'dropdown' }: DataEditActionProps) => {
  const { can } = useGrantedPolicies()

  // For dropdown menu style (first image)
  if (variant === 'dropdown') {
    return (
      <div className="flex justify-end">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
            >
              <RiMoreLine className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[160px]">
            {can('IdentityServer.OpenIddictScopes.Edit') && (
              <DropdownMenuItem
                className="cursor-pointer text-sm"
                onClick={() => onAction(dataId, dataEdit, 'edit')}
              >
                Edit
              </DropdownMenuItem>
            )}
            {can('IdentityServer.OpenIddictScopes.Delete') && (
              <DropdownMenuItem
                className="cursor-pointer text-sm text-red-500"
                onClick={() => onAction(dataId, dataEdit, 'delete')}
              >
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  // For button group style (second image)
  return (
    <div className="flex items-center justify-end gap-1">
      {can('IdentityServer.OpenIddictScopes.Edit') && (
        <Button
          variant="primary"
          size="sm"
          className="flex items-center gap-1 px-2 py-1"
          onClick={() => onAction(dataId, dataEdit, 'edit')}
        >
          <RiPencilLine className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      )}
    </div>
  )
}
