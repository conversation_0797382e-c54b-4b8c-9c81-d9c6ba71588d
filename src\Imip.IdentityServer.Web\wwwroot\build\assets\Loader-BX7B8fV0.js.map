{"version": 3, "file": "Loader-BX7B8fV0.js", "sources": ["../../../../../frontend/node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm-browser/stringify.js", "../../../../../frontend/node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm-browser/rng.js", "../../../../../frontend/node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm-browser/native.js", "../../../../../frontend/node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm-browser/v4.js", "../../../../../frontend/src/components/ui/Error.tsx", "../../../../../frontend/src/components/ui/Loader.tsx"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n", "let getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n", "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default { randomUUID };\n", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n", "'use client'\r\nimport { router } from '@inertiajs/react'\r\nimport { Button } from './button'\r\n\r\nconst Error = () => {\r\n  return (\r\n    <div className=\"bg-base-200 flex flex-col-reverse items-center justify-center gap-1 px-4 py-24 md:gap-28 md:px-44 md:py-20 lg:flex-row lg:px-24 lg:py-24\">\r\n      <div className=\"relative w-full lg:pb-0 xl:w-1/2\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute\">\r\n            <h1 className=\"my-2 text-2xl font-bold text-primary\">\r\n              Looks like you have found the doorway to the great nothing\r\n            </h1>\r\n            <p className=\"text-base-content my-2\">\r\n              Sorry about that! Please visit our homepage to get where you need to go.\r\n            </p>\r\n            <Button\r\n              onClick={() => {\r\n                router.visit('/')\r\n              }}\r\n            >\r\n              Take me there!\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div>\r\n        <img src=\"/img/Group.png\" width={500} height={500} alt=\"Something went wrong\" />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Error", "import { TableSkeleton } from '@/components/ui/TableSkeleton'\r\n\r\nconst Loader = () => {\r\n  return (\r\n    <div className=\"z-50 w-full min-h-[20rem]\">\r\n      <TableSkeleton\r\n        rowCount={10}\r\n        columnCount={4}\r\n        hasTitle={true}\r\n        hasSearch={true}\r\n        hasFilters={true}\r\n        hasPagination={true}\r\n        hasActions={true}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Loader\r\n"], "names": ["byteToHex", "i", "unsafeStringify", "arr", "offset", "getRandomValues", "rnds8", "rng", "randomUUID", "native", "v4", "options", "buf", "rnds", "Error", "jsxs", "jsx", "<PERSON><PERSON>", "router", "Loader", "TableSkeleton"], "mappings": "4KACA,MAAMA,EAAY,CAAE,EACpB,QAASC,EAAI,EAAGA,EAAI,IAAK,EAAEA,EACvBD,EAAU,MAAMC,EAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC,EAE7C,SAASC,EAAgBC,EAAKC,EAAS,EAAG,CAC7C,OAAQJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EAC7BJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EACzBJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EACzBJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EACzB,IACAJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EACzBJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EACzB,IACAJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EACzBJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EACzB,IACAJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EACzBJ,EAAUG,EAAIC,EAAS,CAAC,CAAC,EACzB,IACAJ,EAAUG,EAAIC,EAAS,EAAE,CAAC,EAC1BJ,EAAUG,EAAIC,EAAS,EAAE,CAAC,EAC1BJ,EAAUG,EAAIC,EAAS,EAAE,CAAC,EAC1BJ,EAAUG,EAAIC,EAAS,EAAE,CAAC,EAC1BJ,EAAUG,EAAIC,EAAS,EAAE,CAAC,EAC1BJ,EAAUG,EAAIC,EAAS,EAAE,CAAC,GAAG,YAAa,CAClD,CC1BA,IAAIC,EACJ,MAAMC,EAAQ,IAAI,WAAW,EAAE,EAChB,SAASC,GAAM,CAC1B,GAAI,CAACF,EAAiB,CAClB,GAAI,OAAO,OAAW,KAAe,CAAC,OAAO,gBACzC,MAAM,IAAI,MAAM,0GAA0G,EAE9HA,EAAkB,OAAO,gBAAgB,KAAK,MAAM,CAC5D,CACI,OAAOA,EAAgBC,CAAK,CAChC,CCVA,MAAME,EAAa,OAAO,OAAW,KAAe,OAAO,YAAc,OAAO,WAAW,KAAK,MAAM,EACvFC,EAAA,CAAE,WAAAD,CAAY,ECE7B,SAASE,EAAGC,EAASC,EAAKR,EAAQ,CAC9B,GAAIK,EAAO,YAAsB,CAACE,EAC9B,OAAOF,EAAO,WAAY,EAE9BE,EAAUA,GAAW,CAAE,EACvB,MAAME,EAAOF,EAAQ,QAAUA,EAAQ,MAAO,GAAIJ,EAAK,EACvD,GAAIM,EAAK,OAAS,GACd,MAAM,IAAI,MAAM,mCAAmC,EAEvD,OAAAA,EAAK,CAAC,EAAKA,EAAK,CAAC,EAAI,GAAQ,GAC7BA,EAAK,CAAC,EAAKA,EAAK,CAAC,EAAI,GAAQ,IAWtBX,EAAgBW,CAAI,CAC/B,CCrBA,MAAMC,EAAQ,IAEVC,EAAA,KAAC,MAAI,CAAA,UAAU,2IACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,mCACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,WACb,SAAAD,OAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,uCAAuC,SAErD,6DAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,yBAAyB,SAEtC,2EAAA,EACAA,EAAA,IAACC,EAAA,CACC,QAAS,IAAM,CACbC,EAAO,MAAM,GAAG,CAClB,EACD,SAAA,gBAAA,CAAA,CAED,CACF,CAAA,CACF,CAAA,EACF,EACCF,EAAA,IAAA,MAAA,CACC,SAACA,EAAAA,IAAA,MAAA,CAAI,IAAI,iBAAiB,MAAO,IAAK,OAAQ,IAAK,IAAI,sBAAA,CAAuB,CAChF,CAAA,CAAA,EACF,EC3BEG,EAAS,IAEXH,EAAAA,IAAC,MAAI,CAAA,UAAU,4BACb,SAAAA,EAAA,IAACI,EAAA,CACC,SAAU,GACV,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CAAA,EAEhB", "x_google_ignoreList": [0, 1, 2, 3]}