'use client'

import { type OpenIddictScopeDto } from '@/client'
import { type ColumnDef } from '@tanstack/react-table'
import { Actions } from './Actions'
import { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'
import { Badge } from '@/components/ui/badge'
import { customFilterFunction } from '../../../data-table/filterFunctions'

// Type for the callback function to handle user actions
type UserActionCallback = (dataId: string, dataEdit: OpenIddictScopeDto, dialogType: 'edit' | 'permission' | 'delete') => void

// Function to create user columns with the action callback
export const getColumns = (
  handleUserAction: UserActionCallback
): ColumnDef<OpenIddictScopeDto>[] => [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Name",
      },
      filterFn: customFilterFunction,
    },
    {
      accessorKey: 'displayName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Display Name" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Display Name",
      },
      filterFn: customFilterFunction,
    },
    {
      accessorKey: 'description',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Description",
      },
      filterFn: customFilterFunction,
    },
    {
      accessorKey: 'resources',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Resources" />
      ),
      enableSorting: true,
      enableHiding: true,
      filterFn: customFilterFunction,
      cell: (info) => {
        const resources = info.getValue() as string[];
        return (
          <div className="flex flex-wrap gap-1">
            {resources?.map((resource) => (
              <Badge key={resource} variant="secondary">
                {resource}
              </Badge>
            ))}
          </div>
        );
      },
      meta: {
        className: "text-left",
        displayName: "Resources",
      },
    },
    {
      id: 'actions',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Actions" />
      ),
      enableHiding: true,
      cell: (info) => (
        <Actions
          dataId={info.row.original.id!}
          dataEdit={info.row.original}
          onAction={handleUserAction}
          variant="dropdown" // Use "dropdown" for the first image style or "buttons" for the second image style
        />
      ),
      meta: {
        className: "text-right",
        displayName: "Actions",
      },
    },
  ]

