"use client"

import * as React from "react";
import { useState } from "react";
import { Sidebar, SidebarContent, SidebarGroup, SidebarGroupContent, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarProvider } from "@/components/ui/sidebar";
import { Lock, Mail, ToggleRight, Server } from "lucide-react";
import { Emailing } from "./Emailing";
import { FeatureManagement } from "./FeatureManagement";
import { ActiveDirectory } from "./ActiveDirectory";
import { PolicyGuard } from "@/components/auth/policy-guard";
import type { Policy } from "@/lib/hooks/useGrantedPolicies";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";

// Define the navigation items with their components
const data = {
  nav: [
    {
      name: "Emailing",
      icon: Mail,
      content: Emailing,
      policy: "SettingManagement.Emailing" as Policy
    },
    {
      name: "Feature management",
      icon: ToggleRight,
      content: FeatureManagement,
      policy: "FeatureManagement.ManageHostFeatures" as Policy
    },
    {
      name: "Active Directory",
      icon: Server,
      content: ActiveDirectory,
      policy: "IdentityServer.ActiveDirectorySettings" as Policy
    },
    {
      name: "Privacy & visibility",
      icon: Lock,
      policy: "AbpIdentity.Users" as Policy,
      content: () => (
        <section className="privacy p-5 xl:p-10">
          <h1 className="font-medium text-xl">Privacy & Visibility</h1>
          <hr className="mt-2 border" />
          <div className="pt-5">
            <article className="mb-5 text-base-content">
              <p>Configure privacy and visibility settings here.</p>
            </article>
          </div>
        </section>
      )
    },
  ],
}

export default function SettingSidebar() {
  const { can } = useGrantedPolicies();

  // Filter navigation items based on user permissions
  const authorizedNavItems = data.nav.filter(item => can(item.policy));

  // State to track the selected menu item
  const [selectedItem, setSelectedItem] = useState<string>(
    authorizedNavItems.length > 0 && authorizedNavItems[0]?.name ? authorizedNavItems[0].name : ""
  );

  // Find the selected component to render
  const selectedNavItem = authorizedNavItems.length > 0
    ? (authorizedNavItems.find(item => item.name === selectedItem) || authorizedNavItems[0])
    : undefined;

  // If no authorized items, show a message
  if (authorizedNavItems.length === 0) {
    return (
      <div className="flex items-center justify-center">
        <p className="text-muted-foreground">You don't have permission to access any settings.</p>
      </div>
    );
  }

  return (
    <SidebarProvider className="items-start">
      <Sidebar collapsible="none" className="hidden md:flex">
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu>
                {authorizedNavItems.map((item) => (
                  <SidebarMenuItem key={item.name}>
                    <SidebarMenuButton
                      asChild
                      isActive={item.name === selectedItem}
                      onClick={() => setSelectedItem(item.name)}
                    >
                      <button type="button">
                        <item.icon />
                        <span>{item.name}</span>
                      </button>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>

      <main className="flex flex-1 flex-col overflow-hidden">
        <div className="flex flex-1 flex-col overflow-y-auto">
          {/* Render the selected component with policy guard */}
          {selectedNavItem && (
            <PolicyGuard
              policy={selectedNavItem.policy}
              message={`You don't have permission to access ${selectedNavItem.name} settings.`}
            >
              {typeof selectedNavItem.content === 'function'
                ? <selectedNavItem.content />
                : React.createElement(selectedNavItem.content)}
            </PolicyGuard>
          )}
        </div>
      </main>
    </SidebarProvider>
  )
}