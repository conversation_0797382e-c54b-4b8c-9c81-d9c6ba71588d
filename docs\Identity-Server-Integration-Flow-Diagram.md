# Identity Server Integration Flow Diagram

This document provides a comprehensive flow diagram illustrating the integration between client applications and ABP Framework Identity Server for authentication and authorization.

## Authentication Flow

```mermaid
sequenceDiagram
    participant Client as Client Application
    participant API as Backend API
    participant IS as Identity Server
    participant DB as Identity Database

    Client->>Client: User enters credentials
    Client->>IS: POST /connect/token
    IS->>DB: Validate credentials
    DB-->>IS: Validation result

    alt Credentials valid
        IS-->>Client: Return JWT token
        Client->>Client: Store token
    else Credentials invalid
        IS-->>Client: Return 401 Unauthorized
    end

    Client->>API: Request with Bearer token
    API->>API: Validate token signature

    alt Token valid
        API->>IS: GET /api/abp/application-configuration
        IS-->>API: Return user permissions
        API->>API: Process request
        API-->>Client: Return response
    else Token invalid or expired
        API-->>Client: Return 401 Unauthorized
    end
```

## Authorization Flow

```mermaid
sequenceDiagram
    participant Client as Client Application
    participant API as Backend API
    participant Auth as Authorization Middleware
    participant IS as Identity Server

    Client->>API: Request with Bearer token
    API->>Auth: Check authorization

    alt Token valid
        Auth->>IS: GET /api/abp/application-configuration
        IS-->>Auth: Return granted policies

        alt Has required permission
            Auth-->>API: Allow request
            API->>API: Process request
            API-->>Client: Return response
        else Permission denied
            Auth-->>API: Deny request
            API-->>Client: Return 403 Forbidden with JSON error
        end
    else Token invalid or expired
        Auth-->>API: Deny request
        API-->>Client: Return 401 Unauthorized with JSON error
    end
```

## Token Validation Process

```mermaid
flowchart TD
    A[Receive request with token] --> B{Token present?}
    B -->|No| C[Return 401 Unauthorized]
    B -->|Yes| D[Extract token from Authorization header]
    D --> E[Validate token signature]
    E --> F{Signature valid?}
    F -->|No| G[Return 401 Unauthorized]
    F -->|Yes| H[Check token expiration]
    H --> I{Token expired?}
    I -->|Yes| J[Return 401 Unauthorized]
    I -->|No| K[Extract claims from token]
    K --> L[Create ClaimsPrincipal]
    L --> M[Set User identity]
    M --> N[Continue request processing]
```

## Permission Check Process

```mermaid
flowchart TD
    A[Request reaches protected endpoint] --> B{User authenticated?}
    B -->|No| C[Return 401 Unauthorized]
    B -->|Yes| D[Extract required permission from endpoint]
    D --> E[Get user permissions from Identity Server]
    E --> F{Permission granted?}
    F -->|No| G[Return 403 Forbidden with details]
    F -->|Yes| H[Execute endpoint logic]
    H --> I[Return successful response]
```

## Integration Architecture

```mermaid
flowchart TB
    subgraph "Client Applications"
        A[Angular SPA]
        B[React App]
        C[Mobile App]
        D[.NET App]
        E[Laravel App]
        F[CodeIgniter App]
    end

    subgraph "Identity Server"
        G[OpenIddict]
        H[User Manager]
        I[Permission Manager]
        J[Token Service]
        K[JWKS Endpoint]
    end

    subgraph "Backend APIs"
        L[API Gateway]
        M[Microservice 1]
        N[Microservice 2]
        O[Legacy API]
    end

    subgraph "Databases"
        P[(Identity DB)]
        Q[(Application DB)]
    end

    A & B & C & D & E & F -->|Authentication| G
    G --> H
    H --> P
    G --> J
    J --> K

    A & B & C & D & E & F -->|API Calls with Token| L
    L --> M & N & O

    M & N & O -->|Permission Check| I
    I --> P

    M & N & O --> Q
```

## Token Structure

```mermaid
classDiagram
    class JWTToken {
        +Header
        +Payload
        +Signature
    }

    class Header {
        +alg: String
        +typ: String
        +kid: String
    }

    class Payload {
        +iss: String
        +sub: String
        +aud: String
        +exp: Number
        +nbf: Number
        +iat: Number
        +jti: String
        +name: String
        +email: String
        +roles: String[]
    }

    class Signature {
        +signedData: String
    }

    JWTToken *-- Header
    JWTToken *-- Payload
    JWTToken *-- Signature
```

## Error Handling Flow

```mermaid
flowchart TD
    A[Request with error] --> B{Error type?}

    B -->|Authentication error| C[401 Unauthorized]
    C --> D[JSON response with error details]

    B -->|Authorization error| E[403 Forbidden]
    E --> F[JSON response with permission details]

    B -->|Validation error| G[400 Bad Request]
    G --> H[JSON response with validation errors]

    B -->|Server error| I[500 Internal Server Error]
    I --> J[JSON response with error message]

    D & F & H & J --> K[Client displays error to user]
```

## Complete Integration Workflow

```mermaid
flowchart TB
    A[Setup Identity Server] --> B[Configure Client Applications]
    B --> C[Define Permissions]
    C --> D[Implement Authentication]
    D --> E[Implement Authorization]
    E --> F[Handle Token Expiration]
    F --> G[Implement Error Handling]
    G --> H[Test Integration]

    subgraph "Setup Identity Server"
        A1[Install ABP Framework]
        A2[Configure OpenIddict]
        A3[Define API Resources]
        A4[Create Client Applications]
    end

    subgraph "Configure Client Applications"
        B1[Install Authentication Libraries]
        B2[Configure Token Endpoints]
        B3[Implement Token Storage]
        B4[Add Authorization Headers]
    end

    subgraph "Define Permissions"
        C1[Create Permission Groups]
        C2[Define Granular Permissions]
        C3[Assign Permissions to Roles]
        C4[Create Permission Policies]
    end

    subgraph "Implement Authentication"
        D1[Create Login UI]
        D2[Implement Token Request]
        D3[Store Tokens Securely]
        D4[Add Token to Requests]
    end

    subgraph "Implement Authorization"
        E1[Create Authorization Middleware]
        E2[Implement Permission Checker]
        E3[Add Authorization Attributes]
        E4[Create Custom Authorization Handlers]
    end

    A --> A1 & A2 & A3 & A4
    B --> B1 & B2 & B3 & B4
    C --> C1 & C2 & C3 & C4
    D --> D1 & D2 & D3 & D4
    E --> E1 & E2 & E3 & E4
```

## Client-Specific Integration

### .NET Client Integration

```mermaid
flowchart LR
    A[.NET Client] -->|1. Configure| B[Authentication]
    A -->|2. Configure| C[Authorization]

    B -->|JWT Bearer| D[Token Validation]
    C -->|Policies| E[Permission Handler]

    D --> F[Identity Server]
    E --> F

    F -->|JWT Token| D
    F -->|Permissions| E

    D -->|Valid Token| G[Protected Resources]
    E -->|Granted Permission| G
```

### Laravel Client Integration

```mermaid
flowchart LR
    A[Laravel Client] -->|1. Configure| B[JWT Auth Guard]
    A -->|2. Configure| C[Permission Middleware]

    B -->|Token Validation| D[JWT Validation]
    C -->|Check Permissions| E[Permission Service]

    D --> F[Identity Server]
    E --> F

    F -->|JWT Token| D
    F -->|Permissions| E

    D -->|Valid Token| G[Protected Resources]
    E -->|Granted Permission| G
```

### CodeIgniter Client Integration

```mermaid
flowchart LR
    A[CodeIgniter Client] -->|1. Configure| B[Auth Library]
    A -->|2. Configure| C[Permission Hook]

    B -->|Token Validation| D[JWT Helper]
    C -->|Check Permissions| E[Permission Service]

    D --> F[Identity Server]
    E --> F

    F -->|JWT Token| D
    F -->|Permissions| E

    D -->|Valid Token| G[Protected Resources]
    E -->|Granted Permission| G
```

## Conclusion

These diagrams illustrate the complete flow of integration between client applications and the ABP Framework Identity Server. The integration covers authentication, authorization, token validation, permission checking, and error handling processes. By following these flows, developers can implement a secure and robust identity management solution for their applications.
