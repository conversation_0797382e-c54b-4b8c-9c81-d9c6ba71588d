{"version": 3, "file": "role-ClsarOrJ.js", "sources": ["../../../../../frontend/src/components/app/role/Delete.tsx", "../../../../../frontend/src/lib/hooks/useRoles.ts", "../../../../../frontend/src/components/app/role/Add.tsx", "../../../../../frontend/src/components/ui/YesNoBadge.tsx", "../../../../../frontend/src/components/app/role/Actions.tsx", "../../../../../frontend/src/components/app/role/Columns.tsx", "../../../../../frontend/src/components/app/role/Edit.tsx", "../../../../../frontend/src/components/app/role/Filter.tsx", "../../../../../frontend/src/lib/hooks/useRoleApplications.ts", "../../../../../frontend/src/components/app/role/ManageRoleApplications.tsx", "../../../../../frontend/src/components/app/role/RolePermission.tsx", "../../../../../frontend/src/components/app/role/List.tsx", "../../../../../frontend/src/pages/role.tsx"], "sourcesContent": ["import { deleteApiIdentityRolesById } from '@/client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { useEffect, useState } from 'react'\r\n\r\ntype DeleteUserProps = {\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\nexport const Delete = ({ dataId, onDismiss }: DeleteUserProps) => {\r\n  const { toast } = useToast()\r\n  const [open, setOpen] = useState<boolean>(false)\r\n  const onYesEvent = async () => {\r\n    try {\r\n      await deleteApiIdentityRolesById({\r\n        path: { id: dataId },\r\n      })\r\n      toast({\r\n        title: 'Success',\r\n        description: `Role has been deleted successfully.`,\r\n      })\r\n      onDismiss()\r\n    } catch (err: unknown) {\r\n      if (err instanceof Error) {\r\n        toast({\r\n          title: 'Failed',\r\n          description: `There was a problem when deleting the role. Kindly try again.`,\r\n          variant: 'destructive',\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <AlertDialog open={open}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            This action cannot be undone. This will permanently delete your this role.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={onDismiss}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction onClick={onYesEvent}>Yes</AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n", "import { getApiIdentityRoles } from '@/client'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { QueryNames } from './QueryConstants'\r\n\r\n/**\r\n * Custom hook to fetch a list of roles.\r\n *\r\n * This hook uses the `useQuery` hook from `react-query` to fetch the role data\r\n * asynchronously. The query key used is `QueryNames.GetRoles` along with pagination,\r\n * filter, and sorting parameters.\r\n *\r\n * @param {number} pageIndex - The current page index.\r\n * @param {number} pageSize - The number of items per page.\r\n * @param {string} [filter] - Optional filter string.\r\n * @param {string} [sorting] - Optional sorting string.\r\n * @returns {UseQueryResult} The result of the query, which includes the role data and query status.\r\n */\r\nexport const useRoles = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filter?: string  ,\r\n  sorting?: string  \r\n) => {\r\n  return useQuery({\r\n    queryKey: [QueryNames.GetRoles, pageIndex, pageSize, filter, sorting],\r\n    queryFn: async () => {\r\n      let skip = 0\r\n      if (pageIndex > 0) {\r\n        skip = pageIndex * pageSize\r\n      }\r\n      const { data } = await getApiIdentityRoles({\r\n        query: {\r\n          MaxResultCount: pageSize,\r\n          SkipCount: skip,\r\n          Filter: filter,\r\n          Sorting: sorting,\r\n        }\r\n      })\r\n      return data\r\n    },\r\n  })\r\n}\r\n", "'use client'\r\nimport { type IdentityRoleCreateDto, postApiIdentityRoles, type RemoteServiceErrorResponse } from '@/client'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Toaster } from '@/components/ui/toaster'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { RiAddLine } from '@remixicon/react'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\n\r\nexport type AddClientProps = {\r\n  children?: React.ReactNode\r\n}\r\n\r\nexport const Add = ({ children }: AddClientProps) => {\r\n  const { can } = useGrantedPolicies()\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, reset } = useForm<IdentityRoleCreateDto>()\r\n  const [, setSelectedValueType] = useState<string[]>([])\r\n\r\n  // Reset form when dialog is closed\r\n  useEffect(() => {\r\n    if (!open) {\r\n      reset({\r\n        name: '',\r\n        isDefault: false,\r\n        isPublic: false,\r\n      })\r\n      setSelectedValueType([])\r\n    }\r\n  }, [open, reset])\r\n\r\n  const createDataMutation = useMutation({\r\n    mutationFn: async (formData: IdentityRoleCreateDto) =>\r\n      postApiIdentityRoles({\r\n        body: {\r\n          name: formData.name ?? '',\r\n          isDefault: formData.isDefault ?? false,\r\n          isPublic: formData.isPublic ?? false,\r\n        }\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Role Created Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n      setOpen(false)\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: IdentityRoleCreateDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: IdentityRoleCreateDto = {\r\n      ...formData,\r\n    }\r\n\r\n    createDataMutation.mutate(userData)\r\n  }\r\n\r\n  const handleOpenChange = (newOpenState: boolean) => {\r\n    setOpen(newOpenState)\r\n  }\r\n\r\n  return (\r\n    <section>\r\n      <Toaster />\r\n      <Dialog open={open} onOpenChange={handleOpenChange}>\r\n        <DialogTrigger asChild>{children}</DialogTrigger>\r\n        <section className=\"flex items-center justify-between pb-5\">\r\n          {can('IdentityServer.ClaimTypes.Create') && (\r\n            <Button size='sm' className=\"w-full sm:py-1 sm:mt-0 sm:w-fit\" onClick={() => setOpen(true)}>\r\n              <RiAddLine className=\"-ml-1 size-4 shrink-0\" aria-hidden=\"true\" />\r\n              <span className=\"hidden truncate sm:inline\">New Role</span>\r\n            </Button>\r\n          )}\r\n        </section>\r\n        <DialogContent className='max-w-2xl'>\r\n          <DialogHeader>\r\n            <DialogTitle>Create a New Role</DialogTitle>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit(onSubmit)} className='mt-2' onKeyDown={(e) => {\r\n            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {\r\n              e.preventDefault();\r\n              void handleSubmit(onSubmit)();\r\n            }\r\n          }}>\r\n            <section className=\"flex w-full flex-col space-y-2\">\r\n              <FormSection>\r\n                <FormField\r\n                  label=\"Name\"\r\n                  description=\"The name of the claim\"\r\n                >\r\n                  <Input required {...register('name')} placeholder=\"Claim Name\" />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Default\"\r\n                  description=\"Whether the role is default\"\r\n                >\r\n                  <Checkbox {...register('isDefault', {\r\n                    setValueAs: (value) => value === true\r\n                  })} />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  label=\"Public\"\r\n                  description=\"Whether the role is public\"\r\n                >\r\n                  <Checkbox {...register('isPublic', {\r\n                    setValueAs: (value) => value === true\r\n                  })} />\r\n                </FormField>\r\n              </FormSection>\r\n            </section>\r\n            <DialogFooter className=\"mt-5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                onClick={(e) => {\r\n                  e.preventDefault()\r\n                  setOpen(false)\r\n                }}\r\n                disabled={createDataMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={createDataMutation.isPending}>\r\n                {createDataMutation.isPending ? 'Saving...' : 'Save'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </section>\r\n  )\r\n}\r\n", "import React from 'react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface YesNoBadgeProps {\r\n  value: boolean;\r\n  className?: string;\r\n}\r\n\r\nexport const YesNoBadge: React.FC<YesNoBadgeProps> = ({ value, className }) => {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\",\r\n        value\r\n          ? \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\"\r\n          : \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\r\n        className\r\n      )}\r\n    >\r\n      {value ? 'Yes' : 'No'}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default YesNoBadge; ", "'use client'\r\n\r\nimport { type IdentityUserUpdateDto } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\nimport { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'\r\nimport { RiMoreLine, RiPencilLine, RiShieldKeyholeLine } from '@remixicon/react'\r\n\r\ntype UserActionProps = {\r\n  userId: string\r\n  userDto: IdentityUserUpdateDto\r\n  onAction: (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete' | 'application') => void\r\n  variant?: 'dropdown' | 'buttons'\r\n}\r\n\r\nexport const Actions = ({ userId, userDto, onAction, variant = 'dropdown' }: UserActionProps) => {\r\n  const { can } = useGrantedPolicies()\r\n\r\n  // For dropdown menu style (first image)\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <div className=\"flex justify-end\">\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"h-8 w-8 p-0\"\r\n            >\r\n              <RiMoreLine className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Open menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"w-[160px]\">\r\n            {can('AbpIdentity.Roles.Update') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'edit')}\r\n              >\r\n                Edit\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('AbpIdentity.Roles.ManagePermissions') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'permission')}\r\n              >\r\n                Permission\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('AbpIdentity.Roles.ManagePermissions') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm\"\r\n                onClick={() => onAction(userId, userDto, 'application')}\r\n              >\r\n                Application\r\n              </DropdownMenuItem>\r\n            )}\r\n            {can('AbpIdentity.Roles.Delete') && (\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-sm text-red-500\"\r\n                onClick={() => onAction(userId, userDto, 'delete')}\r\n              >\r\n                Delete\r\n              </DropdownMenuItem>\r\n            )}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // For button group style (second image)\r\n  return (\r\n    <div className=\"flex items-center justify-end gap-1\">\r\n      {can('AbpIdentity.Users.ManagePermissions') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'permission')}\r\n        >\r\n          <RiShieldKeyholeLine className=\"h-4 w-4\" />\r\n          <span>Permission</span>\r\n        </Button>\r\n      )}\r\n      {can('AbpIdentity.Users.Update') && (\r\n        <Button\r\n          variant=\"primary\"\r\n          size=\"sm\"\r\n          className=\"flex items-center gap-1 px-2 py-1\"\r\n          onClick={() => onAction(userId, userDto, 'edit')}\r\n        >\r\n          <RiPencilLine className=\"h-4 w-4\" />\r\n          <span>Edit</span>\r\n        </Button>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n", "'use client'\r\n\r\nimport { type IdentityRoleDto, type IdentityUserUpdateDto } from '@/client'\r\nimport { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { YesNoBadge } from '@/components/ui/YesNoBadge'\r\nimport { type ColumnDef } from '@tanstack/react-table'\r\nimport { Actions } from './Actions'\r\n\r\n// Type for the callback function to handle user actions\r\ntype UserActionCallback = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete' | 'application') => void\r\n\r\n// Function to create columns with the action callback\r\nexport const getColumns = (\r\n  handleUserAction: UserActionCallback\r\n): ColumnDef<IdentityRoleDto>[] => {\r\n  return [\r\n    {\r\n      id: \"select\",\r\n      header: ({ table }) => (\r\n        <Checkbox\r\n          checked={\r\n            table.getIsAllPageRowsSelected()\r\n              ? true\r\n              : table.getIsSomeRowsSelected()\r\n                ? \"indeterminate\"\r\n                : false\r\n          }\r\n          onCheckedChange={() => table.toggleAllPageRowsSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select all\"\r\n        />\r\n      ),\r\n      cell: ({ row }) => (\r\n        <Checkbox\r\n          checked={row.getIsSelected()}\r\n          onCheckedChange={() => row.toggleSelected()}\r\n          className=\"translate-y-0.5\"\r\n          aria-label=\"Select row\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: true,\r\n      meta: {\r\n        displayName: \"Select\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"name\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Name\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => info.getValue(),\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Name\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"isDefault\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Is Default\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => <YesNoBadge value={info.getValue() as boolean} />,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Is Default\",\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"isPublic\",\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title=\"Is Public\" />\r\n      ),\r\n      enableSorting: true,\r\n      enableHiding: true,\r\n      cell: (info) => <YesNoBadge value={info.getValue() as boolean} />,\r\n      meta: {\r\n        className: \"text-left\",\r\n        displayName: \"Is Public\",\r\n      },\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      header: \"Actions\",\r\n      cell: (info) => (\r\n        <Actions\r\n          userId={info.row.original.id!}\r\n          userDto={info.row.original as unknown as IdentityUserUpdateDto}\r\n          onAction={handleUserAction}\r\n          variant=\"dropdown\"\r\n        />\r\n      ),\r\n      enableSorting: false,\r\n      enableHiding: true,\r\n      meta: {\r\n        className: \"text-right\",\r\n        displayName: \"Action\",\r\n      },\r\n    }\r\n  ];\r\n}\r\n", "import { type IdentityRoleUpdateDto, putApiIdentityRolesById, type RemoteServiceErrorResponse } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  <PERSON><PERSON>Footer,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { FormField, FormSection } from '@/components/ui/FormField'\r\nimport { Input } from '@/components/ui/input'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\n\r\ntype UserEditProps = {\r\n  dataEdit: IdentityRoleUpdateDto\r\n  dataId: string\r\n  onDismiss: () => void\r\n}\r\nexport const Edit = ({ dataEdit, dataId, onDismiss }: UserEditProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n  const { handleSubmit, register, setValue } = useForm<IdentityRoleUpdateDto>()\r\n  const [isDefault, setIsDefault] = useState(dataEdit.isDefault ?? false)\r\n  const [isPublic, setIsPublic] = useState(dataEdit.isPublic ?? false)\r\n\r\n  const updateDataMutation = useMutation({\r\n    mutationFn: async (formData: IdentityRoleUpdateDto) =>\r\n      putApiIdentityRolesById({\r\n        path: { id: dataId },\r\n        body: {\r\n          name: formData.name ?? dataEdit.name ?? '',\r\n          isDefault: formData.isDefault ?? dataEdit.isDefault ?? false,\r\n          isPublic: formData.isPublic ?? dataEdit.isPublic ?? false,\r\n        },\r\n      }),\r\n    onSuccess: () => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Role Updated Successfully',\r\n        variant: 'success',\r\n      })\r\n      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n      onCloseEvent()\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message as string,\r\n        description: err?.error?.details,\r\n        variant: 'destructive',\r\n      })\r\n    }\r\n  })\r\n\r\n  const onSubmit = (formData: IdentityRoleUpdateDto) => {\r\n    // Merge form data with consent type and permissions\r\n    const userData: IdentityRoleUpdateDto = {\r\n      ...formData,\r\n    }\r\n\r\n    updateDataMutation.mutate(userData)\r\n  }\r\n\r\n  const onCloseEvent = () => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }\r\n\r\n  // Initialize form when opened\r\n  useEffect(() => {\r\n    if (open) {\r\n      // Initialize form values from dataEdit\r\n      setValue('name', dataEdit.name || '')\r\n      setValue('isDefault', dataEdit.isDefault ?? false)\r\n      setValue('isPublic', dataEdit.isPublic ?? false)\r\n    }\r\n  }, [open, dataEdit, setValue])\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n  }, [])\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent>\r\n        <DialogHeader>\r\n          <DialogTitle>Update a Role: {dataEdit.name}</DialogTitle>\r\n        </DialogHeader>\r\n        <form\r\n          onSubmit={handleSubmit(onSubmit)}\r\n          className='mt-2'\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {\r\n              e.preventDefault();\r\n              void handleSubmit(onSubmit)();\r\n            }\r\n          }}\r\n        >\r\n          <section className=\"flex w-full flex-col space-y-2\">\r\n            <FormSection>\r\n              <FormField\r\n                label=\"Name\"\r\n                description=\"The name of the role\"\r\n              >\r\n                <Input required {...register('name')} defaultValue={dataEdit.name ?? ''} placeholder=\"Role Name\" />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Default\"\r\n                description=\"Whether the role is default\"\r\n              >\r\n                <Checkbox\r\n                  checked={isDefault}\r\n                  onCheckedChange={(checked) => {\r\n                    setIsDefault(!!checked)\r\n                    setValue('isDefault', !!checked)\r\n                  }}\r\n                />\r\n              </FormField>\r\n\r\n              <FormField\r\n                label=\"Public\"\r\n                description=\"Whether the role is public\"\r\n              >\r\n                <Checkbox\r\n                  checked={isPublic}\r\n                  onCheckedChange={(checked) => {\r\n                    setIsPublic(!!checked)\r\n                    setValue('isPublic', !!checked)\r\n                  }}\r\n                />\r\n              </FormField>\r\n            </FormSection>\r\n          </section>\r\n          <DialogFooter className=\"mt-5\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              onClick={(e) => {\r\n                e.preventDefault()\r\n                setOpen(false)\r\n              }}\r\n              disabled={updateDataMutation.isPending}\r\n              type=\"button\"\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={updateDataMutation.isPending}\r\n            >\r\n              {updateDataMutation.isPending ? 'Saving...' : 'Save'}\r\n            </Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "\"use client\"\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { type Table } from \"@tanstack/react-table\"\r\nimport { ViewOptions } from \"@/components/data-table/DataTableViewOptions\"\r\nimport { Search } from \"@/components/ui/search\"\r\n\r\ninterface FilterProps<TData> {\r\n  table: Table<TData>\r\n  onSearch?: (value: string) => void\r\n  searchValue?: string\r\n}\r\n\r\nexport function Filter<TData>({\r\n  table,\r\n  onSearch,\r\n  searchValue = \"\",\r\n}: FilterProps<TData>) {\r\n  const isFiltered = table.getState().columnFilters.length > 0\r\n\r\n  return (\r\n    <div className=\"flex flex-wrap items-center justify-between gap-2\">\r\n      {onSearch && (\r\n        <div className=\"w-full sm:w-auto sm:max-w-[250px]\">\r\n          <Search onUpdate={onSearch} value={searchValue} />\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex items-center gap-2 ml-auto\">\r\n        {isFiltered && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => table.resetColumnFilters()}\r\n            className=\"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500\"\r\n          >\r\n            Clear filters\r\n          </Button>\r\n        )}\r\n        {/* <Button\r\n          variant=\"secondary\"\r\n          className=\"gap-x-2 px-2 py-1.5 text-sm sm:text-xs\"\r\n        >\r\n          <RiDownloadLine className=\"size-4 shrink-0\" aria-hidden=\"true\" />\r\n          <span className=\"hidden sm:inline\">Export</span>\r\n        </Button> */}\r\n        <ViewOptions table={table} />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n", "import { useQuery } from '@tanstack/react-query';\r\nimport { getApiRoleApplicationsByRoleByRoleId } from '@/client/sdk.gen';\r\n\r\nexport const useRoleApplicationsByRole = (roleId: string) => {\r\n  return useQuery({\r\n    queryKey: ['roleApplicationsByRole', roleId],\r\n    queryFn: () => getApiRoleApplicationsByRoleByRoleId({\r\n      path: {\r\n      roleId\r\n    }}),\r\n    enabled: !!roleId,\r\n  });\r\n}; ", "import { type IdentityRoleDto, type RoleApplicationDto, postApiRoleApplicationsUpdate } from '@/client'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'\r\nimport { useOpeniddictApplications } from '@/lib/hooks/useOpeniddictApplications'\r\nimport { useRoleApplicationsByRole } from '@/lib/hooks/useRoleApplications'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { type FormEvent, useCallback, useEffect, useRef, useState } from 'react'\r\n\r\ntype ManageRoleApplicationsProps = {\r\n  roleDto: IdentityRoleDto\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const ManageRoleApplications = ({ roleDto, onDismiss }: ManageRoleApplicationsProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  // Fetch all available applications (1000 limit)\r\n  const { data: allApplications, isLoading: isLoadingApplications } = useOpeniddictApplications(0, 1000)\r\n\r\n  // Fetch current role applications\r\n  const { data: roleApplications, isLoading: isLoadingRoleApps } = useRoleApplicationsByRole(roleDto.id!)\r\n\r\n  const [selectedApplications, setSelectedApplications] = useState<string[]>([])\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n    return () => {\r\n      void queryClient.invalidateQueries({ queryKey: ['roleApplicationsByRole'] })\r\n    }\r\n  }, [queryClient])\r\n\r\n  // Update selected applications when role applications data loads\r\n  useEffect(() => {\r\n    if (roleApplications?.data) {\r\n      setSelectedApplications(roleApplications.data\r\n        .map((app: RoleApplicationDto) => app.applicationId)\r\n        .filter((id): id is string => id !== undefined)\r\n      )\r\n    }\r\n  }, [roleApplications])\r\n\r\n  const onCloseEvent = useCallback(() => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }, [onDismiss])\r\n\r\n  const onSubmit = useCallback(\r\n    async (e: FormEvent) => {\r\n      e.preventDefault()\r\n\r\n      try {\r\n        await postApiRoleApplicationsUpdate({\r\n          body: {\r\n            roleId: roleDto.id!,\r\n            applicationIds: selectedApplications\r\n          }\r\n        })\r\n\r\n        toast({\r\n          title: 'Success',\r\n          description: 'Applications Updated Successfully',\r\n          variant: 'default',\r\n        })\r\n\r\n        void queryClient.invalidateQueries({\r\n          queryKey: ['roleApplicationsByRole'],\r\n        })\r\n\r\n        onCloseEvent()\r\n      } catch (err: unknown) {\r\n        if (err instanceof Error) {\r\n          toast({\r\n            title: 'Failed',\r\n            description: \"Application update wasn't successful.\",\r\n            variant: 'destructive',\r\n          })\r\n        }\r\n      }\r\n    },\r\n    [roleDto.id, selectedApplications, toast, queryClient, onCloseEvent]\r\n  )\r\n\r\n  const handleApplicationToggle = (applicationId: string) => {\r\n    setSelectedApplications(prev =>\r\n      prev.includes(applicationId)\r\n        ? prev.filter(id => id !== applicationId)\r\n        : [...prev, applicationId]\r\n    )\r\n  }\r\n\r\n  // Use stable keys to prevent remounting issues\r\n  const dialogKey = useRef(`dialog-${Math.random().toString(36).substring(2, 9)}`).current\r\n  const isLoading = isLoadingApplications || isLoadingRoleApps\r\n\r\n  return (\r\n    <Dialog key={dialogKey} open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent className=\"max-h-[90vh] overflow-hidden flex flex-col\" style={{ maxWidth: \"800px\", width: \"90vw\" }}>\r\n        <DialogHeader>\r\n          <DialogTitle>Manage Applications - {roleDto.name}</DialogTitle>\r\n        </DialogHeader>\r\n        <form onSubmit={onSubmit} className=\"flex-1 overflow-y-auto\">\r\n          <div className=\"p-4\">\r\n            {isLoading ? (\r\n              <div className=\"text-center py-4\">Loading applications...</div>\r\n            ) : (\r\n              <div className=\"space-y-4\">\r\n                <div className=\"text-sm text-muted-foreground mb-4\">\r\n                  Select the applications that this role can access:\r\n                </div>\r\n                <div className=\"grid grid-cols-1 gap-3\">\r\n                  {allApplications?.items?.map((app) => (\r\n                    <label key={app.id} className=\"flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={selectedApplications.includes(app.id || '')}\r\n                        onChange={() => handleApplicationToggle(app.id || '')}\r\n                        className=\"w-4 h-4 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400\"\r\n                      />\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"font-medium\">{app.displayName || app.clientId || 'Unnamed Application'}</div>\r\n                        {app.clientUri && (\r\n                          <div className=\"text-sm text-muted-foreground\">{app.clientUri}</div>\r\n                        )}\r\n                      </div>\r\n                    </label>\r\n                  ))}\r\n                  {(!allApplications?.items || allApplications.items.length === 0) && (\r\n                    <div className=\"text-center py-8 text-muted-foreground\">\r\n                      No applications available\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </form>\r\n        <DialogFooter className=\"mt-4 border-t pt-4 bg-white dark:bg-gray-950\">\r\n          <Button\r\n            onClick={(e) => {\r\n              e.preventDefault()\r\n              onCloseEvent()\r\n            }}\r\n            variant=\"ghost\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={onSubmit} disabled={isLoading}>\r\n            Save\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n} ", "import { type FormEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react'\r\n\r\nimport {\r\n  type IdentityRoleDto,\r\n  type PermissionGrantInfoDto,\r\n  type PermissionGroupDto,\r\n  type UpdatePermissionsDto,\r\n  putApiPermissionManagementPermissions,\r\n} from '@/client'\r\nimport { Permission } from '@/components/app/permission/PermissionToggle'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\r\nimport { usePermissions } from '@/lib/hooks/usePermissions'\r\nimport { useToast } from '@/lib/useToast'\r\nimport { PermissionProvider, USER_ROLE } from '@/lib/utils'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\n\r\ntype RolePermissionProps = {\r\n  roleDto: IdentityRoleDto\r\n  onDismiss: () => void\r\n}\r\n\r\nexport const RolePermission = ({ roleDto, onDismiss }: RolePermissionProps) => {\r\n  const [open, setOpen] = useState(false)\r\n  const { toast } = useToast()\r\n\r\n  // flag determine to enable/disable all the permissions to a role.\r\n  const [hasAllGranted, setHasAllGranted] = useState(false)\r\n  const { data } = usePermissions(PermissionProvider.R, roleDto.name ?? undefined)\r\n  const queryClient = useQueryClient()\r\n\r\n  const [permissionGroups, setPermissionGroups] = useState<PermissionGroupDto[]>([])\r\n  const [selectedTab, setSelectedTab] = useState(0)\r\n\r\n  useEffect(() => {\r\n    setOpen(true)\r\n    return () => {\r\n      void queryClient.invalidateQueries({ queryKey: [PermissionProvider.R] })\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [])\r\n\r\n  // Update the local state with the remote data\r\n  useEffect(() => {\r\n    setPermissionGroups(Array.isArray(data?.groups) ? [...data.groups] : [])\r\n  }, [data])\r\n\r\n  // Check if all permissions are granted already - only run when data changes\r\n  useEffect(() => {\r\n    if (data?.groups && data.groups.length > 0) {\r\n      const hasAllPermissionGranted = data.groups\r\n        .map((g) => g.permissions?.every((p) => p.isGranted))\r\n        .every((e) => e)\r\n      setHasAllGranted(hasAllPermissionGranted)\r\n    }\r\n  }, [data])\r\n\r\n  // Apply hasAllGranted to all permissions - only when hasAllGranted changes\r\n  useEffect(() => {\r\n    if (permissionGroups.length > 0) {\r\n      // Create a new array with updated permissions to avoid direct mutation\r\n      const updatedGroups = permissionGroups.map(group => ({\r\n        ...group,\r\n        permissions: group.permissions?.map(permission => ({\r\n          ...permission,\r\n          isGranted: hasAllGranted\r\n        })) ?? null\r\n      }));\r\n\r\n      // Update state with the new array\r\n      setPermissionGroups(updatedGroups);\r\n    }\r\n  }, [hasAllGranted]); // Remove permissionGroups from dependency array\r\n\r\n  const onCloseEvent = useCallback(() => {\r\n    setOpen(false)\r\n    onDismiss()\r\n  }, [onDismiss])\r\n\r\n  const onSubmit = useCallback(\r\n    async (e: FormEvent) => {\r\n      e.preventDefault()\r\n\r\n      if (!permissionGroups || permissionGroups.length === 0) return;\r\n\r\n      // Create a stable copy of the permissions to prevent reference issues\r\n      const payload = permissionGroups\r\n        .map((p) =>\r\n          (p.permissions ?? []).map((grant) => ({\r\n            name: grant.name ?? null,\r\n            isGranted: grant.isGranted ?? false,\r\n          }))\r\n        )\r\n        .flat()\r\n\r\n      const requestPayload: UpdatePermissionsDto = {\r\n        permissions: payload,\r\n      }\r\n\r\n      try {\r\n        await putApiPermissionManagementPermissions({\r\n          query: {\r\n            providerKey: roleDto.name ?? '',\r\n            providerName: PermissionProvider.R\r\n          },\r\n          body: requestPayload,\r\n        })\r\n\r\n        toast({\r\n          title: 'Success',\r\n          description: 'Permission Updated Successfully',\r\n          variant: 'default',\r\n        })\r\n\r\n        void queryClient.invalidateQueries({\r\n          queryKey: [PermissionProvider.R],\r\n        })\r\n\r\n        onCloseEvent()\r\n      } catch (err: unknown) {\r\n        if (err instanceof Error) {\r\n          toast({\r\n            title: 'Failed',\r\n            description: \"Permission update wasn't successful.\",\r\n            variant: 'destructive',\r\n          })\r\n        }\r\n      }\r\n    },\r\n    [permissionGroups, roleDto.name, toast, queryClient, onCloseEvent]\r\n  )\r\n\r\n  const hasAdmin = useMemo(() => roleDto.name?.includes(USER_ROLE.ADMIN) ?? false, [roleDto])\r\n\r\n  // Use stable keys to prevent remounting issues\r\n  const dialogKey = useRef(`dialog-${Math.random().toString(36).substring(2, 9)}`).current;\r\n\r\n  // Helper: Group permissions by parent and children (Create, View, Edit, Delete)\r\n  const groupPermissionsByParent = (permissions: PermissionGrantInfoDto[] | null | undefined) => {\r\n    if (!Array.isArray(permissions) || permissions.length === 0) return { parentRows: [], otherPermissions: [] }\r\n    const parentMap: Record<string, { parent: PermissionGrantInfoDto, children: Record<string, PermissionGrantInfoDto> }> = {}\r\n    const otherPermissions: PermissionGrantInfoDto[] = []\r\n    const childActions = [\"Create\", \"View\", \"Edit\", \"Delete\"]\r\n    permissions.forEach((perm) => {\r\n      // Parent: no dot in name, or ends with parent entity\r\n      const dotCount = (perm.name?.match(/\\./g) || []).length\r\n      if (dotCount === 1 && !childActions.some(a => perm.name?.endsWith('.' + a))) {\r\n        // e.g., EkbApp.MasterAgent\r\n        parentMap[perm.name!] = { parent: perm, children: {} }\r\n      }\r\n    })\r\n    // Now, assign children\r\n    permissions.forEach((perm) => {\r\n      const match = perm.name?.match(/^(.*)\\.(Create|View|Edit|Delete)$/)\r\n      if (match) {\r\n        const parentKey = match[1]\r\n        const action = match[2]\r\n        if (parentMap[parentKey]) {\r\n          parentMap[parentKey].children[action] = perm\r\n        } else {\r\n          otherPermissions.push(perm)\r\n        }\r\n      } else {\r\n        // If not a parent or child, and not already in parentMap, treat as other\r\n        if (!parentMap[perm.name!]) {\r\n          otherPermissions.push(perm)\r\n        }\r\n      }\r\n    })\r\n    return { parentRows: Object.values(parentMap), otherPermissions }\r\n  }\r\n\r\n  return (\r\n    <Dialog key={dialogKey} open={open} onOpenChange={onCloseEvent}>\r\n      <DialogContent className=\"max-h-[90vh] overflow-hidden flex flex-col\" style={{ maxWidth: \"1000px\", width: \"90vw\" }}>\r\n        <DialogHeader>\r\n          <DialogTitle>Permissions - {roleDto.name}</DialogTitle>\r\n        </DialogHeader>\r\n        <form onSubmit={onSubmit} className=\"flex-1 overflow-y-auto\">\r\n          <div className=\"p-1\">\r\n            <Permission\r\n              name=\"Grant All Permissions\"\r\n              isGranted={hasAllGranted}\r\n              id=\"all_granted\"\r\n              disabled={!hasAdmin}\r\n              onUpdate={() => {\r\n                setHasAllGranted((prev) => !prev)\r\n              }}\r\n              className=\"ml-2 mb-4\"\r\n            />\r\n\r\n            {/* Tabs for permission groups */}\r\n            <Tabs\r\n              value={selectedTab.toString()}\r\n              onValueChange={v => setSelectedTab(Number(v))}\r\n              orientation='vertical'\r\n              className=\"flex flex-col justify-stretch lg:flex-row gap-4 text-sm text-muted-foreground w-full p-4 border border-border rounded-lg\"\r\n            >\r\n              <div className=\"lg:w-[200px] lg:shrink-0\">\r\n                <TabsList variant=\"button\" className=\"flex flex-col items-stretch *:justify-start sticky top-0 bg-white z-10\">\r\n                  {permissionGroups.map((group, idx) => (\r\n                    <TabsTrigger key={group.name} value={idx.toString()} className=\"inline-block\">\r\n                      {group.displayName}\r\n                    </TabsTrigger>\r\n                  ))}\r\n                </TabsList>\r\n              </div>\r\n              <div className=\"grow border-s border-border py-0 ps-4 overflow-y-auto\">\r\n                {permissionGroups.map((group, idx) => {\r\n                  const { parentRows, otherPermissions } = groupPermissionsByParent(group.permissions ?? [])\r\n                  // Gather all parent and child permissions in this group (table only)\r\n                  const allTablePerms: PermissionGrantInfoDto[] = [\r\n                    ...parentRows.map(r => r.parent),\r\n                    ...parentRows.flatMap(r => Object.values(r.children))\r\n                  ]\r\n                  const allTableChecked = allTablePerms.length > 0 && allTablePerms.every(p => p.isGranted)\r\n                  const handleSelectAllTable = (checked: boolean) => {\r\n                    setPermissionGroups(prev => prev.map((g, gIdx) => {\r\n                      if (gIdx !== idx) return g\r\n                      return {\r\n                        ...g,\r\n                        permissions: g.permissions?.map(p => {\r\n                          // Only update parent and child permissions\r\n                          const isParent = parentRows.some(r => r.parent.name === p.name)\r\n                          const isChild = parentRows.some(r => Object.values(r.children).some(c => c.name === p.name))\r\n                          if (isParent || isChild) {\r\n                            return { ...p, isGranted: checked }\r\n                          }\r\n                          return p\r\n                        })\r\n                      }\r\n                    }))\r\n                  }\r\n                  return (\r\n                    <TabsContent key={group.name} value={idx.toString()} className=\"overflow-x-auto\">\r\n                      {/* Select All for table */}\r\n                      <div className=\"mb-2 flex items-center gap-2\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={allTableChecked}\r\n                          onChange={e => handleSelectAllTable(e.target.checked)}\r\n                          aria-label=\"Select all permissions in this table\"\r\n                          className=\"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400\"\r\n                        />\r\n                        <span>Select All</span>\r\n                      </div>\r\n                      {/* Table for parent/child permissions */}\r\n                      {parentRows.length > 0 && (\r\n                        <table className=\"min-w-full border mb-6\">\r\n                          <thead>\r\n                            <tr>\r\n                              <th className=\"px-4 py-2 text-left\">Name</th>\r\n                              <th className=\"px-4 py-2 text-center\">Parent</th>\r\n                              <th className=\"px-4 py-2 text-center\">View</th>\r\n                              <th className=\"px-4 py-2 text-center\">Create</th>\r\n                              <th className=\"px-4 py-2 text-center\">Edit</th>\r\n                              <th className=\"px-4 py-2 text-center\">Delete</th>\r\n                            </tr>\r\n                          </thead>\r\n                          <tbody>\r\n                            {parentRows.map(({ parent, children }) => (\r\n                              <tr key={parent.name} className=\"border-t\">\r\n                                <td className=\"px-4 py-2 font-medium\">{parent.displayName ?? parent.name}</td>\r\n                                {/* Parent checkbox */}\r\n                                <td className=\"px-4 py-2 text-center\">\r\n                                  <input\r\n                                    type=\"checkbox\"\r\n                                    checked={parent.isGranted}\r\n                                    onChange={() => {\r\n                                      setPermissionGroups(prev => prev.map((g, gIdx) => {\r\n                                        if (gIdx !== idx) return g\r\n                                        return {\r\n                                          ...g,\r\n                                          permissions: g.permissions?.map(p =>\r\n                                            p.name === parent.name ? { ...p, isGranted: !p.isGranted } : p\r\n                                          )\r\n                                        }\r\n                                      }))\r\n                                    }}\r\n                                    aria-label={String(parent.displayName ?? parent.name)}\r\n                                    className=\"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400\"\r\n                                  />\r\n                                </td>\r\n                                {/* Child checkboxes: View, Create, Edit, Delete */}\r\n                                {['View', 'Create', 'Edit', 'Delete'].map(action => (\r\n                                  <td key={action} className=\"px-4 py-2 text-center\">\r\n                                    {children[action] ? (\r\n                                      <input\r\n                                        type=\"checkbox\"\r\n                                        checked={children[action].isGranted}\r\n                                        onChange={() => {\r\n                                          setPermissionGroups(prev => prev.map((g, gIdx) => {\r\n                                            if (gIdx !== idx) return g\r\n                                            return {\r\n                                              ...g,\r\n                                              permissions: g.permissions?.map(p =>\r\n                                                p.name === children[action].name ? { ...p, isGranted: !p.isGranted } : p\r\n                                              )\r\n                                            }\r\n                                          }))\r\n                                        }}\r\n                                        aria-label={String(children[action].displayName ?? children[action].name)}\r\n                                        className=\"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400\"\r\n                                      />\r\n                                    ) : null}\r\n                                  </td>\r\n                                ))}\r\n                              </tr>\r\n                            ))}\r\n                          </tbody>\r\n                        </table>\r\n                      )}\r\n                      {/* Other permissions */}\r\n                      {otherPermissions.length > 0 && (\r\n                        <div className=\"mb-4\">\r\n                          <div className=\"font-semibold mb-2\">Other Permissions</div>\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-1 gap-2\">\r\n                            {otherPermissions.map(perm => (\r\n                              <label key={perm.name} className=\"flex items-center gap-2\">\r\n                                <input\r\n                                  type=\"checkbox\"\r\n                                  checked={perm.isGranted}\r\n                                  onChange={() => {\r\n                                    setPermissionGroups(prev => prev.map((g, gIdx) => {\r\n                                      if (gIdx !== idx) return g\r\n                                      return {\r\n                                        ...g,\r\n                                        permissions: g.permissions?.map(p =>\r\n                                          p.name === perm.name ? { ...p, isGranted: !p.isGranted } : p\r\n                                        )\r\n                                      }\r\n                                    }))\r\n                                  }}\r\n                                  aria-label={String(perm.name ?? perm.displayName)}\r\n                                  className=\"w-5 h-5 accent-blue-600 rounded focus:ring-2 focus:ring-blue-400\"\r\n                                />\r\n                                <span>{perm.name ?? perm.displayName}</span>\r\n                              </label>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </TabsContent>\r\n                  )\r\n                })}\r\n              </div>\r\n            </Tabs>\r\n          </div>\r\n        </form>\r\n        <DialogFooter className=\"mt-4 border-t pt-4 bg-white dark:bg-gray-950\">\r\n          <Button\r\n            onClick={(e) => {\r\n              e.preventDefault()\r\n              onCloseEvent()\r\n            }}\r\n            variant=\"ghost\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={onSubmit}>Save</Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "'use client'\r\nimport { QueryNames } from '@/lib/hooks/QueryConstants'\r\nimport { useState } from 'react'\r\n\r\nimport { type IdentityRoleDto, type IdentityRoleUpdateDto } from '@/client'\r\nimport { type PaginationState, type SortingState } from '@tanstack/react-table'\r\n\r\nimport { useToast } from '@/lib/useToast'\r\nimport { Delete } from './Delete'\r\n\r\nimport { DataTable } from '@/components/data-table/DataTable'\r\nimport { TableSkeleton } from '@/components/ui/TableSkeleton'\r\nimport { useRoles } from '@/lib/hooks/useRoles'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Add } from './Add'\r\nimport { getColumns } from './Columns'\r\nimport { Edit } from './Edit'\r\nimport { Filter } from './Filter'\r\nimport { ManageRoleApplications } from './ManageRoleApplications'\r\nimport { RolePermission } from './RolePermission'\r\n\r\nexport const RoleList = () => {\r\n  const { toast } = useToast()\r\n  const queryClient = useQueryClient()\r\n\r\n  const [searchStr, setSearchStr] = useState<string>('')\r\n  const [userActionDialog, setUserActionDialog] = useState<{\r\n    dataId: string\r\n    dataEdit: IdentityRoleDto\r\n    dialogType?: 'edit' | 'permission' | 'delete' | 'application'\r\n  } | null>()\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  })\r\n\r\n  // Initialize sorting state\r\n  const [sorting, setSorting] = useState<SortingState>([\r\n    { id: 'name', desc: false }\r\n  ])\r\n\r\n  // Convert SortingState to API sorting string\r\n  const getSortingString = (sortState: SortingState): string => {\r\n    if (!sortState.length) return 'name asc';\r\n\r\n    const sort = sortState[0]; // Just use the first sort for now\r\n    return `${sort?.id} ${sort?.desc ? 'desc' : 'asc'}`;\r\n  }\r\n\r\n  const { isLoading, data } = useRoles(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    searchStr,\r\n    getSortingString(sorting)\r\n  )\r\n\r\n  // Handler for user actions (edit, permission, delete)\r\n  const handleUserAction = (dataId: string, dataEdit: IdentityRoleDto, dialogType: 'edit' | 'permission' | 'delete' | 'application') => {\r\n    setUserActionDialog({\r\n      dataId,\r\n      dataEdit,\r\n      dialogType,\r\n    })\r\n  }\r\n\r\n  // Get columns with the action handler\r\n  const columns = getColumns(handleUserAction)\r\n\r\n  const handleSearch = (value: string) => {\r\n    setSearchStr(value)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search\r\n  }\r\n\r\n  const handlePaginationChange = (newPagination: PaginationState) => {\r\n    setPagination(newPagination)\r\n  }\r\n\r\n  // Handler for sorting change\r\n  const handleSortingChange = (newSorting: SortingState) => {\r\n    setSorting(newSorting)\r\n    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change\r\n  }\r\n\r\n  // Handler for refreshing the data\r\n  const handleRefresh = () => {\r\n    // Invalidate the query to fetch fresh data\r\n    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n    // Show toast notification after a short delay to match the animation\r\n    setTimeout(() => {\r\n      toast({\r\n        title: \"Data refreshed\",\r\n        description: \"The claims list has been refreshed.\",\r\n        variant: \"success\",\r\n      })\r\n    }, 100)\r\n  }\r\n\r\n  if (isLoading) return (\r\n    <TableSkeleton\r\n      rowCount={pagination.pageSize}\r\n      columnCount={4}\r\n      hasTitle={true}\r\n      hasSearch={true}\r\n      hasFilters={true}\r\n      hasPagination={true}\r\n      hasActions={true}\r\n    />\r\n  )\r\n\r\n  // Ensure we have valid data to render\r\n  const items = data?.items ?? [];\r\n  const totalCount = data?.totalCount ?? 0;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4\">\r\n        <DataTable\r\n          title=\"Roles\"\r\n          columns={columns}\r\n          data={items}\r\n          totalCount={totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          manualSorting={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={handlePaginationChange}\r\n          onSortingChange={handleSortingChange}\r\n          sortingState={sorting}\r\n          onSearch={handleSearch}\r\n          searchValue={searchStr}\r\n          customFilterbar={Filter}\r\n          hideDefaultFilterbar={true}\r\n          onRefresh={handleRefresh}\r\n          enableRowSelection={false}\r\n          actionButton={{\r\n            // label: \"Create New User\",\r\n            onClick: () => { /* Required but not used */ },\r\n            content: <Add />\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {userActionDialog && userActionDialog.dialogType === 'edit' && (\r\n        <Edit\r\n          dataId={userActionDialog.dataId}\r\n          dataEdit={userActionDialog.dataEdit as IdentityRoleUpdateDto}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'delete' && (\r\n        <Delete\r\n          dataId={userActionDialog.dataId}\r\n          onDismiss={() => {\r\n            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })\r\n            setUserActionDialog(null)\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {userActionDialog && userActionDialog.dialogType === 'permission' && (\r\n        <RolePermission\r\n          roleDto={userActionDialog.dataEdit}\r\n          onDismiss={() => setUserActionDialog(null)}\r\n        />\r\n      )}\r\n      {userActionDialog && userActionDialog.dialogType === 'application' && (\r\n        <ManageRoleApplications\r\n          roleDto={userActionDialog.dataEdit}\r\n          onDismiss={() => setUserActionDialog(null)}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n", "'use client';\r\n\r\nimport { RoleList } from '@/components/app/role/List';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Roles\" />\r\n\r\n      <RoleList />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["Delete", "dataId", "on<PERSON><PERSON><PERSON>", "toast", "useToast", "open", "<PERSON><PERSON><PERSON>", "useState", "onYesEvent", "deleteApiIdentityRolesById", "err", "useEffect", "jsx", "AlertDialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "useRoles", "pageIndex", "pageSize", "filter", "sorting", "useQuery", "QueryNames", "skip", "data", "getApiIdentityRoles", "Add", "children", "can", "useGrantedPolicies", "queryClient", "useQueryClient", "handleSubmit", "register", "reset", "useForm", "setSelectedValueType", "createDataMutation", "useMutation", "formData", "postApiIdentityRoles", "onSubmit", "userData", "handleOpenChange", "newOpenState", "Toaster", "Dialog", "DialogTrigger", "<PERSON><PERSON>", "RiAddLine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "e", "FormSection", "FormField", "Input", "Checkbox", "value", "<PERSON><PERSON><PERSON><PERSON>er", "YesNoBadge", "className", "cn", "Actions", "userId", "userDto", "onAction", "variant", "DropdownMenu", "DropdownMenuTrigger", "RiMoreLine", "DropdownMenuContent", "DropdownMenuItem", "RiShieldKeyholeLine", "RiPencilLine", "getColumns", "handleUserAction", "table", "row", "column", "DataTableColumnHeader", "info", "Edit", "dataEdit", "setValue", "isDefault", "setIsDefault", "isPublic", "setIsPublic", "updateDataMutation", "putApiIdentityRolesById", "onCloseEvent", "checked", "Filter", "onSearch", "searchValue", "isFiltered", "Search", "ViewOptions", "useRoleApplicationsByRole", "roleId", "getApiRoleApplicationsByRoleByRoleId", "ManageRoleApplications", "roleDto", "allApplications", "isLoadingApplications", "useOpeniddictApplications", "roleApplications", "isLoadingRoleApps", "selectedApplications", "setSelectedApplications", "app", "id", "useCallback", "postApiRoleApplicationsUpdate", "handleApplicationToggle", "applicationId", "prev", "dialogKey", "useRef", "isLoading", "RolePermission", "hasAllGranted", "setHasAllGranted", "usePermissions", "PermissionProvider", "permissionGroups", "setPermissionGroups", "selectedTab", "setSelectedTab", "hasAllPermissionGranted", "g", "p", "updatedGroups", "group", "permission", "requestPayload", "grant", "putApiPermissionManagementPermissions", "has<PERSON>dmin", "useMemo", "USER_ROLE", "groupPermissionsByParent", "permissions", "parentMap", "otherPermissions", "childActions", "perm", "a", "match", "parent<PERSON><PERSON>", "action", "Permission", "Tabs", "v", "TabsList", "idx", "TabsTrigger", "parentRows", "allTablePerms", "r", "allTableChecked", "handleSelectAllTable", "gIdx", "isParent", "<PERSON><PERSON><PERSON><PERSON>", "c", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent", "RoleList", "searchStr", "setSearchStr", "userActionDialog", "setUserActionDialog", "pagination", "setPagination", "setSorting", "getSortingString", "sortState", "sort", "columns", "dialogType", "handleSearch", "handlePaginationChange", "newPagination", "handleSortingChange", "newSorting", "handleRefresh", "TableSkeleton", "items", "totalCount", "Fragment", "DataTable", "OverViewLayout", "AppLayout", "Head"], "mappings": "g6BAkBO,MAAMA,GAAS,CAAC,CAAE,OAAAC,EAAQ,UAAAC,KAAiC,CAC1D,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAkB,EAAK,EACzCC,EAAa,SAAY,CACzB,GAAA,CACF,MAAMC,GAA2B,CAC/B,KAAM,CAAE,GAAIR,CAAO,CAAA,CACpB,EACKE,EAAA,CACJ,MAAO,UACP,YAAa,qCAAA,CACd,EACSD,EAAA,QACHQ,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,gEACb,QAAS,aAAA,CACV,CACH,CAEJ,EAEAQ,OAAAA,EAAAA,UAAU,IAAM,CACdL,EAAQ,EAAI,CACd,EAAG,EAAE,EAGFM,EAAA,IAAAC,GAAA,CAAY,KAAAR,EACX,SAAAS,OAACC,GACC,CAAA,SAAA,CAAAD,OAACE,GACC,CAAA,SAAA,CAAAJ,EAAAA,IAACK,IAAiB,SAAwB,0BAAA,CAAA,EAC1CL,EAAAA,IAACM,IAAuB,SAExB,4EAAA,CAAA,CAAA,EACF,SACCC,GACC,CAAA,SAAA,CAACP,EAAA,IAAAQ,GAAA,CAAkB,QAASlB,EAAW,SAAM,SAAA,EAC5CU,EAAA,IAAAS,GAAA,CAAkB,QAASb,EAAY,SAAG,KAAA,CAAA,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC7Cac,GAAW,CACtBC,EACAC,EACAC,EACAC,IAEOC,EAAS,CACd,SAAU,CAACC,EAAW,SAAUL,EAAWC,EAAUC,EAAQC,CAAO,EACpE,QAAS,SAAY,CACnB,IAAIG,EAAO,EACPN,EAAY,IACdM,EAAON,EAAYC,GAErB,KAAM,CAAE,KAAAM,GAAS,MAAMC,GAAoB,CACzC,MAAO,CACL,eAAgBP,EAChB,UAAWK,EACX,OAAQJ,EACR,QAASC,CAAA,CACX,CACD,EACM,OAAAI,CAAA,CACT,CACD,ECbUE,GAAM,CAAC,CAAE,SAAAC,KAA+B,CAC7C,KAAA,CAAE,IAAAC,CAAI,EAAIC,EAAmB,EAC7B,CAAC9B,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBgC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,MAAAC,CAAA,EAAUC,GAA+B,EACnE,EAAGC,CAAoB,EAAInC,EAAA,SAAmB,EAAE,EAGtDI,EAAAA,UAAU,IAAM,CACTN,IACGmC,EAAA,CACJ,KAAM,GACN,UAAW,GACX,SAAU,EAAA,CACX,EACDE,EAAqB,CAAA,CAAE,EACzB,EACC,CAACrC,EAAMmC,CAAK,CAAC,EAEhB,MAAMG,EAAqBC,EAAY,CACrC,WAAY,MAAOC,GACjBC,GAAqB,CACnB,KAAM,CACJ,KAAMD,EAAS,MAAQ,GACvB,UAAWA,EAAS,WAAa,GACjC,SAAUA,EAAS,UAAY,EAAA,CACjC,CACD,EACH,UAAW,IAAM,CACT1C,EAAA,CACJ,MAAO,UACP,YAAa,4BACb,QAAS,SAAA,CACV,EACIiC,EAAY,kBAAkB,CAAE,SAAU,CAACR,EAAW,QAAQ,EAAG,EACtEtB,EAAQ,EAAK,CACf,EACA,QAAUI,GAAoC,CACtCP,EAAA,CACJ,MAAOO,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKqC,EAAYF,GAAoC,CAEpD,MAAMG,EAAkC,CACtC,GAAGH,CACL,EAEAF,EAAmB,OAAOK,CAAQ,CACpC,EAEMC,EAAoBC,GAA0B,CAClD5C,EAAQ4C,CAAY,CACtB,EAEA,cACG,UACC,CAAA,SAAA,CAAAtC,EAAA,IAACuC,GAAQ,EAAA,EACRrC,EAAA,KAAAsC,EAAA,CAAO,KAAA/C,EAAY,aAAc4C,EAChC,SAAA,CAACrC,EAAAA,IAAAyC,GAAA,CAAc,QAAO,GAAE,SAAApB,CAAS,CAAA,QAChC,UAAQ,CAAA,UAAU,yCAChB,SAAAC,EAAI,kCAAkC,GACrCpB,OAACwC,EAAO,CAAA,KAAK,KAAK,UAAU,kCAAkC,QAAS,IAAMhD,EAAQ,EAAI,EACvF,SAAA,CAAAM,EAAA,IAAC2C,GAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/D3C,EAAA,IAAA,OAAA,CAAK,UAAU,4BAA4B,SAAQ,UAAA,CAAA,CAAA,CAAA,CACtD,CAEJ,CAAA,EACAE,EAAAA,KAAC0C,EAAc,CAAA,UAAU,YACvB,SAAA,CAAA5C,MAAC6C,EACC,CAAA,SAAA7C,EAAA,IAAC8C,EAAY,CAAA,SAAA,mBAAiB,CAAA,EAChC,EACA5C,EAAAA,KAAC,OAAK,CAAA,SAAUwB,EAAaS,CAAQ,EAAG,UAAU,OAAO,UAAYY,GAAM,CACrEA,EAAE,MAAQ,SAAWA,EAAE,kBAAkB,mBAC3CA,EAAE,eAAe,EACZrB,EAAaS,CAAQ,EAAE,EAG9B,EAAA,SAAA,CAAAnC,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAAC8C,EACC,CAAA,SAAA,CAAAhD,EAAA,IAACiD,EAAA,CACC,MAAM,OACN,YAAY,wBAEZ,SAAAjD,EAAA,IAACkD,IAAM,SAAQ,GAAE,GAAGvB,EAAS,MAAM,EAAG,YAAY,YAAa,CAAA,CAAA,CACjE,EAEA3B,EAAA,IAACiD,EAAA,CACC,MAAM,UACN,YAAY,8BAEZ,SAACjD,EAAAA,IAAAmD,EAAA,CAAU,GAAGxB,EAAS,YAAa,CAClC,WAAayB,GAAUA,IAAU,EAAA,CAClC,CAAG,CAAA,CAAA,CACN,EAEApD,EAAA,IAACiD,EAAA,CACC,MAAM,SACN,YAAY,6BAEZ,SAACjD,EAAAA,IAAAmD,EAAA,CAAU,GAAGxB,EAAS,WAAY,CACjC,WAAayB,GAAUA,IAAU,EAAA,CAClC,CAAG,CAAA,CAAA,CAAA,CACN,CAAA,CACF,CACF,CAAA,EACAlD,EAAAA,KAACmD,EAAa,CAAA,UAAU,OACtB,SAAA,CAAArD,EAAA,IAAC0C,EAAA,CACC,QAAQ,QACR,QAAUK,GAAM,CACdA,EAAE,eAAe,EACjBrD,EAAQ,EAAK,CACf,EACA,SAAUqC,EAAmB,UAC9B,SAAA,QAAA,CAED,EACA/B,EAAAA,IAAC0C,EAAO,CAAA,KAAK,SAAS,SAAUX,EAAmB,UAChD,SAAAA,EAAmB,UAAY,YAAc,MAChD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECtJauB,EAAwC,CAAC,CAAE,MAAAF,EAAO,UAAAG,KAE3DvD,EAAA,IAAC,OAAA,CACC,UAAWwD,GACT,0EACAJ,EACI,oEACA,4DACJG,CACF,EAEC,WAAQ,MAAQ,IAAA,CACnB,ECASE,GAAU,CAAC,CAAE,OAAAC,EAAQ,QAAAC,EAAS,SAAAC,EAAU,QAAAC,EAAU,cAAkC,CACzF,KAAA,CAAE,IAAAvC,CAAI,EAAIC,EAAmB,EAGnC,OAAIsC,IAAY,WAEX7D,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,gBAAC8D,GACC,CAAA,SAAA,CAAC9D,EAAAA,IAAA+D,GAAA,CAAoB,QAAO,GAC1B,SAAA7D,EAAA,KAACwC,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,cAEV,SAAA,CAAC1C,EAAAA,IAAAgE,GAAA,CAAW,UAAU,SAAU,CAAA,EAC/BhE,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEvC,EACCE,EAAA,KAAA+D,GAAA,CAAoB,MAAM,MAAM,UAAU,YACxC,SAAA,CAAA3C,EAAI,0BAA0B,GAC7BtB,EAAA,IAACkE,EAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,MAAM,EAChD,SAAA,MAAA,CAED,EAEDrC,EAAI,qCAAqC,GACxCtB,EAAA,IAACkE,EAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,YAAY,EACtD,SAAA,YAAA,CAED,EAEDrC,EAAI,qCAAqC,GACxCtB,EAAA,IAACkE,EAAA,CACC,UAAU,yBACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,aAAa,EACvD,SAAA,aAAA,CAED,EAEDrC,EAAI,0BAA0B,GAC7BtB,EAAA,IAACkE,EAAA,CACC,UAAU,sCACV,QAAS,IAAMN,EAASF,EAAQC,EAAS,QAAQ,EAClD,SAAA,QAAA,CAAA,CAED,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAMFzD,EAAA,KAAC,MAAI,CAAA,UAAU,sCACZ,SAAA,CAAAoB,EAAI,qCAAqC,GACxCpB,EAAA,KAACwC,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMkB,EAASF,EAAQC,EAAS,YAAY,EAErD,SAAA,CAAC3D,EAAAA,IAAAmE,GAAA,CAAoB,UAAU,SAAU,CAAA,EACzCnE,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,EAEDsB,EAAI,0BAA0B,GAC7BpB,EAAA,KAACwC,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAAS,IAAMkB,EAASF,EAAQC,EAAS,MAAM,EAE/C,SAAA,CAAC3D,EAAAA,IAAAoE,GAAA,CAAa,UAAU,SAAU,CAAA,EAClCpE,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,CAAA,CAAA,CACZ,EAEJ,CAEJ,EC3FaqE,GACXC,GAEO,CACL,CACE,GAAI,SACJ,OAAQ,CAAC,CAAE,MAAAC,CAAA,IACTvE,EAAA,IAACmD,EAAA,CACC,QACEoB,EAAM,2BACF,GACAA,EAAM,sBAAA,EACJ,gBACA,GAER,gBAAiB,IAAMA,EAAM,0BAA0B,EACvD,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,KAAM,CAAC,CAAE,IAAAC,CAAA,IACPxE,EAAA,IAACmD,EAAA,CACC,QAASqB,EAAI,cAAc,EAC3B,gBAAiB,IAAMA,EAAI,eAAe,EAC1C,UAAU,kBACV,aAAW,YAAA,CACb,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,YAAa,QAAA,CAEjB,EACA,CACE,YAAa,OACb,OAAQ,CAAC,CAAE,OAAAC,CAAA,IACRzE,EAAA,IAAA0E,EAAA,CAAsB,OAAAD,EAAgB,MAAM,OAAO,EAEtD,cAAe,GACf,aAAc,GACd,KAAOE,GAASA,EAAK,SAAS,EAC9B,KAAM,CACJ,UAAW,YACX,YAAa,MAAA,CAEjB,EACA,CACE,YAAa,YACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IACRzE,EAAA,IAAA0E,EAAA,CAAsB,OAAAD,EAAgB,MAAM,aAAa,EAE5D,cAAe,GACf,aAAc,GACd,KAAOE,GAAS3E,MAACsD,GAAW,MAAOqB,EAAK,WAAuB,EAC/D,KAAM,CACJ,UAAW,YACX,YAAa,YAAA,CAEjB,EACA,CACE,YAAa,WACb,OAAQ,CAAC,CAAE,OAAAF,CAAA,IACRzE,EAAA,IAAA0E,EAAA,CAAsB,OAAAD,EAAgB,MAAM,YAAY,EAE3D,cAAe,GACf,aAAc,GACd,KAAOE,GAAS3E,MAACsD,GAAW,MAAOqB,EAAK,WAAuB,EAC/D,KAAM,CACJ,UAAW,YACX,YAAa,WAAA,CAEjB,EACA,CACE,GAAI,UACJ,OAAQ,UACR,KAAOA,GACL3E,EAAA,IAACyD,GAAA,CACC,OAAQkB,EAAK,IAAI,SAAS,GAC1B,QAASA,EAAK,IAAI,SAClB,SAAUL,EACV,QAAQ,UAAA,CACV,EAEF,cAAe,GACf,aAAc,GACd,KAAM,CACJ,UAAW,aACX,YAAa,QAAA,CACf,CAEJ,ECjFWM,GAAO,CAAC,CAAE,SAAAC,EAAU,OAAAxF,EAAQ,UAAAC,KAA+B,CACtE,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBgC,EAAcC,EAAe,EAC7B,CAAE,aAAAC,EAAc,SAAAC,EAAU,SAAAmD,CAAA,EAAajD,GAA+B,EACtE,CAACkD,EAAWC,CAAY,EAAIrF,EAAS,SAAAkF,EAAS,WAAa,EAAK,EAChE,CAACI,EAAUC,CAAW,EAAIvF,EAAS,SAAAkF,EAAS,UAAY,EAAK,EAE7DM,EAAqBnD,EAAY,CACrC,WAAY,MAAOC,GACjBmD,GAAwB,CACtB,KAAM,CAAE,GAAI/F,CAAO,EACnB,KAAM,CACJ,KAAM4C,EAAS,MAAQ4C,EAAS,MAAQ,GACxC,UAAW5C,EAAS,WAAa4C,EAAS,WAAa,GACvD,SAAU5C,EAAS,UAAY4C,EAAS,UAAY,EAAA,CACtD,CACD,EACH,UAAW,IAAM,CACTtF,EAAA,CACJ,MAAO,UACP,YAAa,4BACb,QAAS,SAAA,CACV,EACIiC,EAAY,kBAAkB,CAAE,SAAU,CAACR,EAAW,QAAQ,EAAG,EACzDqE,EAAA,CACf,EACA,QAAUvF,GAAoC,CACtCP,EAAA,CACJ,MAAOO,GAAK,OAAO,QACnB,YAAaA,GAAK,OAAO,QACzB,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKqC,EAAYF,GAAoC,CAEpD,MAAMG,EAAkC,CACtC,GAAGH,CACL,EAEAkD,EAAmB,OAAO/C,CAAQ,CACpC,EAEMiD,EAAe,IAAM,CACzB3F,EAAQ,EAAK,EACHJ,EAAA,CACZ,EAGAS,OAAAA,EAAAA,UAAU,IAAM,CACVN,IAEOqF,EAAA,OAAQD,EAAS,MAAQ,EAAE,EAC3BC,EAAA,YAAaD,EAAS,WAAa,EAAK,EACxCC,EAAA,WAAYD,EAAS,UAAY,EAAK,EAEhD,EAAA,CAACpF,EAAMoF,EAAUC,CAAQ,CAAC,EAE7B/E,EAAAA,UAAU,IAAM,CACdL,EAAQ,EAAI,CACd,EAAG,EAAE,QAGF8C,EAAO,CAAA,KAAA/C,EAAY,aAAc4F,EAChC,gBAACzC,EACC,CAAA,SAAA,CAAC5C,EAAA,IAAA6C,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,kBAAgB+B,EAAS,IAAA,CAAA,CAAK,CAC7C,CAAA,EACA3E,EAAA,KAAC,OAAA,CACC,SAAUwB,EAAaS,CAAQ,EAC/B,UAAU,OACV,UAAYY,GAAM,CACZA,EAAE,MAAQ,SAAWA,EAAE,kBAAkB,mBAC3CA,EAAE,eAAe,EACZrB,EAAaS,CAAQ,EAAE,EAEhC,EAEA,SAAA,CAAAnC,MAAC,UAAQ,CAAA,UAAU,iCACjB,SAAAE,EAAA,KAAC8C,EACC,CAAA,SAAA,CAAAhD,EAAA,IAACiD,EAAA,CACC,MAAM,OACN,YAAY,uBAEZ,SAACjD,EAAA,IAAAkD,GAAA,CAAM,SAAQ,GAAE,GAAGvB,EAAS,MAAM,EAAG,aAAckD,EAAS,MAAQ,GAAI,YAAY,WAAY,CAAA,CAAA,CACnG,EAEA7E,EAAA,IAACiD,EAAA,CACC,MAAM,UACN,YAAY,8BAEZ,SAAAjD,EAAA,IAACmD,EAAA,CACC,QAAS4B,EACT,gBAAkBO,GAAY,CACfN,EAAA,CAAC,CAACM,CAAO,EACbR,EAAA,YAAa,CAAC,CAACQ,CAAO,CAAA,CACjC,CAAA,CACF,CACF,EAEAtF,EAAA,IAACiD,EAAA,CACC,MAAM,SACN,YAAY,6BAEZ,SAAAjD,EAAA,IAACmD,EAAA,CACC,QAAS8B,EACT,gBAAkBK,GAAY,CAChBJ,EAAA,CAAC,CAACI,CAAO,EACZR,EAAA,WAAY,CAAC,CAACQ,CAAO,CAAA,CAChC,CAAA,CACF,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACApF,EAAAA,KAACmD,EAAa,CAAA,UAAU,OACtB,SAAA,CAAArD,EAAA,IAAC0C,EAAA,CACC,QAAQ,QACR,QAAUK,GAAM,CACdA,EAAE,eAAe,EACjBrD,EAAQ,EAAK,CACf,EACA,SAAUyF,EAAmB,UAC7B,KAAK,SACN,SAAA,QAAA,CAED,EACAnF,EAAA,IAAC0C,EAAA,CACC,KAAK,SACL,SAAUyC,EAAmB,UAE5B,SAAAA,EAAmB,UAAY,YAAc,MAAA,CAAA,CAChD,CACF,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAEJ,ECrJO,SAASI,GAAc,CAC5B,MAAAhB,EACA,SAAAiB,EACA,YAAAC,EAAc,EAChB,EAAuB,CACrB,MAAMC,EAAanB,EAAM,SAAS,EAAE,cAAc,OAAS,EAGzD,OAAArE,EAAA,KAAC,MAAI,CAAA,UAAU,oDACZ,SAAA,CACCsF,GAAAxF,EAAA,IAAC,MAAI,CAAA,UAAU,oCACb,SAAAA,EAAAA,IAAC2F,IAAO,SAAUH,EAAU,MAAOC,CAAA,CAAa,CAClD,CAAA,EAGFvF,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACZ,SAAA,CACCwF,GAAA1F,EAAA,IAAC0C,EAAA,CACC,QAAQ,QACR,QAAS,IAAM6B,EAAM,mBAAmB,EACxC,UAAU,6HACX,SAAA,eAAA,CAED,EASFvE,MAAC4F,IAAY,MAAArB,CAAc,CAAA,CAAA,CAC7B,CAAA,CAAA,EACF,CAEJ,CC9Ca,MAAAsB,GAA6BC,GACjC/E,EAAS,CACd,SAAU,CAAC,yBAA0B+E,CAAM,EAC3C,QAAS,IAAMC,GAAqC,CAClD,KAAM,CACN,OAAAD,CAAA,CACF,CAAE,EACF,QAAS,CAAC,CAACA,CAAA,CACZ,ECGUE,GAAyB,CAAC,CAAE,QAAAC,EAAS,UAAA3G,KAA6C,CAC7F,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EACrBgC,EAAcC,EAAe,EAG7B,CAAE,KAAMyE,EAAiB,UAAWC,GAA0BC,GAA0B,EAAG,GAAI,EAG/F,CAAE,KAAMC,EAAkB,UAAWC,GAAsBT,GAA0BI,EAAQ,EAAG,EAEhG,CAACM,EAAsBC,CAAuB,EAAI7G,EAAAA,SAAmB,CAAA,CAAE,EAE7EI,EAAAA,UAAU,KACRL,EAAQ,EAAI,EACL,IAAM,CACN8B,EAAY,kBAAkB,CAAE,SAAU,CAAC,wBAAwB,EAAG,CAC7E,GACC,CAACA,CAAW,CAAC,EAGhBzB,EAAAA,UAAU,IAAM,CACVsG,GAAkB,MACpBG,EAAwBH,EAAiB,KACtC,IAAKI,GAA4BA,EAAI,aAAa,EAClD,OAAQC,GAAqBA,IAAO,MAAS,CAChD,CACF,EACC,CAACL,CAAgB,CAAC,EAEf,MAAAhB,EAAesB,EAAAA,YAAY,IAAM,CACrCjH,EAAQ,EAAK,EACHJ,EAAA,CAAA,EACT,CAACA,CAAS,CAAC,EAER6C,EAAWwE,EAAA,YACf,MAAO5D,GAAiB,CACtBA,EAAE,eAAe,EAEb,GAAA,CACF,MAAM6D,GAA8B,CAClC,KAAM,CACJ,OAAQX,EAAQ,GAChB,eAAgBM,CAAA,CAClB,CACD,EAEKhH,EAAA,CACJ,MAAO,UACP,YAAa,oCACb,QAAS,SAAA,CACV,EAEIiC,EAAY,kBAAkB,CACjC,SAAU,CAAC,wBAAwB,CAAA,CACpC,EAEY6D,EAAA,QACNvF,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,wCACb,QAAS,aAAA,CACV,CACH,CAEJ,EACA,CAAC0G,EAAQ,GAAIM,EAAsBhH,EAAOiC,EAAa6D,CAAY,CACrE,EAEMwB,EAA2BC,GAA0B,CACzDN,EACEO,GAAAA,EAAK,SAASD,CAAa,EACvBC,EAAK,OAAaL,GAAAA,IAAOI,CAAa,EACtC,CAAC,GAAGC,EAAMD,CAAa,CAC7B,CACF,EAGME,EAAYC,EAAAA,OAAO,UAAU,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,EAAG,CAAC,CAAC,EAAE,EAAE,QAC3EC,EAAYf,GAAyBG,EAE3C,OACGtG,EAAA,IAAAwC,EAAA,CAAuB,KAAA/C,EAAY,aAAc4F,EAChD,SAACnF,EAAAA,KAAA0C,EAAA,CAAc,UAAU,6CAA6C,MAAO,CAAE,SAAU,QAAS,MAAO,MACvG,EAAA,SAAA,CAAC5C,EAAA,IAAA6C,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,yBAAuBmD,EAAQ,IAAA,CAAA,CAAK,CACnD,CAAA,QACC,OAAK,CAAA,SAAA9D,EAAoB,UAAU,yBAClC,SAAAnC,EAAAA,IAAC,OAAI,UAAU,MACZ,WACEA,EAAA,IAAA,MAAA,CAAI,UAAU,mBAAmB,SAAA,yBAAA,CAAuB,EAExDE,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACF,EAAA,IAAA,MAAA,CAAI,UAAU,qCAAqC,SAEpD,qDAAA,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,yBACZ,SAAA,CAAAgG,GAAiB,OAAO,IAAKO,GAC3BvG,OAAA,QAAA,CAAmB,UAAU,oFAC5B,SAAA,CAAAF,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASuG,EAAqB,SAASE,EAAI,IAAM,EAAE,EACnD,SAAU,IAAMI,EAAwBJ,EAAI,IAAM,EAAE,EACpD,UAAU,kEAAA,CACZ,EACAvG,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAAAF,EAAAA,IAAC,OAAI,UAAU,cAAe,WAAI,aAAeyG,EAAI,UAAY,qBAAsB,CAAA,EACtFA,EAAI,WACHzG,EAAA,IAAC,OAAI,UAAU,gCAAiC,WAAI,SAAU,CAAA,CAAA,CAElE,CAAA,CAAA,GAZUyG,EAAI,EAahB,CACD,GACC,CAACP,GAAiB,OAASA,EAAgB,MAAM,SAAW,IAC3DlG,EAAA,IAAA,MAAA,CAAI,UAAU,yCAAyC,SAExD,2BAAA,CAAA,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CAEJ,CAAA,EACF,EACAE,EAAAA,KAACmD,EAAa,CAAA,UAAU,+CACtB,SAAA,CAAArD,EAAA,IAAC0C,EAAA,CACC,QAAUK,GAAM,CACdA,EAAE,eAAe,EACJsC,EAAA,CACf,EACA,QAAQ,QACT,SAAA,QAAA,CAED,QACC3C,EAAO,CAAA,QAASP,EAAU,SAAU+E,EAAW,SAEhD,MAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,GAvDWF,CAwDb,CAEJ,ECrIaG,GAAiB,CAAC,CAAE,QAAAlB,EAAS,UAAA3G,KAAqC,CAC7E,KAAM,CAACG,EAAMC,CAAO,EAAIC,EAAAA,SAAS,EAAK,EAChC,CAAE,MAAAJ,CAAM,EAAIC,EAAS,EAGrB,CAAC4H,EAAeC,CAAgB,EAAI1H,EAAAA,SAAS,EAAK,EAClD,CAAE,KAAAuB,CAAS,EAAAoG,GAAeC,EAAmB,EAAGtB,EAAQ,MAAQ,MAAS,EACzEzE,EAAcC,EAAe,EAE7B,CAAC+F,EAAkBC,CAAmB,EAAI9H,EAAAA,SAA+B,CAAA,CAAE,EAC3E,CAAC+H,EAAaC,CAAc,EAAIhI,EAAAA,SAAS,CAAC,EAEhDI,EAAAA,UAAU,KACRL,EAAQ,EAAI,EACL,IAAM,CACN8B,EAAY,kBAAkB,CAAE,SAAU,CAAC+F,EAAmB,CAAC,EAAG,CACzE,GAEC,EAAE,EAGLxH,EAAAA,UAAU,IAAM,CACM0H,EAAA,MAAM,QAAQvG,GAAM,MAAM,EAAI,CAAC,GAAGA,EAAK,MAAM,EAAI,EAAE,CAAA,EACtE,CAACA,CAAI,CAAC,EAGTnB,EAAAA,UAAU,IAAM,CACd,GAAImB,GAAM,QAAUA,EAAK,OAAO,OAAS,EAAG,CAC1C,MAAM0G,EAA0B1G,EAAK,OAClC,IAAK2G,GAAMA,EAAE,aAAa,MAAOC,GAAMA,EAAE,SAAS,CAAC,EACnD,MAAO/E,GAAMA,CAAC,EACjBsE,EAAiBO,CAAuB,CAAA,CAC1C,EACC,CAAC1G,CAAI,CAAC,EAGTnB,EAAAA,UAAU,IAAM,CACV,GAAAyH,EAAiB,OAAS,EAAG,CAEzB,MAAAO,EAAgBP,EAAiB,IAAcQ,IAAA,CACnD,GAAGA,EACH,YAAaA,EAAM,aAAa,IAAmBC,IAAA,CACjD,GAAGA,EACH,UAAWb,GACX,GAAK,IAAA,EACP,EAGFK,EAAoBM,CAAa,CAAA,CACnC,EACC,CAACX,CAAa,CAAC,EAEZ,MAAA/B,EAAesB,EAAAA,YAAY,IAAM,CACrCjH,EAAQ,EAAK,EACHJ,EAAA,CAAA,EACT,CAACA,CAAS,CAAC,EAER6C,EAAWwE,EAAA,YACf,MAAO5D,GAAiB,CAGtB,GAFAA,EAAE,eAAe,EAEb,CAACyE,GAAoBA,EAAiB,SAAW,EAAG,OAYxD,MAAMU,EAAuC,CAC3C,YAVcV,EACb,IAAKM,IACHA,EAAE,aAAe,CAAA,GAAI,IAAKK,IAAW,CACpC,KAAMA,EAAM,MAAQ,KACpB,UAAWA,EAAM,WAAa,EAAA,EAC9B,GAEH,KAAK,CAIR,EAEI,GAAA,CACF,MAAMC,GAAsC,CAC1C,MAAO,CACL,YAAanC,EAAQ,MAAQ,GAC7B,aAAcsB,EAAmB,CACnC,EACA,KAAMW,CAAA,CACP,EAEK3I,EAAA,CACJ,MAAO,UACP,YAAa,kCACb,QAAS,SAAA,CACV,EAEIiC,EAAY,kBAAkB,CACjC,SAAU,CAAC+F,EAAmB,CAAC,CAAA,CAChC,EAEYlC,EAAA,QACNvF,EAAc,CACjBA,aAAe,OACXP,EAAA,CACJ,MAAO,SACP,YAAa,uCACb,QAAS,aAAA,CACV,CACH,CAEJ,EACA,CAACiI,EAAkBvB,EAAQ,KAAM1G,EAAOiC,EAAa6D,CAAY,CACnE,EAEMgD,EAAWC,EAAAA,QAAQ,IAAMrC,EAAQ,MAAM,SAASsC,GAAU,KAAK,GAAK,GAAO,CAACtC,CAAO,CAAC,EAGpFe,EAAYC,EAAAA,OAAO,UAAU,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,EAAG,CAAC,CAAC,EAAE,EAAE,QAG3EuB,EAA4BC,GAA6D,CAC7F,GAAI,CAAC,MAAM,QAAQA,CAAW,GAAKA,EAAY,SAAW,EAAG,MAAO,CAAE,WAAY,GAAI,iBAAkB,CAAA,CAAG,EAC3G,MAAMC,EAAkH,CAAC,EACnHC,EAA6C,CAAC,EAC9CC,EAAe,CAAC,SAAU,OAAQ,OAAQ,QAAQ,EAC5C,OAAAH,EAAA,QAASI,GAAS,EAEVA,EAAK,MAAM,MAAM,KAAK,GAAK,CAAA,GAAI,SAChC,GAAK,CAACD,EAAa,KAAKE,GAAKD,EAAK,MAAM,SAAS,IAAMC,CAAC,CAAC,IAE9DJ,EAAAG,EAAK,IAAK,EAAI,CAAE,OAAQA,EAAM,SAAU,EAAG,EACvD,CACD,EAEWJ,EAAA,QAASI,GAAS,CAC5B,MAAME,EAAQF,EAAK,MAAM,MAAM,mCAAmC,EAClE,GAAIE,EAAO,CACH,MAAAC,EAAYD,EAAM,CAAC,EACnBE,EAASF,EAAM,CAAC,EAClBL,EAAUM,CAAS,EACrBN,EAAUM,CAAS,EAAE,SAASC,CAAM,EAAIJ,EAExCF,EAAiB,KAAKE,CAAI,CAC5B,MAGKH,EAAUG,EAAK,IAAK,GACvBF,EAAiB,KAAKE,CAAI,CAE9B,CACD,EACM,CAAE,WAAY,OAAO,OAAOH,CAAS,EAAG,iBAAAC,CAAiB,CAClE,EAEA,OACG3I,EAAA,IAAAwC,EAAA,CAAuB,KAAA/C,EAAY,aAAc4F,EAChD,SAACnF,EAAAA,KAAA0C,EAAA,CAAc,UAAU,6CAA6C,MAAO,CAAE,SAAU,SAAU,MAAO,MACxG,EAAA,SAAA,CAAC5C,EAAA,IAAA6C,EAAA,CACC,gBAACC,EAAY,CAAA,SAAA,CAAA,iBAAemD,EAAQ,IAAA,CAAA,CAAK,CAC3C,CAAA,EACAjG,EAAAA,IAAC,QAAK,SAAAmC,EAAoB,UAAU,yBAClC,SAACjC,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAF,EAAA,IAACkJ,GAAA,CACC,KAAK,wBACL,UAAW9B,EACX,GAAG,cACH,SAAU,CAACiB,EACX,SAAU,IAAM,CACGhB,EAACN,GAAS,CAACA,CAAI,CAClC,EACA,UAAU,WAAA,CACZ,EAGA7G,EAAA,KAACiJ,GAAA,CACC,MAAOzB,EAAY,SAAS,EAC5B,cAAe0B,GAAKzB,EAAe,OAAOyB,CAAC,CAAC,EAC5C,YAAY,WACZ,UAAU,2HAEV,SAAA,CAAApJ,EAAA,IAAC,MAAI,CAAA,UAAU,2BACb,SAAAA,EAAAA,IAACqJ,GAAS,CAAA,QAAQ,SAAS,UAAU,yEAClC,SAAA7B,EAAiB,IAAI,CAACQ,EAAOsB,IAC3BtJ,EAAA,IAAAuJ,GAAA,CAA6B,MAAOD,EAAI,SAAS,EAAG,UAAU,eAC5D,SAAMtB,EAAA,aADSA,EAAM,IAExB,CACD,CACH,CAAA,EACF,EACAhI,MAAC,OAAI,UAAU,wDACZ,WAAiB,IAAI,CAACgI,EAAOsB,IAAQ,CAC9B,KAAA,CAAE,WAAAE,EAAY,iBAAAb,CAAiB,EAAIH,EAAyBR,EAAM,aAAe,EAAE,EAEnFyB,EAA0C,CAC9C,GAAGD,EAAW,IAAIE,GAAKA,EAAE,MAAM,EAC/B,GAAGF,EAAW,QAAQE,GAAK,OAAO,OAAOA,EAAE,QAAQ,CAAC,CACtD,EACMC,EAAkBF,EAAc,OAAS,GAAKA,EAAc,MAAM3B,GAAKA,EAAE,SAAS,EAClF8B,EAAwBtE,GAAqB,CACjDmC,EAA4BV,GAAAA,EAAK,IAAI,CAACc,EAAGgC,IACnCA,IAASP,EAAYzB,EAClB,CACL,GAAGA,EACH,YAAaA,EAAE,aAAa,IAASC,GAAA,CAE7B,MAAAgC,EAAWN,EAAW,KAAKE,GAAKA,EAAE,OAAO,OAAS5B,EAAE,IAAI,EACxDiC,EAAUP,EAAW,KAAKE,GAAK,OAAO,OAAOA,EAAE,QAAQ,EAAE,KAAUM,IAAAA,GAAE,OAASlC,EAAE,IAAI,CAAC,EAC3F,OAAIgC,GAAYC,EACP,CAAE,GAAGjC,EAAG,UAAWxC,CAAQ,EAE7BwC,CACR,CAAA,CACH,CACD,CAAC,CACJ,EACA,cACGmC,GAA6B,CAAA,MAAOX,EAAI,WAAY,UAAU,kBAE7D,SAAA,CAACpJ,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAF,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS2J,EACT,SAAU5G,GAAK6G,EAAqB7G,EAAE,OAAO,OAAO,EACpD,aAAW,uCACX,UAAU,kEAAA,CACZ,EACA/C,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,EAClB,EAECwJ,EAAW,OAAS,GAClBtJ,EAAA,KAAA,QAAA,CAAM,UAAU,yBACf,SAAA,CAACF,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,sBAAsB,SAAI,OAAA,EACvCA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAM,SAAA,EAC3CA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAI,OAAA,EACzCA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAM,SAAA,EAC3CA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAI,OAAA,EACzCA,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAM,QAAA,CAAA,CAAA,CAAA,CAC9C,CACF,CAAA,EACCA,EAAA,IAAA,QAAA,CACE,SAAWwJ,EAAA,IAAI,CAAC,CAAE,OAAAU,EAAQ,SAAA7I,CACzB,IAAAnB,EAAA,KAAC,KAAqB,CAAA,UAAU,WAC9B,SAAA,CAAAF,MAAC,MAAG,UAAU,wBAAyB,SAAOkK,EAAA,aAAeA,EAAO,KAAK,EAEzElK,EAAAA,IAAC,KAAG,CAAA,UAAU,wBACZ,SAAAA,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASkK,EAAO,UAChB,SAAU,IAAM,CACdzC,EAA4BV,GAAAA,EAAK,IAAI,CAACc,EAAGgC,IACnCA,IAASP,EAAYzB,EAClB,CACL,GAAGA,EACH,YAAaA,EAAE,aAAa,IAC1BC,GAAAA,EAAE,OAASoC,EAAO,KAAO,CAAE,GAAGpC,EAAG,UAAW,CAACA,EAAE,SAAA,EAAcA,CAAA,CAEjE,CACD,CAAC,CACJ,EACA,aAAY,OAAOoC,EAAO,aAAeA,EAAO,IAAI,EACpD,UAAU,kEAAA,CAAA,EAEd,EAEC,CAAC,OAAQ,SAAU,OAAQ,QAAQ,EAAE,IACpCjB,GAAAjJ,EAAA,IAAC,KAAgB,CAAA,UAAU,wBACxB,SAAAqB,EAAS4H,CAAM,EACdjJ,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASqB,EAAS4H,CAAM,EAAE,UAC1B,SAAU,IAAM,CACdxB,EAA4BV,GAAAA,EAAK,IAAI,CAACc,EAAGgC,IACnCA,IAASP,EAAYzB,EAClB,CACL,GAAGA,EACH,YAAaA,EAAE,aAAa,IAC1BC,GAAAA,EAAE,OAASzG,EAAS4H,CAAM,EAAE,KAAO,CAAE,GAAGnB,EAAG,UAAW,CAACA,EAAE,WAAcA,CAAA,CAE3E,CACD,CAAC,CACJ,EACA,aAAY,OAAOzG,EAAS4H,CAAM,EAAE,aAAe5H,EAAS4H,CAAM,EAAE,IAAI,EACxE,UAAU,kEAAA,CAAA,EAEV,IAnBG,EAAAA,CAoBT,CACD,CAAA,GA7CMiB,EAAO,IA8ChB,CACD,CACH,CAAA,CAAA,EACF,EAGDvB,EAAiB,OAAS,GACxBzI,EAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACF,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAAiB,oBAAA,EACrDA,EAAA,IAAC,MAAI,CAAA,UAAU,wCACZ,SAAA2I,EAAiB,IAChBE,GAAA3I,EAAA,KAAC,QAAsB,CAAA,UAAU,0BAC/B,SAAA,CAAAF,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS6I,EAAK,UACd,SAAU,IAAM,CACdpB,EAA4BV,GAAAA,EAAK,IAAI,CAACc,EAAGgC,IACnCA,IAASP,EAAYzB,EAClB,CACL,GAAGA,EACH,YAAaA,EAAE,aAAa,IAC1BC,GAAAA,EAAE,OAASe,EAAK,KAAO,CAAE,GAAGf,EAAG,UAAW,CAACA,EAAE,SAAA,EAAcA,CAAA,CAE/D,CACD,CAAC,CACJ,EACA,aAAY,OAAOe,EAAK,MAAQA,EAAK,WAAW,EAChD,UAAU,kEAAA,CACZ,EACC7I,EAAA,IAAA,OAAA,CAAM,SAAK6I,EAAA,MAAQA,EAAK,WAAY,CAAA,CAAA,GAlB3BA,EAAK,IAmBjB,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,EA1Gcb,EAAM,IA4GxB,CAAA,CAEH,CACH,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACA9H,EAAAA,KAACmD,EAAa,CAAA,UAAU,+CACtB,SAAA,CAAArD,EAAA,IAAC0C,EAAA,CACC,QAAUK,GAAM,CACdA,EAAE,eAAe,EACJsC,EAAA,CACf,EACA,QAAQ,QACT,SAAA,QAAA,CAED,EACCrF,EAAA,IAAA0C,EAAA,CAAO,QAASP,EAAU,SAAI,MAAA,CAAA,CAAA,CACjC,CAAA,CAAA,CACF,CAAA,GA5LW6E,CA6Lb,CAEJ,ECxVamD,GAAW,IAAM,CACtB,KAAA,CAAE,MAAA5K,CAAM,EAAIC,EAAS,EACrBgC,EAAcC,EAAe,EAE7B,CAAC2I,EAAWC,CAAY,EAAI1K,EAAAA,SAAiB,EAAE,EAC/C,CAAC2K,EAAkBC,CAAmB,EAAI5K,WAItC,EAEJ,CAAC6K,EAAYC,CAAa,EAAI9K,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAGK,CAACmB,EAAS4J,CAAU,EAAI/K,WAAuB,CACnD,CAAE,GAAI,OAAQ,KAAM,EAAM,CAAA,CAC3B,EAGKgL,EAAoBC,GAAoC,CACxD,GAAA,CAACA,EAAU,OAAe,MAAA,WAExB,MAAAC,EAAOD,EAAU,CAAC,EACxB,MAAO,GAAGC,GAAM,EAAE,IAAIA,GAAM,KAAO,OAAS,KAAK,EACnD,EAEM,CAAE,UAAA3D,EAAW,KAAAhG,CAAA,EAASR,GAC1B8J,EAAW,UACXA,EAAW,SACXJ,EACAO,EAAiB7J,CAAO,CAC1B,EAYMgK,EAAUzG,GATS,CAAChF,EAAgBwF,EAA2BkG,IAAiE,CAChHR,EAAA,CAClB,OAAAlL,EACA,SAAAwF,EACA,WAAAkG,CAAA,CACD,CACH,CAG2C,EAErCC,EAAgB5H,GAAkB,CACtCiH,EAAajH,CAAK,EAClBqH,MAAuB,CAAE,GAAG1D,EAAM,UAAW,GAAI,CACnD,EAEMkE,EAA0BC,GAAmC,CACjET,EAAcS,CAAa,CAC7B,EAGMC,EAAuBC,GAA6B,CACxDV,EAAWU,CAAU,EACrBX,MAAuB,CAAE,GAAG1D,EAAM,UAAW,GAAI,CACnD,EAGMsE,EAAgB,IAAM,CAErB7J,EAAY,kBAAkB,CAAE,SAAU,CAACR,EAAW,QAAQ,EAAG,EAEtE,WAAW,IAAM,CACTzB,EAAA,CACJ,MAAO,iBACP,YAAa,sCACb,QAAS,SAAA,CACV,GACA,GAAG,CACR,EAEA,GAAI2H,EACF,OAAAlH,EAAA,IAACsL,GAAA,CACC,SAAUd,EAAW,SACrB,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CACd,EAII,MAAAe,EAAQrK,GAAM,OAAS,CAAC,EACxBsK,EAAatK,GAAM,YAAc,EAEvC,OAEIhB,EAAA,KAAAuL,WAAA,CAAA,SAAA,CAACzL,EAAAA,IAAA,MAAA,CAAI,UAAU,+EACb,SAAAA,EAAA,IAAC0L,GAAA,CACC,MAAM,QACN,QAAAZ,EACA,KAAMS,EACN,WAAAC,EACA,UAAAtE,EACA,iBAAkB,GAClB,cAAe,GACf,SAAUsD,EAAW,SACrB,mBAAoBS,EACpB,gBAAiBE,EACjB,aAAcrK,EACd,SAAUkK,EACV,YAAaZ,EACb,gBAAiB7E,GACjB,qBAAsB,GACtB,UAAW8F,EACX,mBAAoB,GACpB,aAAc,CAEZ,QAAS,IAAM,CAA8B,EAC7C,cAAUjK,GAAI,CAAA,CAAA,CAAA,CAChB,CAAA,EAEJ,EAECkJ,GAAoBA,EAAiB,aAAe,QACnDtK,EAAA,IAAC4E,GAAA,CACC,OAAQ0F,EAAiB,OACzB,SAAUA,EAAiB,SAC3B,UAAW,IAAM,CACV9I,EAAY,kBAAkB,CAAE,SAAU,CAACR,EAAW,QAAQ,EAAG,EACtEuJ,EAAoB,IAAI,CAAA,CAC1B,CACF,EAEDD,GAAoBA,EAAiB,aAAe,UACnDtK,EAAA,IAACZ,GAAA,CACC,OAAQkL,EAAiB,OACzB,UAAW,IAAM,CACV9I,EAAY,kBAAkB,CAAE,SAAU,CAACR,EAAW,QAAQ,EAAG,EACtEuJ,EAAoB,IAAI,CAAA,CAC1B,CACF,EAGDD,GAAoBA,EAAiB,aAAe,cACnDtK,EAAA,IAACmH,GAAA,CACC,QAASmD,EAAiB,SAC1B,UAAW,IAAMC,EAAoB,IAAI,CAAA,CAC3C,EAEDD,GAAoBA,EAAiB,aAAe,eACnDtK,EAAA,IAACgG,GAAA,CACC,QAASsE,EAAiB,SAC1B,UAAW,IAAMC,EAAoB,IAAI,CAAA,CAAA,CAC3C,EAEJ,CAEJ,EC5KA,SAAwBoB,IAAiB,CACvC,cACGC,GACC,CAAA,SAAA,CAAC5L,EAAAA,IAAA6L,GAAA,CAAK,MAAM,OAAQ,CAAA,QAEnB1B,GAAS,CAAA,CAAA,CAAA,EACZ,CAEJ"}