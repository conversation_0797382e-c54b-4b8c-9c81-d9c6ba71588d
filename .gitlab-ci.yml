# GitLab CI/CD Configuration
# Using Docker 24.0.7 with <PERSON><PERSON><PERSON><PERSON> disabled to avoid compatibility issues
# All deployments target imdevapp18 node

stages:
  - build
  - migrate
  - deploy

# Global variables
variables:
  # Disable git clean to avoid permission issues
  GIT_CLEAN_FLAGS: none
  GIT_STRATEGY: fetch

  # Docker configuration for GitLab.com shared runners
  DOCKER_DRIVER: overlay2
  # DOCKER_TLS_CERTDIR is set in individual jobs

  # NuGet package caching
  NUGET_PACKAGES_DIRECTORY: ".nuget"

  # Common variables
  SEQ_SERVER_URL: "http://**********:5341"
  SEQ_API_KEY: "PWGKGQqjozYkMWa0x6lN"
  CERT_PASSPHRASE: "c31cfec0-c4ac-4219-b65d-87e29fa8e042"
  ENCRYPTION_PASSPHRASE: "k3q5h6xbMkFjb5se"
  DEV_EXTERNAL_AUTH_URL: "http://***************/api/common/RequestAuthenticationToken"
  PROD_EXTERNAL_AUTH_URL: "http://***************/api/common/RequestAuthenticationToken"
  # Active Directory variables
  AD_DEV_PASSWORD: "Sulawesi1"
  AD_DEV_TOKEN_SECRET: "imip-identity-server-secure-token-key-dev"
  AD_SERVICE_PASSWORD: "YourProductionADPassword"

  DEV_APP_NAME: "Imip.IdentityServer.DEV"
  PROD_APP_NAME: "Imip.IdentityServer.PROD"

  # Redis configuration
  DEV_REDIS_CONFIGURATION: "**********:6378"
  PROD_REDIS_CONFIGURATION: "**********:6379"

  # RabbitMQ configuration
  RABBITMQ_HOST: "**********"
  RABBITMQ_PORT: "5672"
  RABBITMQ_USERNAME: "guest"
  RABBITMQ_PASSWORD: "guest"
  RABBITMQ_EXCHANGE_NAME: "LogoutEvents"

  # Target node for deployment
  TARGET_NODE: "imdevapp18"
  DEV_HOSTNAME: "imdevapp18"
  PROD_HOSTNAME: "imprdapp27"

  # Certificate variables - the actual certificate will be stored in GitLab CI/CD variables
  # IDENTITY_SERVER_CERT: Base64 encoded .pfx file stored in GitLab CI/CD variables

  # Development environment variables
  DEV_DB_CONNECTION: "Server=**************;Database=identityprovider_dev;User ID=identityprovider_dev;Password=${DB_PASSWORD_DEV};TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"
  DEV_APP_URL: "https://api-identity-dev.imip.co.id"
  DEV_CLIENT_URL: "https://identity-dev.imip.co.id"
  DEV_CORS_ORIGINS: "https://identity-dev.imip.co.id,http://identity-dev.imip.co.id,https://api-identity-dev.imip.co.id,http://api-identity-dev.imip.co.id,https://localhost:44309,http://localhost:3000"

  # Production environment variables
  PROD_DB_CONNECTION: "Server=**************;Database=identityprovider_prd;User ID=identityprovider_prd;Password=${DB_PASSWORD_PROD};TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"
  PROD_APP_URL: "https://identity.imip.co.id"
  PROD_CLIENT_URL: "https://identity.imip.co.id"
  PROD_CORS_ORIGINS: "https://identity.imip.co.id,http://identity.imip.co.id,https://identity.imip.co.id,http://identity.imip.co.id,http://localhost:3000,http://localhost:5000,http://localhost:5001,http://localhost:5002"

# Build stage for development

# Build .NET applications for development using Docker
build-dotnet-dev:
  stage: build
  tags:
    - docker-builder # Use your self-hosted runner to avoid GitLab minutes
  variables:
    BUILD_DIR: "${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-nuget
    paths:
      - ${NUGET_PACKAGES_DIRECTORY}
    policy: pull-push
  before_script:
    # Clean up any previous build artifacts to avoid permission issues
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
    - |
      if [ -d "$BUILD_DIR" ]; then
        echo "Cleaning up previous build directory..."
        # Special handling for problematic DLL files
        echo "Handling problematic DLL files..."
        find $BUILD_DIR -name "*.dll" -type f -exec chmod -f 644 {} \; 2>/dev/null || true

        # Use find with chmod to handle permissions better for all files
        find $BUILD_DIR -type f -exec chmod -f 644 {} \; 2>/dev/null || true
        find $BUILD_DIR -type d -exec chmod -f 755 {} \; 2>/dev/null || true

        # Try to remove the directory, but don't fail if it can't be fully removed
        echo "Note: Some permission errors during cleanup are expected and can be safely ignored"
        rm -rf $BUILD_DIR || echo "Warning: Could not fully remove $BUILD_DIR, continuing anyway"
      fi
    - mkdir -p $BUILD_DIR
    - mkdir -p $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context
    # Create a clean copy of the repository to avoid permission issues
    - mkdir -p $BUILD_DIR/repo
    - cp -r * $BUILD_DIR/repo/ || true
  script:
    # Create NuGet packages directory if it doesn't exist
    - mkdir -p ${NUGET_PACKAGES_DIRECTORY}

    # Build Web Application using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/web-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj && dotnet publish src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj -c Release -o /output"

    # Build DB Migrator using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/migrator-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj && dotnet publish src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj -c Release -o /output"

    # Create a Docker build context with the published files
    - mkdir -p $BUILD_DIR/docker-context/web $BUILD_DIR/docker-context/db-migrator
    - cp -r $BUILD_DIR/web-build/* $BUILD_DIR/docker-context/web/
    - cp -r $BUILD_DIR/migrator-build/* $BUILD_DIR/docker-context/db-migrator/
    - cp $BUILD_DIR/repo/src/Imip.IdentityServer.Web/entrypoint.sh $BUILD_DIR/docker-context/web/
    - cp $BUILD_DIR/repo/src/Imip.IdentityServer.Web/Dockerfile.new $BUILD_DIR/docker-context/Dockerfile
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}/docker-context/
    expire_in: 1 hour
  only:
    - dev

# Build Docker images for development on dedicated builder
build-docker-dev:
  stage: build
  tags:
    - docker-builder # This targets your new runner on imdevapp20
  variables:
    DOCKER_DEFAULT_PLATFORM: "linux/amd64" # Explicitly set the target platform
    CLEANUP_OLDER_THAN: "168h" # Remove images older than 7 days
  before_script:
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    # Cleanup old images to optimize storage
    - echo "Cleaning up old Docker images to optimize storage..."
    - docker image prune -a --filter "until=$CLEANUP_OLDER_THAN" --force || true
  script:
    # Set build directory
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}" # Was "/tmp/gitlab-build-${CI_PIPELINE_ID}"

    # Build Docker images with explicit platform using the prepared context
    - cd $BUILD_DIR/docker-context
    # Build and push web image
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=web .
    - docker push $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA}
    # Copy DB migrator Dockerfile and build
    - cp -f $BUILD_DIR/repo/src/Imip.IdentityServer.DbMigrator/Dockerfile.new ./Dockerfile || cp -f $CI_PROJECT_DIR/src/Imip.IdentityServer.DbMigrator/Dockerfile.new ./Dockerfile
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=db-migrator .
    - docker push $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA}

    # Tag with branch name for easier reference
    - docker tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
  after_script:
    # Clean up build directory
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
    - |
      if [ -d "$BUILD_DIR" ]; then
        echo "Cleaning up build directory..."
        # Special handling for problematic DLL files
        echo "Handling problematic DLL files..."
        find $BUILD_DIR -name "*.dll" -type f -exec chmod -f 644 {} \; 2>/dev/null || true

        # Use find with chmod to handle permissions better for all files
        find $BUILD_DIR -type f -exec chmod -f 644 {} \; 2>/dev/null || true
        find $BUILD_DIR -type d -exec chmod -f 755 {} \; 2>/dev/null || true

        # Try to remove the directory, but don't fail if it can't be fully removed
        echo "Note: Some permission errors during cleanup are expected and can be safely ignored"
        rm -rf $BUILD_DIR || echo "Warning: Could not fully remove $BUILD_DIR, continuing anyway"
      fi
    # Additional cleanup after build to free up space
    - echo "Removing build-specific images to free up space..."
    - docker rmi $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || true
    - docker rmi $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || true
    # Clean up dangling images and containers
    - docker system prune -f || true
  needs:
    - build-dotnet-dev
  only:
    - dev

# Build stage for production

# Build .NET applications for production using Docker
build-dotnet-prod:
  stage: build
  timeout: 30m  # Prevent infinite hanging on network issues
  tags:
    - docker-builder-prod # Use your self-hosted runner to avoid GitLab minutes
  variables:
    BUILD_DIR: "${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-nuget-1-protected
    paths:
      - ${NUGET_PACKAGES_DIRECTORY}
    policy: pull-push
    when: always
  before_script:
    # Set umask to ensure files are created with permissive permissions
    - umask 0000

    # Set DOTNET_CLI_HOME environment variable
    - export DOTNET_CLI_HOME="/tmp/dotnet_cli_home"

    # Clean up any previous build artifacts to avoid permission issues
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
    - rm -rf $BUILD_DIR || true
    - mkdir -p $BUILD_DIR
    - chmod 777 $BUILD_DIR
    - mkdir -p $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context
    - chmod -R 777 $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context

    # Create a clean copy of the repository to avoid permission issues
    # Exclude the build directory from the copy to prevent recursive copy
    - mkdir -p $BUILD_DIR/repo
    - chmod 777 $BUILD_DIR/repo
    - find . -maxdepth 1 ! -name "build-*" ! -name "." -exec cp -r {} $BUILD_DIR/repo/ \;

    # Create dotnet CLI home directory with proper permissions
    - mkdir -p $DOTNET_CLI_HOME
    - chmod 777 $DOTNET_CLI_HOME
  script:
    # Create NuGet packages directory if it doesn't exist
    - mkdir -p ${NUGET_PACKAGES_DIRECTORY}

    # Build Web Application using Docker with network fixes and retry logic
    - |
      echo "Building Web Application with enhanced network configuration..."
      MAX_RETRIES=3
      RETRY_COUNT=0

      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        echo "Attempt $((RETRY_COUNT+1)) of $MAX_RETRIES for Web Application build..."

        if docker run --rm \
          --network=host \
          --dns=******* --dns=******* \
          -e DOTNET_SYSTEM_NET_HTTP_USESOCKETSHTTPHANDLER=0 \
          -e NUGET_XMLDOC_MODE=skip \
          -v $BUILD_DIR/repo:/src \
          -v $BUILD_DIR/web-build:/output \
          -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages \
          mcr.microsoft.com/dotnet/sdk:9.0 \
          /bin/bash -c "
            cd /src &&
            echo 'Testing network connectivity...' &&
            ping -c 2 api.nuget.org || echo 'Direct ping failed, continuing...' &&
            echo 'Starting restore with retry settings...' &&
            dotnet restore --verbosity normal --disable-parallel --force --no-cache --source https://api.nuget.org/v3/index.json src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj &&
            echo 'Starting publish...' &&
            dotnet publish src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj -c Release -o /output --no-restore
          "; then
          echo "Web Application build succeeded!"
          break
        else
          RETRY_COUNT=$((RETRY_COUNT+1))
          if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
            echo "Web Application build failed after $MAX_RETRIES attempts"
            echo "Trying alternative approach with offline packages..."
            # Try using only cached packages as fallback
            docker run --rm \
              -v $BUILD_DIR/repo:/src \
              -v $BUILD_DIR/web-build:/output \
              -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages \
              mcr.microsoft.com/dotnet/sdk:9.0 \
              /bin/bash -c "
                cd /src &&
                echo 'Attempting build with cached packages only...' &&
                dotnet restore --verbosity normal --packages /root/.nuget/packages --no-dependencies src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj &&
                dotnet publish src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj -c Release -o /output --no-restore
              " || exit 1
            break
          fi
          echo "Web Application build failed, waiting 30 seconds before retry..."
          sleep 30
        fi
      done

    # Build DB Migrator using Docker with network fixes and retry logic
    - |
      echo "Building DB Migrator with enhanced network configuration..."
      MAX_RETRIES=3
      RETRY_COUNT=0

      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        echo "Attempt $((RETRY_COUNT+1)) of $MAX_RETRIES for DB Migrator build..."

        if docker run --rm \
          --network=host \
          --dns=******* --dns=******* \
          -e DOTNET_SYSTEM_NET_HTTP_USESOCKETSHTTPHANDLER=0 \
          -e NUGET_XMLDOC_MODE=skip \
          -v $BUILD_DIR/repo:/src \
          -v $BUILD_DIR/migrator-build:/output \
          -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages \
          mcr.microsoft.com/dotnet/sdk:9.0 \
          /bin/bash -c "
            cd /src &&
            echo 'Testing network connectivity...' &&
            ping -c 2 api.nuget.org || echo 'Direct ping failed, continuing...' &&
            echo 'Starting restore with retry settings...' &&
            dotnet restore --verbosity normal --disable-parallel --force --no-cache --source https://api.nuget.org/v3/index.json src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj &&
            echo 'Starting publish...' &&
            dotnet publish src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj -c Release -o /output --no-restore
          "; then
          echo "DB Migrator build succeeded!"
          break
        else
          RETRY_COUNT=$((RETRY_COUNT+1))
          if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
            echo "DB Migrator build failed after $MAX_RETRIES attempts"
            echo "Trying alternative approach with offline packages..."
            # Try using only cached packages as fallback
            docker run --rm \
              -v $BUILD_DIR/repo:/src \
              -v $BUILD_DIR/migrator-build:/output \
              -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages \
              mcr.microsoft.com/dotnet/sdk:9.0 \
              /bin/bash -c "
                cd /src &&
                echo 'Attempting build with cached packages only...' &&
                dotnet restore --verbosity normal --packages /root/.nuget/packages --no-dependencies src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj &&
                dotnet publish src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj -c Release -o /output --no-restore
              " || exit 1
            break
          fi
          echo "DB Migrator build failed, waiting 30 seconds before retry..."
          sleep 30
        fi
      done

    # Create a Docker build context with the published files
    - mkdir -p $BUILD_DIR/docker-context/web $BUILD_DIR/docker-context/db-migrator
    - cp -r $BUILD_DIR/web-build/* $BUILD_DIR/docker-context/web/
    - cp -r $BUILD_DIR/migrator-build/* $BUILD_DIR/docker-context/db-migrator/
    - cp $BUILD_DIR/repo/src/Imip.IdentityServer.Web/entrypoint.sh $BUILD_DIR/docker-context/web/
    - cp $BUILD_DIR/repo/src/Imip.IdentityServer.Web/Dockerfile.new $BUILD_DIR/docker-context/Dockerfile
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}/docker-context/
    expire_in: 1 hour
  retry:
    max: 2
    when:
      - unknown_failure
      - api_failure
      - stuck_or_timeout_failure
      - runner_system_failure
  only:
    - main

# Build Docker images for production on dedicated builder
build-docker-prod:
  stage: build
  timeout: 45m  # Prevent infinite hanging on network issues
  tags:
    - docker-builder-prod # This targets your new runner on imdevapp20
  variables:
    DOCKER_DEFAULT_PLATFORM: "linux/amd64" # Explicitly set the target platform
    CLEANUP_OLDER_THAN: "168h" # Remove images older than 14 days for production
    DOCKER_BUILDKIT: "1" # Enable BuildKit for better performance and caching
  before_script:
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

    # Configure DNS for better network connectivity
    - |
      echo "Configuring DNS for better network connectivity..."
      # Test network connectivity
      ping -c 2 ******* || echo "Warning: Cannot reach Google DNS"
      ping -c 2 registry.gitlab.com || echo "Warning: Cannot reach GitLab registry"

      # Show current DNS configuration
      echo "Current DNS configuration:"
      cat /etc/resolv.conf || echo "Cannot read resolv.conf"

    # Cleanup old images to optimize storage
    - echo "Cleaning up old Docker images to optimize storage..."
    - docker image prune -a --filter "until=${CLEANUP_OLDER_THAN}" --force || true
    - docker system prune --volumes -f || true
  script:
    # Set build directory
    # Fix 2: Use consistent build directory path
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}" # Was "/tmp/gitlab-build-${CI_PIPELINE_ID}"

    # Build Docker images with explicit platform using the prepared context
    - cd $BUILD_DIR/docker-context
    # Build and push web image with retry logic
    - echo "Building web image with tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA}..."
    - |
      # Build with retry logic (DNS configuration is handled in Dockerfile)
      MAX_RETRIES=3
      RETRY_COUNT=0

      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        echo "Docker build attempt $((RETRY_COUNT+1)) of $MAX_RETRIES..."

        if DOCKER_BUILDKIT=1 docker build \
          --platform linux/amd64 \
          --build-arg BUILDKIT_INLINE_CACHE=1 \
          --build-arg PUBLISH_DIR=web \
          -t $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} \
          .; then
          echo "Docker build successful!"
          break
        else
          RETRY_COUNT=$((RETRY_COUNT+1))
          if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
            echo "Docker build failed after $MAX_RETRIES attempts"
            echo "Trying fallback build without BuildKit..."
            # Fallback without BuildKit
            if docker build \
              --platform linux/amd64 \
              --build-arg PUBLISH_DIR=web \
              -t $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} \
              .; then
              echo "Fallback Docker build successful!"
              break
            else
              echo "All build attempts failed"
              exit 1
            fi
          fi
          echo "Docker build failed, waiting 30 seconds before retry..."
          sleep 30
        fi
      done
    - docker images | grep $CI_REGISTRY_IMAGE/web || echo "Web image not found in local registry"
    - echo "Pushing web image to registry..."
    - docker push $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || { echo "Failed to push web image"; exit 1; }

    # Copy DB migrator Dockerfile and build with retry logic
    - cp -f $BUILD_DIR/repo/src/Imip.IdentityServer.DbMigrator/Dockerfile.new ./Dockerfile || cp -f $CI_PROJECT_DIR/src/Imip.IdentityServer.DbMigrator/Dockerfile.new ./Dockerfile
    - echo "Building db-migrator image with tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA}..."
    - |
      # Build DB migrator with retry logic (DNS configuration is handled in Dockerfile)
      MAX_RETRIES=3
      RETRY_COUNT=0

      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        echo "DB migrator Docker build attempt $((RETRY_COUNT+1)) of $MAX_RETRIES..."

        if DOCKER_BUILDKIT=1 docker build \
          --platform linux/amd64 \
          --build-arg BUILDKIT_INLINE_CACHE=1 \
          --build-arg PUBLISH_DIR=db-migrator \
          -t $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} \
          .; then
          echo "DB migrator Docker build successful!"
          break
        else
          RETRY_COUNT=$((RETRY_COUNT+1))
          if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
            echo "DB migrator Docker build failed after $MAX_RETRIES attempts"
            echo "Trying fallback build without BuildKit..."
            # Fallback without BuildKit
            if docker build \
              --platform linux/amd64 \
              --build-arg PUBLISH_DIR=db-migrator \
              -t $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} \
              .; then
              echo "Fallback DB migrator Docker build successful!"
              break
            else
              echo "All DB migrator build attempts failed"
              exit 1
            fi
          fi
          echo "DB migrator Docker build failed, waiting 30 seconds before retry..."
          sleep 30
        fi
      done
    - docker images | grep $CI_REGISTRY_IMAGE/db-migrator || echo "DB migrator image not found in local registry"
    - echo "Pushing db-migrator image to registry..."
    - docker push $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || { echo "Failed to push db-migrator image"; exit 1; }

    # Verify images exist in registry before tagging
    - echo "Verifying images exist in registry before tagging..."
    - docker pull $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || { echo "Failed to pull web image from registry"; exit 1; }
    - docker pull $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || { echo "Failed to pull db-migrator image from registry"; exit 1; }

    # Tag with branch name for easier reference
    - echo "Tagging images with branch name $CI_COMMIT_REF_SLUG..."
    - docker tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
  after_script:
    # Clean up build directory with improved permission handling (non-blocking)
    - |
      set +e  # Don't exit on errors in cleanup
      BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
      echo "Starting cleanup process for: $BUILD_DIR"

      if [ -d "$BUILD_DIR" ]; then
        echo "Using Docker to clean up build directory with root privileges..."

        # Method 1: Use Docker with root privileges to clean up the directory
        if docker run --rm \
          -v "$BUILD_DIR:/cleanup" \
          --entrypoint="" \
          alpine:latest \
          sh -c "
            echo 'Cleaning up /cleanup directory...'
            rm -rf /cleanup/* /cleanup/.[^.]* /cleanup/..?* 2>/dev/null || true
            echo 'Docker cleanup completed'
          "; then
          echo "✅ Docker cleanup successful"
        else
          echo "⚠️ Docker cleanup failed, trying alternative methods..."

          # Method 2: Try to change ownership using Docker
          echo "Attempting to fix ownership..."
          docker run --rm \
            -v "$BUILD_DIR:/cleanup" \
            --entrypoint="" \
            alpine:latest \
            sh -c "chown -R 1000:1000 /cleanup 2>/dev/null || true" 2>/dev/null || true

          # Method 3: Try manual cleanup with permission fixes
          echo "Attempting manual cleanup..."
          find "$BUILD_DIR" -type f -exec chmod 666 {} \; 2>/dev/null || true
          find "$BUILD_DIR" -type d -exec chmod 777 {} \; 2>/dev/null || true
          rm -rf "$BUILD_DIR" 2>/dev/null || echo "Manual cleanup completed with warnings"
        fi

        # Verify cleanup (informational only)
        if [ ! -d "$BUILD_DIR" ] || [ -z "$(ls -A "$BUILD_DIR" 2>/dev/null)" ]; then
          echo "✅ Build directory cleanup successful"
        else
          echo "ℹ️ Some files remain in build directory (this is normal and won't affect future builds)"
        fi
      else
        echo "Build directory not found, skipping cleanup"
      fi

      echo "Cleanup process completed"
      set -e  # Re-enable exit on error

    # Additional cleanup after build to free up space (non-blocking)
    - |
      echo "Performing Docker cleanup (errors will not fail the job)..."

      # Remove build-specific images
      echo "Checking for web image..."
      if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "$CI_REGISTRY_IMAGE/web:$CI_COMMIT_SHORT_SHA"; then
        echo "Removing web image..."
        docker rmi "$CI_REGISTRY_IMAGE/web:$CI_COMMIT_SHORT_SHA" 2>/dev/null || echo "Could not remove web image (may not exist locally)"
      else
        echo "Web image not found locally"
      fi

      echo "Checking for db-migrator image..."
      if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "$CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA"; then
        echo "Removing db-migrator image..."
        docker rmi "$CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA" 2>/dev/null || echo "Could not remove db-migrator image (may not exist locally)"
      else
        echo "DB migrator image not found locally"
      fi

      # Clean up dangling images and containers (best effort)
      echo "Cleaning up dangling images and containers..."
      docker system prune -f 2>/dev/null || echo "Docker system prune completed with warnings (this is normal)"

      echo "✅ Docker cleanup completed (any errors above are non-critical)"

      # Ensure the job doesn't fail due to cleanup issues
      exit 0
  needs:
    - build-dotnet-prod
  only:
    - main

# Create ConfigMap and Secrets for Development
prepare_dev_config:
  stage: migrate
  timeout: 15m  # Prevent infinite hanging
  tags:
    - identityserverbackend # Use runner on K8s master node for Kubernetes operations
  environment:
    name: development
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi

    # Quick API server check
    - |
      echo "Checking Kubernetes API server..."
      if ! kubectl cluster-info &>/dev/null; then
        echo "Failed to connect to Kubernetes API server"
        exit 1
      fi

    # Create namespace if it doesn't exist
    - |
      echo "Creating namespace..."
      kubectl create namespace imip-identity-dev-new --dry-run=client -o yaml | kubectl apply -f -

    # Apply ConfigMap first (no dependencies)
    - |
      echo "Applying ConfigMap..."
      envsubst < k8s/dev/configmap.yaml | kubectl apply -f -

    # Graceful deployment cleanup with proper sequencing
    - |
      echo "Cleaning up existing deployment..."
      if kubectl get deployment imip-identity-web -n imip-identity-dev-new >/dev/null 2>&1; then
        echo "Scaling down deployment to 0..."
        kubectl scale deployment imip-identity-web --replicas=0 -n imip-identity-dev-new --timeout=60s || true

        echo "Waiting for pods to terminate..."
        kubectl wait --for=delete pods -l app=imip-identity-web -n imip-identity-dev-new --timeout=120s || true

        echo "Deleting deployment..."
        kubectl delete deployment imip-identity-web -n imip-identity-dev-new --timeout=60s || true
      fi

    # Clean up PVC only after pods are gone
    - |
      echo "Cleaning up PVC..."
      if kubectl get pvc imip-identity-data-protection -n imip-identity-dev-new >/dev/null 2>&1; then
        # Check if any pods are still using the PVC
        USING_PODS=$(kubectl get pods -n imip-identity-dev-new -o jsonpath='{range .items[*]}{.metadata.name}{" "}{range .spec.volumes[*]}{.persistentVolumeClaim.claimName}{" "}{end}{"\n"}{end}' | grep imip-identity-data-protection | awk '{print $1}' || true)

        if [ ! -z "$USING_PODS" ]; then
          echo "Force deleting pods still using PVC: $USING_PODS"
          echo "$USING_PODS" | xargs -r kubectl delete pod -n imip-identity-dev-new --force --grace-period=0
          sleep 10
        fi

        echo "Deleting PVC..."
        kubectl delete pvc imip-identity-data-protection -n imip-identity-dev-new --timeout=60s || {
          echo "PVC deletion timed out, force removing finalizers..."
          kubectl patch pvc imip-identity-data-protection -n imip-identity-dev-new -p '{"metadata":{"finalizers":null}}' || true
        }
      fi

    # Wait for PVC to be completely gone
    - |
      echo "Waiting for PVC cleanup to complete..."
      for i in {1..30}; do
        if ! kubectl get pvc imip-identity-data-protection -n imip-identity-dev-new >/dev/null 2>&1; then
          echo "PVC successfully deleted"
          break
        fi
        echo "Waiting for PVC deletion... ($i/30)"
        sleep 2
      done
    # Create PV and PVC
    - |
      echo "Creating PV and PVC..."
      envsubst < k8s/dev/data-protection-pv.yaml | kubectl apply -f -
      envsubst < k8s/dev/data-protection-pvc.yaml | kubectl apply -f -

    # Wait for PVC to bind with timeout
    - |
      echo "Waiting for PVC to bind..."
      kubectl wait --for=condition=Bound pvc/imip-identity-data-protection -n imip-identity-dev-new --timeout=120s || {
        echo "PVC binding timed out, checking status..."
        kubectl describe pvc imip-identity-data-protection -n imip-identity-dev-new
        kubectl get pv imip-identity-data-protection-pv-dev-05
      }

    # Apply remaining configurations
    - |
      echo "Applying secrets and certificates..."
      envsubst < k8s/dev/secrets.yaml | kubectl apply -f -
      envsubst < k8s/dev/certificate-secret.yaml | kubectl apply -f -

    # Create registry credentials
    - |
      echo "Creating registry credentials..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-dev-new \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -

    # Verify configurations
    - |
      echo "Verifying configurations..."
      kubectl get pv,pvc,configmap,secret -n imip-identity-dev-new
      echo "Configuration preparation completed successfully!"

  retry:
    max: 2
    when:
      - unknown_failure
      - api_failure
      - stuck_or_timeout_failure

  only:
    - dev

# Run DB Migrator for Development
migrate_dev:
  stage: migrate
  tags:
    - identityserverbackend # Use runner on K8s master node for Kubernetes operations
  environment:
    name: development
  script:
    # Check if kubectl is installed, if not, install it, and test
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Login to Docker registry
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

    # Basic connectivity check
    - |
      echo "Checking basic connectivity to database server..."
      ping -c 2 ************** || echo "Warning: Could not ping database server"

    # Verify Docker image exists and is accessible
    - |
      echo "Verifying Docker image exists and is accessible..."
      if ! docker pull $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA; then
        echo "ERROR: Failed to pull image $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA"
        echo "This could be due to authentication issues or the image doesn't exist."
        echo "Checking if image was built correctly in previous stage..."
        docker images | grep db-migrator
        exit 1
      fi
      echo "Image verification successful!"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Creating registry credentials..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-dev-new \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-identity-dev-new

    # Check and prepare the production environment
    - |
      echo "Checking production environment prerequisites..."

      # Check if the production node is available
      if ! kubectl get node imdevapp18 &>/dev/null; then
        echo "ERROR: Production node 'imdevapp18' not found"
        kubectl get nodes
        exit 1
      fi

      # Check if the data protection directory exists on the production node
      echo "Ensuring data protection directory exists on production node..."
      cat <<EOF | kubectl apply -f -
      apiVersion: v1
      kind: Pod
      metadata:
        name: prep-directory-$CI_COMMIT_SHORT_SHA
        namespace: imip-identity-dev-new
      spec:
        nodeSelector:
          kubernetes.io/hostname: imdevapp18
        containers:
        - name: prep
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              mkdir -p /host/mnt/data/identity-data-protection
              chmod 755 /host/mnt/data/identity-data-protection
              echo "Directory prepared successfully"
          volumeMounts:
          - name: host-root
            mountPath: /host
        volumes:
        - name: host-root
          hostPath:
            path: /
            type: Directory
        restartPolicy: Never
      EOF

      # Wait for prep pod to complete (wait for Succeeded phase)
      kubectl wait --for=condition=complete pod/prep-directory-$CI_COMMIT_SHORT_SHA -n imip-identity-dev-new --timeout=120s || true
      kubectl delete pod prep-directory-$CI_COMMIT_SHORT_SHA -n imip-identity-dev-new --ignore-not-found

      # Verify PV and PVC status
      echo "Checking PV/PVC status..."
      kubectl get pv imip-identity-data-protection-pv-dev-05 || echo "PV not found"
      kubectl get pvc -n imip-identity-dev-new imip-identity-data-protection || echo "PVC not found"

      # Check required secrets and configmaps
      echo "Verifying required resources..."
      kubectl get secret -n imip-identity-dev-new imip-identity-secrets || { echo "ERROR: Required secrets not found"; exit 1; }
      kubectl get secret -n imip-identity-dev-new imip-identity-certificate || { echo "ERROR: Certificate secret not found"; exit 1; }
      kubectl get configmap -n imip-identity-dev-new imip-identity-config || { echo "ERROR: ConfigMap not found"; exit 1; }

    # Clean up any previous failed migration jobs with the same commit SHA
    - |
      echo "Cleaning up any previous failed migration jobs..."
      JOB_NAME="imip-identity-db-migrator-$CI_COMMIT_SHORT_SHA"
      kubectl delete job $JOB_NAME -n imip-identity-dev-new --ignore-not-found
      # Wait a moment to ensure resources are released
      sleep 5

    # Apply DB Migrator job with proper variable substitution
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/dev/db-migrator-job.yaml
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/dev/db-migrator-job.yaml
    - |
      echo "Applying DB migrator job with the following configuration:"
      cat k8s/dev/db-migrator-job.yaml
    - kubectl apply -f k8s/dev/db-migrator-job.yaml
    # Wait for the migration job to complete with proper timeout
    - |
      # Verify that we have a valid commit SHA
      if [ -z "$CI_COMMIT_SHORT_SHA" ]; then
        echo "Error: CI_COMMIT_SHORT_SHA is empty. Cannot proceed with migration job."
        exit 1
      fi

      echo "Using commit SHA: $CI_COMMIT_SHORT_SHA"
      echo "Waiting for database migration job to complete (timeout: 10 minutes)..."

      JOB_NAME="imip-identity-db-migrator-$CI_COMMIT_SHORT_SHA"
      echo "Job name: $JOB_NAME"

      # First, wait for the pod to be created and running
      echo "Waiting for pod to start..."
      RETRY_COUNT=0
      MAX_RETRIES=30
      POD_RUNNING=false

      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        POD_NAME=$(kubectl get pods -n imip-identity-dev-new -l "job-name=$JOB_NAME" -o name 2>/dev/null | head -n 1)
        if [ -n "$POD_NAME" ]; then
          POD_STATUS=$(kubectl get $POD_NAME -n imip-identity-dev-new -o jsonpath='{.status.phase}' 2>/dev/null)
          if [ "$POD_STATUS" = "Running" ]; then
            echo "Pod $POD_NAME is running!"
            POD_RUNNING=true
            break
          elif [ "$POD_STATUS" = "Succeeded" ]; then
            echo "Pod $POD_NAME has already completed successfully!"
            POD_RUNNING=true
            break
          fi
        fi
        echo "Waiting for pod to start (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)..."
        RETRY_COUNT=$((RETRY_COUNT+1))
        sleep 2
      done

      if [ "$POD_RUNNING" = "false" ]; then
        echo "Pod did not start within the expected time. Checking for errors..."
        kubectl get pods -n imip-identity-dev-new -l "job-name=$JOB_NAME" -o wide
        POD_NAME=$(kubectl get pods -n imip-identity-dev-new -l "job-name=$JOB_NAME" -o name 2>/dev/null | head -n 1)
        if [ -n "$POD_NAME" ]; then
          echo "Pod details:"
          kubectl describe $POD_NAME -n imip-identity-dev-new

          # Check for specific issues
          echo "Checking for common issues..."

          # Check PVC binding
          echo "PVC Status:"
          kubectl get pvc -n imip-identity-dev-new imip-identity-data-protection

          # Check node resources
          echo "Node resource usage:"
          kubectl describe node imdevapp18 | grep -A 10 "Allocated resources" || echo "Could not get node resources"

          # Check events
          echo "Recent events in namespace:"
          kubectl get events -n imip-identity-dev-new --sort-by='.lastTimestamp' | tail -n 10

          # Check if volumes are available
          echo "Checking volume availability:"
          kubectl get pv imip-identity-data-protection-pv-dev-05
        fi
        exit 1
      fi

      # Now wait for job completion with proper timeout
      if ! kubectl wait --for=condition=complete --timeout=600s "job/$JOB_NAME" -n imip-identity-dev-new; then
        echo "Migration job timed out or failed. Checking logs..."

        # List all pods in the namespace to help with debugging
        echo "All pods in namespace:"
        kubectl get pods -n imip-identity-dev-new

        # Try to find the pod with a more robust approach
        echo "Looking for pod with label job-name=$JOB_NAME"
        POD_LIST=$(kubectl get pods -n imip-identity-dev-new -l "job-name=$JOB_NAME" -o name)

        if [ -n "$POD_LIST" ]; then
          # Get the first pod if there are multiple
          POD_NAME=$(echo "$POD_LIST" | head -n 1 | sed 's/^pod\///')
          echo "Found pod: $POD_NAME"

          echo "Migration job pod logs:"
          kubectl logs "pod/$POD_NAME" -n imip-identity-dev-new || echo "Failed to get logs"

          echo "Pod details:"
          kubectl describe "pod/$POD_NAME" -n imip-identity-dev-new || echo "Failed to describe pod"

          # Check for specific error conditions
          POD_STATUS=$(kubectl get pod/$POD_NAME -n imip-identity-dev-new -o jsonpath='{.status.containerStatuses[0].state}' 2>/dev/null)
          if [[ $POD_STATUS == *"ImagePullBackOff"* ]]; then
            echo "ERROR: Image pull failure detected. This is likely due to authentication issues."
            echo "Verifying registry credentials..."
            kubectl get secret gitlab-registry-credentials -n imip-identity-dev-new -o yaml | grep -v "dockerconfigjson:"
          fi
        else
          echo "Could not find any pods for job: $JOB_NAME"
          echo "Checking job status:"
          kubectl describe "job/$JOB_NAME" -n imip-identity-dev-new || echo "Failed to describe job"
        fi
        exit 1
      else
        echo "Migration job completed successfully!"
        # Get the logs from the successful job for reference
        POD_NAME=$(kubectl get pods -n imip-identity-dev-new -l "job-name=$JOB_NAME" -o name | head -n 1 | sed 's/^pod\///')
        if [ -n "$POD_NAME" ]; then
          echo "Migration job logs:"
          kubectl logs "pod/$POD_NAME" -n imip-identity-dev-new
        fi
      fi
  needs:
    - prepare_dev_config
  only:
    - dev

# Deploy to Development
deploy_dev:
  stage: deploy
  tags:
    - identityserverbackend # Use runner on K8s master node for Kubernetes operations
  environment:
    name: development
    url: https://api-identity-dev.imip.co.id
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi

    # Check ingress-nginx webhook status
    - |
      echo "Checking ingress-nginx webhook status..."
      if ! kubectl get validatingwebhookconfigurations ingress-nginx-admission &>/dev/null; then
        echo "Warning: ingress-nginx webhook not found, attempting to fix..."
        # Try to patch the webhook to add a timeout
        kubectl patch validatingwebhookconfigurations ingress-nginx-admission --type='json' -p='[{"op": "replace", "path": "/webhooks/0/timeoutSeconds", "value": 30}]' || true
      fi

    # Basic connectivity check
    - |
      echo "Checking basic connectivity to database server..."
      ping -c 2 ************** || echo "Warning: Could not ping database server"

    # Apply Pod Disruption Budget to ensure minimum availability
    - echo "Applying Pod Disruption Budget..."
    - kubectl apply -f k8s/dev/pod-disruption-budget.yaml

    # Clean up old jobs but preserve running pods for zero-downtime deployment
    - |
      echo "Cleaning up old completed jobs..."
      # List all jobs in the namespace and filter using grep/awk instead of field selectors
      OLD_JOBS=$(kubectl get jobs -n imip-identity-dev-new -o name | grep "imip-identity-db-migrator" || echo "")
      if [ -n "$OLD_JOBS" ]; then
        echo "Found completed jobs to clean up:"
        echo "$OLD_JOBS"
        # Use a loop to handle each job individually to prevent errors if one job can't be deleted
        for JOB in $OLD_JOBS; do
          echo "Deleting job: $JOB"
          kubectl delete $JOB -n imip-identity-dev-new --ignore-not-found || echo "Failed to delete $JOB, continuing..."
        done
      else
        echo "No old jobs found to clean up"
      fi

      # Check node resources (with error handling)
      echo "Node resource usage:"
      kubectl describe nodes ${DEV_HOSTNAME} | grep -A 10 "Allocated resources" || echo "Could not get node resource information"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Creating registry credentials..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-dev-new \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-identity-dev-new

    # Apply web deployment using the YAML file
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/dev/web-deployment.yaml
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/dev/web-deployment.yaml
    - echo "Applying web deployment with environment variable substitution..."
    - envsubst < k8s/dev/web-deployment.yaml | kubectl apply -f -

    # Apply services
    - kubectl apply -f k8s/dev/web-service.yaml
    # Apply NodePort service for direct IP access
    - echo "Applying NodePort service for direct IP access..."
    - kubectl apply -f k8s/dev/web-service-nodeport.yaml

    # Apply ingress with retry mechanism and health check
    - |
      echo "Applying ingress configuration..."
      MAX_RETRIES=3
      RETRY_COUNT=0

      # Clean up existing webhook configurations
      echo "Cleaning up existing webhook configurations..."
      kubectl delete validatingwebhookconfigurations ingress-nginx-admission --ignore-not-found
      kubectl delete job -n ingress-nginx ingress-nginx-admission-create ingress-nginx-admission-patch --ignore-not-found
      sleep 5  # Wait for cleanup to complete

      # First, ensure ingress-nginx controller is healthy with more robust checking
      echo "Checking ingress-nginx controller health..."
      
      # Check if ingress-nginx namespace exists
      if ! kubectl get namespace ingress-nginx &>/dev/null; then
        echo "Warning: ingress-nginx namespace not found, skipping ingress-nginx checks"
      else
        # Check if there are any running pods
        RUNNING_PODS=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx --field-selector=status.phase=Running -o name 2>/dev/null | wc -l)
        TOTAL_PODS=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx -o name 2>/dev/null | wc -l)
        
        echo "Found $RUNNING_PODS running pods out of $TOTAL_PODS total pods"
        
        if [ "$RUNNING_PODS" -eq 0 ] && [ "$TOTAL_PODS" -gt 0 ]; then
          echo "Warning: No running ingress-nginx pods found"
          kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx
          echo "Attempting to restart ingress-nginx controller..."
          kubectl rollout restart deployment -n ingress-nginx ingress-nginx-controller --ignore-not-found || echo "Could not restart deployment"
        fi
        
        # Wait for at least 50% of pods to be ready (more tolerant)
        echo "Waiting for ingress-nginx controller to be ready (tolerant mode)..."
        READY_PODS=0
        MAX_ATTEMPTS=30
        ATTEMPT=0
        
        while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
          READY_PODS=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx --field-selector=status.phase=Running -o name 2>/dev/null | wc -l)
          TOTAL_PODS=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx -o name 2>/dev/null | wc -l)
          
          if [ "$TOTAL_PODS" -gt 0 ] && [ "$READY_PODS" -ge $((TOTAL_PODS / 2)) ]; then
            echo "✅ At least 50% of ingress-nginx pods are ready ($READY_PODS/$TOTAL_PODS). Proceeding..."
            break
          fi
          
          echo "Waiting for ingress-nginx pods to be ready... ($READY_PODS/$TOTAL_PODS ready, attempt $((ATTEMPT+1))/$MAX_ATTEMPTS)"
          ATTEMPT=$((ATTEMPT+1))
          sleep 10
        done
        
        if [ "$ATTEMPT" -eq "$MAX_ATTEMPTS" ]; then
          echo "⚠️ Warning: Ingress-nginx pods may not be fully ready, but proceeding anyway..."
          kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx
        fi
      fi

      # Skip the strict wait since we already checked with tolerant mode above
      echo "Skipping strict ingress-nginx wait (already checked with tolerant mode)..."

      # Now try to apply the ingress
      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if kubectl apply -f k8s/dev/ingress.yaml; then
          echo "Ingress applied successfully!"
          # Verify the ingress was created
          if kubectl get ingress -n imip-identity-dev-new imip-identity-web; then
            echo "Ingress verification successful!"
            # Wait for ingress to be ready
            echo "Waiting for ingress to be ready..."
            sleep 10
            # Check ingress status
            kubectl describe ingress -n imip-identity-dev-new imip-identity-web
            break
          else
            echo "Ingress was not created properly, retrying..."
          fi
        else
          RETRY_COUNT=$((RETRY_COUNT+1))
          if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
            echo "Failed to apply ingress after $MAX_RETRIES attempts"
            echo "Checking ingress-nginx controller status:"
            kubectl get pods -n ingress-nginx
            kubectl describe pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx
            kubectl get events -n ingress-nginx
            exit 1
          fi
          echo "Retrying ingress application (attempt $RETRY_COUNT of $MAX_RETRIES)..."
          sleep 10
        fi
      done

      # Verify ingress is working
      echo "Verifying ingress is working..."
      # Get the ingress controller's external IP
      INGRESS_IP=$(kubectl get service -n ingress-nginx ingress-nginx-controller -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
      if [ -z "$INGRESS_IP" ]; then
        echo "Warning: Could not get ingress controller IP"
        INGRESS_IP=$(kubectl get nodes -o wide | awk 'NR==2 {print $6}')
      fi
      echo "Ingress controller IP: $INGRESS_IP"

      # Test the ingress
      echo "Testing ingress access..."
      curl -v -H "Host: identity.imip.co.id" http://$INGRESS_IP || echo "Ingress test completed"

    # Monitor deployment status with improved handling
    - |
      echo "Monitoring deployment status..."
      if kubectl rollout status deployment/imip-identity-web -n imip-identity-dev-new --timeout=300s; then
        echo "✅ Deployment completed successfully!"
      else
        echo "⚠️ Deployment may not have completed within the timeout period."
        echo "Checking deployment status..."
        kubectl get deployment imip-identity-web -n imip-identity-dev-new -o wide

        echo "Current pod status:"
        kubectl get pods -n imip-identity-dev-new -l app=imip-identity-web -o wide

        # Check for any problematic pods and get their logs
        PROBLEMATIC_PODS=$(kubectl get pods -n imip-identity-dev-new -l app=imip-identity-web --field-selector=status.phase!=Running,status.phase!=Succeeded,status.phase!=Completed -o name)
        if [ -n "$PROBLEMATIC_PODS" ]; then
          echo "Found problematic pods:"
          for POD in $PROBLEMATIC_PODS; do
            echo "Details for $POD:"
            kubectl describe $POD -n imip-identity-dev-new
            echo "Logs for $POD:"
            kubectl logs $POD -n imip-identity-dev-new --tail=50 || echo "Could not get logs"
          done
        fi

        # Check for any events that might indicate issues
        echo "Recent events:"
        kubectl get events -n imip-identity-dev-new --sort-by='.lastTimestamp' | tail -n 20

        # Continue anyway - we don't want to fail the pipeline if some pods are still starting
        echo "Continuing despite deployment issues..."
      fi

      # Check if we have at least one pod running (minimum availability)
      RUNNING_PODS=$(kubectl get pods -n imip-identity-dev-new -l app=imip-identity-web --field-selector=status.phase=Running -o name | wc -l)
      if [ "$RUNNING_PODS" -ge 1 ]; then
        echo "✅ At least one pod is running. Service should be available."
      else
        echo "⚠️ Warning: No running pods found. Service may be unavailable!"
        # We still don't fail the pipeline, but this is a clear warning
      fi
  needs:
    - migrate_dev
  only:
    - dev

# Create ConfigMap and Secrets for Production
prepare_prod_config:
  stage: migrate
  tags:
    - identityserverbackendprod
  environment:
    name: production
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi

    # Quick API server check
    - |
      echo "Checking Kubernetes API server..."
      if ! kubectl cluster-info &>/dev/null; then
        echo "Failed to connect to Kubernetes API server"
        exit 1
      fi

    # Create namespace if it doesn't exist
    - |
      echo "Creating namespace..."
      kubectl create namespace imip-identity-prod --dry-run=client -o yaml | kubectl apply -f -

    # Apply all configurations
    - |
      echo "Applying configurations..."
      # Create PV
      envsubst < k8s/prod/data-protection-pv.yaml | kubectl apply -f -
      # Create PVC
      envsubst < k8s/prod/data-protection-pvc.yaml | kubectl apply -f -

    # Wait for PVC to bind
    - |
      echo "Waiting for PVC to bind..."
      kubectl wait --for=condition=Bound pvc/imip-identity-data-protection -n imip-identity-prod --timeout=60s || true

    # Verify status
    - |
      echo "Checking PV/PVC status:"
      kubectl get pv imip-identity-data-protection-pv-prod
      kubectl get pvc -n imip-identity-prod imip-identity-data-protection

    # Apply remaining configurations
    - |
      echo "Applying remaining configurations..."
      # Apply ConfigMap
      envsubst < k8s/prod/configmap.yaml | kubectl apply -f -
      # Apply Secrets
      envsubst < k8s/prod/secrets.yaml | kubectl apply -f -
      # Apply Certificate Secret
      envsubst < k8s/prod/certificate-secret.yaml | kubectl apply -f -

    # Create registry credentials
    - |
      echo "Creating registry credentials..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-prod \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -

    # Verify configurations
    - |
      echo "Verifying configurations..."
      kubectl get pv,pvc,configmap,secret -n imip-identity-prod
  only:
    - main

# Run DB Migrator for Production
migrate_prod:
  stage: migrate
  tags:
    - identityserverbackendprod # Use runner on K8s master node for Kubernetes operations
  environment:
    name: production
  script:
    # Check if kubectl is installed, if not, install it, and test
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Login to Docker registry
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

    # Basic connectivity check
    - |
      echo "Checking basic connectivity to database server..."
      ping -c 2 ************** || echo "Warning: Could not ping database server"

    # Verify Docker image exists and is accessible
    - |
      echo "Verifying Docker image exists and is accessible..."
      if ! docker pull $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA; then
        echo "ERROR: Failed to pull image $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA"
        echo "This could be due to authentication issues or the image doesn't exist."
        echo "Checking if image was built correctly in previous stage..."
        docker images | grep db-migrator
        exit 1
      fi
      echo "Image verification successful!"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Creating registry credentials..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-prod \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-identity-prod

    # Check and prepare the production environment
    - |
      echo "Checking production environment prerequisites..."

      # Check if the production node is available
      if ! kubectl get node imprdapp27 &>/dev/null; then
        echo "ERROR: Production node 'imprdapp27' not found"
        kubectl get nodes
        exit 1
      fi

      # Check if the data protection directory exists on the production node
      echo "Ensuring data protection directory exists on production node..."
      cat <<EOF | kubectl apply -f -
      apiVersion: v1
      kind: Pod
      metadata:
        name: prep-directory-$CI_COMMIT_SHORT_SHA
        namespace: imip-identity-prod
      spec:
        nodeSelector:
          kubernetes.io/hostname: imprdapp27
        containers:
        - name: prep
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              mkdir -p /host/mnt/data/identity-data-protection
              chmod 755 /host/mnt/data/identity-data-protection
              echo "Directory prepared successfully"
          volumeMounts:
          - name: host-root
            mountPath: /host
        volumes:
        - name: host-root
          hostPath:
            path: /
            type: Directory
        restartPolicy: Never
      EOF

      # Wait for prep pod to complete (wait for Succeeded phase)
      kubectl wait --for=condition=complete pod/prep-directory-$CI_COMMIT_SHORT_SHA -n imip-identity-prod --timeout=120s || true
      kubectl delete pod prep-directory-$CI_COMMIT_SHORT_SHA -n imip-identity-prod --ignore-not-found

      # Verify PV and PVC status
      echo "Checking PV/PVC status..."
      kubectl get pv imip-identity-data-protection-pv-prod || echo "PV not found"
      kubectl get pvc -n imip-identity-prod imip-identity-data-protection || echo "PVC not found"

      # Check required secrets and configmaps
      echo "Verifying required resources..."
      kubectl get secret -n imip-identity-prod imip-identity-secrets || { echo "ERROR: Required secrets not found"; exit 1; }
      kubectl get secret -n imip-identity-prod imip-identity-certificate || { echo "ERROR: Certificate secret not found"; exit 1; }
      kubectl get configmap -n imip-identity-prod imip-identity-config || { echo "ERROR: ConfigMap not found"; exit 1; }

    # Clean up any previous failed migration jobs with the same commit SHA
    - |
      echo "Cleaning up any previous failed migration jobs..."
      JOB_NAME="imip-identity-db-migrator-$CI_COMMIT_SHORT_SHA"
      kubectl delete job $JOB_NAME -n imip-identity-prod --ignore-not-found
      # Wait a moment to ensure resources are released
      sleep 5

    # Apply DB Migrator job with proper variable substitution
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/prod/db-migrator-job.yaml
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/prod/db-migrator-job.yaml
    - |
      echo "Applying DB migrator job with the following configuration:"
      cat k8s/prod/db-migrator-job.yaml
    - kubectl apply -f k8s/prod/db-migrator-job.yaml
    # Wait for the migration job to complete with proper timeout
    - |
      # Verify that we have a valid commit SHA
      if [ -z "$CI_COMMIT_SHORT_SHA" ]; then
        echo "Error: CI_COMMIT_SHORT_SHA is empty. Cannot proceed with migration job."
        exit 1
      fi

      echo "Using commit SHA: $CI_COMMIT_SHORT_SHA"
      echo "Waiting for database migration job to complete (timeout: 10 minutes)..."

      JOB_NAME="imip-identity-db-migrator-$CI_COMMIT_SHORT_SHA"
      echo "Job name: $JOB_NAME"

      # First, wait for the pod to be created and running
      echo "Waiting for pod to start..."
      RETRY_COUNT=0
      MAX_RETRIES=30
      POD_RUNNING=false

      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        POD_NAME=$(kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o name 2>/dev/null | head -n 1)
        if [ -n "$POD_NAME" ]; then
          POD_STATUS=$(kubectl get $POD_NAME -n imip-identity-prod -o jsonpath='{.status.phase}' 2>/dev/null)
          if [ "$POD_STATUS" = "Running" ]; then
            echo "Pod $POD_NAME is running!"
            POD_RUNNING=true
            break
          elif [ "$POD_STATUS" = "Succeeded" ]; then
            echo "Pod $POD_NAME has already completed successfully!"
            POD_RUNNING=true
            break
          fi
        fi
        echo "Waiting for pod to start (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)..."
        RETRY_COUNT=$((RETRY_COUNT+1))
        sleep 2
      done

      if [ "$POD_RUNNING" = "false" ]; then
        echo "Pod did not start within the expected time. Checking for errors..."
        kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o wide
        POD_NAME=$(kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o name 2>/dev/null | head -n 1)
        if [ -n "$POD_NAME" ]; then
          echo "Pod details:"
          kubectl describe $POD_NAME -n imip-identity-prod

          # Check for specific issues
          echo "Checking for common issues..."

          # Check PVC binding
          echo "PVC Status:"
          kubectl get pvc -n imip-identity-prod imip-identity-data-protection

          # Check node resources
          echo "Node resource usage:"
          kubectl describe node imprdapp27 | grep -A 10 "Allocated resources" || echo "Could not get node resources"

          # Check events
          echo "Recent events in namespace:"
          kubectl get events -n imip-identity-prod --sort-by='.lastTimestamp' | tail -n 10

          # Check if volumes are available
          echo "Checking volume availability:"
          kubectl get pv imip-identity-data-protection-pv-prod
        fi
        exit 1
      fi

      # Now wait for job completion with proper timeout
      if ! kubectl wait --for=condition=complete --timeout=600s "job/$JOB_NAME" -n imip-identity-prod; then
        echo "Migration job timed out or failed. Checking logs..."

        # List all pods in the namespace to help with debugging
        echo "All pods in namespace:"
        kubectl get pods -n imip-identity-prod

        # Try to find the pod with a more robust approach
        echo "Looking for pod with label job-name=$JOB_NAME"
        POD_LIST=$(kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o name)

        if [ -n "$POD_LIST" ]; then
          # Get the first pod if there are multiple
          POD_NAME=$(echo "$POD_LIST" | head -n 1 | sed 's/^pod\///')
          echo "Found pod: $POD_NAME"

          echo "Migration job pod logs:"
          kubectl logs "pod/$POD_NAME" -n imip-identity-prod || echo "Failed to get logs"

          echo "Pod details:"
          kubectl describe "pod/$POD_NAME" -n imip-identity-prod || echo "Failed to describe pod"

          # Check for specific error conditions
          POD_STATUS=$(kubectl get pod/$POD_NAME -n imip-identity-prod -o jsonpath='{.status.containerStatuses[0].state}' 2>/dev/null)
          if [[ $POD_STATUS == *"ImagePullBackOff"* ]]; then
            echo "ERROR: Image pull failure detected. This is likely due to authentication issues."
            echo "Verifying registry credentials..."
            kubectl get secret gitlab-registry-credentials -n imip-identity-prod -o yaml | grep -v "dockerconfigjson:"
          fi
        else
          echo "Could not find any pods for job: $JOB_NAME"
          echo "Checking job status:"
          kubectl describe "job/$JOB_NAME" -n imip-identity-prod || echo "Failed to describe job"
        fi
        exit 1
      else
        echo "Migration job completed successfully!"
        # Get the logs from the successful job for reference
        POD_NAME=$(kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o name | head -n 1 | sed 's/^pod\///')
        if [ -n "$POD_NAME" ]; then
          echo "Migration job logs:"
          kubectl logs "pod/$POD_NAME" -n imip-identity-prod
        fi
      fi
  needs:
    - prepare_prod_config
  only:
    - main

# Deploy to Production
deploy_prod:
  stage: deploy
  tags:
    - identityserverbackendprod # Use runner on K8s master node for Kubernetes operations
  environment:
    name: production
    url: https://identity.imip.co.id
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi

    # Check ingress-nginx webhook status
    - |
      echo "Checking ingress-nginx webhook status..."
      if ! kubectl get validatingwebhookconfigurations ingress-nginx-admission &>/dev/null; then
        echo "Warning: ingress-nginx webhook not found, attempting to fix..."
        # Try to patch the webhook to add a timeout
        kubectl patch validatingwebhookconfigurations ingress-nginx-admission --type='json' -p='[{"op": "replace", "path": "/webhooks/0/timeoutSeconds", "value": 30}]' || true
      fi

    # Basic connectivity check
    - |
      echo "Checking basic connectivity to database server..."
      ping -c 2 ************** || echo "Warning: Could not ping database server"

    # Apply Pod Disruption Budget to ensure minimum availability
    - echo "Applying Pod Disruption Budget..."
    - kubectl apply -f k8s/prod/pod-disruption-budget.yaml

    # Clean up old jobs but preserve running pods for zero-downtime deployment
    - |
      echo "Cleaning up old completed jobs..."
      # List all jobs in the namespace and filter using grep/awk instead of field selectors
      OLD_JOBS=$(kubectl get jobs -n imip-identity-prod -o name | grep "imip-identity-db-migrator")
      if [ -n "$OLD_JOBS" ]; then
        echo "Found completed jobs to clean up:"
        echo "$OLD_JOBS"
        # Use a loop to handle each job individually to prevent errors if one job can't be deleted
        for JOB in $OLD_JOBS; do
          echo "Deleting job: $JOB"
          kubectl delete $JOB -n imip-identity-prod --ignore-not-found || echo "Failed to delete $JOB, continuing..."
        done
      fi

      # Check node resources
      echo "Node resource usage:"
      kubectl describe nodes ${PROD_HOSTNAME} | grep -A 10 "Allocated resources"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Creating registry credentials..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-prod \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-identity-prod

    # Apply web deployment using the YAML file
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/prod/web-deployment.yaml
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/prod/web-deployment.yaml
    - echo "Applying web deployment with environment variable substitution..."
    - envsubst < k8s/prod/web-deployment.yaml | kubectl apply -f -

    # Apply services
    - kubectl apply -f k8s/prod/web-service.yaml
    # Apply NodePort service for direct IP access
    - echo "Applying NodePort service for direct IP access..."
    - kubectl apply -f k8s/prod/web-service-nodeport.yaml

    # Apply ingress with retry mechanism and health check
    - |
      echo "Applying ingress configuration..."
      MAX_RETRIES=3
      RETRY_COUNT=0

      # Clean up existing webhook configurations
      echo "Cleaning up existing webhook configurations..."
      kubectl delete validatingwebhookconfigurations ingress-nginx-admission --ignore-not-found
      kubectl delete job -n ingress-nginx ingress-nginx-admission-create ingress-nginx-admission-patch --ignore-not-found
      sleep 5  # Wait for cleanup to complete

      # First, ensure ingress-nginx controller is healthy with more robust checking
      echo "Checking ingress-nginx controller health..."
      
      # Check if ingress-nginx namespace exists
      if ! kubectl get namespace ingress-nginx &>/dev/null; then
        echo "Warning: ingress-nginx namespace not found, skipping ingress-nginx checks"
      else
        # Check if there are any running pods
        RUNNING_PODS=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx --field-selector=status.phase=Running -o name 2>/dev/null | wc -l)
        TOTAL_PODS=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx -o name 2>/dev/null | wc -l)
        
        echo "Found $RUNNING_PODS running pods out of $TOTAL_PODS total pods"
        
        if [ "$RUNNING_PODS" -eq 0 ] && [ "$TOTAL_PODS" -gt 0 ]; then
          echo "Warning: No running ingress-nginx pods found"
          kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx
          echo "Attempting to restart ingress-nginx controller..."
          kubectl rollout restart deployment -n ingress-nginx ingress-nginx-controller --ignore-not-found || echo "Could not restart deployment"
        fi
        
        # Wait for at least 50% of pods to be ready (more tolerant)
        echo "Waiting for ingress-nginx controller to be ready (tolerant mode)..."
        READY_PODS=0
        MAX_ATTEMPTS=30
        ATTEMPT=0
        
        while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
          READY_PODS=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx --field-selector=status.phase=Running -o name 2>/dev/null | wc -l)
          TOTAL_PODS=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx -o name 2>/dev/null | wc -l)
          
          if [ "$TOTAL_PODS" -gt 0 ] && [ "$READY_PODS" -ge $((TOTAL_PODS / 2)) ]; then
            echo "✅ At least 50% of ingress-nginx pods are ready ($READY_PODS/$TOTAL_PODS). Proceeding..."
            break
          fi
          
          echo "Waiting for ingress-nginx pods to be ready... ($READY_PODS/$TOTAL_PODS ready, attempt $((ATTEMPT+1))/$MAX_ATTEMPTS)"
          ATTEMPT=$((ATTEMPT+1))
          sleep 10
        done
        
        if [ "$ATTEMPT" -eq "$MAX_ATTEMPTS" ]; then
          echo "⚠️ Warning: Ingress-nginx pods may not be fully ready, but proceeding anyway..."
          kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx
        fi
      fi

      # Skip the strict wait since we already checked with tolerant mode above
      echo "Skipping strict ingress-nginx wait (already checked with tolerant mode)..."

      # Now try to apply the ingress
      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if kubectl apply -f k8s/prod/ingress.yaml; then
          echo "Ingress applied successfully!"
          # Verify the ingress was created
          if kubectl get ingress -n imip-identity-prod imip-identity-web; then
            echo "Ingress verification successful!"
            # Wait for ingress to be ready
            echo "Waiting for ingress to be ready..."
            sleep 10
            # Check ingress status
            kubectl describe ingress -n imip-identity-prod imip-identity-web
            break
          else
            echo "Ingress was not created properly, retrying..."
          fi
        else
          RETRY_COUNT=$((RETRY_COUNT+1))
          if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
            echo "Failed to apply ingress after $MAX_RETRIES attempts"
            echo "Checking ingress-nginx controller status:"
            kubectl get pods -n ingress-nginx
            kubectl describe pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx
            kubectl get events -n ingress-nginx
            exit 1
          fi
          echo "Retrying ingress application (attempt $RETRY_COUNT of $MAX_RETRIES)..."
          sleep 10
        fi
      done

      # Verify ingress is working
      echo "Verifying ingress is working..."
      # Get the ingress controller's external IP
      INGRESS_IP=$(kubectl get service -n ingress-nginx ingress-nginx-controller -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
      if [ -z "$INGRESS_IP" ]; then
        echo "Warning: Could not get ingress controller IP"
        INGRESS_IP=$(kubectl get nodes -o wide | awk 'NR==2 {print $6}')
      fi
      echo "Ingress controller IP: $INGRESS_IP"

      # Test the ingress
      echo "Testing ingress access..."
      curl -v -H "Host: identity.imip.co.id" http://$INGRESS_IP || echo "Ingress test completed"

    # Monitor deployment status with improved handling
    - |
      echo "Monitoring deployment status..."
      if kubectl rollout status deployment/imip-identity-web -n imip-identity-prod --timeout=300s; then
        echo "✅ Deployment completed successfully!"
      else
        echo "⚠️ Deployment may not have completed within the timeout period."
        echo "Checking deployment status..."
        kubectl get deployment imip-identity-web -n imip-identity-prod -o wide

        echo "Current pod status:"
        kubectl get pods -n imip-identity-prod -l app=imip-identity-web -o wide

        # Check for any problematic pods and get their logs
        PROBLEMATIC_PODS=$(kubectl get pods -n imip-identity-prod -l app=imip-identity-web --field-selector=status.phase!=Running,status.phase!=Succeeded,status.phase!=Completed -o name)
        if [ -n "$PROBLEMATIC_PODS" ]; then
          echo "Found problematic pods:"
          for POD in $PROBLEMATIC_PODS; do
            echo "Details for $POD:"
            kubectl describe $POD -n imip-identity-prod
            echo "Logs for $POD:"
            kubectl logs $POD -n imip-identity-prod --tail=50 || echo "Could not get logs"
          done
        fi

        # Check for any events that might indicate issues
        echo "Recent events:"
        kubectl get events -n imip-identity-prod --sort-by='.lastTimestamp' | tail -n 20

        # Continue anyway - we don't want to fail the pipeline if some pods are still starting
        echo "Continuing despite deployment issues..."
      fi

      # Check if we have at least one pod running (minimum availability)
      RUNNING_PODS=$(kubectl get pods -n imip-identity-prod -l app=imip-identity-web --field-selector=status.phase=Running -o name | wc -l)
      if [ "$RUNNING_PODS" -ge 1 ]; then
        echo "✅ At least one pod is running. Service should be available."
      else
        echo "⚠️ Warning: No running pods found. Service may be unavailable!"
        # We still don't fail the pipeline, but this is a clear warning
      fi

  needs:
    - migrate_prod
  only:
    - main
