'use client'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useState } from 'react'

import { type IdentityUserUpdateDto } from '@/client'
import { type PaginationState } from '@tanstack/react-table'

import { useToast } from '@/lib/useToast'
import { Delete } from './Delete'

import { DataTable } from '@/components/data-table/DataTable'
import { NotionFilter } from '@/components/data-table/NotionFilter'
import { TableSkeleton } from '@/components/ui/TableSkeleton'
import { useIdentityClaims } from '@/lib/hooks/useIdentityClaims'
import { type FilterCondition } from '@/lib/interfaces/IFilterCondition'
import { useQueryClient } from '@tanstack/react-query'
import { Add } from './Add'
import { getColumns } from './Columns'
import { Edit } from './Edit'

export const List = () => {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const [searchStr, setSearchStr] = useState<string>('')
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([])
  const [userActionDialog, setUserActionDialog] = useState<{
    userId: string
    userDto: IdentityUserUpdateDto
    dialogType?: 'edit' | 'permission' | 'delete'
  } | null>()

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })

  // Initialize sorting state
  // const [sorting, setSorting] = useState<SortingState>([
  //   { id: 'name', desc: false }
  // ])

  const { isLoading, data } = useIdentityClaims(
    pagination.pageIndex,
    pagination.pageSize,
    filterConditions
  )

  // Handler for user actions (edit, permission, delete)
  const handleUserAction = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => {
    setUserActionDialog({
      userId,
      userDto,
      dialogType,
    })
  }

  // Get columns with the action handler
  const columns = getColumns(handleUserAction)

  const handleSearch = (value: string) => {
    // Always update the search string for UI consistency
    setSearchStr(value)

    // Create a search filter condition if there's a search value
    // First, remove any existing name filter
    const existingFilters = filterConditions.filter(fc => fc.fieldName !== 'name')
    const newFilterConditions = [...existingFilters]

    // Only add the search filter if there's a value
    if (value) {
      newFilterConditions.push({
        fieldName: 'name',
        operator: 'Contains',
        value: value
      })
    }

    // Only update state if filters have changed
    const currentFiltersStr = JSON.stringify(filterConditions)
    const newFiltersStr = JSON.stringify(newFilterConditions)

    if (currentFiltersStr !== newFiltersStr) {
      setFilterConditions(newFilterConditions)
      setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search
    }
  }

  const handlePaginationChange = (newPagination: PaginationState) => {
    setPagination(newPagination)
  }

  // Handler for sorting change
  // const handleSortingChange = (newSorting: SortingState) => {
  //   setSorting(newSorting)
  //   setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change
  // }

  // Handler for refreshing the data
  const handleRefresh = () => {
    // Invalidate the query to fetch fresh data
    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetIdentityClaims] })

    // Show toast notification after a short delay to match the animation
    setTimeout(() => {
      toast({
        title: "Data refreshed",
        description: "The client list has been refreshed.",
        variant: "success",
      })
    }, 800)
  }

  if (isLoading) return (
    <TableSkeleton
      rowCount={pagination.pageSize}
      columnCount={4}
      hasTitle={true}
      hasSearch={true}
      hasFilters={true}
      hasPagination={true}
      hasActions={true}
    />
  )

  // Ensure we have valid data to render
  const items = data?.items ?? [];
  const totalCount = data?.totalCount ?? 0;

  return (
    <>
      <div className="space-y-2 bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4">
        <DataTable
          title="Identity Claims"
          columns={columns}
          data={items}
          totalCount={totalCount}
          isLoading={isLoading}
          manualPagination={true}
          pageSize={pagination.pageSize}
          onPaginationChange={handlePaginationChange}
          onSearch={handleSearch}
          searchValue={searchStr}
          customFilterbar={(props) => (
            <NotionFilter
              {...props}
              activeFilters={filterConditions}
              onServerFilter={(conditions) => {
                // Only update if the conditions have actually changed
                const currentStr = JSON.stringify(filterConditions);
                const newStr = JSON.stringify(conditions);

                if (currentStr !== newStr) {
                  setFilterConditions(conditions)
                  setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on filter change
                }
              }}
            />
          )}
          hideDefaultFilterbar={true}
          onRefresh={handleRefresh}
          enableRowSelection={false}
          actionButton={{
            // label: "Create New User",
            onClick: () => { /* Required but not used */ },
            content: <Add />
          }}
        />
      </div>

      {userActionDialog && userActionDialog.dialogType === 'edit' && (
        <Edit
          userId={userActionDialog.userId}
          userDto={userActionDialog.userDto}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictResources] })
            setUserActionDialog(null)
          }}
        />
      )}
      {userActionDialog && userActionDialog.dialogType === 'permission' && (
        <Edit
          userId={userActionDialog.userId}
          userDto={userActionDialog.userDto}
          onDismiss={() => setUserActionDialog(null)}
        />
      )}
      {userActionDialog && userActionDialog.dialogType === 'delete' && (
        <Delete
          user={{
            username: userActionDialog.userDto.userName,
            userId: userActionDialog.userId,
          }}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictApplications] })
            setUserActionDialog(null)
          }}
        />
      )}
    </>
  )
}
