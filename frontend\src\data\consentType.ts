import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Shield<PERSON><PERSON><PERSON> } from "lucide-react";

export const CONSENT_TYPES = [
  {
    value: "implicit",
    option: "Implicit",
    description: "Consent is assumed without requiring explicit user approval",
    icon: Shield<PERSON>he<PERSON>
  },
  {
    value: "explicit",
    option: "Explicit",
    description: "Requires explicit user approval before granting access",
    icon: UserCheck
  },
  {
    value: "hybrid",
    option: "Hybrid",
    description: "Requires explicit user approval before granting access",
    icon: ShieldQuestion
  }
] as const;
