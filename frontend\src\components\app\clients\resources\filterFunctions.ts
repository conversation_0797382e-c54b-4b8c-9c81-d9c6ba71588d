import type { FilterOperator } from "@/client"
import type { Row } from "@tanstack/react-table"

// Define the filter value type
export interface FilterValue {
  operator: FilterOperator;
  value: string | null;
}

// Custom filter function for handling our filter operators
export function customFilterFunction<TData>(
  row: Row<TData>,
  columnId: string,
  filterValue: unknown
): boolean {
  // Handle case when filterValue is not in the expected format
  if (!filterValue || typeof filterValue !== 'object' || !('operator' in filterValue)) {
    return true
  }

  // Type guard to ensure filterValue is of type FilterValue
  const isFilterValue = (value: unknown): value is FilterValue => {
    return (
      typeof value === 'object' &&
      value !== null &&
      'operator' in value &&
      'value' in value
    );
  };

  // If not a valid FilterValue, return true (show all rows)
  if (!isFilterValue(filterValue)) {
    return true;
  }

  // Get the value from the row
  const value = row.getValue(columnId)

  // Extract operator and filter value
  const { operator, value: filterVal } = filterValue

  // Handle null or undefined values
  if (value === undefined || value === null) {
    if (operator === "IsNull") return true
    if (operator === "IsNotNull") return false
    if (operator === "IsEmpty") return true
    if (operator === "IsNotEmpty") return false
    return false
  }

  // Convert value to string for string operations
  let stringValue = "";
  if (typeof value === 'object' && value !== null) {
    // Safely stringify objects with proper handling
    try {
      stringValue = JSON.stringify(value).toLowerCase();
    } catch {
      // If JSON stringify fails, use a more descriptive representation
      stringValue = `[${Object.prototype.toString.call(value)}]`;
    }
  } else if (typeof value === 'number' || typeof value === 'boolean' || typeof value === 'string') {
    // For primitive values, use standard string conversion
    stringValue = String(value).toLowerCase();
  } else {
    // For other types, use a safe representation
    stringValue = `[${typeof value}]`;
  }

  const stringFilterVal = filterVal ? String(filterVal).toLowerCase() : ""

  // Handle different operators
  switch (operator) {
    case "Equals":
      return stringValue === stringFilterVal
    case "NotEquals":
      return stringValue !== stringFilterVal
    case "Contains":
      return stringValue.includes(stringFilterVal)
    case "StartsWith":
      return stringValue.startsWith(stringFilterVal)
    case "EndsWith":
      return stringValue.endsWith(stringFilterVal)
    case "GreaterThan":
      return Number(value) > Number(filterVal)
    case "GreaterThanOrEqual":
      return Number(value) >= Number(filterVal)
    case "LessThan":
      return Number(value) < Number(filterVal)
    case "LessThanOrEqual":
      return Number(value) <= Number(filterVal)
    case "IsEmpty":
      return stringValue === ""
    case "IsNotEmpty":
      return stringValue !== ""
    case "IsNull":
      return value === null || value === undefined
    case "IsNotNull":
      return value !== null && value !== undefined
    default:
      return true
  }
}
