'use client'
import { getApiImportTemplates, postApiImportCsv, type RemoteServiceErrorResponse } from '@/client'

// Define interfaces for API error handling

import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useToast } from '@/lib/useToast'
import { RiDownloadLine, RiUploadLine } from '@remixicon/react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'

export const UploadUsersCsv = () => {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [open, setOpen] = useState(false)
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const [uploadType, setUploadType] = useState<'users' | 'roles' | 'userroles'>('users')
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0]

      // Validate file type
      if (!selectedFile?.name.toLowerCase().endsWith('.csv')) {
        toast({
          title: 'Invalid File Type',
          description: 'Please select a CSV file',
          variant: 'destructive',
        })
        // Reset the input
        e.target.value = ''
        return
      }

      setFile(selectedFile)
    }
  }

  // Download template mutation
  const downloadTemplateMutation = useMutation({
    mutationFn: async () => {
      setIsDownloading(true)
      try {
        const response = await getApiImportTemplates({
          query: {
            type: uploadType
          }
        })

        // Create a blob from the response data
        console.log('response.data', response.data)
        const blob = new Blob([response.data as unknown as BlobPart], {
          type: 'text/csv'
        })

        // Create a download link and trigger the download
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'users_import_template.csv'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)

        return response.data
      } finally {
        setIsDownloading(false)
      }
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err?.error?.message as string,
        description: err?.error?.details,
        variant: 'destructive',
      })
    }
  })

  // Upload CSV mutation
  const uploadCsvMutation = useMutation({
    mutationFn: async () => {
      if (!file) {
        throw new Error('No file selected')
      }

      setIsUploading(true)

      try {
        // Use the SDK function with formDataBodySerializer
        const response = await postApiImportCsv({
          body: {
            File: file,  // Use uppercase to match backend DTO FromForm attributes
            Type: uploadType,  // Use uppercase to match backend DTO FromForm attributes
            Delimiter: ';'  // Use uppercase to match backend DTO FromForm attributes
          },
        })

        return response.data
      } finally {
        setIsUploading(false)
      }
    },
    onSuccess: () => {
      // Check if there were any errors during import
      toast({
        title: 'Import Successful',
        description: `Imported successfully`,
        variant: 'success',
      })

      // Refresh the users list
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })

      // Close the dialog
      setOpen(false)

      // Reset the file input
      setFile(null)
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err?.error?.message as string,
        description: err?.error?.details,
        variant: 'destructive',
      })
    }
  })

  // Handle download template button click
  const handleDownloadTemplate = () => {
    downloadTemplateMutation.mutate()
  }

  // Handle upload button click
  const handleUpload = () => {
    if (!file) {
      toast({
        title: 'No File Selected',
        description: 'Please select a CSV file to upload',
        variant: 'warning',
      })
      return
    }

    uploadCsvMutation.mutate()
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" className="w-full sm:py-1 sm:mt-0 sm:w-fit">
          <RiUploadLine className="-ml-1 size-4 shrink-0" aria-hidden="true" />
          <span className="hidden truncate sm:inline">Upload CSV</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Users CSV</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-4 py-4">
          <div className="flex flex-col gap-2">
            <Label htmlFor="csvFile" className="text-left">Upload Type</Label>
            <Select value={uploadType} onValueChange={(value) => setUploadType(value as "users" | "roles" | "userroles")}>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder="Select upload type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="users">Users</SelectItem>
                <SelectItem value="roles">Roles</SelectItem>
                <SelectItem value="userroles">User Roles</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-2">
            <Label htmlFor="template" className="text-left">Download Template</Label>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleDownloadTemplate}
                disabled={isDownloading}
                className="w-full"
              >
                <RiDownloadLine className="mr-2 h-4 w-4" />
                {isDownloading ? 'Downloading...' : 'Download Template'}
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Download the template first, fill it with your data, and then upload it.
            </p>
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="csvFile" className="text-left">Upload CSV File</Label>
            <Input
              id="csvFile"
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              disabled={isUploading}
            />
            {file && (
              <p className="text-sm text-muted-foreground">
                Selected file: {file.name}
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="ghost"
            onClick={() => {
              setOpen(false)
              setFile(null)
            }}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!file || isUploading}
          >
            {isUploading ? 'Uploading...' : 'Upload'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

