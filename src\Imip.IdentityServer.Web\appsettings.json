{"App": {"SelfUrl": "https://localhost:44309", "HealthCheckUrl": "/health", "ClientUrl": "http://localhost:3000", "CorsOrigins": "http://localhost:3000,https://localhost:3000", "AppName": "Imip.IdentityServer.Local"}, "RabbitMQ": {"Connections": {"Default": {"HostName": "**********", "UserName": "guest", "Password": "guest", "Port": 5672, "RequestedConnectionTimeout": 10000, "SocketReadTimeout": 10000, "SocketWriteTimeout": 10000, "RequestedHeartbeat": 60, "NetworkRecoveryInterval": 5000, "AutomaticRecoveryEnabled": true, "TopologyRecoveryEnabled": true}}, "EventBus": {"ClientName": "IdentityServer", "ExchangeName": "LogoutEvents"}}, "ConnectionStrings": {"Default": "Server=localhost;Database=IdentityServer;User ID=sa;Password=*********;Integrated Security=false;TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"}, "Seq": {"ServerUrl": "http://localhost:5341", "ApiKey": "kft2m21YxDrakKGT8xRd"}, "AuthServer": {"Authority": "https://localhost:44309", "RequireHttpsMetadata": true, "CertificatePassPhrase": "c31cfec0-c4ac-4219-b65d-87e29fa8e042", "CertificatePath": "/app/certs/identity-server.pfx"}, "StringEncryption": {"DefaultPassPhrase": "k3q5h6xbMkFjb5se"}, "ExternalAuth": {"ApiUrl": "http://***************/api/common/RequestAuthenticationToken", "Enabled": true}, "Redis": {"IsEnabled": "true", "Configuration": "localhost:6379"}, "ActiveDirectory": {"Domain": "corp.imip.co.id", "LdapServer": "IMADDC01.corp.imip.co.id", "BaseDn": "DC=corp,DC=imip,DC=co,DC=id", "Username": "<EMAIL>", "Password": "Test*010203", "Port": "636", "UseSsl": "true", "Enabled": true, "AutoLogin": true, "TokenSecret": "your-secure-token-secret-key-change-this-in-production", "DefaultUsername": null, "WindowsAuthEnabled": true}, "LogoutNotification": {"RedisLogoutChannel": "identityserver:logout", "EnableRedisNotifications": true, "EnableRabbitMQNotifications": false, "HttpClientTimeoutSeconds": 30, "MaxRetryAttempts": 3, "RetryDelaySeconds": 5, "NotifyOnlyActiveSessionClients": true, "EnableRabbitMQHealthChecks": true, "ContinueOnRabbitMQFailure": true}}