import { useCurrentUserRoleApplications } from '@/lib/hooks/useCurrentUserRoleApplications'
import React, { useState, useMemo } from 'react'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Search, ExternalLink, Globe } from 'lucide-react'

export const UserRoleApplicationsGrid: React.FC = () => {
    const apps = useCurrentUserRoleApplications()
    const [searchQuery, setSearchQuery] = useState('')

    // Filter applications based on search query
    const filteredApps = useMemo(() => {
        if (!searchQuery.trim()) return apps

        const query = searchQuery.toLowerCase()
        return apps.filter(app =>
            app.applicationName.toLowerCase().includes(query) ||
            (app.clientUrl && app.clientUrl.toLowerCase().includes(query))
        )
    }, [apps, searchQuery])

    if (!apps.length) {
        return (
            <div className="text-center py-8">
                <Globe className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Applications Available</h3>
                <p className="text-gray-500">You don't have access to any applications yet.</p>
            </div>
        )
    }

    const handleCardClick = (app: typeof apps[0]) => {
        if (app.clientUrl) {
            window.open(app.clientUrl, '_blank', 'noopener,noreferrer')
        }
    }

    return (
        <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h3 className="text-lg font-semibold text-gray-900">Your Accessible Applications</h3>
                    <p className="text-sm text-gray-500 mt-1">
                        {apps.length} application{apps.length !== 1 ? 's' : ''} available based on your roles
                    </p>
                </div>

                {/* Search Input */}
                <div className="relative w-full sm:w-80">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                        type="text"
                        placeholder="Search applications..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                    />
                </div>
            </div>

            {/* Results count */}
            {searchQuery && (
                <div className="text-sm text-gray-600">
                    {filteredApps.length} of {apps.length} applications match your search
                </div>
            )}

            {/* Applications Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {filteredApps.map((app) => (
                    <Card
                        key={app.applicationId}
                        className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${app.clientUrl
                                ? 'hover:border-blue-300 focus-within:ring-2 focus-within:ring-blue-500'
                                : 'opacity-75 cursor-not-allowed'
                            }`}
                        onClick={() => handleCardClick(app)}
                        tabIndex={app.clientUrl ? 0 : -1}
                        onKeyDown={(e) => {
                            if ((e.key === 'Enter' || e.key === ' ') && app.clientUrl) {
                                e.preventDefault()
                                handleCardClick(app)
                            }
                        }}
                        role={app.clientUrl ? "button" : "presentation"}
                        aria-label={app.clientUrl ? `Open ${app.applicationName}` : app.applicationName}
                    >
                        <CardHeader className="pb-3">
                            <div className="flex items-start justify-between">
                                <CardTitle className="text-base font-medium line-clamp-2 flex-1">
                                    {app.applicationName}
                                </CardTitle>
                                {app.clientUrl && (
                                    <ExternalLink className="h-4 w-4 text-gray-400 ml-2 flex-shrink-0" />
                                )}
                            </div>
                        </CardHeader>

                        <CardContent className="pt-0">
                            {app.clientUrl ? (
                                <CardDescription className="text-xs text-blue-600 truncate">
                                    {app.clientUrl}
                                </CardDescription>
                            ) : (
                                <Badge variant="secondary" className="text-xs">
                                    No URL configured
                                </Badge>
                            )}
                        </CardContent>
                    </Card>
                ))}
            </div>

            {/* No results message */}
            {searchQuery && filteredApps.length === 0 && (
                <div className="text-center py-8">
                    <Search className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
                    <p className="text-gray-500">
                        Try adjusting your search terms or browse all available applications.
                    </p>
                </div>
            )}
        </div>
    )
}