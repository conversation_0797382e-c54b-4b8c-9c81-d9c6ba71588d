{"version": 3, "file": "query-utils-extended-wVOeERM5.js", "sources": ["../../../../../frontend/src/lib/query-utils.ts", "../../../../../frontend/src/lib/query-utils-extended.ts"], "sourcesContent": ["import {\r\n  type FilterCondition,\r\n  type FilterGroup,\r\n  type FilterOperator,\r\n  type LogicalOperator,\r\n  type QueryParameters,\r\n  type SortInfo,\r\n} from '@/client'\r\n\r\n// Define interfaces for API error handling\r\ninterface ApiErrorDetails {\r\n  status?: number;\r\n  title?: string;\r\n  detail?: string;\r\n  message?: string;\r\n  error?: {\r\n    message?: string;\r\n    details?: string;\r\n  };\r\n  [key: string]: unknown;\r\n}\r\n\r\ninterface ApiError {\r\n  error?: string;\r\n  message?: string;\r\n  details?: ApiErrorDetails;\r\n  status?: number;\r\n  url?: string;\r\n}\r\n\r\n// Define interface for sorting structure\r\ninterface SortingItem {\r\n  id: string;\r\n  desc: boolean;\r\n}\r\n\r\n/**\r\n * Interface for parameters needed to generate a query body\r\n */\r\nexport interface QueryBuilderParams {\r\n  pageIndex: number\r\n  pageSize: number\r\n  filter?: string\r\n  sorting?: string\r\n  filterField?: string\r\n  operator?: FilterOperator\r\n}\r\n\r\n/**\r\n * Generates a query parameters object for API list requests\r\n *\r\n * @param params Query parameters containing pagination, filtering, and sorting info\r\n * @returns A query parameters object ready to be used in API requests\r\n */\r\nexport function generateQueryParameters(params: QueryBuilderParams): QueryParameters {\r\n  const {\r\n    pageIndex,\r\n    pageSize,\r\n    filter,\r\n    sorting,\r\n    filterField = 'clientId',\r\n    operator = 'Contains',\r\n  } = params\r\n\r\n  // Skip calculation is handled by the API through the page parameter\r\n\r\n  // Ensure Sorting is never empty string\r\n  const sortingValue = sorting ?? ''\r\n\r\n  // Create filter conditions if filter is provided\r\n  const filterConditions: FilterCondition[] = []\r\n  if (filter && filter !== 'null') {\r\n    filterConditions.push({\r\n      fieldName: filterField,\r\n      operator: operator,\r\n      value: filter,\r\n    })\r\n  }\r\n\r\n  // Create filter group\r\n  const filterGroup: FilterGroup = {\r\n    operator: 'And' as LogicalOperator,\r\n    conditions: filterConditions,\r\n  }\r\n\r\n  // Convert sorting string to SortInfo array\r\n  const sortArray: Array<SortInfo> = []\r\n  if (sorting) {\r\n    try {\r\n      const parsedSorting = JSON.parse(sorting) as SortingItem[]\r\n      parsedSorting.forEach((sort) => {\r\n        sortArray.push({\r\n          field: sort.id,\r\n          desc: sort.desc,\r\n        })\r\n      })\r\n    } catch (e) {\r\n      console.error('Error parsing sorting JSON:', e)\r\n    }\r\n  }\r\n\r\n  // Return query parameters object\r\n  return {\r\n    sorting: sortingValue,\r\n    page: pageIndex + 1,\r\n    sort: sortArray,\r\n    filterGroup: filterGroup,\r\n    maxResultCount: pageSize,\r\n  } as QueryParameters\r\n}\r\n\r\n/**\r\n * Handles API errors consistently\r\n *\r\n * @param error The error object from the API\r\n * @param defaultTitle Default error title if not provided in error\r\n * @param defaultDescription Default error description if not provided in error\r\n * @returns A standard error object with title and description\r\n */\r\nexport function extractApiError(\r\n  error: unknown,\r\n  defaultTitle = 'Error loading data',\r\n  defaultDescription = 'An unexpected error occurred'\r\n): { title: string; description: string } {\r\n  let title = defaultTitle\r\n  let description = defaultDescription\r\n\r\n  try {\r\n    const apiError = error as ApiError\r\n    if (apiError?.details?.error) {\r\n      title = apiError.details.error.message ?? defaultTitle\r\n      description = apiError.details.error.details ?? defaultDescription\r\n    }\r\n  } catch (e) {\r\n    console.error('Error parsing API error:', e)\r\n  }\r\n\r\n  return {\r\n    title,\r\n    description,\r\n  }\r\n}\r\n", "import {\r\n  type FilterCondition,\r\n  type FilterGroup,\r\n  type FilterOperator,\r\n  type LogicalOperator,\r\n  type QueryParameters,\r\n  type SortInfo,\r\n} from '@/client'\r\n\r\n// Define interface for sorting structure\r\ninterface SortingItem {\r\n  id: string;\r\n  desc: boolean;\r\n}\r\n\r\n/**\r\n * Interface for parameters needed to generate a query body with multiple filter conditions\r\n */\r\nexport interface ExtendedQueryBuilderParams {\r\n  pageIndex: number\r\n  pageSize: number\r\n  sorting?: string\r\n  filterConditions?: Array<{\r\n    fieldName: string\r\n    operator: FilterOperator\r\n    value: string | null\r\n  }>\r\n}\r\n\r\n/**\r\n * Generates a query parameters object for API list requests with support for multiple filter conditions\r\n *\r\n * @param params Query parameters containing pagination, filtering, and sorting info\r\n * @returns A query parameters object ready to be used in API requests\r\n */\r\nexport function generateExtendedQueryParameters(params: ExtendedQueryBuilderParams): QueryParameters {\r\n  const {\r\n    pageIndex,\r\n    pageSize,\r\n    sorting,\r\n    filterConditions = [],\r\n  } = params\r\n\r\n  // Skip calculation is handled by the API through the page parameter\r\n\r\n  // Ensure Sorting is never empty string\r\n  const sortingValue = sorting ?? ''\r\n\r\n  // Create filter group with all conditions\r\n  const filterGroup: FilterGroup = {\r\n    operator: 'And' as LogicalOperator,\r\n    conditions: filterConditions as FilterCondition[],\r\n  }\r\n\r\n  // Convert sorting string to SortInfo array\r\n  const sortArray: Array<SortInfo> = []\r\n  if (sorting) {\r\n    try {\r\n      const parsedSorting = JSON.parse(sorting) as SortingItem[]\r\n      parsedSorting.forEach((sort) => {\r\n        sortArray.push({\r\n          field: sort.id,\r\n          desc: sort.desc,\r\n        })\r\n      })\r\n    } catch (e) {\r\n      console.error('Error parsing sorting JSON:', e)\r\n    }\r\n  }\r\n\r\n  // Return query parameters object\r\n  return {\r\n    sorting: sortingValue,\r\n    page: pageIndex + 1,\r\n    sort: sortArray,\r\n    filterGroup: filterGroup,\r\n    maxResultCount: pageSize,\r\n  } as QueryParameters\r\n}\r\n"], "names": ["extractApiError", "error", "defaultTitle", "defaultDescription", "title", "description", "apiError", "generateExtendedQueryParameters", "params", "pageIndex", "pageSize", "sorting", "filterConditions", "sortingValue", "filterGroup", "sortArray", "sort"], "mappings": "0BAuHO,SAASA,EACdC,EACAC,EAAe,qBACfC,EAAqB,+BACmB,CACxC,IAAIC,EAAQF,EACRG,EAAcF,EAEd,GAAA,CACF,MAAMG,EAAWL,EACbK,GAAU,SAAS,QACbF,EAAAE,EAAS,QAAQ,MAAM,SAAWJ,EAC5BG,EAAAC,EAAS,QAAQ,MAAM,SAAWH,QAExC,CAAA,CAIL,MAAA,CACL,MAAAC,EACA,YAAAC,CACF,CACF,CC1GO,SAASE,EAAgCC,EAAqD,CAC7F,KAAA,CACJ,UAAAC,EACA,SAAAC,EACA,QAAAC,EACA,iBAAAC,EAAmB,CAAA,CAAC,EAClBJ,EAKEK,EAAeF,GAAW,GAG1BG,EAA2B,CAC/B,SAAU,MACV,WAAYF,CACd,EAGMG,EAA6B,CAAC,EACpC,GAAIJ,EACE,GAAA,CACoB,KAAK,MAAMA,CAAO,EAC1B,QAASK,GAAS,CAC9BD,EAAU,KAAK,CACb,MAAOC,EAAK,GACZ,KAAMA,EAAK,IAAA,CACZ,CAAA,CACF,OACS,CAAA,CAMP,MAAA,CACL,QAASH,EACT,KAAMJ,EAAY,EAClB,KAAMM,EACN,YAAAD,EACA,eAAgBJ,CAClB,CACF"}