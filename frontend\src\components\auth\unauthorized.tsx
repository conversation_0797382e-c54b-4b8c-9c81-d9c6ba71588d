"use client"

import { <PERSON><PERSON>lert } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface UnauthorizedProps {
  message?: string
  showBackButton?: boolean
}

export function Unauthorized({
  message = "You don't have permission to access this page.",
  showBackButton = true,
}: UnauthorizedProps) {

  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-6">
      <div className="flex flex-col items-center text-center space-y-4 max-w-md">
        <ShieldAlert className="h-12 w-12 text-gray-400" />
        <h2 className="text-2xl font-semibold tracking-tight">Access Restricted</h2>
        <p className="text-gray-500">{message}</p>
        {showBackButton && (
          <Button
            onClick={() => window.history.back()}
            variant="outline"
            className="mt-4"
          >
            Go Back
          </Button>
        )}
      </div>
    </div>
  )
}
