import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground",
        "flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none",
        "file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium",
        "disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
        "md:text-sm",
        // Border and background colors
        "border-input dark:bg-input/30",
        // Focus states
        "focus-visible:border-primary focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40 focus-visible:ring-[3px]",
        // Invalid states
        "aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40",
        // Hover states
        "hover:border-primary/50 dark:hover:border-primary/30",
        // Active states
        "active:border-primary/70 dark:active:border-primary/50",
        className
      )}
      {...props}
    />
  )
}

export { Input }
