"use client"

import type { ReactNode } from "react"
import { type Policy, useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies"
import { Unauthorized } from "@/components/auth/unauthorized"

interface PolicyGuardProps {
  policy: Policy
  children: ReactNode
  fallback?: ReactNode
  message?: string
}

export function PolicyGuard({
  policy,
  children,
  fallback,
  message = "You don't have permission to access this page."
}: PolicyGuardProps) {
  const { can } = useGrantedPolicies()

  if (!can(policy)) {
    if (fallback) {
      return <>{fallback}</>
    }

    return <Unauthorized message={message} />
  }

  return <>{children}</>
}
