{"version": 3, "file": "index.esm-DqIqfoOW.js", "sources": ["../../../../../frontend/src/components/ui/checkbox.tsx", "../../../../../frontend/src/components/ui/input.tsx", "../../../../../frontend/src/components/ui/dialog.tsx", "../../../../../frontend/node_modules/.pnpm/react-hook-form@7.57.0_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { CheckIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot=\"checkbox\"\r\n      className={cn(\r\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot=\"checkbox-indicator\"\r\n        className=\"flex items-center justify-center text-current transition-none\"\r\n      >\r\n        <CheckIcon className=\"size-3.5\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Checkbox }\r\n", "import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground\",\r\n        \"flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none\",\r\n        \"file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium\",\r\n        \"disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\",\r\n        \"md:text-sm\",\r\n        // Border and background colors\r\n        \"border-input dark:bg-input/30\",\r\n        // Focus states\r\n        \"focus-visible:border-primary focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40 focus-visible:ring-[3px]\",\r\n        // Invalid states\r\n        \"aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40\",\r\n        // Hover states\r\n        \"hover:border-primary/50 dark:hover:border-primary/30\",\r\n        // Active states\r\n        \"active:border-primary/70 dark:active:border-primary/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n", "import * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst dialogContentVariants = cva(\r\n  \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200\",\r\n  {\r\n    variants: {\r\n      size: {\r\n        sm: \"sm:max-w-sm\",\r\n        md: \"sm:max-w-md\",\r\n        lg: \"sm:max-w-lg\",\r\n        xl: \"sm:max-w-xl\",\r\n        \"2xl\": \"sm:max-w-2xl\",\r\n        \"3xl\": \"sm:max-w-3xl\",\r\n        \"4xl\": \"sm:max-w-4xl\",\r\n        \"5xl\": \"sm:max-w-5xl\",\r\n        \"6xl\": \"sm:max-w-6xl\",\r\n        \"7xl\": \"sm:max-w-7xl\",\r\n        full: \"sm:max-w-full\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      size: \"lg\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  size,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & \r\n  VariantProps<typeof dialogContentVariants> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(dialogContentVariants({ size }), className)}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n", "import * as React from 'react';\nimport React__default from 'react';\n\nvar isCheckBoxInput = (element) => element.type === 'checkbox';\n\nvar isDateObject = (value) => value instanceof Date;\n\nvar isNullOrUndefined = (value) => value == null;\n\nconst isObjectType = (value) => typeof value === 'object';\nvar isObject = (value) => !isNullOrUndefined(value) &&\n    !Array.isArray(value) &&\n    isObjectType(value) &&\n    !isDateObject(value);\n\nvar getEventValue = (event) => isObject(event) && event.target\n    ? isCheckBoxInput(event.target)\n        ? event.target.checked\n        : event.target.value\n    : event;\n\nvar getNodeParentName = (name) => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\n\nvar isPlainObject = (tempObject) => {\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));\n};\n\nvar isWeb = typeof window !== 'undefined' &&\n    typeof window.HTMLElement !== 'undefined' &&\n    typeof document !== 'undefined';\n\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    }\n    else if (data instanceof Set) {\n        copy = new Set(data);\n    }\n    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&\n        (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        }\n        else {\n            for (const key in data) {\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    }\n    else {\n        return data;\n    }\n    return copy;\n}\n\nvar compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];\n\nvar isUndefined = (val) => val === undefined;\n\nvar get = (object, path, defaultValue) => {\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object\n        ? isUndefined(object[path])\n            ? defaultValue\n            : object[path]\n        : result;\n};\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nvar isKey = (value) => /^\\w*$/.test(value);\n\nvar stringToPath = (input) => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nvar set = (object, path, value) => {\n    let index = -1;\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while (++index < length) {\n        const key = tempPath[index];\n        let newValue = value;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue =\n                isObject(objValue) || Array.isArray(objValue)\n                    ? objValue\n                    : !isNaN(+tempPath[index + 1])\n                        ? []\n                        : {};\n        }\n        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\n\nconst EVENTS = {\n    BLUR: 'blur',\n    FOCUS_OUT: 'focusout',\n    CHANGE: 'change',\n};\nconst VALIDATION_MODE = {\n    onBlur: 'onBlur',\n    onChange: 'onChange',\n    onSubmit: 'onSubmit',\n    onTouched: 'onTouched',\n    all: 'all',\n};\nconst INPUT_VALIDATION_RULES = {\n    max: 'max',\n    min: 'min',\n    maxLength: 'maxLength',\n    minLength: 'minLength',\n    pattern: 'pattern',\n    required: 'required',\n    validate: 'validate',\n};\n\nconst HookFormContext = React__default.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => React__default.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = (props) => {\n    const { children, ...data } = props;\n    return (React__default.createElement(HookFormContext.Provider, { value: data }, children));\n};\n\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n    const result = {\n        defaultValues: control._defaultValues,\n    };\n    for (const key in formState) {\n        Object.defineProperty(result, key, {\n            get: () => {\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            },\n        });\n    }\n    return result;\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = React__default.useState(control._formState);\n    const _localProxyFormState = React__default.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    });\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n            !disabled &&\n                updateFormState({\n                    ...control._formState,\n                    ...formState,\n                });\n        },\n    }), [name, disabled, exact]);\n    React__default.useEffect(() => {\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [control]);\n    return React__default.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\n\nvar isString = (value) => typeof value === 'string';\n\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, } = props || {};\n    const _defaultValue = React__default.useRef(defaultValue);\n    const [value, updateValue] = React__default.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: {\n            values: true,\n        },\n        exact,\n        callback: (formState) => !disabled &&\n            updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current)),\n    }), [name, control, disabled, exact]);\n    React__default.useEffect(() => control._removeUnmounted());\n    return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true,\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true,\n    });\n    const _props = React__default.useRef(props);\n    const _registerProps = React__default.useRef(control.register(name, {\n        ...props.rules,\n        value,\n        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }));\n    const fieldState = React__default.useMemo(() => Object.defineProperties({}, {\n        invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n        },\n    }), [formState, name]);\n    const onChange = React__default.useCallback((event) => _registerProps.current.onChange({\n        target: {\n            value: getEventValue(event),\n            name: name,\n        },\n        type: EVENTS.CHANGE,\n    }), [name]);\n    const onBlur = React__default.useCallback(() => _registerProps.current.onBlur({\n        target: {\n            value: get(control._formValues, name),\n            name: name,\n        },\n        type: EVENTS.BLUR,\n    }), [name, control._formValues]);\n    const ref = React__default.useCallback((elm) => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: () => elm.focus && elm.focus(),\n                select: () => elm.select && elm.select(),\n                setCustomValidity: (message) => elm.setCustomValidity(message),\n                reportValidity: () => elm.reportValidity(),\n            };\n        }\n    }, [control._fields, name]);\n    const field = React__default.useMemo(() => ({\n        name,\n        value,\n        ...(isBoolean(disabled) || formState.disabled\n            ? { disabled: formState.disabled || disabled }\n            : {}),\n        onChange,\n        onBlur,\n        ref,\n    }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n    React__default.useEffect(() => {\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...(isBoolean(_props.current.disabled)\n                ? { disabled: _props.current.disabled }\n                : {}),\n        });\n        const updateMounted = (name, value) => {\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value);\n            }\n        }\n        !isArrayField && control.register(name);\n        return () => {\n            (isArrayField\n                ? _shouldUnregisterField && !control._state.action\n                : _shouldUnregisterField)\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, isArrayField, shouldUnregister]);\n    React__default.useEffect(() => {\n        control._setDisabledField({\n            disabled,\n            name,\n        });\n    }, [disabled, name, control]);\n    return React__default.useMemo(() => ({\n        field,\n        formState,\n        fieldState,\n    }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = (props) => props.render(useController(props));\n\nconst flatten = (obj) => {\n    const output = {};\n    for (const key of Object.keys(obj)) {\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)) {\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        }\n        else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\n\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = React__default.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event) => {\n        let hasError = false;\n        let type = '';\n        await control.handleSubmit(async (data) => {\n            const formData = new FormData();\n            let formDataJson = '';\n            try {\n                formDataJson = JSON.stringify(data);\n            }\n            catch (_a) { }\n            const flattenFormValues = flatten(control._formValues);\n            for (const key in flattenFormValues) {\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson,\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers['Content-Type'],\n                        encType,\n                    ].some((value) => value && value.includes('json'));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...(encType ? { 'Content-Type': encType } : {}),\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData,\n                    });\n                    if (response &&\n                        (validateStatus\n                            ? !validateStatus(response.status)\n                            : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({ response });\n                        type = String(response.status);\n                    }\n                    else {\n                        onSuccess && onSuccess({ response });\n                    }\n                }\n                catch (error) {\n                    hasError = true;\n                    onError && onError({ error });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false,\n            });\n            props.control.setError('root.server', {\n                type,\n            });\n        }\n    };\n    React__default.useEffect(() => {\n        setMounted(true);\n    }, []);\n    return render ? (React__default.createElement(React__default.Fragment, null, render({\n        submit,\n    }))) : (React__default.createElement(\"form\", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n            ...(errors[name] && errors[name].types ? errors[name].types : {}),\n            [type]: message || true,\n        },\n    }\n    : {};\n\nvar convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);\n\nvar createSubject = () => {\n    let _observers = [];\n    const next = (value) => {\n        for (const observer of _observers) {\n            observer.next && observer.next(value);\n        }\n    };\n    const subscribe = (observer) => {\n        _observers.push(observer);\n        return {\n            unsubscribe: () => {\n                _observers = _observers.filter((o) => o !== observer);\n            },\n        };\n    };\n    const unsubscribe = () => {\n        _observers = [];\n    };\n    return {\n        get observers() {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe,\n    };\n};\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1) {\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== 'ref') {\n            const val2 = object2[key];\n            if ((isDateObject(val1) && isDateObject(val2)) ||\n                (isObject(val1) && isObject(val2)) ||\n                (Array.isArray(val1) && Array.isArray(val2))\n                ? !deepEqual(val1, val2)\n                : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isFileInput = (element) => element.type === 'file';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isHTMLElement = (value) => {\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value ? value.ownerDocument : 0;\n    return (value instanceof\n        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));\n};\n\nvar isMultipleSelect = (element) => element.type === `select-multiple`;\n\nvar isRadioInput = (element) => element.type === 'radio';\n\nvar isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);\n\nvar live = (ref) => isHTMLElement(ref) && ref.isConnected;\n\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while (index < length) {\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path)\n        ? path\n        : isKey(path)\n            ? [path]\n            : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 &&\n        ((isObject(childObject) && isEmptyObject(childObject)) ||\n            (Array.isArray(childObject) && isEmptyArray(childObject)))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\n\nvar objectHasFunction = (data) => {\n    for (const key in data) {\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\n\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            }\n            else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                if (isUndefined(formValues) ||\n                    isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key])\n                        ? markFieldsDirty(data[key], [])\n                        : { ...markFieldsDirty(data[key]) };\n                }\n                else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            }\n            else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\n\nconst defaultResult = {\n    value: false,\n    isValid: false,\n};\nconst validResult = { value: true, isValid: true };\nvar getCheckboxValue = (options) => {\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options\n                .filter((option) => option && option.checked && !option.disabled)\n                .map((option) => option.value);\n            return { value: values, isValid: !!values.length };\n        }\n        return options[0].checked && !options[0].disabled\n            ? // @ts-expect-error expected to work in the browser\n                options[0].attributes && !isUndefined(options[0].attributes.value)\n                    ? isUndefined(options[0].value) || options[0].value === ''\n                        ? validResult\n                        : { value: options[0].value, isValid: true }\n                    : validResult\n            : defaultResult;\n    }\n    return defaultResult;\n};\n\nvar getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)\n    ? value\n    : valueAsNumber\n        ? value === ''\n            ? NaN\n            : value\n                ? +value\n                : value\n        : valueAsDate && isString(value)\n            ? new Date(value)\n            : setValueAs\n                ? setValueAs(value)\n                : value;\n\nconst defaultReturn = {\n    isValid: false,\n    value: null,\n};\nvar getRadioValue = (options) => Array.isArray(options)\n    ? options.reduce((previous, option) => option && option.checked && !option.disabled\n        ? {\n            isValid: true,\n            value: option.value,\n        }\n        : previous, defaultReturn)\n    : defaultReturn;\n\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [...ref.selectedOptions].map(({ value }) => value);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n    const fields = {};\n    for (const name of fieldsNames) {\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [...fieldsNames],\n        fields,\n        shouldUseNativeValidation,\n    };\n};\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getRuleValue = (rule) => isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n        ? rule.source\n        : isObject(rule)\n            ? isRegex(rule.value)\n                ? rule.value.source\n                : rule.value\n            : rule;\n\nvar getValidationModes = (mode) => ({\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\n    isOnChange: mode === VALIDATION_MODE.onChange,\n    isOnAll: mode === VALIDATION_MODE.all,\n    isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = (fieldReference) => !!fieldReference &&\n    !!fieldReference.validate &&\n    !!((isFunction(fieldReference.validate) &&\n        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n        (isObject(fieldReference.validate) &&\n            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));\n\nvar hasValidation = (options) => options.mount &&\n    (options.required ||\n        options.min ||\n        options.max ||\n        options.maxLength ||\n        options.minLength ||\n        options.pattern ||\n        options.validate);\n\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&\n    (_names.watchAll ||\n        _names.watch.has(name) ||\n        [..._names.watch].some((watchName) => name.startsWith(watchName) &&\n            /^\\.\\w+/.test(name.slice(watchName.length))));\n\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n    for (const key of fieldsNames || Object.keys(fields)) {\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                }\n                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                }\n                else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            }\n            else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\n\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name,\n        };\n    }\n    const names = name.split('.');\n    while (names.length) {\n        const fieldName = names.join('.');\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return { name };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError,\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root,\n            };\n        }\n        names.pop();\n    }\n    return {\n        name,\n    };\n}\n\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return (isEmptyObject(formState) ||\n        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n        Object.keys(formState).find((key) => _proxyFormState[key] ===\n            (!isRoot || VALIDATION_MODE.all)));\n};\n\nvar shouldSubscribeByName = (name, signalName, exact) => !name ||\n    !signalName ||\n    name === signalName ||\n    convertToArrayPayload(name).some((currentName) => currentName &&\n        (exact\n            ? currentName === signalName\n            : currentName.startsWith(signalName) ||\n                signalName.startsWith(currentName)));\n\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n    if (mode.isOnAll) {\n        return false;\n    }\n    else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    }\n    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    }\n    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\n\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\n\nvar updateFieldArrayRootError = (errors, error, name) => {\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, 'root', error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\n\nvar isMessage = (value) => isString(value);\n\nfunction getValidateError(result, ref, type = 'validate') {\n    if (isMessage(result) ||\n        (Array.isArray(result) && result.every(isMessage)) ||\n        (isBoolean(result) && !result)) {\n        return {\n            type,\n            message: isMessage(result) ? result : '',\n            ref,\n        };\n    }\n}\n\nvar getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n    };\n\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message) => {\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&\n        isUndefined(ref.value) &&\n        isUndefined(inputValue)) ||\n        (isHTMLElement(ref) && ref.value === '') ||\n        inputValue === '' ||\n        (Array.isArray(inputValue) && !inputValue.length);\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n        };\n    };\n    if (isFieldArray\n        ? !Array.isArray(inputValue) || !inputValue.length\n        : required &&\n            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n                (isBoolean(inputValue) && !inputValue) ||\n                (isCheckBox && !getCheckboxValue(refs).isValid) ||\n                (isRadio && !getRadioValue(refs).isValid))) {\n        const { value, message } = isMessage(required)\n            ? { value: !!required, message: required }\n            : getValueAndMessage(required);\n        if (value) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber ||\n                (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        }\n        else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);\n            const isTime = ref.type == 'time';\n            const isWeek = ref.type == 'week';\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime\n                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n                    : isWeek\n                        ? inputValue > maxOutput.value\n                        : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime\n                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n                    : isWeek\n                        ? inputValue < minOutput.value\n                        : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) &&\n        !isEmpty &&\n        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&\n            inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&\n            inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        }\n        else if (isObject(validate)) {\n            let validationResult = {};\n            for (const key in validate) {\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message),\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult,\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\n\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true,\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props,\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false,\n    };\n    const _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)\n        ? cloneObject(_options.defaultValues || _options.values) || {}\n        : {};\n    let _formValues = _options.shouldUnregister\n        ? {}\n        : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false,\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set(),\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState,\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject(),\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback) => (wait) => {\n        clearTimeout(timer);\n        timer = setTimeout(callback, wait);\n    };\n    const _setValid = async (shouldUpdateValid) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValid ||\n                _proxySubscribeFormState.isValid ||\n                shouldUpdateValid)) {\n            const isValid = _options.resolver\n                ? isEmptyObject((await _runSchema()).errors)\n                : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid,\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValidating ||\n                _proxyFormState.validatingFields ||\n                _proxySubscribeFormState.isValidating ||\n                _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name) => {\n                if (name) {\n                    isValidating\n                        ? set(_formState.validatingFields, name, isValidating)\n                        : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields),\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields ||\n                _proxySubscribeFormState.touchedFields) &&\n                shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid,\n            });\n        }\n        else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error) => {\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors,\n        });\n    };\n    const _setErrors = (errors) => {\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false,\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n            isUndefined(defaultValue) ||\n                (ref && ref.defaultChecked) ||\n                shouldSkipSetValueAs\n                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))\n                : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name,\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine\n                    ? unset(_formState.dirtyFields, name)\n                    : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField =\n                    shouldUpdateField ||\n                        ((_proxyFormState.dirtyFields ||\n                            _proxySubscribeFormState.dirtyFields) &&\n                            isPreviousDirty !== !isCurrentFieldPristine);\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField =\n                        shouldUpdateField ||\n                            ((_proxyFormState.touchedFields ||\n                                _proxySubscribeFormState.touchedFields) &&\n                                isPreviousFieldTouched !== isBlurEvent);\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState) => {\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n            isBoolean(isValid) &&\n            _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(() => updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        }\n        else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error\n                ? set(_formState.errors, name, error)\n                : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n            !isEmptyObject(fieldState) ||\n            shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n                errors: _formState.errors,\n                name,\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState,\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name) => {\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names) => {\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names) {\n                const error = get(errors, name);\n                error\n                    ? set(_formState.errors, name, error)\n                    : unset(_formState.errors, name);\n            }\n        }\n        else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true,\n    }) => {\n        for (const name in fields) {\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid &&\n                        (get(fieldError, _f.name)\n                            ? isFieldArrayRoot\n                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)\n                                : set(_formState.errors, _f.name, fieldError[_f.name])\n                            : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) &&\n                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = () => {\n        for (const name of _names.unMount) {\n            const field = get(_fields, name);\n            field &&\n                (field._f.refs\n                    ? field._f.refs.every((ref) => !live(ref))\n                    : !live(field._f.ref)) &&\n                unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data) => !_options.disabled &&\n        (name && data && set(_formValues, name, data),\n            !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n        ...(_state.mount\n            ? _formValues\n            : isUndefined(defaultValue)\n                ? _defaultValues\n                : isString(names)\n                    ? { [names]: defaultValue }\n                    : defaultValue),\n    }, isGlobal, defaultValue);\n    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        let fieldValue = value;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled &&\n                    set(_formValues, name, getFieldValueAs(value, fieldReference));\n                fieldValue =\n                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n                        ? ''\n                        : value;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));\n                }\n                else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef) => {\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data) => data === checkboxRef.value);\n                                }\n                                else {\n                                    checkboxRef.checked =\n                                        fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    }\n                    else {\n                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));\n                    }\n                }\n                else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = '';\n                }\n                else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues),\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) &&\n            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value, options) => {\n        for (const fieldKey in value) {\n            if (!value.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value[fieldKey];\n            const fieldName = name + '.' + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) ||\n                isObject(fieldValue) ||\n                (field && !field._f)) &&\n                !isDateObject(fieldValue)\n                ? setValues(fieldName, fieldValue, options)\n                : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues),\n            });\n            if ((_proxyFormState.isDirty ||\n                _proxyFormState.dirtyFields ||\n                _proxySubscribeFormState.isDirty ||\n                _proxySubscribeFormState.dirtyFields) &&\n                options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue),\n                });\n            }\n        }\n        else {\n            field && !field._f && !isNullOrUndefined(cloneValue)\n                ? setValues(name, cloneValue, options)\n                : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({ ..._formState });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues),\n        });\n    };\n    const onChange = async (event) => {\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue) => {\n            isFieldValueUpdated =\n                Number.isNaN(fieldValue) ||\n                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n                    deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type\n                ? getFieldValue(field._f)\n                : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = (!hasValidation(field._f) &&\n                !_options.resolver &&\n                !get(_formState.errors, name) &&\n                !field._f.deps) ||\n                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            }\n            else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent &&\n                _subjects.state.next({\n                    name,\n                    type: event.type,\n                    values: cloneObject(_formValues),\n                });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === 'onBlur') {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    }\n                    else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return (shouldRender &&\n                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));\n            }\n            !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            }\n            else {\n                _updateIsValidating([name], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    }\n                    else if (_proxyFormState.isValid ||\n                        _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps &&\n                    trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key) => {\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {}) => {\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name\n                ? !fieldNames.some((name) => get(errors, name))\n                : isValid;\n        }\n        else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        }\n        else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...(!isString(name) ||\n                ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n                    isValid !== _formState.isValid)\n                ? {}\n                : { name }),\n            ...(_options.resolver || !name ? { isValid } : {}),\n            errors: _formState.errors,\n        });\n        options.shouldFocus &&\n            !validationResult &&\n            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames) => {\n        const values = {\n            ...(_state.mount ? _formValues : _defaultValues),\n        };\n        return isUndefined(fieldNames)\n            ? values\n            : isString(fieldNames)\n                ? get(values, fieldNames)\n                : fieldNames.map((name) => get(values, name));\n    };\n    const getFieldState = (name, formState) => ({\n        invalid: !!get((formState || _formState).errors, name),\n        isDirty: !!get((formState || _formState).dirtyFields, name),\n        error: get((formState || _formState).errors, name),\n        isValidating: !!get(_formState.validatingFields, name),\n        isTouched: !!get((formState || _formState).touchedFields, name),\n    });\n    const clearErrors = (name) => {\n        name &&\n            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {},\n        });\n    };\n    const setError = (name, error, options) => {\n        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref,\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false,\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue) => isFunction(name)\n        ? _subjects.state.subscribe({\n            next: (payload) => name(_getWatch(undefined, defaultValue), payload),\n        })\n        : _getWatch(name, defaultValue, true);\n    const _subscribe = (props) => _subjects.state.subscribe({\n        next: (formState) => {\n            if (shouldSubscribeByName(props.name, formState.name, props.exact) &&\n                shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                props.callback({\n                    values: { ..._formValues },\n                    ..._formState,\n                    ...formState,\n                });\n            }\n        },\n    }).unsubscribe;\n    const subscribe = (props) => {\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState,\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState,\n        });\n    };\n    const unregister = (name, options = {}) => {\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating &&\n                unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister &&\n                !options.keepDefaultValue &&\n                unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues),\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name, }) => {\n        if ((isBoolean(disabled) && _state.mount) ||\n            !!disabled ||\n            _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {}) => {\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...(field || {}),\n            _f: {\n                ...(field && field._f ? field._f : { ref: { name } }),\n                name,\n                mount: true,\n                ...options,\n            },\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled)\n                    ? options.disabled\n                    : _options.disabled,\n                name,\n            });\n        }\n        else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...(disabledIsDefined\n                ? { disabled: options.disabled || _options.disabled }\n                : {}),\n            ...(_options.progressive\n                ? {\n                    required: !!options.required,\n                    min: getRuleValue(options.min),\n                    max: getRuleValue(options.max),\n                    minLength: getRuleValue(options.minLength),\n                    maxLength: getRuleValue(options.maxLength),\n                    pattern: getRuleValue(options.pattern),\n                }\n                : {}),\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref) => {\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value)\n                        ? ref.querySelectorAll\n                            ? ref.querySelectorAll('input,select,textarea')[0] || ref\n                            : ref\n                        : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox\n                        ? refs.find((option) => option === fieldRef)\n                        : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...(radioOrCheckbox\n                                ? {\n                                    refs: [\n                                        ...refs.filter(live),\n                                        fieldRef,\n                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                                    ],\n                                    ref: { type: fieldRef.type, name },\n                                }\n                                : { ref: fieldRef }),\n                        },\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                }\n                else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) &&\n                        !(isNameInFieldArray(_names.array, name) && _state.action) &&\n                        _names.unMount.add(name);\n                }\n            },\n        };\n    };\n    const _focusError = () => _options.shouldFocusError &&\n        iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled) => {\n        if (isBoolean(disabled)) {\n            _subjects.state.next({ disabled });\n            iterateFieldsByAction(_fields, (ref, name) => {\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef) => {\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid) => async (e) => {\n        let onValidError = undefined;\n        if (e) {\n            e.preventDefault && e.preventDefault();\n            e.persist &&\n                e.persist();\n        }\n        let fieldValues = cloneObject(_formValues);\n        _subjects.state.next({\n            isSubmitting: true,\n        });\n        if (_options.resolver) {\n            const { errors, values } = await _runSchema();\n            _formState.errors = errors;\n            fieldValues = values;\n        }\n        else {\n            await executeBuiltInValidation(_fields);\n        }\n        if (_names.disabled.size) {\n            for (const name of _names.disabled) {\n                set(fieldValues, name, undefined);\n            }\n        }\n        unset(_formState.errors, 'root');\n        if (isEmptyObject(_formState.errors)) {\n            _subjects.state.next({\n                errors: {},\n            });\n            try {\n                await onValid(fieldValues, e);\n            }\n            catch (error) {\n                onValidError = error;\n            }\n        }\n        else {\n            if (onInvalid) {\n                await onInvalid({ ..._formState.errors }, e);\n            }\n            _focusError();\n            setTimeout(_focusError);\n        }\n        _subjects.state.next({\n            isSubmitted: true,\n            isSubmitting: false,\n            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n            submitCount: _formState.submitCount + 1,\n            errors: _formState.errors,\n        });\n        if (onValidError) {\n            throw onValidError;\n        }\n    };\n    const resetField = (name, options = {}) => {\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            }\n            else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue\n                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n                    : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({ ..._formState });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {}) => {\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)) {\n                    get(_formState.dirtyFields, fieldName)\n                        ? set(values, fieldName, get(_formValues, fieldName))\n                        : setValue(fieldName, get(values, fieldName));\n                }\n            }\n            else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount) {\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs)\n                                ? field._f.refs[0]\n                                : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest('form');\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                for (const fieldName of _names.mount) {\n                    setValue(fieldName, get(values, fieldName));\n                }\n            }\n            _formValues = cloneObject(values);\n            _subjects.array.next({\n                values: { ...values },\n            });\n            _subjects.state.next({\n                values: { ...values },\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: '',\n        };\n        _state.mount =\n            !_proxyFormState.isValid ||\n                !!keepStateOptions.keepIsValid ||\n                !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount\n                ? _formState.submitCount\n                : 0,\n            isDirty: isEmptyResetValues\n                ? false\n                : keepStateOptions.keepDirty\n                    ? _formState.isDirty\n                    : !!(keepStateOptions.keepDefaultValues &&\n                        !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted\n                ? _formState.isSubmitted\n                : false,\n            dirtyFields: isEmptyResetValues\n                ? {}\n                : keepStateOptions.keepDirtyValues\n                    ? keepStateOptions.keepDefaultValues && _formValues\n                        ? getDirtyFields(_defaultValues, _formValues)\n                        : _formState.dirtyFields\n                    : keepStateOptions.keepDefaultValues && formValues\n                        ? getDirtyFields(_defaultValues, formValues)\n                        : keepStateOptions.keepDirty\n                            ? _formState.dirtyFields\n                            : {},\n            touchedFields: keepStateOptions.keepTouched\n                ? _formState.touchedFields\n                : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n                ? _formState.isSubmitSuccessful\n                : false,\n            isSubmitting: false,\n        });\n    };\n    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)\n        ? formValues(_formValues)\n        : formValues, keepStateOptions);\n    const setFocus = (name, options = {}) => {\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs\n                ? fieldReference.refs[0]\n                : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect &&\n                    isFunction(fieldRef.select) &&\n                    fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState) => {\n        _formState = {\n            ..._formState,\n            ...updatedFormState,\n        };\n    };\n    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&\n        _options.defaultValues().then((values) => {\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false,\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields() {\n                return _fields;\n            },\n            get _formValues() {\n                return _formValues;\n            },\n            get _state() {\n                return _state;\n            },\n            set _state(value) {\n                _state = value;\n            },\n            get _defaultValues() {\n                return _defaultValues;\n            },\n            get _names() {\n                return _names;\n            },\n            set _names(value) {\n                _names = value;\n            },\n            get _formState() {\n                return _formState;\n            },\n            get _options() {\n                return _options;\n            },\n            set _options(value) {\n                _options = {\n                    ..._options,\n                    ...value,\n                };\n            },\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState,\n    };\n    return {\n        ...methods,\n        formControl: methods,\n    };\n}\n\nvar generateId = () => {\n    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n    });\n};\n\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n\nvar appendAt = (data, value) => [\n    ...data,\n    ...convertToArrayPayload(value),\n];\n\nvar fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;\n\nfunction insert(data, index, value) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value),\n        ...data.slice(index),\n    ];\n}\n\nvar moveArrayAt = (data, from, to) => {\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\n\nvar prependAt = (data, value) => [\n    ...convertToArrayPayload(value),\n    ...convertToArrayPayload(data),\n];\n\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [...data];\n    for (const index of indexes) {\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index)\n    ? []\n    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\n\nvar swapArrayAt = (data, indexA, indexB) => {\n    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n\nvar updateAt = (fieldValues, index, value) => {\n    fieldValues[index] = value;\n    return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;\n    const [fields, setFields] = React__default.useState(control._getFieldArray(name));\n    const ids = React__default.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = React__default.useRef(fields);\n    const _name = React__default.useRef(name);\n    const _actioned = React__default.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules &&\n        control.register(name, rules);\n    React__default.useEffect(() => control._subjects.array.subscribe({\n        next: ({ values, name: fieldArrayName, }) => {\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n    }).unsubscribe, [control]);\n    const updateValues = React__default.useCallback((updatedFieldArrayValues) => {\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [control, name]);\n    const append = (value, options) => {\n        const appendValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const prepend = (value, options) => {\n        const prependValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const remove = (index) => {\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) &&\n            set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index,\n        });\n    };\n    const insert$1 = (index, value, options) => {\n        const insertValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value),\n        });\n    };\n    const swap = (indexA, indexB) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB,\n        }, false);\n    };\n    const move = (from, to) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to,\n        }, false);\n    };\n    const update = (index, value) => {\n        const updateValue = cloneObject(value);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue,\n        }, true, false);\n    };\n    const replace = (value) => {\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([...updatedFieldArrayValues]);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);\n    };\n    React__default.useEffect(() => {\n        control._state.action = false;\n        isWatched(name, control._names) &&\n            control._subjects.state.next({\n                ...control._formState,\n            });\n        if (_actioned.current &&\n            (!getValidationModes(control._options.mode).isOnSubmit ||\n                control._formState.isSubmitted) &&\n            !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([name]).then((result) => {\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError\n                        ? (!error && existingError.type) ||\n                            (error &&\n                                (existingError.type !== error.type ||\n                                    existingError.message !== error.message))\n                        : error && error.type) {\n                        error\n                            ? set(control._formState.errors, name, error)\n                            : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors,\n                        });\n                    }\n                });\n            }\n            else {\n                const field = get(control._fields, name);\n                if (field &&\n                    field._f &&\n                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&\n                        getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&\n                        control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name),\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues),\n        });\n        control._names.focus &&\n            iterateFieldsByAction(control._fields, (ref, key) => {\n                if (control._names.focus &&\n                    key.startsWith(control._names.focus) &&\n                    ref.focus) {\n                    ref.focus();\n                    return 1;\n                }\n                return;\n            });\n        control._names.focus = '';\n        control._setValid();\n        _actioned.current = false;\n    }, [fields, name, control]);\n    React__default.useEffect(() => {\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return () => {\n            const updateMounted = (name, value) => {\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, keyName, shouldUnregister]);\n    return {\n        swap: React__default.useCallback(swap, [updateValues, name, control]),\n        move: React__default.useCallback(move, [updateValues, name, control]),\n        prepend: React__default.useCallback(prepend, [updateValues, name, control]),\n        append: React__default.useCallback(append, [updateValues, name, control]),\n        remove: React__default.useCallback(remove, [updateValues, name, control]),\n        insert: React__default.useCallback(insert$1, [updateValues, name, control]),\n        update: React__default.useCallback(update, [updateValues, name, control]),\n        replace: React__default.useCallback(replace, [updateValues, name, control]),\n        fields: React__default.useMemo(() => fields.map((field, index) => ({\n            ...field,\n            [keyName]: ids.current[index] || generateId(),\n        })), [fields, keyName]),\n    };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n    const _formControl = React__default.useRef(undefined);\n    const _values = React__default.useRef(undefined);\n    const [formState, updateFormState] = React__default.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues)\n            ? undefined\n            : props.defaultValues,\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...(props.formControl ? props.formControl : createFormControl(props)),\n            formState,\n        };\n        if (props.formControl &&\n            props.defaultValues &&\n            !isFunction(props.defaultValues)) {\n            props.formControl.reset(props.defaultValues, props.resetOptions);\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(() => {\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: () => updateFormState({ ...control._formState }),\n            reRenderRoot: true,\n        });\n        updateFormState((data) => ({\n            ...data,\n            isReady: true,\n        }));\n        control._formState.isReady = true;\n        return sub;\n    }, [control]);\n    React__default.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n    React__default.useEffect(() => {\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [control, props.mode, props.reValidateMode]);\n    React__default.useEffect(() => {\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [control, props.errors]);\n    React__default.useEffect(() => {\n        props.shouldUnregister &&\n            control._subjects.state.next({\n                values: control._getWatch(),\n            });\n    }, [control, props.shouldUnregister]);\n    React__default.useEffect(() => {\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty,\n                });\n            }\n        }\n    }, [control, formState.isDirty]);\n    React__default.useEffect(() => {\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state) => ({ ...state }));\n        }\n        else {\n            control._resetDefaultValues();\n        }\n    }, [control, props.values]);\n    React__default.useEffect(() => {\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({ ...control._formState });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n\nexport { Controller, Form, FormProvider, appendErrors, createFormControl, get, set, useController, useFieldArray, useForm, useFormContext, useFormState, useWatch };\n//# sourceMappingURL=index.esm.mjs.map\n"], "names": ["Checkbox", "className", "props", "jsx", "CheckboxPrimitive.Root", "cn", "CheckboxPrimitive.Indicator", "CheckIcon", "Input", "type", "dialogContentVariants", "cva", "Dialog", "DialogPrimitive.Root", "DialogTrigger", "DialogPrimitive.Trigger", "DialogPortal", "DialogPrimitive.Portal", "DialogOverlay", "DialogPrimitive.Overlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "showCloseButton", "size", "jsxs", "DialogPrimitive.Content", "DialogPrimitive.Close", "XIcon", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogPrimitive.Title", "isCheckBoxInput", "element", "isDateObject", "value", "isNullOrUndefined", "isObjectType", "isObject", "getEventValue", "event", "getNodeParentName", "name", "isNameInFieldArray", "names", "isPlainObject", "tempObject", "prototypeCopy", "isWeb", "cloneObject", "data", "copy", "isArray", "isFileListInstance", "key", "compact", "isUndefined", "val", "get", "object", "path", "defaultValue", "result", "isBoolean", "is<PERSON>ey", "stringToPath", "input", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React__default", "useFormContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "_key", "useIsomorphicLayoutEffect", "React.useLayoutEffect", "React.useEffect", "useFormState", "methods", "disabled", "exact", "updateFormState", "_localProxyFormState", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "fieldName", "useWatch", "_defaultValue", "updateValue", "useController", "shouldUnregister", "isArrayField", "_props", "_registerProps", "fieldState", "onChange", "onBlur", "ref", "elm", "field", "message", "_shouldUnregisterField", "updateMounted", "Controller", "appendErrors", "validateAllFieldCriteria", "errors", "convertToArrayPayload", "createSubject", "_observers", "observer", "o", "isPrimitive", "deepEqual", "object1", "object2", "keys1", "keys2", "val1", "val2", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "isMultipleSelect", "isRadioInput", "isRadioOrCheckbox", "live", "baseGet", "updatePath", "isEmptyArray", "obj", "unset", "paths", "childObject", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultValues", "defaultResult", "validResult", "getCheckboxValue", "options", "values", "option", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "_f", "getResolverOptions", "fieldsNames", "_fields", "criteriaMode", "shouldUseNativeValidation", "isRegex", "getRuleValue", "rule", "getValidationModes", "mode", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "validateFunction", "hasValidation", "isWatched", "isBlurEvent", "watchName", "iterateFieldsByAction", "action", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "error", "found<PERSON><PERSON>r", "shouldRenderFormState", "formStateData", "_proxyFormState", "shouldSubscribeByName", "signalName", "currentName", "skipValidation", "isTouched", "isSubmitted", "reValidateMode", "unsetEmptyArray", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "getValueAndMessage", "validationData", "validateField", "disabled<PERSON>ieldN<PERSON>s", "isFieldArray", "refs", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "validate", "mount", "inputValue", "inputRef", "setCustomValidity", "isRadio", "isCheckBox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueNumber", "valueDate", "convertTimeToDate", "time", "isTime", "isWeek", "maxLengthOutput", "minLengthOutput", "patternValue", "validateError", "validationResult", "defaultOptions", "createFormControl", "_options", "_formState", "_defaultValues", "_formValues", "_state", "delayError<PERSON><PERSON><PERSON>", "timer", "_proxySubscribeFormState", "_subjects", "shouldDisplayAllAssociatedErrors", "debounce", "callback", "wait", "_setValid", "shouldUpdateValid", "<PERSON><PERSON><PERSON><PERSON>", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "isValidating", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "field<PERSON><PERSON><PERSON>", "touchedFields", "_getDirty", "updateErrors", "_setErrors", "updateValidAndValue", "shouldSkipSetValueAs", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "output", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "updatedFormState", "executeSchemaAndUpdateState", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "context", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "_removeUnmounted", "unregister", "getV<PERSON>ues", "_getWatch", "_getFieldArray", "optionRef", "checkboxRef", "radioRef", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "target", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "getFieldState", "clearErrors", "inputName", "setError", "currentError", "currentRef", "restOfErrorTree", "watch", "payload", "_subscribe", "_setFormState", "subscribe", "_setDisabledField", "register", "disabledIsDefined", "fieldRef", "radioOrCheckbox", "_focusError", "_disableForm", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "reset<PERSON>ield", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "fieldsToCheck", "form", "reset", "setFocus", "useForm", "_formControl", "_values", "sub", "isDirty", "state"], "mappings": "wOAQA,SAASA,GAAS,CAChB,UAAAC,EACA,GAAGC,CACL,EAAwD,CAEpD,OAAAC,EAAA,IAACC,GAAA,CACC,YAAU,WACV,UAAWC,GACT,8eACAJ,CACF,EACC,GAAGC,EAEJ,SAAAC,EAAA,IAACG,GAAA,CACC,YAAU,qBACV,UAAU,gEAEV,SAAAH,EAAAA,IAACI,GAAU,CAAA,UAAU,UAAW,CAAA,CAAA,CAAA,CAClC,CACF,CAEJ,CCzBA,SAASC,GAAM,CAAE,UAAAP,EAAW,KAAAQ,EAAM,GAAGP,GAAwC,CAEzE,OAAAC,EAAA,IAAC,QAAA,CACC,KAAAM,EACA,YAAU,QACV,UAAWJ,GACT,gHACA,oIACA,4FACA,+EACA,aAEA,gCAEA,yHAEA,yGAEA,uDAEA,yDACAJ,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CCvBA,MAAMQ,GAAwBC,GAC5B,kWACA,CACE,SAAU,CACR,KAAM,CACJ,GAAI,cACJ,GAAI,cACJ,GAAI,cACJ,GAAI,cACJ,MAAO,eACP,MAAO,eACP,MAAO,eACP,MAAO,eACP,MAAO,eACP,MAAO,eACP,KAAM,eAAA,CAEV,EACA,gBAAiB,CACf,KAAM,IAAA,CACR,CAEJ,EAEA,SAASC,GAAO,CACd,GAAGV,CACL,EAAsD,CACpD,aAAQW,GAAA,CAAqB,YAAU,SAAU,GAAGX,EAAO,CAC7D,CAEA,SAASY,GAAc,CACrB,GAAGZ,CACL,EAAyD,CACvD,aAAQa,GAAA,CAAwB,YAAU,iBAAkB,GAAGb,EAAO,CACxE,CAEA,SAASc,GAAa,CACpB,GAAGd,CACL,EAAwD,CACtD,aAAQe,GAAA,CAAuB,YAAU,gBAAiB,GAAGf,EAAO,CACtE,CAQA,SAASgB,GAAc,CACrB,UAAAjB,EACA,GAAGC,CACL,EAAyD,CAErD,OAAAC,EAAA,IAACgB,GAAA,CACC,YAAU,iBACV,UAAWd,GACT,yJACAJ,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASkB,GAAc,CACrB,UAAAnB,EACA,SAAAoB,EACA,gBAAAC,EAAkB,GAClB,KAAAC,EACA,GAAGrB,CACL,EAGG,CAEC,OAAAsB,EAAA,KAACR,GAAa,CAAA,YAAU,gBACtB,SAAA,CAAAb,EAAA,IAACe,GAAc,EAAA,EACfM,EAAA,KAACC,GAAA,CACC,YAAU,iBACV,UAAWpB,GAAGK,GAAsB,CAAE,KAAAa,CAAM,CAAA,EAAGtB,CAAS,EACvD,GAAGC,EAEH,SAAA,CAAAmB,EACAC,GACCE,EAAA,KAACE,GAAA,CACC,YAAU,eACV,UAAU,oWAEV,SAAA,CAAAvB,EAAA,IAACwB,GAAM,EAAA,EACNxB,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAK,OAAA,CAAA,CAAA,CAAA,CAAA,CACjC,CAAA,CAAA,CAEJ,EACF,CAEJ,CAEA,SAASyB,GAAa,CAAE,UAAA3B,EAAW,GAAGC,GAAsC,CAExE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,gBACV,UAAWE,GAAG,+CAAgDJ,CAAS,EACtE,GAAGC,CAAA,CACN,CAEJ,CAEA,SAAS2B,GAAa,CAAE,UAAA5B,EAAW,GAAGC,GAAsC,CAExE,OAAAC,EAAA,IAAC,MAAA,CACC,YAAU,gBACV,UAAWE,GACT,yDACAJ,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAAS4B,GAAY,CACnB,UAAA7B,EACA,GAAGC,CACL,EAAuD,CAEnD,OAAAC,EAAA,IAAC4B,GAAA,CACC,YAAU,eACV,UAAW1B,GAAG,qCAAsCJ,CAAS,EAC5D,GAAGC,CAAA,CACN,CAEJ,CCvIA,IAAI8B,GAAmBC,GAAYA,EAAQ,OAAS,WAEhDC,GAAgBC,GAAUA,aAAiB,KAE3CC,EAAqBD,GAAUA,GAAS,KAE5C,MAAME,GAAgBF,GAAU,OAAOA,GAAU,SACjD,IAAIG,EAAYH,GAAU,CAACC,EAAkBD,CAAK,GAC9C,CAAC,MAAM,QAAQA,CAAK,GACpBE,GAAaF,CAAK,GAClB,CAACD,GAAaC,CAAK,EAEnBI,GAAiBC,GAAUF,EAASE,CAAK,GAAKA,EAAM,OAClDR,GAAgBQ,EAAM,MAAM,EACxBA,EAAM,OAAO,QACbA,EAAM,OAAO,MACjBA,EAEFC,GAAqBC,GAASA,EAAK,UAAU,EAAGA,EAAK,OAAO,aAAa,CAAC,GAAKA,EAE/EC,GAAqB,CAACC,EAAOF,IAASE,EAAM,IAAIH,GAAkBC,CAAI,CAAC,EAEvEG,GAAiBC,GAAe,CAChC,MAAMC,EAAgBD,EAAW,aAAeA,EAAW,YAAY,UACvE,OAAQR,EAASS,CAAa,GAAKA,EAAc,eAAe,eAAe,CACnF,EAEIC,GAAQ,OAAO,OAAW,KAC1B,OAAO,OAAO,YAAgB,KAC9B,OAAO,SAAa,IAExB,SAASC,EAAYC,EAAM,CACvB,IAAIC,EACJ,MAAMC,EAAU,MAAM,QAAQF,CAAI,EAC5BG,EAAqB,OAAO,SAAa,IAAcH,aAAgB,SAAW,GACxF,GAAIA,aAAgB,KAChBC,EAAO,IAAI,KAAKD,CAAI,UAEfA,aAAgB,IACrBC,EAAO,IAAI,IAAID,CAAI,UAEd,EAAEF,KAAUE,aAAgB,MAAQG,MACxCD,GAAWd,EAASY,CAAI,GAEzB,GADAC,EAAOC,EAAU,CAAA,EAAK,CAAE,EACpB,CAACA,GAAW,CAACP,GAAcK,CAAI,EAC/BC,EAAOD,MAGP,WAAWI,KAAOJ,EACVA,EAAK,eAAeI,CAAG,IACvBH,EAAKG,CAAG,EAAIL,EAAYC,EAAKI,CAAG,CAAC,OAM7C,QAAOJ,EAEX,OAAOC,CACX,CAEA,IAAII,GAAWpB,GAAU,MAAM,QAAQA,CAAK,EAAIA,EAAM,OAAO,OAAO,EAAI,CAAE,EAEtEqB,EAAeC,GAAQA,IAAQ,OAE/BC,EAAM,CAACC,EAAQC,EAAMC,IAAiB,CACtC,GAAI,CAACD,GAAQ,CAACtB,EAASqB,CAAM,EACzB,OAAOE,EAEX,MAAMC,EAASP,GAAQK,EAAK,MAAM,WAAW,CAAC,EAAE,OAAO,CAACE,EAAQR,IAAQlB,EAAkB0B,CAAM,EAAIA,EAASA,EAAOR,CAAG,EAAGK,CAAM,EAChI,OAAOH,EAAYM,CAAM,GAAKA,IAAWH,EACnCH,EAAYG,EAAOC,CAAI,CAAC,EACpBC,EACAF,EAAOC,CAAI,EACfE,CACV,EAEIC,EAAa5B,GAAU,OAAOA,GAAU,UAExC6B,GAAS7B,GAAU,QAAQ,KAAKA,CAAK,EAErC8B,GAAgBC,GAAUX,GAAQW,EAAM,QAAQ,YAAa,EAAE,EAAE,MAAM,OAAO,CAAC,EAE/EC,EAAM,CAACR,EAAQC,EAAMzB,IAAU,CAC/B,IAAIiC,EAAQ,GACZ,MAAMC,EAAWL,GAAMJ,CAAI,EAAI,CAACA,CAAI,EAAIK,GAAaL,CAAI,EACnDU,EAASD,EAAS,OAClBE,EAAYD,EAAS,EAC3B,KAAO,EAAEF,EAAQE,GAAQ,CACrB,MAAMhB,EAAMe,EAASD,CAAK,EAC1B,IAAII,EAAWrC,EACf,GAAIiC,IAAUG,EAAW,CACrB,MAAME,EAAWd,EAAOL,CAAG,EAC3BkB,EACIlC,EAASmC,CAAQ,GAAK,MAAM,QAAQA,CAAQ,EACtCA,EACC,MAAM,CAACJ,EAASD,EAAQ,CAAC,CAAC,EAEvB,CAAE,EADF,CAAA,CAE1B,CACQ,GAAId,IAAQ,aAAeA,IAAQ,eAAiBA,IAAQ,YACxD,OAEJK,EAAOL,CAAG,EAAIkB,EACdb,EAASA,EAAOL,CAAG,CAC3B,CACA,EAEA,MAAMoB,GAAS,CACX,KAAM,OACN,UAAW,WACX,OAAQ,QACZ,EACMC,EAAkB,CACpB,OAAQ,SACR,SAAU,WACV,SAAU,WACV,UAAW,YACX,IAAK,KACT,EACMC,EAAyB,CAC3B,IAAK,MACL,IAAK,MACL,UAAW,YACX,UAAW,YACX,QAAS,UACT,SAAU,WACV,SAAU,UACd,EAEMC,GAAkBC,EAAe,cAAc,IAAI,EA+BnDC,GAAiB,IAAMD,EAAe,WAAWD,EAAe,EAoCtE,IAAIG,GAAoB,CAACC,EAAWC,EAASC,EAAqBC,EAAS,KAAS,CAChF,MAAMtB,EAAS,CACX,cAAeoB,EAAQ,cAC1B,EACD,UAAW5B,KAAO2B,EACd,OAAO,eAAenB,EAAQR,EAAK,CAC/B,IAAK,IAAM,CACP,MAAM+B,EAAO/B,EACb,OAAI4B,EAAQ,gBAAgBG,CAAI,IAAMV,EAAgB,MAClDO,EAAQ,gBAAgBG,CAAI,EAAI,CAACD,GAAUT,EAAgB,KAE/DQ,IAAwBA,EAAoBE,CAAI,EAAI,IAC7CJ,EAAUI,CAAI,CACxB,CACb,CAAS,EAEL,OAAOvB,CACX,EAEA,MAAMwB,GAA4B,OAAO,OAAW,IAAcC,GAAqB,gBAAGC,GAAe,UAgCzG,SAASC,GAAavF,EAAO,CACzB,MAAMwF,EAAUX,GAAgB,EAC1B,CAAE,QAAAG,EAAUQ,EAAQ,QAAS,SAAAC,EAAU,KAAAjD,EAAM,MAAAkD,GAAU1F,GAAS,CAAE,EAClE,CAAC+E,EAAWY,CAAe,EAAIf,EAAe,SAASI,EAAQ,UAAU,EACzEY,EAAuBhB,EAAe,OAAO,CAC/C,QAAS,GACT,UAAW,GACX,YAAa,GACb,cAAe,GACf,iBAAkB,GAClB,aAAc,GACd,QAAS,GACT,OAAQ,EAChB,CAAK,EACD,OAAAQ,GAA0B,IAAMJ,EAAQ,WAAW,CAC/C,KAAAxC,EACA,UAAWoD,EAAqB,QAChC,MAAAF,EACA,SAAWX,GAAc,CACrB,CAACU,GACGE,EAAgB,CACZ,GAAGX,EAAQ,WACX,GAAGD,CACvB,CAAiB,CACR,CACJ,CAAA,EAAG,CAACvC,EAAMiD,EAAUC,CAAK,CAAC,EAC3Bd,EAAe,UAAU,IAAM,CAC3BgB,EAAqB,QAAQ,SAAWZ,EAAQ,UAAU,EAAI,CACtE,EAAO,CAACA,CAAO,CAAC,EACLJ,EAAe,QAAQ,IAAME,GAAkBC,EAAWC,EAASY,EAAqB,QAAS,EAAK,EAAG,CAACb,EAAWC,CAAO,CAAC,CACxI,CAEA,IAAIa,EAAY5D,GAAU,OAAOA,GAAU,SAEvC6D,GAAsB,CAACpD,EAAOqD,EAAQC,EAAYC,EAAUtC,IACxDkC,EAASnD,CAAK,GACduD,GAAYF,EAAO,MAAM,IAAIrD,CAAK,EAC3Bc,EAAIwC,EAAYtD,EAAOiB,CAAY,GAE1C,MAAM,QAAQjB,CAAK,EACZA,EAAM,IAAKwD,IAAeD,GAAYF,EAAO,MAAM,IAAIG,CAAS,EAAG1C,EAAIwC,EAAYE,CAAS,EAAE,GAEzGD,IAAaF,EAAO,SAAW,IACxBC,GAmBX,SAASG,GAASnG,EAAO,CACrB,MAAMwF,EAAUX,GAAgB,EAC1B,CAAE,QAAAG,EAAUQ,EAAQ,QAAS,KAAAhD,EAAM,aAAAmB,EAAc,SAAA8B,EAAU,MAAAC,GAAW1F,GAAS,CAAE,EACjFoG,EAAgBxB,EAAe,OAAOjB,CAAY,EAClD,CAAC1B,EAAOoE,CAAW,EAAIzB,EAAe,SAASI,EAAQ,UAAUxC,EAAM4D,EAAc,OAAO,CAAC,EACnG,OAAAhB,GAA0B,IAAMJ,EAAQ,WAAW,CAC/C,KAAAxC,EACA,UAAW,CACP,OAAQ,EACX,EACD,MAAAkD,EACA,SAAWX,GAAc,CAACU,GACtBY,EAAYP,GAAoBtD,EAAMwC,EAAQ,OAAQD,EAAU,QAAUC,EAAQ,YAAa,GAAOoB,EAAc,OAAO,CAAC,CACnI,CAAA,EAAG,CAAC5D,EAAMwC,EAASS,EAAUC,CAAK,CAAC,EACpCd,EAAe,UAAU,IAAMI,EAAQ,iBAAgB,CAAE,EAClD/C,CACX,CA0BA,SAASqE,GAActG,EAAO,CAC1B,MAAMwF,EAAUX,GAAgB,EAC1B,CAAE,KAAArC,EAAM,SAAAiD,EAAU,QAAAT,EAAUQ,EAAQ,QAAS,iBAAAe,CAAgB,EAAKvG,EAClEwG,EAAe/D,GAAmBuC,EAAQ,OAAO,MAAOxC,CAAI,EAC5DP,EAAQkE,GAAS,CACnB,QAAAnB,EACA,KAAAxC,EACA,aAAcgB,EAAIwB,EAAQ,YAAaxC,EAAMgB,EAAIwB,EAAQ,eAAgBxC,EAAMxC,EAAM,YAAY,CAAC,EAClG,MAAO,EACf,CAAK,EACK+E,EAAYQ,GAAa,CAC3B,QAAAP,EACA,KAAAxC,EACA,MAAO,EACf,CAAK,EACKiE,EAAS7B,EAAe,OAAO5E,CAAK,EACpC0G,EAAiB9B,EAAe,OAAOI,EAAQ,SAASxC,EAAM,CAChE,GAAGxC,EAAM,MACT,MAAAiC,EACA,GAAI4B,EAAU7D,EAAM,QAAQ,EAAI,CAAE,SAAUA,EAAM,QAAU,EAAG,EACvE,CAAK,CAAC,EACI2G,EAAa/B,EAAe,QAAQ,IAAM,OAAO,iBAAiB,GAAI,CACxE,QAAS,CACL,WAAY,GACZ,IAAK,IAAM,CAAC,CAACpB,EAAIuB,EAAU,OAAQvC,CAAI,CAC1C,EACD,QAAS,CACL,WAAY,GACZ,IAAK,IAAM,CAAC,CAACgB,EAAIuB,EAAU,YAAavC,CAAI,CAC/C,EACD,UAAW,CACP,WAAY,GACZ,IAAK,IAAM,CAAC,CAACgB,EAAIuB,EAAU,cAAevC,CAAI,CACjD,EACD,aAAc,CACV,WAAY,GACZ,IAAK,IAAM,CAAC,CAACgB,EAAIuB,EAAU,iBAAkBvC,CAAI,CACpD,EACD,MAAO,CACH,WAAY,GACZ,IAAK,IAAMgB,EAAIuB,EAAU,OAAQvC,CAAI,CACxC,CACT,CAAK,EAAG,CAACuC,EAAWvC,CAAI,CAAC,EACfoE,EAAWhC,EAAe,YAAatC,GAAUoE,EAAe,QAAQ,SAAS,CACnF,OAAQ,CACJ,MAAOrE,GAAcC,CAAK,EAC1B,KAAME,CACT,EACD,KAAMgC,GAAO,MACrB,CAAK,EAAG,CAAChC,CAAI,CAAC,EACJqE,GAASjC,EAAe,YAAY,IAAM8B,EAAe,QAAQ,OAAO,CAC1E,OAAQ,CACJ,MAAOlD,EAAIwB,EAAQ,YAAaxC,CAAI,EACpC,KAAMA,CACT,EACD,KAAMgC,GAAO,IAChB,CAAA,EAAG,CAAChC,EAAMwC,EAAQ,WAAW,CAAC,EACzB8B,EAAMlC,EAAe,YAAamC,GAAQ,CAC5C,MAAMC,EAAQxD,EAAIwB,EAAQ,QAASxC,CAAI,EACnCwE,GAASD,IACTC,EAAM,GAAG,IAAM,CACX,MAAO,IAAMD,EAAI,OAASA,EAAI,MAAO,EACrC,OAAQ,IAAMA,EAAI,QAAUA,EAAI,OAAQ,EACxC,kBAAoBE,GAAYF,EAAI,kBAAkBE,CAAO,EAC7D,eAAgB,IAAMF,EAAI,eAAgB,CAC7C,EAER,EAAE,CAAC/B,EAAQ,QAASxC,CAAI,CAAC,EACpBwE,EAAQpC,EAAe,QAAQ,KAAO,CACxC,KAAApC,EACA,MAAAP,EACA,GAAI4B,EAAU4B,CAAQ,GAAKV,EAAU,SAC/B,CAAE,SAAUA,EAAU,UAAYU,CAAQ,EAC1C,GACN,SAAAmB,EACA,OAAAC,GACA,IAAAC,CACR,GAAQ,CAACtE,EAAMiD,EAAUV,EAAU,SAAU6B,EAAUC,GAAQC,EAAK7E,CAAK,CAAC,EACtE2C,OAAAA,EAAe,UAAU,IAAM,CAC3B,MAAMsC,EAAyBlC,EAAQ,SAAS,kBAAoBuB,EACpEvB,EAAQ,SAASxC,EAAM,CACnB,GAAGiE,EAAO,QAAQ,MAClB,GAAI5C,EAAU4C,EAAO,QAAQ,QAAQ,EAC/B,CAAE,SAAUA,EAAO,QAAQ,QAAQ,EACnC,EAClB,CAAS,EACD,MAAMU,EAAgB,CAAC3E,EAAMP,IAAU,CACnC,MAAM+E,EAAQxD,EAAIwB,EAAQ,QAASxC,CAAI,EACnCwE,GAASA,EAAM,KACfA,EAAM,GAAG,MAAQ/E,EAExB,EAED,GADAkF,EAAc3E,EAAM,EAAI,EACpB0E,EAAwB,CACxB,MAAMjF,EAAQc,EAAYS,EAAIwB,EAAQ,SAAS,cAAexC,CAAI,CAAC,EACnEyB,EAAIe,EAAQ,eAAgBxC,EAAMP,CAAK,EACnCqB,EAAYE,EAAIwB,EAAQ,YAAaxC,CAAI,CAAC,GAC1CyB,EAAIe,EAAQ,YAAaxC,EAAMP,CAAK,CAEpD,CACQ,OAACuE,GAAgBxB,EAAQ,SAASxC,CAAI,EAC/B,IAAM,EACRgE,EACKU,GAA0B,CAAClC,EAAQ,OAAO,OAC1CkC,GACAlC,EAAQ,WAAWxC,CAAI,EACvB2E,EAAc3E,EAAM,EAAK,CAClC,CACJ,EAAE,CAACA,EAAMwC,EAASwB,EAAcD,CAAgB,CAAC,EAClD3B,EAAe,UAAU,IAAM,CAC3BI,EAAQ,kBAAkB,CACtB,SAAAS,EACA,KAAAjD,CACZ,CAAS,CACJ,EAAE,CAACiD,EAAUjD,EAAMwC,CAAO,CAAC,EACrBJ,EAAe,QAAQ,KAAO,CACjC,MAAAoC,EACA,UAAAjC,EACA,WAAA4B,CACH,GAAG,CAACK,EAAOjC,EAAW4B,CAAU,CAAC,CACtC,CA4CK,MAACS,GAAcpH,GAAUA,EAAM,OAAOsG,GAActG,CAAK,CAAC,EAqH/D,IAAIqH,GAAe,CAAC7E,EAAM8E,EAA0BC,EAAQhH,EAAM0G,IAAYK,EACxE,CACE,GAAGC,EAAO/E,CAAI,EACd,MAAO,CACH,GAAI+E,EAAO/E,CAAI,GAAK+E,EAAO/E,CAAI,EAAE,MAAQ+E,EAAO/E,CAAI,EAAE,MAAQ,CAAA,EAC9D,CAACjC,CAAI,EAAG0G,GAAW,EACtB,CACT,EACM,CAAE,EAEJO,GAAyBvF,GAAW,MAAM,QAAQA,CAAK,EAAIA,EAAQ,CAACA,CAAK,EAEzEwF,GAAgB,IAAM,CACtB,IAAIC,EAAa,CAAE,EAiBnB,MAAO,CACH,IAAI,WAAY,CACZ,OAAOA,CACV,EACD,KApBUzF,GAAU,CACpB,UAAW0F,KAAYD,EACnBC,EAAS,MAAQA,EAAS,KAAK1F,CAAK,CAE3C,EAiBG,UAhBe0F,IACfD,EAAW,KAAKC,CAAQ,EACjB,CACH,YAAa,IAAM,CACfD,EAAaA,EAAW,OAAQE,GAAMA,IAAMD,CAAQ,CACvD,CACJ,GAWD,YATgB,IAAM,CACtBD,EAAa,CAAE,CAClB,CAQA,CACL,EAEIG,GAAe5F,GAAUC,EAAkBD,CAAK,GAAK,CAACE,GAAaF,CAAK,EAE5E,SAAS6F,GAAUC,EAASC,EAAS,CACjC,GAAIH,GAAYE,CAAO,GAAKF,GAAYG,CAAO,EAC3C,OAAOD,IAAYC,EAEvB,GAAIhG,GAAa+F,CAAO,GAAK/F,GAAagG,CAAO,EAC7C,OAAOD,EAAQ,YAAcC,EAAQ,QAAS,EAElD,MAAMC,EAAQ,OAAO,KAAKF,CAAO,EAC3BG,EAAQ,OAAO,KAAKF,CAAO,EACjC,GAAIC,EAAM,SAAWC,EAAM,OACvB,MAAO,GAEX,UAAW9E,KAAO6E,EAAO,CACrB,MAAME,EAAOJ,EAAQ3E,CAAG,EACxB,GAAI,CAAC8E,EAAM,SAAS9E,CAAG,EACnB,MAAO,GAEX,GAAIA,IAAQ,MAAO,CACf,MAAMgF,EAAOJ,EAAQ5E,CAAG,EACxB,GAAKpB,GAAamG,CAAI,GAAKnG,GAAaoG,CAAI,GACvChG,EAAS+F,CAAI,GAAK/F,EAASgG,CAAI,GAC/B,MAAM,QAAQD,CAAI,GAAK,MAAM,QAAQC,CAAI,EACxC,CAACN,GAAUK,EAAMC,CAAI,EACrBD,IAASC,EACX,MAAO,EAEvB,CACA,CACI,MAAO,EACX,CAEA,IAAIC,EAAiBpG,GAAUG,EAASH,CAAK,GAAK,CAAC,OAAO,KAAKA,CAAK,EAAE,OAElEqG,GAAevG,GAAYA,EAAQ,OAAS,OAE5CwG,EAActG,GAAU,OAAOA,GAAU,WAEzCuG,GAAiBvG,GAAU,CAC3B,GAAI,CAACa,GACD,MAAO,GAEX,MAAM2F,EAAQxG,EAAQA,EAAM,cAAgB,EAC5C,OAAQA,aACHwG,GAASA,EAAM,YAAcA,EAAM,YAAY,YAAc,YACtE,EAEIC,GAAoB3G,GAAYA,EAAQ,OAAS,kBAEjD4G,GAAgB5G,GAAYA,EAAQ,OAAS,QAE7C6G,GAAqB9B,GAAQ6B,GAAa7B,CAAG,GAAKhF,GAAgBgF,CAAG,EAErE+B,GAAQ/B,GAAQ0B,GAAc1B,CAAG,GAAKA,EAAI,YAE9C,SAASgC,GAAQrF,EAAQsF,EAAY,CACjC,MAAM3E,EAAS2E,EAAW,MAAM,EAAG,EAAE,EAAE,OACvC,IAAI7E,EAAQ,EACZ,KAAOA,EAAQE,GACXX,EAASH,EAAYG,CAAM,EAAIS,IAAUT,EAAOsF,EAAW7E,GAAO,CAAC,EAEvE,OAAOT,CACX,CACA,SAASuF,GAAaC,EAAK,CACvB,UAAW7F,KAAO6F,EACd,GAAIA,EAAI,eAAe7F,CAAG,GAAK,CAACE,EAAY2F,EAAI7F,CAAG,CAAC,EAChD,MAAO,GAGf,MAAO,EACX,CACA,SAAS8F,EAAMzF,EAAQC,EAAM,CACzB,MAAMyF,EAAQ,MAAM,QAAQzF,CAAI,EAC1BA,EACAI,GAAMJ,CAAI,EACN,CAACA,CAAI,EACLK,GAAaL,CAAI,EACrB0F,EAAcD,EAAM,SAAW,EAAI1F,EAASqF,GAAQrF,EAAQ0F,CAAK,EACjEjF,EAAQiF,EAAM,OAAS,EACvB/F,EAAM+F,EAAMjF,CAAK,EACvB,OAAIkF,GACA,OAAOA,EAAYhG,CAAG,EAEtBc,IAAU,IACR9B,EAASgH,CAAW,GAAKf,EAAce,CAAW,GAC/C,MAAM,QAAQA,CAAW,GAAKJ,GAAaI,CAAW,IAC3DF,EAAMzF,EAAQ0F,EAAM,MAAM,EAAG,EAAE,CAAC,EAE7B1F,CACX,CAEA,IAAI4F,GAAqBrG,GAAS,CAC9B,UAAWI,KAAOJ,EACd,GAAIuF,EAAWvF,EAAKI,CAAG,CAAC,EACpB,MAAO,GAGf,MAAO,EACX,EAEA,SAASkG,GAAgBtG,EAAMuG,EAAS,GAAI,CACxC,MAAMC,EAAoB,MAAM,QAAQxG,CAAI,EAC5C,GAAIZ,EAASY,CAAI,GAAKwG,EAClB,UAAWpG,KAAOJ,EACV,MAAM,QAAQA,EAAKI,CAAG,CAAC,GACtBhB,EAASY,EAAKI,CAAG,CAAC,GAAK,CAACiG,GAAkBrG,EAAKI,CAAG,CAAC,GACpDmG,EAAOnG,CAAG,EAAI,MAAM,QAAQJ,EAAKI,CAAG,CAAC,EAAI,CAAA,EAAK,CAAE,EAChDkG,GAAgBtG,EAAKI,CAAG,EAAGmG,EAAOnG,CAAG,CAAC,GAEhClB,EAAkBc,EAAKI,CAAG,CAAC,IACjCmG,EAAOnG,CAAG,EAAI,IAI1B,OAAOmG,CACX,CACA,SAASE,GAAgCzG,EAAMgD,EAAY0D,EAAuB,CAC9E,MAAMF,EAAoB,MAAM,QAAQxG,CAAI,EAC5C,GAAIZ,EAASY,CAAI,GAAKwG,EAClB,UAAWpG,KAAOJ,EACV,MAAM,QAAQA,EAAKI,CAAG,CAAC,GACtBhB,EAASY,EAAKI,CAAG,CAAC,GAAK,CAACiG,GAAkBrG,EAAKI,CAAG,CAAC,EAChDE,EAAY0C,CAAU,GACtB6B,GAAY6B,EAAsBtG,CAAG,CAAC,EACtCsG,EAAsBtG,CAAG,EAAI,MAAM,QAAQJ,EAAKI,CAAG,CAAC,EAC9CkG,GAAgBtG,EAAKI,CAAG,EAAG,CAAE,CAAA,EAC7B,CAAE,GAAGkG,GAAgBtG,EAAKI,CAAG,CAAC,CAAG,EAGvCqG,GAAgCzG,EAAKI,CAAG,EAAGlB,EAAkB8D,CAAU,EAAI,CAAE,EAAGA,EAAW5C,CAAG,EAAGsG,EAAsBtG,CAAG,CAAC,EAI/HsG,EAAsBtG,CAAG,EAAI,CAAC0E,GAAU9E,EAAKI,CAAG,EAAG4C,EAAW5C,CAAG,CAAC,EAI9E,OAAOsG,CACX,CACA,IAAIC,GAAiB,CAACC,EAAe5D,IAAeyD,GAAgCG,EAAe5D,EAAYsD,GAAgBtD,CAAU,CAAC,EAE1I,MAAM6D,GAAgB,CAClB,MAAO,GACP,QAAS,EACb,EACMC,GAAc,CAAE,MAAO,GAAM,QAAS,EAAM,EAClD,IAAIC,GAAoBC,GAAY,CAChC,GAAI,MAAM,QAAQA,CAAO,EAAG,CACxB,GAAIA,EAAQ,OAAS,EAAG,CACpB,MAAMC,EAASD,EACV,OAAQE,GAAWA,GAAUA,EAAO,SAAW,CAACA,EAAO,QAAQ,EAC/D,IAAKA,GAAWA,EAAO,KAAK,EACjC,MAAO,CAAE,MAAOD,EAAQ,QAAS,CAAC,CAACA,EAAO,MAAQ,CAC9D,CACQ,OAAOD,EAAQ,CAAC,EAAE,SAAW,CAACA,EAAQ,CAAC,EAAE,SAEjCA,EAAQ,CAAC,EAAE,YAAc,CAAC1G,EAAY0G,EAAQ,CAAC,EAAE,WAAW,KAAK,EAC3D1G,EAAY0G,EAAQ,CAAC,EAAE,KAAK,GAAKA,EAAQ,CAAC,EAAE,QAAU,GAClDF,GACA,CAAE,MAAOE,EAAQ,CAAC,EAAE,MAAO,QAAS,EAAI,EAC5CF,GACRD,EACd,CACI,OAAOA,EACX,EAEIM,GAAkB,CAAClI,EAAO,CAAE,cAAAmI,EAAe,YAAAC,EAAa,WAAAC,CAAU,IAAOhH,EAAYrB,CAAK,EACxFA,EACAmI,EACInI,IAAU,GACN,IACAA,GACI,CAACA,EAEToI,GAAexE,EAAS5D,CAAK,EACzB,IAAI,KAAKA,CAAK,EACdqI,EACIA,EAAWrI,CAAK,EAChBA,EAElB,MAAMsI,GAAgB,CAClB,QAAS,GACT,MAAO,IACX,EACA,IAAIC,GAAiBR,GAAY,MAAM,QAAQA,CAAO,EAChDA,EAAQ,OAAO,CAACS,EAAUP,IAAWA,GAAUA,EAAO,SAAW,CAACA,EAAO,SACrE,CACE,QAAS,GACT,MAAOA,EAAO,KAC1B,EACUO,EAAUF,EAAa,EAC3BA,GAEN,SAASG,GAAcC,EAAI,CACvB,MAAM7D,EAAM6D,EAAG,IACf,OAAIrC,GAAYxB,CAAG,EACRA,EAAI,MAEX6B,GAAa7B,CAAG,EACT0D,GAAcG,EAAG,IAAI,EAAE,MAE9BjC,GAAiB5B,CAAG,EACb,CAAC,GAAGA,EAAI,eAAe,EAAE,IAAI,CAAC,CAAE,MAAA7E,CAAO,IAAKA,CAAK,EAExDH,GAAgBgF,CAAG,EACZiD,GAAiBY,EAAG,IAAI,EAAE,MAE9BR,GAAgB7G,EAAYwD,EAAI,KAAK,EAAI6D,EAAG,IAAI,MAAQ7D,EAAI,MAAO6D,CAAE,CAChF,CAEA,IAAIC,GAAqB,CAACC,EAAaC,EAASC,EAAcC,IAA8B,CACxF,MAAMzB,EAAS,CAAE,EACjB,UAAW/G,KAAQqI,EAAa,CAC5B,MAAM7D,EAAQxD,EAAIsH,EAAStI,CAAI,EAC/BwE,GAAS/C,EAAIsF,EAAQ/G,EAAMwE,EAAM,EAAE,CAC3C,CACI,MAAO,CACH,aAAA+D,EACA,MAAO,CAAC,GAAGF,CAAW,EACtB,OAAAtB,EACA,0BAAAyB,CACH,CACL,EAEIC,GAAWhJ,GAAUA,aAAiB,OAEtCiJ,GAAgBC,GAAS7H,EAAY6H,CAAI,EACvCA,EACAF,GAAQE,CAAI,EACRA,EAAK,OACL/I,EAAS+I,CAAI,EACTF,GAAQE,EAAK,KAAK,EACdA,EAAK,MAAM,OACXA,EAAK,MACTA,EAEVC,GAAsBC,IAAU,CAChC,WAAY,CAACA,GAAQA,IAAS5G,EAAgB,SAC9C,SAAU4G,IAAS5G,EAAgB,OACnC,WAAY4G,IAAS5G,EAAgB,SACrC,QAAS4G,IAAS5G,EAAgB,IAClC,UAAW4G,IAAS5G,EAAgB,SACxC,GAEA,MAAM6G,GAAiB,gBACvB,IAAIC,GAAwBC,GAAmB,CAAC,CAACA,GAC7C,CAAC,CAACA,EAAe,UACjB,CAAC,EAAGjD,EAAWiD,EAAe,QAAQ,GAClCA,EAAe,SAAS,YAAY,OAASF,IAC5ClJ,EAASoJ,EAAe,QAAQ,GAC7B,OAAO,OAAOA,EAAe,QAAQ,EAAE,KAAMC,GAAqBA,EAAiB,YAAY,OAASH,EAAc,GAE9HI,GAAiB1B,GAAYA,EAAQ,QACpCA,EAAQ,UACLA,EAAQ,KACRA,EAAQ,KACRA,EAAQ,WACRA,EAAQ,WACRA,EAAQ,SACRA,EAAQ,UAEZ2B,GAAY,CAACnJ,EAAMuD,EAAQ6F,IAAgB,CAACA,IAC3C7F,EAAO,UACJA,EAAO,MAAM,IAAIvD,CAAI,GACrB,CAAC,GAAGuD,EAAO,KAAK,EAAE,KAAM8F,GAAcrJ,EAAK,WAAWqJ,CAAS,GAC3D,SAAS,KAAKrJ,EAAK,MAAMqJ,EAAU,MAAM,CAAC,CAAC,GAEvD,MAAMC,GAAwB,CAACvC,EAAQwC,EAAQlB,EAAamB,IAAe,CACvE,UAAW5I,KAAOyH,GAAe,OAAO,KAAKtB,CAAM,EAAG,CAClD,MAAMvC,EAAQxD,EAAI+F,EAAQnG,CAAG,EAC7B,GAAI4D,EAAO,CACP,KAAM,CAAE,GAAA2D,EAAI,GAAGsB,CAAY,EAAKjF,EAChC,GAAI2D,EAAI,CACJ,GAAIA,EAAG,MAAQA,EAAG,KAAK,CAAC,GAAKoB,EAAOpB,EAAG,KAAK,CAAC,EAAGvH,CAAG,GAAK,CAAC4I,EACrD,MAAO,GAEN,GAAIrB,EAAG,KAAOoB,EAAOpB,EAAG,IAAKA,EAAG,IAAI,GAAK,CAACqB,EAC3C,MAAO,GAGP,GAAIF,GAAsBG,EAAcF,CAAM,EAC1C,KAGxB,SACqB3J,EAAS6J,CAAY,GACtBH,GAAsBG,EAAcF,CAAM,EAC1C,KAGpB,CACA,CAEA,EAEA,SAASG,GAAkB3E,EAAQuD,EAAStI,EAAM,CAC9C,MAAM2J,EAAQ3I,EAAI+D,EAAQ/E,CAAI,EAC9B,GAAI2J,GAASrI,GAAMtB,CAAI,EACnB,MAAO,CACH,MAAA2J,EACA,KAAA3J,CACH,EAEL,MAAME,EAAQF,EAAK,MAAM,GAAG,EAC5B,KAAOE,EAAM,QAAQ,CACjB,MAAMwD,EAAYxD,EAAM,KAAK,GAAG,EAC1BsE,EAAQxD,EAAIsH,EAAS5E,CAAS,EAC9BkG,EAAa5I,EAAI+D,EAAQrB,CAAS,EACxC,GAAIc,GAAS,CAAC,MAAM,QAAQA,CAAK,GAAKxE,IAAS0D,EAC3C,MAAO,CAAE,KAAA1D,CAAM,EAEnB,GAAI4J,GAAcA,EAAW,KACzB,MAAO,CACH,KAAMlG,EACN,MAAOkG,CACV,EAEL,GAAIA,GAAcA,EAAW,MAAQA,EAAW,KAAK,KACjD,MAAO,CACH,KAAM,GAAGlG,CAAS,QAClB,MAAOkG,EAAW,IACrB,EAEL1J,EAAM,IAAK,CACnB,CACI,MAAO,CACH,KAAAF,CACH,CACL,CAEA,IAAI6J,GAAwB,CAACC,EAAeC,EAAiB5G,EAAiBT,IAAW,CACrFS,EAAgB2G,CAAa,EAC7B,KAAM,CAAE,KAAA9J,EAAM,GAAGuC,CAAS,EAAKuH,EAC/B,OAAQjE,EAActD,CAAS,GAC3B,OAAO,KAAKA,CAAS,EAAE,QAAU,OAAO,KAAKwH,CAAe,EAAE,QAC9D,OAAO,KAAKxH,CAAS,EAAE,KAAM3B,GAAQmJ,EAAgBnJ,CAAG,KACnD,CAAC8B,GAAUT,EAAgB,IAAI,CAC5C,EAEI+H,GAAwB,CAAChK,EAAMiK,EAAY/G,IAAU,CAAClD,GACtD,CAACiK,GACDjK,IAASiK,GACTjF,GAAsBhF,CAAI,EAAE,KAAMkK,GAAgBA,IAC7ChH,EACKgH,IAAgBD,EAChBC,EAAY,WAAWD,CAAU,GAC/BA,EAAW,WAAWC,CAAW,EAAE,EAE/CC,GAAiB,CAACf,EAAagB,EAAWC,EAAaC,EAAgBzB,IACnEA,EAAK,QACE,GAEF,CAACwB,GAAexB,EAAK,UACnB,EAAEuB,GAAahB,IAEjBiB,EAAcC,EAAe,SAAWzB,EAAK,UAC3C,CAACO,GAEHiB,EAAcC,EAAe,WAAazB,EAAK,YAC7CO,EAEJ,GAGPmB,GAAkB,CAACjG,EAAKtE,IAAS,CAACa,GAAQG,EAAIsD,EAAKtE,CAAI,CAAC,EAAE,QAAU0G,EAAMpC,EAAKtE,CAAI,EAEnFwK,GAA4B,CAACzF,EAAQ4E,EAAO3J,IAAS,CACrD,MAAMyK,EAAmBzF,GAAsBhE,EAAI+D,EAAQ/E,CAAI,CAAC,EAChE,OAAAyB,EAAIgJ,EAAkB,OAAQd,EAAM3J,CAAI,CAAC,EACzCyB,EAAIsD,EAAQ/E,EAAMyK,CAAgB,EAC3B1F,CACX,EAEI2F,GAAajL,GAAU4D,EAAS5D,CAAK,EAEzC,SAASkL,GAAiBvJ,EAAQkD,EAAKvG,EAAO,WAAY,CACtD,GAAI2M,GAAUtJ,CAAM,GACf,MAAM,QAAQA,CAAM,GAAKA,EAAO,MAAMsJ,EAAS,GAC/CrJ,EAAUD,CAAM,GAAK,CAACA,EACvB,MAAO,CACH,KAAArD,EACA,QAAS2M,GAAUtJ,CAAM,EAAIA,EAAS,GACtC,IAAAkD,CACH,CAET,CAEA,IAAIsG,GAAsBC,GAAmBjL,EAASiL,CAAc,GAAK,CAACpC,GAAQoC,CAAc,EAC1FA,EACA,CACE,MAAOA,EACP,QAAS,EACZ,EAEDC,GAAgB,MAAOtG,EAAOuG,EAAoBvH,EAAYsB,EAA0B0D,EAA2BwC,IAAiB,CACpI,KAAM,CAAE,IAAA1G,EAAK,KAAA2G,EAAM,SAAAC,EAAU,UAAAC,EAAW,UAAAC,EAAW,IAAAC,EAAK,IAAAC,EAAK,QAAAC,GAAS,SAAAC,EAAU,KAAAxL,EAAM,cAAA4H,EAAe,MAAA6D,CAAK,EAAMjH,EAAM,GAChHkH,EAAa1K,EAAIwC,EAAYxD,CAAI,EACvC,GAAI,CAACyL,GAASV,EAAmB,IAAI/K,CAAI,EACrC,MAAO,CAAE,EAEb,MAAM2L,EAAWV,EAAOA,EAAK,CAAC,EAAI3G,EAC5BsH,EAAqBnH,GAAY,CAC/B+D,GAA6BmD,EAAS,iBACtCA,EAAS,kBAAkBtK,EAAUoD,CAAO,EAAI,GAAKA,GAAW,EAAE,EAClEkH,EAAS,eAAgB,EAEhC,EACKhC,EAAQ,CAAE,EACVkC,GAAU1F,GAAa7B,CAAG,EAC1BwH,GAAaxM,GAAgBgF,CAAG,EAChC8B,GAAoByF,IAAWC,GAC/BC,GAAYnE,GAAiB9B,GAAYxB,CAAG,IAC9CxD,EAAYwD,EAAI,KAAK,GACrBxD,EAAY4K,CAAU,GACrB1F,GAAc1B,CAAG,GAAKA,EAAI,QAAU,IACrCoH,IAAe,IACd,MAAM,QAAQA,CAAU,GAAK,CAACA,EAAW,OACxCM,GAAoBnH,GAAa,KAAK,KAAM7E,EAAM8E,EAA0B6E,CAAK,EACjFsC,EAAmB,CAACC,EAAWC,EAAkBC,EAAkBC,EAAUnK,EAAuB,UAAWoK,EAAUpK,EAAuB,YAAc,CAChK,MAAMuC,EAAUyH,EAAYC,EAAmBC,EAC/CzC,EAAM3J,CAAI,EAAI,CACV,KAAMkM,EAAYG,EAAUC,EAC5B,QAAA7H,EACA,IAAAH,EACA,GAAG0H,GAAkBE,EAAYG,EAAUC,EAAS7H,CAAO,CAC9D,CACJ,EACD,GAAIuG,EACE,CAAC,MAAM,QAAQU,CAAU,GAAK,CAACA,EAAW,OAC1CR,IACI,CAAC9E,KAAsB2F,GAAWrM,EAAkBgM,CAAU,IAC3DrK,EAAUqK,CAAU,GAAK,CAACA,GAC1BI,IAAc,CAACvE,GAAiB0D,CAAI,EAAE,SACtCY,IAAW,CAAC7D,GAAciD,CAAI,EAAE,SAAW,CACpD,KAAM,CAAE,MAAAxL,EAAO,QAAAgF,CAAS,EAAGiG,GAAUQ,CAAQ,EACvC,CAAE,MAAO,CAAC,CAACA,EAAU,QAASA,CAAQ,EACtCN,GAAmBM,CAAQ,EACjC,GAAIzL,IACAkK,EAAM3J,CAAI,EAAI,CACV,KAAMkC,EAAuB,SAC7B,QAAAuC,EACA,IAAKkH,EACL,GAAGK,GAAkB9J,EAAuB,SAAUuC,CAAO,CAChE,EACG,CAACK,GACD,OAAA8G,EAAkBnH,CAAO,EAClBkF,CAGvB,CACI,GAAI,CAACoC,IAAY,CAACrM,EAAkB2L,CAAG,GAAK,CAAC3L,EAAkB4L,CAAG,GAAI,CAClE,IAAIY,EACAK,EACJ,MAAMC,EAAY5B,GAAmBU,CAAG,EAClCmB,EAAY7B,GAAmBS,CAAG,EACxC,GAAI,CAAC3L,EAAkBgM,CAAU,GAAK,CAAC,MAAMA,CAAU,EAAG,CACtD,MAAMgB,EAAcpI,EAAI,eACnBoH,GAAa,CAACA,EACdhM,EAAkB8M,EAAU,KAAK,IAClCN,EAAYQ,EAAcF,EAAU,OAEnC9M,EAAkB+M,EAAU,KAAK,IAClCF,EAAYG,EAAcD,EAAU,MAEpD,KACa,CACD,MAAME,EAAYrI,EAAI,aAAe,IAAI,KAAKoH,CAAU,EAClDkB,EAAqBC,IAAS,IAAI,KAAK,IAAI,KAAI,EAAG,aAAY,EAAK,IAAMA,EAAI,EAC7EC,GAASxI,EAAI,MAAQ,OACrByI,GAASzI,EAAI,MAAQ,OACvBjB,EAASmJ,EAAU,KAAK,GAAKd,IAC7BQ,EAAYY,GACNF,EAAkBlB,CAAU,EAAIkB,EAAkBJ,EAAU,KAAK,EACjEO,GACIrB,EAAac,EAAU,MACvBG,EAAY,IAAI,KAAKH,EAAU,KAAK,GAE9CnJ,EAASoJ,EAAU,KAAK,GAAKf,IAC7Ba,EAAYO,GACNF,EAAkBlB,CAAU,EAAIkB,EAAkBH,EAAU,KAAK,EACjEM,GACIrB,EAAae,EAAU,MACvBE,EAAY,IAAI,KAAKF,EAAU,KAAK,EAE9D,CACQ,IAAIP,GAAaK,KACbN,EAAiB,CAAC,CAACC,EAAWM,EAAU,QAASC,EAAU,QAASvK,EAAuB,IAAKA,EAAuB,GAAG,EACtH,CAAC4C,GACD,OAAA8G,EAAkBjC,EAAM3J,CAAI,EAAE,OAAO,EAC9B2J,CAGvB,CACI,IAAKwB,GAAaC,IACd,CAACW,IACA1I,EAASqI,CAAU,GAAMV,GAAgB,MAAM,QAAQU,CAAU,GAAK,CACvE,MAAMsB,EAAkBpC,GAAmBO,CAAS,EAC9C8B,EAAkBrC,GAAmBQ,CAAS,EAC9Cc,EAAY,CAACxM,EAAkBsN,EAAgB,KAAK,GACtDtB,EAAW,OAAS,CAACsB,EAAgB,MACnCT,EAAY,CAAC7M,EAAkBuN,EAAgB,KAAK,GACtDvB,EAAW,OAAS,CAACuB,EAAgB,MACzC,IAAIf,GAAaK,KACbN,EAAiBC,EAAWc,EAAgB,QAASC,EAAgB,OAAO,EACxE,CAACnI,GACD,OAAA8G,EAAkBjC,EAAM3J,CAAI,EAAE,OAAO,EAC9B2J,CAGvB,CACI,GAAI4B,IAAW,CAACQ,GAAW1I,EAASqI,CAAU,EAAG,CAC7C,KAAM,CAAE,MAAOwB,EAAc,QAAAzI,CAAO,EAAKmG,GAAmBW,EAAO,EACnE,GAAI9C,GAAQyE,CAAY,GAAK,CAACxB,EAAW,MAAMwB,CAAY,IACvDvD,EAAM3J,CAAI,EAAI,CACV,KAAMkC,EAAuB,QAC7B,QAAAuC,EACA,IAAAH,EACA,GAAG0H,GAAkB9J,EAAuB,QAASuC,CAAO,CAC/D,EACG,CAACK,GACD,OAAA8G,EAAkBnH,CAAO,EAClBkF,CAGvB,CACI,GAAI6B,GACA,GAAIzF,EAAWyF,CAAQ,EAAG,CACtB,MAAMpK,EAAS,MAAMoK,EAASE,EAAYlI,CAAU,EAC9C2J,EAAgBxC,GAAiBvJ,EAAQuK,CAAQ,EACvD,GAAIwB,IACAxD,EAAM3J,CAAI,EAAI,CACV,GAAGmN,EACH,GAAGnB,GAAkB9J,EAAuB,SAAUiL,EAAc,OAAO,CAC9E,EACG,CAACrI,GACD,OAAA8G,EAAkBuB,EAAc,OAAO,EAChCxD,CAG3B,SACiB/J,EAAS4L,CAAQ,EAAG,CACzB,IAAI4B,EAAmB,CAAE,EACzB,UAAWxM,KAAO4K,EAAU,CACxB,GAAI,CAAC3F,EAAcuH,CAAgB,GAAK,CAACtI,EACrC,MAEJ,MAAMqI,EAAgBxC,GAAiB,MAAMa,EAAS5K,CAAG,EAAE8K,EAAYlI,CAAU,EAAGmI,EAAU/K,CAAG,EAC7FuM,IACAC,EAAmB,CACf,GAAGD,EACH,GAAGnB,GAAkBpL,EAAKuM,EAAc,OAAO,CAClD,EACDvB,EAAkBuB,EAAc,OAAO,EACnCrI,IACA6E,EAAM3J,CAAI,EAAIoN,GAGtC,CACY,GAAI,CAACvH,EAAcuH,CAAgB,IAC/BzD,EAAM3J,CAAI,EAAI,CACV,IAAK2L,EACL,GAAGyB,CACN,EACG,CAACtI,GACD,OAAO6E,CAG3B,EAEI,OAAAiC,EAAkB,EAAI,EACfjC,CACX,EAEA,MAAM0D,GAAiB,CACnB,KAAMpL,EAAgB,SACtB,eAAgBA,EAAgB,SAChC,iBAAkB,EACtB,EACA,SAASqL,GAAkB9P,EAAQ,GAAI,CACnC,IAAI+P,EAAW,CACX,GAAGF,GACH,GAAG7P,CACN,EACGgQ,EAAa,CACb,YAAa,EACb,QAAS,GACT,QAAS,GACT,UAAWzH,EAAWwH,EAAS,aAAa,EAC5C,aAAc,GACd,YAAa,GACb,aAAc,GACd,mBAAoB,GACpB,QAAS,GACT,cAAe,CAAE,EACjB,YAAa,CAAE,EACf,iBAAkB,CAAE,EACpB,OAAQA,EAAS,QAAU,CAAE,EAC7B,SAAUA,EAAS,UAAY,EAClC,EACD,MAAMjF,EAAU,CAAE,EAClB,IAAImF,EAAiB7N,EAAS2N,EAAS,aAAa,GAAK3N,EAAS2N,EAAS,MAAM,EAC3EhN,EAAYgN,EAAS,eAAiBA,EAAS,MAAM,GAAK,CAAA,EAC1D,CAAE,EACJG,EAAcH,EAAS,iBACrB,CAAA,EACAhN,EAAYkN,CAAc,EAC5BE,EAAS,CACT,OAAQ,GACR,MAAO,GACP,MAAO,EACV,EACGpK,EAAS,CACT,MAAO,IAAI,IACX,SAAU,IAAI,IACd,QAAS,IAAI,IACb,MAAO,IAAI,IACX,MAAO,IAAI,GACd,EACGqK,EACAC,EAAQ,EACZ,MAAM9D,EAAkB,CACpB,QAAS,GACT,YAAa,GACb,iBAAkB,GAClB,cAAe,GACf,aAAc,GACd,QAAS,GACT,OAAQ,EACX,EACD,IAAI+D,EAA2B,CAC3B,GAAG/D,CACN,EACD,MAAMgE,EAAY,CACd,MAAO9I,GAAe,EACtB,MAAOA,GAAe,CACzB,EACK+I,GAAmCT,EAAS,eAAiBtL,EAAgB,IAC7EgM,EAAYC,GAAcC,GAAS,CACrC,aAAaN,CAAK,EAClBA,EAAQ,WAAWK,EAAUC,CAAI,CACpC,EACKC,EAAY,MAAOC,GAAsB,CAC3C,GAAI,CAACd,EAAS,WACTxD,EAAgB,SACb+D,EAAyB,SACzBO,GAAoB,CACxB,MAAMC,EAAUf,EAAS,SACnB1H,GAAe,MAAM0I,GAAU,GAAI,MAAM,EACzC,MAAMC,EAAyBlG,EAAS,EAAI,EAC9CgG,IAAYd,EAAW,SACvBO,EAAU,MAAM,KAAK,CACjB,QAAAO,CACpB,CAAiB,CAEjB,CACK,EACKG,EAAsB,CAACvO,EAAOwO,IAAiB,CAC7C,CAACnB,EAAS,WACTxD,EAAgB,cACbA,EAAgB,kBAChB+D,EAAyB,cACzBA,EAAyB,qBAC5B5N,GAAS,MAAM,KAAKqD,EAAO,KAAK,GAAG,QAASvD,GAAS,CAC9CA,IACA0O,EACMjN,EAAI+L,EAAW,iBAAkBxN,EAAM0O,CAAY,EACnDhI,EAAM8G,EAAW,iBAAkBxN,CAAI,EAEjE,CAAa,EACD+N,EAAU,MAAM,KAAK,CACjB,iBAAkBP,EAAW,iBAC7B,aAAc,CAAC3H,EAAc2H,EAAW,gBAAgB,CACxE,CAAa,EAER,EACKmB,EAAiB,CAAC3O,EAAMyH,EAAS,CAAE,EAAEmH,EAAQC,EAAMC,EAAkB,GAAMC,EAA6B,KAAS,CACnH,GAAIF,GAAQD,GAAU,CAACrB,EAAS,SAAU,CAEtC,GADAI,EAAO,OAAS,GACZoB,GAA8B,MAAM,QAAQ/N,EAAIsH,EAAStI,CAAI,CAAC,EAAG,CACjE,MAAMgP,EAAcJ,EAAO5N,EAAIsH,EAAStI,CAAI,EAAG6O,EAAK,KAAMA,EAAK,IAAI,EACnEC,GAAmBrN,EAAI6G,EAAStI,EAAMgP,CAAW,CACjE,CACY,GAAID,GACA,MAAM,QAAQ/N,EAAIwM,EAAW,OAAQxN,CAAI,CAAC,EAAG,CAC7C,MAAM+E,EAAS6J,EAAO5N,EAAIwM,EAAW,OAAQxN,CAAI,EAAG6O,EAAK,KAAMA,EAAK,IAAI,EACxEC,GAAmBrN,EAAI+L,EAAW,OAAQxN,EAAM+E,CAAM,EACtDwF,GAAgBiD,EAAW,OAAQxN,CAAI,CACvD,CACY,IAAK+J,EAAgB,eACjB+D,EAAyB,gBACzBiB,GACA,MAAM,QAAQ/N,EAAIwM,EAAW,cAAexN,CAAI,CAAC,EAAG,CACpD,MAAMiP,EAAgBL,EAAO5N,EAAIwM,EAAW,cAAexN,CAAI,EAAG6O,EAAK,KAAMA,EAAK,IAAI,EACtFC,GAAmBrN,EAAI+L,EAAW,cAAexN,EAAMiP,CAAa,CACpF,EACgBlF,EAAgB,aAAe+D,EAAyB,eACxDN,EAAW,YAAcrG,GAAesG,EAAgBC,CAAW,GAEvEK,EAAU,MAAM,KAAK,CACjB,KAAA/N,EACA,QAASkP,EAAUlP,EAAMyH,CAAM,EAC/B,YAAa+F,EAAW,YACxB,OAAQA,EAAW,OACnB,QAASA,EAAW,OACpC,CAAa,CACb,MAEY/L,EAAIiM,EAAa1N,EAAMyH,CAAM,CAEpC,EACK0H,EAAe,CAACnP,EAAM2J,IAAU,CAClClI,EAAI+L,EAAW,OAAQxN,EAAM2J,CAAK,EAClCoE,EAAU,MAAM,KAAK,CACjB,OAAQP,EAAW,MAC/B,CAAS,CACJ,EACK4B,EAAcrK,GAAW,CAC3ByI,EAAW,OAASzI,EACpBgJ,EAAU,MAAM,KAAK,CACjB,OAAQP,EAAW,OACnB,QAAS,EACrB,CAAS,CACJ,EACK6B,EAAsB,CAACrP,EAAMsP,EAAsB7P,EAAO6E,IAAQ,CACpE,MAAME,EAAQxD,EAAIsH,EAAStI,CAAI,EAC/B,GAAIwE,EAAO,CACP,MAAMrD,EAAeH,EAAI0M,EAAa1N,EAAMc,EAAYrB,CAAK,EAAIuB,EAAIyM,EAAgBzN,CAAI,EAAIP,CAAK,EAClGqB,EAAYK,CAAY,GACnBmD,GAAOA,EAAI,gBACZgL,EACE7N,EAAIiM,EAAa1N,EAAMsP,EAAuBnO,EAAe+G,GAAc1D,EAAM,EAAE,CAAC,EACpF+K,EAAcvP,EAAMmB,CAAY,EACtCwM,EAAO,OAASS,EAAW,CACvC,CACK,EACKoB,EAAsB,CAACxP,EAAMyP,EAAYrG,EAAasG,EAAaC,IAAiB,CACtF,IAAIC,EAAoB,GACpBC,EAAkB,GACtB,MAAMC,EAAS,CACX,KAAA9P,CACH,EACD,GAAI,CAACuN,EAAS,SAAU,CACpB,GAAI,CAACnE,GAAesG,EAAa,EACzB3F,EAAgB,SAAW+D,EAAyB,WACpD+B,EAAkBrC,EAAW,QAC7BA,EAAW,QAAUsC,EAAO,QAAUZ,EAAW,EACjDU,EAAoBC,IAAoBC,EAAO,SAEnD,MAAMC,EAAyBzK,GAAUtE,EAAIyM,EAAgBzN,CAAI,EAAGyP,CAAU,EAC9EI,EAAkB,CAAC,CAAC7O,EAAIwM,EAAW,YAAaxN,CAAI,EACpD+P,EACMrJ,EAAM8G,EAAW,YAAaxN,CAAI,EAClCyB,EAAI+L,EAAW,YAAaxN,EAAM,EAAI,EAC5C8P,EAAO,YAActC,EAAW,YAChCoC,EACIA,IACM7F,EAAgB,aACd+D,EAAyB,cACzB+B,IAAoB,CAACE,CACjD,CACY,GAAI3G,EAAa,CACb,MAAM4G,EAAyBhP,EAAIwM,EAAW,cAAexN,CAAI,EAC5DgQ,IACDvO,EAAI+L,EAAW,cAAexN,EAAMoJ,CAAW,EAC/C0G,EAAO,cAAgBtC,EAAW,cAClCoC,EACIA,IACM7F,EAAgB,eACd+D,EAAyB,gBACzBkC,IAA2B5G,EAE3D,CACYwG,GAAqBD,GAAgB5B,EAAU,MAAM,KAAK+B,CAAM,CAC5E,CACQ,OAAOF,EAAoBE,EAAS,CAAE,CACzC,EACKG,GAAsB,CAACjQ,EAAMsO,EAAS3E,EAAOxF,IAAe,CAC9D,MAAM+L,EAAqBlP,EAAIwM,EAAW,OAAQxN,CAAI,EAChDqO,GAAqBtE,EAAgB,SAAW+D,EAAyB,UAC3EzM,EAAUiN,CAAO,GACjBd,EAAW,UAAYc,EAY3B,GAXIf,EAAS,YAAc5D,GACvBiE,EAAqBK,EAAS,IAAMkB,EAAanP,EAAM2J,CAAK,CAAC,EAC7DiE,EAAmBL,EAAS,UAAU,IAGtC,aAAaM,CAAK,EAClBD,EAAqB,KACrBjE,EACMlI,EAAI+L,EAAW,OAAQxN,EAAM2J,CAAK,EAClCjD,EAAM8G,EAAW,OAAQxN,CAAI,IAElC2J,EAAQ,CAACrE,GAAU4K,EAAoBvG,CAAK,EAAIuG,IACjD,CAACrK,EAAc1B,CAAU,GACzBkK,EAAmB,CACnB,MAAM8B,EAAmB,CACrB,GAAGhM,EACH,GAAIkK,GAAqBhN,EAAUiN,CAAO,EAAI,CAAE,QAAAA,CAAS,EAAG,GAC5D,OAAQd,EAAW,OACnB,KAAAxN,CACH,EACDwN,EAAa,CACT,GAAGA,EACH,GAAG2C,CACN,EACDpC,EAAU,MAAM,KAAKoC,CAAgB,CACjD,CACK,EACK5B,GAAa,MAAOvO,GAAS,CAC/ByO,EAAoBzO,EAAM,EAAI,EAC9B,MAAMoB,EAAS,MAAMmM,EAAS,SAASG,EAAaH,EAAS,QAASnF,GAAmBpI,GAAQuD,EAAO,MAAO+E,EAASiF,EAAS,aAAcA,EAAS,yBAAyB,CAAC,EAClL,OAAAkB,EAAoBzO,CAAI,EACjBoB,CACV,EACKgP,GAA8B,MAAOlQ,GAAU,CACjD,KAAM,CAAE,OAAA6E,CAAM,EAAK,MAAMwJ,GAAWrO,CAAK,EACzC,GAAIA,EACA,UAAWF,KAAQE,EAAO,CACtB,MAAMyJ,EAAQ3I,EAAI+D,EAAQ/E,CAAI,EAC9B2J,EACMlI,EAAI+L,EAAW,OAAQxN,EAAM2J,CAAK,EAClCjD,EAAM8G,EAAW,OAAQxN,CAAI,CACnD,MAGYwN,EAAW,OAASzI,EAExB,OAAOA,CACV,EACKyJ,EAA2B,MAAOzH,EAAQsJ,EAAsBC,EAAU,CAC5E,MAAO,EACf,IAAU,CACF,UAAWtQ,KAAQ+G,EAAQ,CACvB,MAAMvC,EAAQuC,EAAO/G,CAAI,EACzB,GAAIwE,EAAO,CACP,KAAM,CAAE,GAAA2D,EAAI,GAAGsH,CAAU,EAAKjL,EAC9B,GAAI2D,EAAI,CACJ,MAAMoI,EAAmBhN,EAAO,MAAM,IAAI4E,EAAG,IAAI,EAC3CqI,EAAoBhM,EAAM,IAAMuE,GAAqBvE,EAAM,EAAE,EAC/DgM,GAAqBzG,EAAgB,kBACrC0E,EAAoB,CAACzO,CAAI,EAAG,EAAI,EAEpC,MAAMyQ,EAAa,MAAM3F,GAActG,EAAOjB,EAAO,SAAUmK,EAAaM,GAAkCT,EAAS,2BAA6B,CAAC8C,EAAsBE,CAAgB,EAI3L,GAHIC,GAAqBzG,EAAgB,kBACrC0E,EAAoB,CAACzO,CAAI,CAAC,EAE1ByQ,EAAWtI,EAAG,IAAI,IAClBmI,EAAQ,MAAQ,GACZD,GACA,MAGR,CAACA,IACIrP,EAAIyP,EAAYtI,EAAG,IAAI,EAClBoI,EACI/F,GAA0BgD,EAAW,OAAQiD,EAAYtI,EAAG,IAAI,EAChE1G,EAAI+L,EAAW,OAAQrF,EAAG,KAAMsI,EAAWtI,EAAG,IAAI,CAAC,EACvDzB,EAAM8G,EAAW,OAAQrF,EAAG,IAAI,EAC9D,CACgB,CAACtC,EAAc4J,CAAU,GACpB,MAAMjB,EAAyBiB,EAAYY,EAAsBC,CAAO,CAC7F,CACA,CACQ,OAAOA,EAAQ,KAClB,EACKI,GAAmB,IAAM,CAC3B,UAAW1Q,KAAQuD,EAAO,QAAS,CAC/B,MAAMiB,EAAQxD,EAAIsH,EAAStI,CAAI,EAC/BwE,IACKA,EAAM,GAAG,KACJA,EAAM,GAAG,KAAK,MAAOF,GAAQ,CAAC+B,GAAK/B,CAAG,CAAC,EACvC,CAAC+B,GAAK7B,EAAM,GAAG,GAAG,IACxBmM,GAAW3Q,CAAI,CAC/B,CACQuD,EAAO,QAAU,IAAI,GACxB,EACK2L,EAAY,CAAClP,EAAMQ,IAAS,CAAC+M,EAAS,WACvCvN,GAAQQ,GAAQiB,EAAIiM,EAAa1N,EAAMQ,CAAI,EACxC,CAAC8E,GAAUsL,KAAanD,CAAc,GACxCoD,EAAY,CAAC3Q,EAAOiB,EAAcsC,IAAaH,GAAoBpD,EAAOqD,EAAQ,CACpF,GAAIoK,EAAO,MACLD,EACA5M,EAAYK,CAAY,EACpBsM,EACApK,EAASnD,CAAK,EACV,CAAE,CAACA,CAAK,EAAGiB,CAAY,EACvBA,CACtB,EAAOsC,EAAUtC,CAAY,EACnB2P,EAAkB9Q,GAASa,GAAQG,EAAI2M,EAAO,MAAQD,EAAcD,EAAgBzN,EAAMuN,EAAS,iBAAmBvM,EAAIyM,EAAgBzN,EAAM,CAAE,CAAA,EAAI,CAAA,CAAE,CAAC,EACzJuP,EAAgB,CAACvP,EAAMP,EAAO+H,EAAU,CAAA,IAAO,CACjD,MAAMhD,EAAQxD,EAAIsH,EAAStI,CAAI,EAC/B,IAAIyP,EAAahQ,EACjB,GAAI+E,EAAO,CACP,MAAMwE,EAAiBxE,EAAM,GACzBwE,IACA,CAACA,EAAe,UACZvH,EAAIiM,EAAa1N,EAAM2H,GAAgBlI,EAAOuJ,CAAc,CAAC,EACjEyG,EACIzJ,GAAcgD,EAAe,GAAG,GAAKtJ,EAAkBD,CAAK,EACtD,GACAA,EACNyG,GAAiB8C,EAAe,GAAG,EACnC,CAAC,GAAGA,EAAe,IAAI,OAAO,EAAE,QAAS+H,GAAeA,EAAU,SAAWtB,EAAW,SAASsB,EAAU,KAAK,CAAE,EAE7G/H,EAAe,KAChB1J,GAAgB0J,EAAe,GAAG,EAClCA,EAAe,KAAK,QAASgI,GAAgB,EACrC,CAACA,EAAY,gBAAkB,CAACA,EAAY,YACxC,MAAM,QAAQvB,CAAU,EACxBuB,EAAY,QAAU,CAAC,CAACvB,EAAW,KAAMjP,GAASA,IAASwQ,EAAY,KAAK,EAG5EA,EAAY,QACRvB,IAAeuB,EAAY,OAAS,CAAC,CAACvB,EAG9E,CAAyB,EAGDzG,EAAe,KAAK,QAASiI,GAAcA,EAAS,QAAUA,EAAS,QAAUxB,CAAW,EAG3F3J,GAAYkD,EAAe,GAAG,EACnCA,EAAe,IAAI,MAAQ,IAG3BA,EAAe,IAAI,MAAQyG,EACtBzG,EAAe,IAAI,MACpB+E,EAAU,MAAM,KAAK,CACjB,KAAA/N,EACA,OAAQO,EAAYmN,CAAW,CAC3D,CAAyB,GAIzB,EACSlG,EAAQ,aAAeA,EAAQ,cAC5BgI,EAAoBxP,EAAMyP,EAAYjI,EAAQ,YAAaA,EAAQ,YAAa,EAAI,EACxFA,EAAQ,gBAAkB0J,GAAQlR,CAAI,CACzC,EACKmR,EAAY,CAACnR,EAAMP,EAAO+H,IAAY,CACxC,UAAW4J,KAAY3R,EAAO,CAC1B,GAAI,CAACA,EAAM,eAAe2R,CAAQ,EAC9B,OAEJ,MAAM3B,EAAahQ,EAAM2R,CAAQ,EAC3B1N,EAAY1D,EAAO,IAAMoR,EACzB5M,EAAQxD,EAAIsH,EAAS5E,CAAS,GACnCH,EAAO,MAAM,IAAIvD,CAAI,GAClBJ,EAAS6P,CAAU,GAClBjL,GAAS,CAACA,EAAM,KACjB,CAAChF,GAAaiQ,CAAU,EACtB0B,EAAUzN,EAAW+L,EAAYjI,CAAO,EACxC+H,EAAc7L,EAAW+L,EAAYjI,CAAO,CAC9D,CACK,EACK6J,EAAW,CAACrR,EAAMP,EAAO+H,EAAU,CAAA,IAAO,CAC5C,MAAMhD,EAAQxD,EAAIsH,EAAStI,CAAI,EACzBgL,EAAezH,EAAO,MAAM,IAAIvD,CAAI,EACpCsR,EAAa/Q,EAAYd,CAAK,EACpCgC,EAAIiM,EAAa1N,EAAMsR,CAAU,EAC7BtG,GACA+C,EAAU,MAAM,KAAK,CACjB,KAAA/N,EACA,OAAQO,EAAYmN,CAAW,CAC/C,CAAa,GACI3D,EAAgB,SACjBA,EAAgB,aAChB+D,EAAyB,SACzBA,EAAyB,cACzBtG,EAAQ,aACRuG,EAAU,MAAM,KAAK,CACjB,KAAA/N,EACA,YAAamH,GAAesG,EAAgBC,CAAW,EACvD,QAASwB,EAAUlP,EAAMsR,CAAU,CACvD,CAAiB,GAIL9M,GAAS,CAACA,EAAM,IAAM,CAAC9E,EAAkB4R,CAAU,EAC7CH,EAAUnR,EAAMsR,EAAY9J,CAAO,EACnC+H,EAAcvP,EAAMsR,EAAY9J,CAAO,EAEjD2B,GAAUnJ,EAAMuD,CAAM,GAAKwK,EAAU,MAAM,KAAK,CAAE,GAAGP,EAAY,EACjEO,EAAU,MAAM,KAAK,CACjB,KAAMJ,EAAO,MAAQ3N,EAAO,OAC5B,OAAQO,EAAYmN,CAAW,CAC3C,CAAS,CACJ,EACKtJ,EAAW,MAAOtE,GAAU,CAC9B6N,EAAO,MAAQ,GACf,MAAM4D,EAASzR,EAAM,OACrB,IAAIE,EAAOuR,EAAO,KACdC,EAAsB,GAC1B,MAAMhN,EAAQxD,EAAIsH,EAAStI,CAAI,EACzByR,EAA8BhC,GAAe,CAC/C+B,EACI,OAAO,MAAM/B,CAAU,GAClBjQ,GAAaiQ,CAAU,GAAK,MAAMA,EAAW,QAAS,CAAA,GACvDnK,GAAUmK,EAAYzO,EAAI0M,EAAa1N,EAAMyP,CAAU,CAAC,CACnE,EACKiC,EAA6B9I,GAAmB2E,EAAS,IAAI,EAC7DoE,EAA4B/I,GAAmB2E,EAAS,cAAc,EAC5E,GAAI/I,EAAO,CACP,IAAImF,EACA2E,EACJ,MAAMmB,GAAa8B,EAAO,KACpBrJ,GAAc1D,EAAM,EAAE,EACtB3E,GAAcC,CAAK,EACnBsJ,GAActJ,EAAM,OAASkC,GAAO,MAAQlC,EAAM,OAASkC,GAAO,UAClE4P,GAAwB,CAAC1I,GAAc1E,EAAM,EAAE,GACjD,CAAC+I,EAAS,UACV,CAACvM,EAAIwM,EAAW,OAAQxN,CAAI,GAC5B,CAACwE,EAAM,GAAG,MACV2F,GAAef,GAAapI,EAAIwM,EAAW,cAAexN,CAAI,EAAGwN,EAAW,YAAamE,EAA2BD,CAA0B,EAC5IG,GAAU1I,GAAUnJ,EAAMuD,EAAQ6F,EAAW,EACnD3H,EAAIiM,EAAa1N,EAAMyP,EAAU,EAC7BrG,IACA5E,EAAM,GAAG,QAAUA,EAAM,GAAG,OAAO1E,CAAK,EACxC8N,GAAsBA,EAAmB,CAAC,GAErCpJ,EAAM,GAAG,UACdA,EAAM,GAAG,SAAS1E,CAAK,EAE3B,MAAMqE,GAAaqL,EAAoBxP,EAAMyP,GAAYrG,EAAW,EAC9DuG,GAAe,CAAC9J,EAAc1B,EAAU,GAAK0N,GAOnD,GANA,CAACzI,IACG2E,EAAU,MAAM,KAAK,CACjB,KAAA/N,EACA,KAAMF,EAAM,KACZ,OAAQS,EAAYmN,CAAW,CACnD,CAAiB,EACDkE,GACA,OAAI7H,EAAgB,SAAW+D,EAAyB,WAChDP,EAAS,OAAS,SACdnE,IACAgF,EAAW,EAGThF,IACNgF,EAAW,GAGXuB,IACJ5B,EAAU,MAAM,KAAK,CAAE,KAAA/N,EAAM,GAAI6R,GAAU,CAAA,EAAK1N,GAAa,EAGrE,GADA,CAACiF,IAAeyI,IAAW9D,EAAU,MAAM,KAAK,CAAE,GAAGP,EAAY,EAC7DD,EAAS,SAAU,CACnB,KAAM,CAAE,OAAAxI,EAAQ,EAAG,MAAMwJ,GAAW,CAACvO,CAAI,CAAC,EAE1C,GADAyR,EAA2BhC,EAAU,EACjC+B,EAAqB,CACrB,MAAMM,GAA4BpI,GAAkB8D,EAAW,OAAQlF,EAAStI,CAAI,EAC9E+R,GAAoBrI,GAAkB3E,GAAQuD,EAASwJ,GAA0B,MAAQ9R,CAAI,EACnG2J,EAAQoI,GAAkB,MAC1B/R,EAAO+R,GAAkB,KACzBzD,EAAUzI,EAAcd,EAAM,CAClD,CACA,MAEgB0J,EAAoB,CAACzO,CAAI,EAAG,EAAI,EAChC2J,GAAS,MAAMmB,GAActG,EAAOjB,EAAO,SAAUmK,EAAaM,GAAkCT,EAAS,yBAAyB,GAAGvN,CAAI,EAC7IyO,EAAoB,CAACzO,CAAI,CAAC,EAC1ByR,EAA2BhC,EAAU,EACjC+B,IACI7H,EACA2E,EAAU,IAELvE,EAAgB,SACrB+D,EAAyB,WACzBQ,EAAU,MAAME,EAAyBlG,EAAS,EAAI,IAI9DkJ,IACAhN,EAAM,GAAG,MACL0M,GAAQ1M,EAAM,GAAG,IAAI,EACzByL,GAAoBjQ,EAAMsO,EAAS3E,EAAOxF,EAAU,EAEpE,CACK,EACK6N,GAAc,CAAC1N,EAAK1D,IAAQ,CAC9B,GAAII,EAAIwM,EAAW,OAAQ5M,CAAG,GAAK0D,EAAI,MACnC,OAAAA,EAAI,MAAO,EACJ,CAGd,EACK4M,GAAU,MAAOlR,EAAMwH,EAAU,CAAA,IAAO,CAC1C,IAAI8G,EACAlB,EACJ,MAAM6E,EAAajN,GAAsBhF,CAAI,EAC7C,GAAIuN,EAAS,SAAU,CACnB,MAAMxI,EAAS,MAAMqL,GAA4BtP,EAAYd,CAAI,EAAIA,EAAOiS,CAAU,EACtF3D,EAAUzI,EAAcd,CAAM,EAC9BqI,EAAmBpN,EACb,CAACiS,EAAW,KAAMjS,GAASgB,EAAI+D,EAAQ/E,CAAI,CAAC,EAC5CsO,CAClB,MACiBtO,GACLoN,GAAoB,MAAM,QAAQ,IAAI6E,EAAW,IAAI,MAAOvO,GAAc,CACtE,MAAMc,EAAQxD,EAAIsH,EAAS5E,CAAS,EACpC,OAAO,MAAM8K,EAAyBhK,GAASA,EAAM,GAAK,CAAE,CAACd,CAAS,EAAGc,CAAO,EAAGA,CAAK,CACxG,CAAa,CAAC,GAAG,MAAM,OAAO,EAClB,EAAE,CAAC4I,GAAoB,CAACI,EAAW,UAAYY,EAAW,GAG1DhB,EAAmBkB,EAAU,MAAME,EAAyBlG,CAAO,EAEvE,OAAAyF,EAAU,MAAM,KAAK,CACjB,GAAI,CAAC1K,EAASrD,CAAI,IACZ+J,EAAgB,SAAW+D,EAAyB,UAClDQ,IAAYd,EAAW,QACzB,CAAA,EACA,CAAE,KAAAxN,CAAI,EACZ,GAAIuN,EAAS,UAAY,CAACvN,EAAO,CAAE,QAAAsO,CAAS,EAAG,GAC/C,OAAQd,EAAW,MAC/B,CAAS,EACDhG,EAAQ,aACJ,CAAC4F,GACD9D,GAAsBhB,EAAS0J,GAAahS,EAAOiS,EAAa1O,EAAO,KAAK,EACzE6J,CACV,EACKwD,GAAaqB,GAAe,CAC9B,MAAMxK,EAAS,CACX,GAAIkG,EAAO,MAAQD,EAAcD,CACpC,EACD,OAAO3M,EAAYmR,CAAU,EACvBxK,EACApE,EAAS4O,CAAU,EACfjR,EAAIyG,EAAQwK,CAAU,EACtBA,EAAW,IAAKjS,GAASgB,EAAIyG,EAAQzH,CAAI,CAAC,CACvD,EACKkS,GAAgB,CAAClS,EAAMuC,KAAe,CACxC,QAAS,CAAC,CAACvB,GAAKuB,GAAaiL,GAAY,OAAQxN,CAAI,EACrD,QAAS,CAAC,CAACgB,GAAKuB,GAAaiL,GAAY,YAAaxN,CAAI,EAC1D,MAAOgB,GAAKuB,GAAaiL,GAAY,OAAQxN,CAAI,EACjD,aAAc,CAAC,CAACgB,EAAIwM,EAAW,iBAAkBxN,CAAI,EACrD,UAAW,CAAC,CAACgB,GAAKuB,GAAaiL,GAAY,cAAexN,CAAI,CACtE,GACUmS,GAAenS,GAAS,CAC1BA,GACIgF,GAAsBhF,CAAI,EAAE,QAASoS,GAAc1L,EAAM8G,EAAW,OAAQ4E,CAAS,CAAC,EAC1FrE,EAAU,MAAM,KAAK,CACjB,OAAQ/N,EAAOwN,EAAW,OAAS,CAAE,CACjD,CAAS,CACJ,EACK6E,GAAW,CAACrS,EAAM2J,EAAOnC,IAAY,CACvC,MAAMlD,GAAOtD,EAAIsH,EAAStI,EAAM,CAAE,GAAI,EAAI,CAAA,EAAE,IAAM,CAAE,GAAE,IAChDsS,EAAetR,EAAIwM,EAAW,OAAQxN,CAAI,GAAK,CAAE,EAEjD,CAAE,IAAKuS,EAAY,QAAA9N,EAAS,KAAA1G,EAAM,GAAGyU,CAAe,EAAKF,EAC/D7Q,EAAI+L,EAAW,OAAQxN,EAAM,CACzB,GAAGwS,EACH,GAAG7I,EACH,IAAArF,CACZ,CAAS,EACDyJ,EAAU,MAAM,KAAK,CACjB,KAAA/N,EACA,OAAQwN,EAAW,OACnB,QAAS,EACrB,CAAS,EACDhG,GAAWA,EAAQ,aAAelD,GAAOA,EAAI,OAASA,EAAI,MAAO,CACpE,EACKmO,GAAQ,CAACzS,EAAMmB,IAAiB4E,EAAW/F,CAAI,EAC/C+N,EAAU,MAAM,UAAU,CACxB,KAAO2E,GAAY1S,EAAK6Q,EAAU,OAAW1P,CAAY,EAAGuR,CAAO,CACtE,CAAA,EACC7B,EAAU7Q,EAAMmB,EAAc,EAAI,EAClCwR,GAAcnV,GAAUuQ,EAAU,MAAM,UAAU,CACpD,KAAOxL,GAAc,CACbyH,GAAsBxM,EAAM,KAAM+E,EAAU,KAAM/E,EAAM,KAAK,GAC7DqM,GAAsBtH,EAAW/E,EAAM,WAAauM,EAAiB6I,GAAepV,EAAM,YAAY,GACtGA,EAAM,SAAS,CACX,OAAQ,CAAE,GAAGkQ,CAAa,EAC1B,GAAGF,EACH,GAAGjL,CACvB,CAAiB,CAER,CACJ,CAAA,EAAE,YACGsQ,GAAarV,IACfmQ,EAAO,MAAQ,GACfG,EAA2B,CACvB,GAAGA,EACH,GAAGtQ,EAAM,SACZ,EACMmV,GAAW,CACd,GAAGnV,EACH,UAAWsQ,CACvB,CAAS,GAEC6C,GAAa,CAAC3Q,EAAMwH,EAAU,CAAA,IAAO,CACvC,UAAW9D,KAAa1D,EAAOgF,GAAsBhF,CAAI,EAAIuD,EAAO,MAChEA,EAAO,MAAM,OAAOG,CAAS,EAC7BH,EAAO,MAAM,OAAOG,CAAS,EACxB8D,EAAQ,YACTd,EAAM4B,EAAS5E,CAAS,EACxBgD,EAAMgH,EAAahK,CAAS,GAEhC,CAAC8D,EAAQ,WAAad,EAAM8G,EAAW,OAAQ9J,CAAS,EACxD,CAAC8D,EAAQ,WAAad,EAAM8G,EAAW,YAAa9J,CAAS,EAC7D,CAAC8D,EAAQ,aAAed,EAAM8G,EAAW,cAAe9J,CAAS,EACjE,CAAC8D,EAAQ,kBACLd,EAAM8G,EAAW,iBAAkB9J,CAAS,EAChD,CAAC6J,EAAS,kBACN,CAAC/F,EAAQ,kBACTd,EAAM+G,EAAgB/J,CAAS,EAEvCqK,EAAU,MAAM,KAAK,CACjB,OAAQxN,EAAYmN,CAAW,CAC3C,CAAS,EACDK,EAAU,MAAM,KAAK,CACjB,GAAGP,EACH,GAAKhG,EAAQ,UAAiB,CAAE,QAAS0H,EAAS,GAAzB,CAAA,CACrC,CAAS,EACD,CAAC1H,EAAQ,aAAe4G,EAAW,CACtC,EACK0E,GAAoB,CAAC,CAAE,SAAA7P,EAAU,KAAAjD,CAAI,IAAQ,EAC1CqB,EAAU4B,CAAQ,GAAK0K,EAAO,OAC7B1K,GACFM,EAAO,SAAS,IAAIvD,CAAI,KACxBiD,EAAWM,EAAO,SAAS,IAAIvD,CAAI,EAAIuD,EAAO,SAAS,OAAOvD,CAAI,EAEzE,EACK+S,GAAW,CAAC/S,EAAMwH,EAAU,CAAA,IAAO,CACrC,IAAIhD,EAAQxD,EAAIsH,EAAStI,CAAI,EAC7B,MAAMgT,EAAoB3R,EAAUmG,EAAQ,QAAQ,GAAKnG,EAAUkM,EAAS,QAAQ,EACpF,OAAA9L,EAAI6G,EAAStI,EAAM,CACf,GAAIwE,GAAS,CAAA,EACb,GAAI,CACA,GAAIA,GAASA,EAAM,GAAKA,EAAM,GAAK,CAAE,IAAK,CAAE,KAAAxE,CAAI,GAChD,KAAAA,EACA,MAAO,GACP,GAAGwH,CACN,CACb,CAAS,EACDjE,EAAO,MAAM,IAAIvD,CAAI,EACjBwE,EACAsO,GAAkB,CACd,SAAUzR,EAAUmG,EAAQ,QAAQ,EAC9BA,EAAQ,SACR+F,EAAS,SACf,KAAAvN,CAChB,CAAa,EAGDqP,EAAoBrP,EAAM,GAAMwH,EAAQ,KAAK,EAE1C,CACH,GAAIwL,EACE,CAAE,SAAUxL,EAAQ,UAAY+F,EAAS,QAAQ,EACjD,GACN,GAAIA,EAAS,YACP,CACE,SAAU,CAAC,CAAC/F,EAAQ,SACpB,IAAKkB,GAAalB,EAAQ,GAAG,EAC7B,IAAKkB,GAAalB,EAAQ,GAAG,EAC7B,UAAWkB,GAAalB,EAAQ,SAAS,EACzC,UAAWkB,GAAalB,EAAQ,SAAS,EACzC,QAASkB,GAAalB,EAAQ,OAAO,CACzD,EACkB,GACN,KAAAxH,EACA,SAAAoE,EACA,OAAQA,EACR,IAAME,GAAQ,CACV,GAAIA,EAAK,CACLyO,GAAS/S,EAAMwH,CAAO,EACtBhD,EAAQxD,EAAIsH,EAAStI,CAAI,EACzB,MAAMiT,EAAWnS,EAAYwD,EAAI,KAAK,GAChCA,EAAI,kBACAA,EAAI,iBAAiB,uBAAuB,EAAE,CAAC,GAAKA,EAGxD4O,EAAkB9M,GAAkB6M,CAAQ,EAC5ChI,EAAOzG,EAAM,GAAG,MAAQ,CAAE,EAChC,GAAI0O,EACEjI,EAAK,KAAMvD,GAAWA,IAAWuL,CAAQ,EACzCA,IAAazO,EAAM,GAAG,IACxB,OAEJ/C,EAAI6G,EAAStI,EAAM,CACf,GAAI,CACA,GAAGwE,EAAM,GACT,GAAI0O,EACE,CACE,KAAM,CACF,GAAGjI,EAAK,OAAO5E,EAAI,EACnB4M,EACA,GAAI,MAAM,QAAQjS,EAAIyM,EAAgBzN,CAAI,CAAC,EAAI,CAAC,EAAE,EAAI,EACzD,EACD,IAAK,CAAE,KAAMiT,EAAS,KAAM,KAAAjT,CAAM,CACtE,EACkC,CAAE,IAAKiT,EAChB,CACzB,CAAqB,EACD5D,EAAoBrP,EAAM,GAAO,OAAWiT,CAAQ,CACxE,MAEoBzO,EAAQxD,EAAIsH,EAAStI,EAAM,CAAA,CAAE,EACzBwE,EAAM,KACNA,EAAM,GAAG,MAAQ,KAEpB+I,EAAS,kBAAoB/F,EAAQ,mBAClC,EAAEvH,GAAmBsD,EAAO,MAAOvD,CAAI,GAAK2N,EAAO,SACnDpK,EAAO,QAAQ,IAAIvD,CAAI,CAElC,CACJ,CACJ,EACKmT,GAAc,IAAM5F,EAAS,kBAC/BjE,GAAsBhB,EAAS0J,GAAazO,EAAO,KAAK,EACtD6P,GAAgBnQ,GAAa,CAC3B5B,EAAU4B,CAAQ,IAClB8K,EAAU,MAAM,KAAK,CAAE,SAAA9K,CAAQ,CAAE,EACjCqG,GAAsBhB,EAAS,CAAChE,EAAKtE,IAAS,CAC1C,MAAMyJ,EAAezI,EAAIsH,EAAStI,CAAI,EAClCyJ,IACAnF,EAAI,SAAWmF,EAAa,GAAG,UAAYxG,EACvC,MAAM,QAAQwG,EAAa,GAAG,IAAI,GAClCA,EAAa,GAAG,KAAK,QAASkC,GAAa,CACvCA,EAAS,SAAWlC,EAAa,GAAG,UAAYxG,CAC5E,CAAyB,EAGzB,EAAe,EAAG,EAAK,EAElB,EACKoQ,GAAe,CAACC,EAASC,IAAc,MAAOC,GAAM,CACtD,IAAIC,EACAD,IACAA,EAAE,gBAAkBA,EAAE,eAAgB,EACtCA,EAAE,SACEA,EAAE,QAAS,GAEnB,IAAIxE,EAAczO,EAAYmN,CAAW,EAIzC,GAHAK,EAAU,MAAM,KAAK,CACjB,aAAc,EAC1B,CAAS,EACGR,EAAS,SAAU,CACnB,KAAM,CAAE,OAAAxI,EAAQ,OAAA0C,CAAQ,EAAG,MAAM8G,GAAY,EAC7Cf,EAAW,OAASzI,EACpBiK,EAAcvH,CAC1B,MAEY,MAAM+G,EAAyBlG,CAAO,EAE1C,GAAI/E,EAAO,SAAS,KAChB,UAAWvD,KAAQuD,EAAO,SACtB9B,EAAIuN,EAAahP,EAAM,MAAS,EAIxC,GADA0G,EAAM8G,EAAW,OAAQ,MAAM,EAC3B3H,EAAc2H,EAAW,MAAM,EAAG,CAClCO,EAAU,MAAM,KAAK,CACjB,OAAQ,CAAE,CAC1B,CAAa,EACD,GAAI,CACA,MAAMuF,EAAQtE,EAAawE,CAAC,CAC5C,OACmB7J,EAAO,CACV8J,EAAe9J,CAC/B,CACA,MAEgB4J,GACA,MAAMA,EAAU,CAAE,GAAG/F,EAAW,MAAM,EAAIgG,CAAC,EAE/CL,GAAa,EACb,WAAWA,EAAW,EAS1B,GAPApF,EAAU,MAAM,KAAK,CACjB,YAAa,GACb,aAAc,GACd,mBAAoBlI,EAAc2H,EAAW,MAAM,GAAK,CAACiG,EACzD,YAAajG,EAAW,YAAc,EACtC,OAAQA,EAAW,MAC/B,CAAS,EACGiG,EACA,MAAMA,CAEb,EACKC,GAAa,CAAC1T,EAAMwH,EAAU,CAAA,IAAO,CACnCxG,EAAIsH,EAAStI,CAAI,IACbc,EAAY0G,EAAQ,YAAY,EAChC6J,EAASrR,EAAMO,EAAYS,EAAIyM,EAAgBzN,CAAI,CAAC,CAAC,GAGrDqR,EAASrR,EAAMwH,EAAQ,YAAY,EACnC/F,EAAIgM,EAAgBzN,EAAMO,EAAYiH,EAAQ,YAAY,CAAC,GAE1DA,EAAQ,aACTd,EAAM8G,EAAW,cAAexN,CAAI,EAEnCwH,EAAQ,YACTd,EAAM8G,EAAW,YAAaxN,CAAI,EAClCwN,EAAW,QAAUhG,EAAQ,aACvB0H,EAAUlP,EAAMO,EAAYS,EAAIyM,EAAgBzN,CAAI,CAAC,CAAC,EACtDkP,EAAW,GAEhB1H,EAAQ,YACTd,EAAM8G,EAAW,OAAQxN,CAAI,EAC7B+J,EAAgB,SAAWqE,EAAW,GAE1CL,EAAU,MAAM,KAAK,CAAE,GAAGP,CAAU,CAAE,EAE7C,EACKmG,GAAS,CAACnQ,EAAYoQ,EAAmB,CAAA,IAAO,CAClD,MAAMC,EAAgBrQ,EAAajD,EAAYiD,CAAU,EAAIiK,EACvDqG,EAAqBvT,EAAYsT,CAAa,EAC9CE,EAAqBlO,EAAcrC,CAAU,EAC7CiE,EAASsM,EAAqBtG,EAAiBqG,EAIrD,GAHKF,EAAiB,oBAClBnG,EAAiBoG,GAEjB,CAACD,EAAiB,WAAY,CAC9B,GAAIA,EAAiB,gBAAiB,CAClC,MAAMI,EAAgB,IAAI,IAAI,CAC1B,GAAGzQ,EAAO,MACV,GAAG,OAAO,KAAK4D,GAAesG,EAAgBC,CAAW,CAAC,CAC9E,CAAiB,EACD,UAAWhK,KAAa,MAAM,KAAKsQ,CAAa,EAC5ChT,EAAIwM,EAAW,YAAa9J,CAAS,EAC/BjC,EAAIgG,EAAQ/D,EAAW1C,EAAI0M,EAAahK,CAAS,CAAC,EAClD2N,EAAS3N,EAAW1C,EAAIyG,EAAQ/D,CAAS,CAAC,CAEpE,KACiB,CACD,GAAIpD,IAASQ,EAAY0C,CAAU,EAC/B,UAAWxD,KAAQuD,EAAO,MAAO,CAC7B,MAAMiB,EAAQxD,EAAIsH,EAAStI,CAAI,EAC/B,GAAIwE,GAASA,EAAM,GAAI,CACnB,MAAMwE,EAAiB,MAAM,QAAQxE,EAAM,GAAG,IAAI,EAC5CA,EAAM,GAAG,KAAK,CAAC,EACfA,EAAM,GAAG,IACf,GAAIwB,GAAcgD,CAAc,EAAG,CAC/B,MAAMiL,EAAOjL,EAAe,QAAQ,MAAM,EAC1C,GAAIiL,EAAM,CACNA,EAAK,MAAO,EACZ,KACpC,CACA,CACA,CACA,CAEgB,UAAWvQ,KAAaH,EAAO,MAC3B8N,EAAS3N,EAAW1C,EAAIyG,EAAQ/D,CAAS,CAAC,CAE9D,CACYgK,EAAcnN,EAAYkH,CAAM,EAChCsG,EAAU,MAAM,KAAK,CACjB,OAAQ,CAAE,GAAGtG,CAAQ,CACrC,CAAa,EACDsG,EAAU,MAAM,KAAK,CACjB,OAAQ,CAAE,GAAGtG,CAAQ,CACrC,CAAa,CACb,CACQlE,EAAS,CACL,MAAOqQ,EAAiB,gBAAkBrQ,EAAO,MAAQ,IAAI,IAC7D,QAAS,IAAI,IACb,MAAO,IAAI,IACX,SAAU,IAAI,IACd,MAAO,IAAI,IACX,SAAU,GACV,MAAO,EACV,EACDoK,EAAO,MACH,CAAC5D,EAAgB,SACb,CAAC,CAAC6J,EAAiB,aACnB,CAAC,CAACA,EAAiB,gBAC3BjG,EAAO,MAAQ,CAAC,CAACJ,EAAS,iBAC1BQ,EAAU,MAAM,KAAK,CACjB,YAAa6F,EAAiB,gBACxBpG,EAAW,YACX,EACN,QAASuG,EACH,GACAH,EAAiB,UACbpG,EAAW,QACX,CAAC,EAAEoG,EAAiB,mBAClB,CAACtO,GAAU9B,EAAYiK,CAAc,GACjD,YAAamG,EAAiB,gBACxBpG,EAAW,YACX,GACN,YAAauG,EACP,CAAA,EACAH,EAAiB,gBACbA,EAAiB,mBAAqBlG,EAClCvG,GAAesG,EAAgBC,CAAW,EAC1CF,EAAW,YACfoG,EAAiB,mBAAqBpQ,EAClC2D,GAAesG,EAAgBjK,CAAU,EACzCoQ,EAAiB,UACbpG,EAAW,YACX,CAAE,EACpB,cAAeoG,EAAiB,YAC1BpG,EAAW,cACX,CAAE,EACR,OAAQoG,EAAiB,WAAapG,EAAW,OAAS,CAAE,EAC5D,mBAAoBoG,EAAiB,uBAC/BpG,EAAW,mBACX,GACN,aAAc,EAC1B,CAAS,CACJ,EACK0G,GAAQ,CAAC1Q,EAAYoQ,IAAqBD,GAAO5N,EAAWvC,CAAU,EACtEA,EAAWkK,CAAW,EACtBlK,EAAYoQ,CAAgB,EAC5BO,GAAW,CAACnU,EAAMwH,EAAU,CAAA,IAAO,CACrC,MAAMhD,EAAQxD,EAAIsH,EAAStI,CAAI,EACzBgJ,EAAiBxE,GAASA,EAAM,GACtC,GAAIwE,EAAgB,CAChB,MAAMiK,EAAWjK,EAAe,KAC1BA,EAAe,KAAK,CAAC,EACrBA,EAAe,IACjBiK,EAAS,QACTA,EAAS,MAAO,EAChBzL,EAAQ,cACJzB,EAAWkN,EAAS,MAAM,GAC1BA,EAAS,OAAQ,EAErC,CACK,EACKL,GAAiBzC,GAAqB,CACxC3C,EAAa,CACT,GAAGA,EACH,GAAG2C,CACN,CACJ,EAQKnN,GAAU,CACZ,QAAS,CACL,SAAA+P,GACA,WAAApC,GACA,cAAAuB,GACA,aAAAmB,GACA,SAAAhB,GACA,WAAAM,GACA,WAAApE,GACA,YAAA4E,GACA,UAAAtC,EACA,UAAA3B,EACA,UAAAd,EACA,eAAAO,EACA,kBAAAmE,GACA,WAAA1D,EACA,eAAA0B,EACA,OAAA6C,GACA,oBAzBoB,IAAM5N,EAAWwH,EAAS,aAAa,GAC/DA,EAAS,cAAa,EAAG,KAAM9F,GAAW,CACtCyM,GAAMzM,EAAQ8F,EAAS,YAAY,EACnCQ,EAAU,MAAM,KAAK,CACjB,UAAW,EAC3B,CAAa,CACb,CAAS,EAoBG,iBAAA2C,GACA,aAAA0C,GACA,UAAArF,EACA,gBAAAhE,EACA,IAAI,SAAU,CACV,OAAOzB,CACV,EACD,IAAI,aAAc,CACd,OAAOoF,CACV,EACD,IAAI,QAAS,CACT,OAAOC,CACV,EACD,IAAI,OAAOlO,EAAO,CACdkO,EAASlO,CACZ,EACD,IAAI,gBAAiB,CACjB,OAAOgO,CACV,EACD,IAAI,QAAS,CACT,OAAOlK,CACV,EACD,IAAI,OAAO9D,EAAO,CACd8D,EAAS9D,CACZ,EACD,IAAI,YAAa,CACb,OAAO+N,CACV,EACD,IAAI,UAAW,CACX,OAAOD,CACV,EACD,IAAI,SAAS9N,EAAO,CAChB8N,EAAW,CACP,GAAGA,EACH,GAAG9N,CACN,CACJ,CACJ,EACD,UAAAoT,GACA,QAAA3B,GACA,SAAA6B,GACA,aAAAM,GACA,MAAAZ,GACA,SAAApB,EACA,UAAAT,GACA,MAAAsD,GACA,WAAAR,GACA,YAAAvB,GACA,WAAAxB,GACA,SAAA0B,GACA,SAAA8B,GACA,cAAAjC,EACH,EACD,MAAO,CACH,GAAGlP,GACH,YAAaA,EAChB,CACL,CAiVA,SAASoR,GAAQ5W,EAAQ,GAAI,CACzB,MAAM6W,EAAejS,EAAe,OAAO,MAAS,EAC9CkS,EAAUlS,EAAe,OAAO,MAAS,EACzC,CAACG,EAAWY,CAAe,EAAIf,EAAe,SAAS,CACzD,QAAS,GACT,aAAc,GACd,UAAW2D,EAAWvI,EAAM,aAAa,EACzC,YAAa,GACb,aAAc,GACd,mBAAoB,GACpB,QAAS,GACT,YAAa,EACb,YAAa,CAAE,EACf,cAAe,CAAE,EACjB,iBAAkB,CAAE,EACpB,OAAQA,EAAM,QAAU,CAAE,EAC1B,SAAUA,EAAM,UAAY,GAC5B,QAAS,GACT,cAAeuI,EAAWvI,EAAM,aAAa,EACvC,OACAA,EAAM,aACpB,CAAK,EACI6W,EAAa,UACdA,EAAa,QAAU,CACnB,GAAI7W,EAAM,YAAcA,EAAM,YAAc8P,GAAkB9P,CAAK,EACnE,UAAA+E,CACH,EACG/E,EAAM,aACNA,EAAM,eACN,CAACuI,EAAWvI,EAAM,aAAa,GAC/BA,EAAM,YAAY,MAAMA,EAAM,cAAeA,EAAM,YAAY,GAGvE,MAAMgF,EAAU6R,EAAa,QAAQ,QACrC,OAAA7R,EAAQ,SAAWhF,EACnBoF,GAA0B,IAAM,CAC5B,MAAM2R,EAAM/R,EAAQ,WAAW,CAC3B,UAAWA,EAAQ,gBACnB,SAAU,IAAMW,EAAgB,CAAE,GAAGX,EAAQ,UAAU,CAAE,EACzD,aAAc,EAC1B,CAAS,EACD,OAAAW,EAAiB3C,IAAU,CACvB,GAAGA,EACH,QAAS,EACrB,EAAU,EACFgC,EAAQ,WAAW,QAAU,GACtB+R,CACf,EAAO,CAAC/R,CAAO,CAAC,EACZJ,EAAe,UAAU,IAAMI,EAAQ,aAAahF,EAAM,QAAQ,EAAG,CAACgF,EAAShF,EAAM,QAAQ,CAAC,EAC9F4E,EAAe,UAAU,IAAM,CACvB5E,EAAM,OACNgF,EAAQ,SAAS,KAAOhF,EAAM,MAE9BA,EAAM,iBACNgF,EAAQ,SAAS,eAAiBhF,EAAM,eAEpD,EAAO,CAACgF,EAAShF,EAAM,KAAMA,EAAM,cAAc,CAAC,EAC9C4E,EAAe,UAAU,IAAM,CACvB5E,EAAM,SACNgF,EAAQ,WAAWhF,EAAM,MAAM,EAC/BgF,EAAQ,YAAa,EAE5B,EAAE,CAACA,EAAShF,EAAM,MAAM,CAAC,EAC1B4E,EAAe,UAAU,IAAM,CAC3B5E,EAAM,kBACFgF,EAAQ,UAAU,MAAM,KAAK,CACzB,OAAQA,EAAQ,UAAW,CAC3C,CAAa,CACR,EAAE,CAACA,EAAShF,EAAM,gBAAgB,CAAC,EACpC4E,EAAe,UAAU,IAAM,CAC3B,GAAII,EAAQ,gBAAgB,QAAS,CACjC,MAAMgS,EAAUhS,EAAQ,UAAW,EAC/BgS,IAAYjS,EAAU,SACtBC,EAAQ,UAAU,MAAM,KAAK,CACzB,QAAAgS,CACpB,CAAiB,CAEjB,CACK,EAAE,CAAChS,EAASD,EAAU,OAAO,CAAC,EAC/BH,EAAe,UAAU,IAAM,CACvB5E,EAAM,QAAU,CAAC8H,GAAU9H,EAAM,OAAQ8W,EAAQ,OAAO,GACxD9R,EAAQ,OAAOhF,EAAM,OAAQgF,EAAQ,SAAS,YAAY,EAC1D8R,EAAQ,QAAU9W,EAAM,OACxB2F,EAAiBsR,IAAW,CAAE,GAAGA,CAAO,EAAC,GAGzCjS,EAAQ,oBAAqB,CAEpC,EAAE,CAACA,EAAShF,EAAM,MAAM,CAAC,EAC1B4E,EAAe,UAAU,IAAM,CACtBI,EAAQ,OAAO,QAChBA,EAAQ,UAAW,EACnBA,EAAQ,OAAO,MAAQ,IAEvBA,EAAQ,OAAO,QACfA,EAAQ,OAAO,MAAQ,GACvBA,EAAQ,UAAU,MAAM,KAAK,CAAE,GAAGA,EAAQ,WAAY,GAE1DA,EAAQ,iBAAkB,CAClC,CAAK,EACD6R,EAAa,QAAQ,UAAY/R,GAAkBC,EAAWC,CAAO,EAC9D6R,EAAa,OACxB", "x_google_ignoreList": [3]}