import { type PermissionGrantInfoDto } from '@/client'
import { Permissions } from '@/lib/utils'
import { useCallback, useEffect, useState } from 'react'

/**
 * Hook to manage permission changes for different types of entities.
 *
 * @param {UsePermissionsChangesProps} props - The properties for the hook.
 * @returns {Object} - An object containing the state and handlers for permission changes.
 */
export type UsePermissionsChangesProps = {
  permissions: PermissionGrantInfoDto[]
  type: 'identity' | 'tenant' | 'feature' | 'setting' | 'identityServer'
}

/**
 * Helper function to update the permission data based on the selected permission.
 *
 * @param {PermissionGrantInfoDto[]} data - The current permission data.
 * @param {PermissionGrantInfoDto} selectedData - The selected permission data.
 * @param {string} permission - The permission to be updated.
 * @param {Function} setData - The function to update the permission data state.
 */
const helper = (
  data: PermissionGrantInfoDto[],
  selectedData: PermissionGrantInfoDto,
  permission: string,
  setData: (data: PermissionGrantInfoDto[]) => void
) => {
  const parent = data.find((f) => !f.parentName && f.name === permission)
  const children = data.filter((f) => f.parentName === permission)

  if (selectedData.parentName === permission && parent) {
    if (selectedData.isGranted) {
      selectedData.isGranted = false
      parent.isGranted = false
    } else {
      selectedData.isGranted = true
    }
    // If all the children got granted then updated the parent as well.
    if (!parent.isGranted) {
      const hasChildrenSelected = children.every((c) => c.isGranted)
      if (hasChildrenSelected) {
        parent.isGranted = true
      }
    }
    setData([...data])
    return false
  }

  if (!selectedData.parentName && selectedData.name === permission) {
    if (parent?.isGranted) {
      parent.isGranted = false
      children.forEach((c) => (c.isGranted = false))
    } else if (parent && !parent.isGranted) {
      parent.isGranted = true
      children.forEach((c) => (c.isGranted = true))
    }
    setData([...data])
  }
}

// Helper function to detect permission type from permissions array
const detectPermissionType = (permissions: PermissionGrantInfoDto[]): string => {
  // Check if any permission is related to IdentityProvider
  if (permissions.some((p) => p.name?.startsWith('IdentityServer.'))) {
    return 'identityServer'
  }
  // Check for other permission types
  if (permissions.some((p) => p.name?.startsWith('AbpIdentity.'))) {
    return 'identity'
  }
  if (permissions.some((p) => p.name?.startsWith('AbpTenantManagement.'))) {
    return 'tenant'
  }
  if (permissions.some((p) => p.name?.startsWith('FeatureManagement.'))) {
    return 'feature'
  }
  if (permissions.some((p) => p.name?.startsWith('SettingManagement.'))) {
    return 'setting'
  }
  // if (permissions.some((p) => p.name?.startsWith('WismaApp.'))) {
  //   return 'wismaApp'
  // }
  return ''
}

// Normalize permission type string to handle various format inputs
const normalizePermissionType = (type: string): string => {
  if (!type) return ''

  // Convert to lowercase and remove spaces
  const normalized = type.toLowerCase().replace(/\s/g, '')

  // Handle special cases
  if (normalized.includes('identityserver') || normalized.includes('permission:identityserver')) {
    return 'identityserver'
  }
  if (normalized.includes('identity') && !normalized.includes('identityserver')) {
    return 'identity'
  }
  if (normalized.includes('tenant')) {
    return 'tenant'
  }
  // if (normalized.includes('wismaApp')) {
  //   return 'wismaApp'
  // }
  if (normalized.includes('feature')) {
    return 'feature'
  }
  if (normalized.includes('setting')) {
    return 'setting'
  }

  return normalized
}

export const usePermissionsChanges = ({
  permissions,
  type: providedType,
}: UsePermissionsChangesProps) => {
  // Flag determine to enable/disable the selected permissions to a user.
  const [hasAllSelected, setHasAllSelected] = useState(false)
  const [data, setData] = useState<PermissionGrantInfoDto[]>(permissions)

  // Auto-detect type if needed and normalize
  const detectedType = detectPermissionType(permissions)
  const normalizedType = normalizePermissionType(providedType || detectedType)

  /**
   * Handler for changes in the current permission.
   *
   * @param {number} idx - The index of the selected permission.
   */
  const onCurrentPermissionChanges = useCallback(
    (idx: number) => {
      const selectedData = data[idx]

      // If selectedData is undefined, don't proceed
      if (!selectedData) return;

      // wait for all the events to get done, then check.
      setTimeout(() => {
        const allSelected = data.every((d) => d.isGranted)
        setHasAllSelected(allSelected)
      }, 0)

      // Check which type we're dealing with based on the normalized type
      if (normalizedType === 'identity') {
        helper(data, selectedData, Permissions.ROLES, setData)
        helper(data, selectedData, Permissions.USERS, setData)
      } else if (normalizedType === 'tenant') {
        helper(data, selectedData, Permissions.TENANTS, setData)
      } else if (normalizedType === 'feature') {
        helper(data, selectedData, Permissions.MANAGE_HOST_FEATURES, setData)
      } else if (normalizedType === 'setting') {
        helper(data, selectedData, Permissions.SETTINGS, setData)
      } else if (normalizedType === 'identityprovider') {
        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_CLAIMS, setData)
        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_CLAIM_TYPES, setData)
        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_OPENIDDICT_APPLICATIONS, setData)
        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_OPENIDDICT_SCOPES, setData)
        helper(data, selectedData, Permissions.IDENTITY_PROVIDER_OPENIDDICT_RESOURCES, setData)
      } else {
        // Toggle the permission directly as a fallback
        console.warn('Unknown permission type:', providedType)
        selectedData.isGranted = !selectedData.isGranted
        setData([...data])
      }
    },
    // Only depend on data and normalizedType, not permissions
    [data, normalizedType]
  )

  /**
   * Handler to update the state when all permissions are selected or deselected.
   */
  const onHasAllSelectedUpdate = useCallback(() => {
    setHasAllSelected((prevAllSelected) => {
      const newState = !prevAllSelected

      // First update root permissions
      const rootPermissions = data.filter((p) => !p.parentName)
      rootPermissions.forEach((root) => {
        root.isGranted = newState

        // Then update all children of this root
        const children = data.filter((p) => p.parentName === root.name)
        children.forEach((child) => {
          child.isGranted = newState
        })
      })

      // Update any permissions that might not follow parent-child pattern
      data.forEach((permission) => {
        if (!permission.parentName && !rootPermissions.includes(permission)) {
          permission.isGranted = newState
        }
      })

      // Update the local state with the new data
      setData([...data])

      return newState
    })
  }, [data])

  // Initialize data when permissions change
  useEffect(() => {
    setData(permissions)
  }, [permissions])

  // Update hasAllSelected when data changes
  useEffect(() => {
    setHasAllSelected(data.every((d) => d.isGranted))
  }, [data])

  return {
    hasAllSelected,
    data,
    onCurrentPermissionChanges,
    onHasAllSelectedUpdate,
  }
}